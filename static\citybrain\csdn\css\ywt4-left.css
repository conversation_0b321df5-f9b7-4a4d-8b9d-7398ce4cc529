[v-cloak] {
  display: none;
}
* {
  margin: 0;
  padding: 0;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
#ywt4-left {
  overflow: hidden;
}
.ywt4-left_box {
  position: relative;
  width: 2070px;
  height: 1900px;
  /* background: url('/img/left-bg.png') no-repeat;
  background-size: 100%; */
  padding: 10px 55px 30px;
  box-sizing: border-box;
}
.ywt4-left_box1 {
  position: relative;
  width: 2070px;
  height: 1900px;
  /* background: url('/img/left-bg.png') no-repeat;
  background-size: 100%; */
  padding: 10px 55px 30px;
  box-sizing: border-box;
}
.yg-con {
  width: 100%;
  height: 50%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 77px;
}
.yg-desc {
  font-size: 32px;
  color: #fff;
  text-indent: 4rem;
  margin-top: 50px;
  line-height: 60px;
}
.yg-con li {
  list-style: none;
  width: 31%;
  height: 400px;
  border: 1px solid #27a7fa;
  box-sizing: border-box;
  position: relative;
}
.btn {
  width: 144px;
  height: 55px;
  line-height: 55px;
  text-align: center;
  font-size: 28px;
  border: 1px solid #02638c;
  background-color: #043755;
  color: #fff;
  position: absolute;
  right: 40px;
  top: 31px;
  cursor: pointer;
}
.yg-title {
  font-size: 32px;
  line-height: 70px;
  height: 70px;
  width: 100%;
  text-align: center;
  color: #fff;
  background-color: rgb(24 27 27 / 70%);
  position: absolute;
  bottom: 0px;
}
.time-con {
  width: 100%;
  height: 25%;
  background-color: #00c0ff;
}
.box {
  margin-bottom: 50px;
}
.box:last-child {
  margin-bottom: 0;
}
.box-con {
  margin-top: 30px;
}
.box1-content {
  width: 100%;
  height: 320px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
}
.box1-con-item {
  width: 25%;
  position: relative;
}
.djtl-echarts {
  position: absolute;
  left: 160px;
  bottom: -20px;
  /* background-color: rgba(142, 12, 12, 0.339); */
}
.box2-content {
  width: 100%;
  height: 470px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box2-left {
  flex: 0.5;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
}
.box2-right {
  flex: 0.49;
  height: 100%;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szzf-right-bg.png');
  background-size: 98%;
  background-repeat: no-repeat;
  background-position: 17px -130px;
}
.box2-left-item {
  width: 470px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szzf-left-bg.png');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
.box2-left-item > img {
  display: inline-block;
  width: 90px;
  height: 90px;
  margin: 0 10px;
}
.b2-l-i-t {
  flex: 1;
}
.b2-l-i-t > p {
  margin: 0;
  padding: 0;
  color: #fff;
  font-size: 28px;
}
.b2-l-i-t > p:first-child > span:first-child {
  font-size: 52px;
}
.box2-title-top {
  width: 100%;
  font-size: 36px;
  color: #fff;
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
.box2-title-top > div {
  flex: 1;
  text-align: center;
}

.box2-r-item {
  width: 100%;
  color: #fff;
  font-size: 30px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;
  top: -20px;
}
.box2-r-item > li {
  width: 20%;
  text-align: center;
}
.box2-r-item img {
  display: inline-block;
  width: 152px;
  height: 130px;
}
.box2-r-item > li:nth-child(2) {
  margin-left: 420px;
}
.box2-r-item > li:nth-child(3) {
  margin-left: 150px;
  margin-top: -30px;
}
.box2-r-item > li:nth-child(4) {
  margin-right: 150px;
  margin-top: -30px;
}
.box2-r-item > li:nth-child(5) {
  position: absolute;
  top: 200px;
  left: 40%;
}
/* 数字经济 */
.box3-content {
  width: 100%;
  height: 500px;
  display: flex;
  justify-content: space-between;
}
.box3-con-left {
  width: 566px;
  height: 100%;
  font-size: 28px;
  color: #fff;
  padding-top: 20px;
  box-sizing: border-box;
}
.box3-con-left p {
  margin: 0;
  padding: 0;
}
.box3-con-left > p {
  font-size: 30px;
  text-align: center;
  margin: 20px 0;
}
.box3-left-item {
  width: 100%;
  height: 130px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szjj-left-bg.png');
  background-size: 100% 100%;
}
.box3-left-item > div:first-child {
  width: 50%;
}
.box3-left-item > div:first-child > div {
  width: 60%;
}
.box3-left-item > div > div {
  width: 100%;
  box-sizing: border-box;
}
.box3-left-item > div {
  width: 25%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.box3-left-item > div p {
  width: 100%;
  text-align: center;
}
.box3-left-item > div p > span {
  display: inline-block;
  width: 22px;
  height: 26px;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szjj-arrow.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  margin-left: 5px;
}
.box3-left-item img {
  width: 116px;
  height: 85px;
}
/* .line-css::after{
        content: "";
        width: 2px;
        height: 110px;
        background: linear-gradient(0deg, rgba(0,41,68,0.01) 0%, rgba(236,239,242,0.97) 46%, rgba(11,49,74,0.03) 100%);
    } */
.line-css::before {
  content: '';
  width: 1px;
  height: 110px;
  background: linear-gradient(
    0deg,
    rgba(0, 41, 68, 0.01) 0%,
    rgba(236, 239, 242, 0.97) 46%,
    rgba(11, 49, 74, 0.03) 100%
  );
}
.box3-con-right {
  width: 1370px;
  height: 100%;
  padding-left: 50px;
  box-sizing: border-box;
}
.box3-con-top {
  width: 100%;
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30px;
  color: #fff;
  margin-bottom: 30px;
}
.b3-tab {
  flex: 0.3;
  height: 100%;
  text-align: center;
  line-height: 70px;
  background: url('/static/citybrain/csdn/img/ywt/doubleclick.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
.tab-acitve {
  background-image: url('/static/citybrain/csdn/img/ywt/doubleclick-active.png') !important;
}
.box3-con-bottom {
  width: 100%;
  height: 400px;
  /* display: flex;
        justify-content: space-between; */
}
.box3-bottom-item {
  width: 395px;
  height: 100%;
  font-size: 30px;
  color: #fff;
  text-align: center;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szjj-right-bg.png');
  background-size: 100% 100%;
}
.box3-bottom-item-2 {
  width: 315px;
}
.box3-bottom-item-2 .b3-b-con {
  height: 60%;
}
.box3-bottom-item-2 .pm-css:first-child {
  position: fixed;
  bottom: 10px;
}
.b3-b-con {
  width: 100%;
  height: calc(100% - 50px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.b3-b-con > div {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.b3-b-con p {
  margin: 20px 0;
}
.szjj-arrow {
  display: inline-block;
  width: 22px;
  height: 26px;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szjj-arrow.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  margin-left: 5px;
}
.b3-b-title {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/tab-item-title.png');
  background-size: 100% 100%;
  background-position: center;
}
.pm-css {
  width: 280px;
  height: 60px;
  margin: 0 auto;
  text-align: center;
  line-height: 60px;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/pm-bg.png');
  background-size: 100%;
  background-repeat: no-repeat;
  font-weight: bold;
}
.btn-left {
  width: 88px;
  position: absolute;
  top: 150px;
  left: -20px;
  opacity: 0.75;
  z-index: 10;
}

.btn-left:active {
  opacity: 1;
}
.btn-right {
  width: 88px;
  position: absolute;
  top: 150px;
  right: -20px;
  opacity: 0.75;
  z-index: 10;
}

.btn-right:active {
  opacity: 1;
}
.el-carousel__container {
  height: 400px;
}
.top-title {
  width: 100%;
  font-size: 30px;
  text-align: center;
  margin: 40px 0;
  color: #fff;
}
.box2-r-item > li:hover {
  background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}
.box2-r-item > li:hover .breath-light {
  opacity: 1 !important;
  animation-play-state: paused;
}
.gzw-img {
  width: 320px;
  height: 230px;
  margin-bottom: 20px;
}
.gzw-right {
  display: flex;
  /* margin-top: -50px; */
  height: 300px;
  width: 100%;
  padding: 0 50px;
  /* margin-left: 32px; */
}
.gzw-tiem-name {
  font-size: 32px;
  color: #fff;
}
.class-item {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url(/static/citybrain/csdn//img/ywt/item.png);
  background-size: 100% 100%;
  width: 197px;
  height: 180px;
  margin-right: 38px;
  margin-top: 20px;
  /* margin: 20px 16px px 0px; */
}
.gzw-item {
  /* flex: 0.98; */
  /* width: 987px; */
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  /* justify-content: space-between; */
  align-content: space-between;
  position: relative;
}
.gzw-item-0 {
  display: flex;
  align-content: center;
  font-size: 32px;
  margin-bottom: 20px;
}
.gzw-item-img {
  width: 120px;
  height: 115px;
  margin-right: 10px;
}
.gzw-item-img > img {
  width: 100%;
  height: 100%;
}
.gzw-tiem-name {
  font-size: 32px;
  color: #fff;
}

.gzw-sp {
  display: flex;
  margin-left: 58px;
}
.gzw-sp-item {
  width: 400px;
  height: 100%;
  font-size: 28px;
  text-align: center;
  color: #fff;
  background-image: url(./img/ywt/gdsp.png);
  background-size: 100% 100%;
}
.gzw-sp-item:first-child {
  margin-right: 50px;
  background-image: url(./img/ywt/xlsp.png);
}
.gzw-img {
  width: 320px;
  height: 230px;
  margin-bottom: 20px;
}
.gzw-img > img {
  width: 100%;
  height: 100%;
}
.class-item .tooltiptext {
  visibility: visible;
  white-space: nowrap;
  font-size: 32px;
  color: #fff;
  text-align: center;
  position: absolute;
  bottom: 20px;
  z-index: 1;
}
.gzw_right {
  overflow: hidden;
}
.pname {
  width: 325px;
  height: 60px;
  line-height: 60px;
  font-weight: bold;
  border-left: 10px solid #00befd;
  padding: 0px 20px;
  box-sizing: border-box;
  color: #fff;
  font-size: 32px;
  margin-top: 45px;
}
.children-con {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  /* justify-content: space-between; */
  align-content: space-between;
}
.wlgz-t {
  width: 48%;
  margin-right: 0px;
}
.wlgz-t:nth-child(9),
.wlgz-t:nth-child(10),
.wlgz-t:nth-child(11),
.wlgz-t:nth-child(12),
.wlgz-t:nth-child(13) {
  width: 12.5%;
  margin-right: -10px;
}
.wlgz-t:nth-child(11) {
  margin-right: 224px;
}
.active {
  background-color: #1076d0 !important;
}
