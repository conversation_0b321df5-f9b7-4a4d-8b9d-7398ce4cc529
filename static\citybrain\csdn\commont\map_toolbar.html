<!--
 * @Author: <PERSON><PERSON> && <EMAIL>
 * @Date: 2023-08-28 17:59:24
 * @LastEditors: <PERSON>_<PERSON><PERSON><PERSON> && <EMAIL>
 * @LastEditTime: 2023-12-13 16:04:33
 * @FilePath: \Jin_hua_Code\screenCode\static\citybrain\csdn\commont\map_toolbar.html
 * @Description: 一个文件三个面板，通过监听main_mapIcon.html点击的按钮来判断加载哪个面板，工具二级面板通过将主面板隐藏，二级面板追加到主面板的方式加载，返回主面板时将二级面板隐藏
 * 
 * Copyright (c) 2023 by <PERSON><PERSON><PERSON><PERSON><PERSON>, All Rights Reserved. 
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>地图工具栏-测量</title>
  <script src="/static/citybrain/hjbh/js/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <link rel="stylesheet" href="/static/citybrain/tckz/icon/iconfont.css" />
  <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/esri/themes/dark/main.css" />
  <script src="/static/js/jslib/jquery-3.6.1.min.js"></script>
  <!-- <script src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/geolocate.min.js"></script> -->
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
  <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
  <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
  <script src="/static/js/home_services/md5.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>  
  <style>
    body,
    html {
      margin: 0;
    }
  
    [v-cloak] {
      display: none;
    }
  
    /* 设置滚动条的样式 */
    ::-webkit-scrollbar {
      width: 15px;
    }
  
    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      border-radius: 5px;
    }
  
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(35, 144, 207, 0.4);
    }
  
    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(27, 146, 215, 0.6);
    }
  
    .toolbar {
      width: 500px;
        height: 1200px;
        background: linear-gradient(180deg, rgba(2, 51, 110, 0.85), rgba(8, 37, 71, 0.85));
      position: relative;
      color: #fff;
      font-size: 30px;
      background-size: 100% 100%;
      padding: 40px 20px 20px 20px;
      box-sizing: border-box;
    }
  
    .header {
      height: 50px;
      line-height: 50px;
      font-size: 30px;
      font-weight: bolder;
      padding-left: 25px;
      background: url('/static/citybrain/tckz/img/main_mapIcon/header.svg') no-repeat;
      background-size: 100% 100%;
    }
  
    .main {
      padding: 30px 30px 0;
      box-sizing: border-box;
      height: 1000px;
      overflow: hidden auto;
    }
  
    .items-title {
      height: 30px;
      margin-bottom: 10px;
    }
  
    .items-title>div,
    #backBtn,
    .skyline,
    .esri-button,
    #cancleRender,
    #saveRender {
      float: right;
      padding: 0 16px;
      height: 35px;
      cursor: pointer;
      /* background-image: linear-gradient(180deg,hsla(0,0%,100%,.4),hsla(0,0%,100%,0)); */
        border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
        transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
        background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
    }
  
    .items-title>div:hover,
    #backBtn:hover,
    .skyline:hover,
    .esri-button:hover,
    #cancleRender:hover,
    #saveRender:hover {
      background: #2960cb;
      color: white;
    }
  
      #backBtn {
      float: left;
    }
  
    #cancleRender,
    #saveRender {
      float: right;
      margin-top: 30px;
      margin-left: 30px;
    }
  
    .skyline {
      float: none;
      margin-top: 25px;
      text-align: center;
    }
  
    #toolPanel {
      margin-top: 60px;
    }
  
    .items-con {
      height: max-content;
        color: hsla(0, 0%, 100%, 0.8);
        background: rgba(25, 27, 35, 0.96);
      padding: 10px 20px;
      box-sizing: border-box;
      font-size: 20px;
    }
  
    .items-con>div {
      height: 30px;
      line-height: 30px;
    }
  
  
    /* el */
    .el-input {
      font-size: 30px;
    }
  
    .el-input__inner {
      margin-top: 30px;
      height: 40px;
      line-height: 30px;
      background-color: rgba(25, 27, 35, 0);
        color: hsla(0, 0%, 100%, 0.8);
    }
  
    .el-input__icon {
      height: 30px;
      line-height: 30px;
    }
  
    .popper__arrow {
      display: none !important;
    }
  
    .el-select-dropdown {
        background-color: rgba(25, 27, 35, 0.96);
    }
  
    .el-popper[x-placement^=bottom] {
      margin-top: -2px;
    }
  
    .el-select-dropdown__item {
      color: #fff;
      height: 40px;
      line-height: 40px;
      font-size: 30px;
    }
  
    .el-scrollbar__wrap {
      margin-bottom: -20px !important;
    }
  
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #2a4b8b;
    }
  
    .el-select-dropdown__item.selected {
      background-color: #2a4b8b;
      color: #fff;
      font-weight: 400;
    }
  
  
    .esri-widget {
      font-size: 30px;
      background-color: rgba(36, 36, 36, 0);
    }
  
    .esri-button {
      float: none;
    }
  
    .esri-daylight {
      width: auto;
      font-size: 14px;
    }
  
    .esri-area-measurement-3d__measurement,
    .esri-direct-line-measurement-3d__measurement {
      background-color: rgba(36, 36, 36, 0);
    }
  
    .esri-area-measurement-3d__units-select,
    .esri-direct-line-measurement-3d__units-select,
    .esri-area-measurement-3d__units-select option,
    .esri-direct-line-measurement-3d__units-select option {
      background: none;
    }
  
    .esri-building-level-picker-label--empty {
      font-size: 20px;
    }
  
    h3.esri-widget__heading {
      font-size: 25px;
    }
  
    .esri-building-disciplines-tree-node__label {
      font-size: 25px;
    }
  
    .esri-building-level-picker__label-container {
      height: 140px;
    }
  
    .esri-building-level-picker__arrow-up,
    .esri-building-level-picker__arrow-down {
      font-size: 50px;
      color: white;
    }
  
    .transparent {
      font-size: 26px;
      color: #c0d6ed;
      padding-left: 20px;
      box-sizing: border-box;
    }
  
    .transparent p {
      margin: 0;
    }
  
    .playBtn {
      float: right;
      padding: 2px 16px;
      height: 35px;
      cursor: pointer;
      background-image: linear-gradient(180deg, hsla(0, 0%, 100%, .4), hsla(0, 0%, 100%, 0));
      border: 1px solid hsla(0, 0%, 100%, .30980392156862746);
      box-sizing: border-box;
      transition: .3s;
      line-height: 26px;
    }
  
    .playBtn:hover {
      background: #2960cb;
    }
  
    .RZSelect {
      margin-top: 20px;
    }
  
    .rzSlider {
      margin-top: 20px;
    }
  
    .el-picker-panel {
      background: linear-gradient(180deg, rgba(2, 51, 110, .85), rgba(0, 37, 80, .85));
      border: 1px solid;
      border-image: linear-gradient(90deg, rgba(0, 251, 255, .8509803921568627), rgba(0, 132, 255, .8509803921568627)) 1 1;
    }
  
    .el-picker-panel__icon-btn {
      color: #fff;
    }
  
    .el-date-picker__header-label {
      font-size: 20px;
      color: #fff;
    }
  
    .el-date-table {
      font-size: 16px;
    }
  
    .el-picker-panel {
      color: #fff;
    }
  
    .el-date-table th {
      color: #fff;
    }
  
    .el-slider__runway {
      width: 400px;
      height: 5px;
      margin-left: 0;
    }
  
    #groundSlider .el-slider__runway {
      width: 230px;
    }
  
    .el-slider__bar {
      height: 5px;
    }
  
    .el-slider__button {
      width: 7.7px;
      height: 7.7px;
      margin-top: -5px;
      margin-left: 10px;
      background-color: #0083ff;
      border-color: #fff;
      border-radius: 0;
      border-width: 1px;
      transform: translateX(-50%) rotate(45deg) !important;
    }
  
    .el-slider__button:after {
      content: "";
      position: absolute;
      background-image: url('/static/citybrain/tckz/img/main_mapIcon/slider.svg');
      width: 28px;
      height: 28px;
      left: -10px;
      top: -10px;
      transform: rotate(45deg);
      background-size: 100%;
    }
  
    .el-button--mini {
      font-size: 20px;
    }
  
    .el-color-picker__panel {
      left: 50px;
      width: 380px;
    }
  
    .el-color-dropdown__btns .el-input__inner {
      color: black;
    }
  
    .el-color-svpanel,
    .el-color-alpha-slider {
      width: 330px;
    }
  
    .el-color-hue-slider.is-vertical {
      width: 40px;
    }
  
    .el-color-picker__trigger {
      width: 400px;
      height: 40px;
    }
  
    .el-color-alpha-slider {
      height: 30px;
    }
  
    .el-color-dropdown__btns .el-input__inner {
      width: 230px;
      font-size: 20px;
    }
  
    .el-scrollbar {
      background: linear-gradient(180deg, rgba(2, 51, 110, .85), rgba(8, 37, 71, .85));
    }
  
    .el-scrollbar__bar.is-vertical {
      width: 40px;
    }
  
    .el-scrollbar__bar {
      opacity: 1;
    }
  
    #angleValue,
    #queryRes {
      padding: 24px;
      font-size: 30px;
    }
  
    .info-box {
      width: 100%;
      margin-top: 50px;
      box-sizing: border-box;
      height: 530px;
      overflow-y: auto;
    }
    .info-ul{
      padding-inline-start: 0px;
    }
    .info-ul > li {
      width: 100%;
      display: flex;
    }
    .info-ul > li > p {
      width: 50%;
    }
    .info-ul>li:nth-child(odd) {
      background-color: #0f3360;
    }
  
    .info-ul>li:nth-child(even) {
      background-color: #09254a;
    }
    
    #mixControlPanel .slider {
        height: 40px;
        width: 100%;
      }
      #buildLayoutPanel p {
        white-space: nowrap;
        text-align: center;
      }
      .viewTitle{
        margin-top: 10px;
        font-weight: bolder;
      }
      .esri-feature-form__label{
        margin: 30px 0;
      }
      .videoMix-border{
        padding: 0 20px 15px;
        border: 1px solid #DCDFE6;
        border-radius: 10px;
      }
      #autoLayout-info{
        display: none;
        height: auto !important;
        font-size: 25px;
      }
      #featureEditorWidget > div > calcite-flow > calcite-flow-item > div.esri-editor__panel-content > div:nth-child(2){
        display: none;
      }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <div class="toolbar" id="toolbar">
      <div class="header" id="header">
        <span></span>
      </div>
      <div class="main" id="mainPanel">
      </div>
      <!-- 测量工具主面板 -->
      <div id="measureMainPanel" style="display: none;">
        <div style="margin-bottom: 45px;" id="area-box">
          <div class="items-title">
            <span>面积测量</span>
            <div @click="measureFun('面积测量')">使用</div>
          </div>
          <div id="areaTool"></div>
        </div>
        <div style="margin-bottom: 45px;" id="dis-box">
          <div class="items-title">
            <span>距离测量</span>
            <div @click="measureFun('距离测量')">使用</div>
          </div>
          <div id="disTool"></div>
        </div>
        <div style="margin-bottom: 45px;" id="angle-box">
          <div class="items-title">
            <span>角度测量</span>
            <div @click="measureFun('角度测量')">使用</div>
          </div>
          <div id="angleTool"></div>
        </div>
        <div id="coordPick-box">
          <div class="items-title">
            <span>坐标拾取</span>
            <div @click="measureFun('坐标拾取')">使用</div>
          </div>
          <div id="coordPickTool" class="esri-widget" style="padding:12px">
            <div class="info-box" style="margin-top: auto;height: 680px;">
              <div id="coordInfoTitle"></div>
              <ul class="info-ul">
                <li v-for="item in coordPickResult">
                  <div v-html="item.coord" style="margin-top: 20px; user-select:text"></div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <!-- 标绘工具主面板 -->
      <div id="drawMainPanel" style="display: none;">
        <div style="margin-bottom: 45px;" id="drawPoint-box">
          <div class="items-title">
            <span>绘点</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘点'?'':'绘点';drawFun('点');">{{drawFlag=='绘点'?'关闭':'使用'}}</div>
          </div>
          <div v-if="drawFlag=='绘点'" class="esri-widget" style="padding:12px" id="drawPoint-box-panel">请在地图上绘制点</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawPolygon-box">
          <div class="items-title">
            <span>绘面</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘面'?'':'绘面';drawFun('面');">{{drawFlag=='绘面'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='绘面'" class="esri-widget" style="padding:12px" id="drawPolygon-box-panel">请在地图上绘制面</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawPolyline-box">
          <div class="items-title">
            <span>绘线</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘线'?'':'绘线';drawFun('线');">{{drawFlag=='绘线'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='绘线'" class="esri-widget" style="padding:12px" id="drawPolyline-box-panel">请在地图上绘制线</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawCircle-box">
          <div class="items-title">
            <span>绘圆</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘圆'?'':'绘圆';drawFun('圆');">{{drawFlag=='绘圆'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='绘圆'" class="esri-widget" style="padding:12px" id="drawCircle-box-panel">请在地图上绘制圆</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawRect-box">
          <div class="items-title">
            <span>绘矩形</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘矩形'?'':'绘矩形';drawFun('矩形');">{{drawFlag=='绘矩形'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='绘矩形'" class="esri-widget" style="padding:12px" id="drawRect-box-panel">请在地图上绘制矩形</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawdotline-box">
          <div class="items-title">
            <span>绘虚线</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘虚线'?'':'绘虚线';drawFun('虚线');">{{drawFlag=='绘虚线'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='绘虚线'" class="esri-widget" style="padding:12px" id="drawdotline-box-panel">请在地图上绘制虚线</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawlinePolygon-box">
          <div class="items-title">
            <span>绘竖向条纹面</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='绘竖向条纹面'?'':'绘竖向条纹面';drawFun('条纹面');">{{drawFlag=='绘竖向条纹面'?'关闭':'使用'}}</div>
          </div>
          <div v-if="drawFlag=='绘竖向条纹面'"  class="esri-widget" style="padding:12px" id="drawlinePolygon-box-panel">请在地图上绘制竖向条纹面</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawdotSignal-box">
          <div class="items-title">
            <span>X标记</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='X标记'?'':'X标记';drawFun('X标记');">{{drawFlag=='X标记'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='X标记'" class="esri-widget" style="padding:12px" id="drawdotSignal-box-panel">请在地图上绘制X标记</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawTriSignal-box">
          <div class="items-title">
            <span>三角标记</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='三角标记'?'':'三角标记';drawFun('三角标记');">{{drawFlag=='三角标记'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='三角标记'" class="esri-widget" style="padding:12px" id="drawTriSignal-box-panel">请在地图上绘制三角标记</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawCubicSignal-box">
          <div class="items-title">
            <span>方形标记</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='方形标记'?'':'方形标记';drawFun('方形标记');">{{drawFlag=='方形标记'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='方形标记'" class="esri-widget" style="padding:12px" id="drawCubicSignal-box-panel">请在地图上绘制方形标记</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawArrow-box">
          <div class="items-title">
            <span>态势箭头</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='态势箭头'?'':'态势箭头';drawFun('态势箭头');">{{drawFlag=='态势箭头'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='态势箭头'" class="esri-widget" style="padding:12px" id="drawArrow-box-panel">请在地图上绘制态势箭头</div>
        </div>
        <div style="margin-bottom: 45px;" id="drawSwallowTail-box">
          <div class="items-title">
            <span>燕尾箭头</span>
            <div class="drawBtn" @click="drawFlag= drawFlag=='燕尾箭头'?'':'燕尾箭头';drawFun('燕尾箭头');">{{drawFlag=='燕尾箭头'?'关闭':'使用'}}</div>
          </div>
          <div  v-if="drawFlag=='燕尾箭头'" class="esri-widget" style="padding:12px" id="drawSwallowTail-box-panel">请在地图上绘制燕尾箭头</div>
        </div>

      </div>
      <!-- 空间分析工具主面板 -->
      <div id="spatialMainPanel" style="display: none;">
        <div style="margin-bottom: 45px;" v-for="item in spatialAnalToolItems">
          <div class="items-title">
            <span>{{item.name}}</span>
            <div @click="spatialAnal(item.name)">使用</div>
          </div>
        </div>
      </div>

      <!-- 地表透明度面板 -->
      <div style="display: none" id="groundSlider">
        <div>
          <el-slider v-model="sliderValue" :min="min" :max="max" :show-tooltip="false"
            @change="groundSliderChange"></el-slider>
          <div class="transparent">
            <p>透明度：{{sliderValue/10}}</p>
          </div>
        </div>
      </div>

      <!-- 穹顶分析面板 -->
      <div id="qdAnalysis" style="display: none;">
        <!-- <div class="esri-widget"></div> -->
        <div class="skyline" id="qdAnalysisBtn" @click="qdAnalysisFun">绘制分析半径</div>
        <div id="analysisRes">
          <p>分析总数：0</p>
          <p>可见总数：0</p>
          <p>不可见总数：0</p>
          <p>开敞度：0%</p>
        </div>
      </div>
      <!-- 日照面板 -->
      <div style="display: none" id="daylight">
        <div>
          <div>
            <span>模拟日照：</span>
            <div class="playBtn" @click="ctRzPlay()">
              <i :class="rzPlay?'el-icon-video-pause':'el-icon-video-play'"></i>
            </div>
          </div>
          <div style="margin-top: 30px;">选择分析日期：</div>
          <el-date-picker class="RZSelect" v-model="rzDate" value-format="yyyy-MM-dd" type="date" @change="changeRzTime"
            placeholder="选择日期">
          </el-date-picker>
          <div class="rzSlider">
            <span>模拟时间：{{formatRzTime}}</span>
            <el-slider :max="1439" v-model="rzTime" :format-tooltip="formatTooltip"
              @change="lightsliderChange"></el-slider>
          </div>
        </div>
      </div>

      <!-- 淹没分析面板 -->
      <div style="display: none" id="floodPanel">
        <div>
          <div class="skyline" id="floodRegion" @click="floodSimulate">绘制分析区域</div>
          <!-- <div class="skyline" id="floodstart" @click="floodSliderChange">开始分析</div> -->
          <div class="skyline" id="floodclear" @click="floodSimulate">清除</div>
          <div id="floodArea"></div>
          <!-- <el-slider style="margin-top: 30px;" v-model="baseSliderValue" :min="baseMin" :max="baseMax"
                    :show-tooltip="false" @change="baseSliderChange"></el-slider>
                  <div class="transparent">
                    <p>设置基面高度：{{baseSliderValue}}米</p>
                  </div>
                  <el-slider v-model="floodTimeSliderValue" :min="floodTimeMin" :max="floodTimeMax" :show-tooltip="false"
                    @change="floodSliderChange"></el-slider>
                  <div class="transparent">
                    <p>模拟降雨量</p>
                  </div> -->
        </div>
      </div>

      <!-- BIM分析面板 -->
      <div style="display: none" id="BIMPanel">
        <div class="skyline" id="BIMStart" @click="BIMAnalysis">开始BIM剖析</div>
        <div class="skyline" id="BIMclear" @click="BIMClear">关闭BIM剖析</div>
        <div id="BIMWidgetpanel"></div>
      </div>

      <!-- 环点漫游面板 -->
      <div style="display: none" id="RoamPanel">
        <div class="skyline" id="RoamStart" @click="RoamStart">开始环点漫游</div>
        <div class="skyline" id="RoamStop" @click="RoamStop">关闭环点漫游</div>
      </div>

      <!-- 仿真视域面板 -->
      <div style="display: none" id="stimuRoamPanel">
        <div class="skyline" id="stimuRoamStart" @click="stimuRoamStart">启动仿真视域</div>
        <div class="skyline" id="stimuRoamStop" @click="stimuRoamStop">关闭仿真视域</div>
      </div>

      <!-- 飞行漫游面板 -->
      <div style="display: none" id="flyRoamPanel">
        <div class="skyline" id="flyRoamDraw" @click="flyRoamDraw">保存航点</div>
        <div class="skyline" id="flyRoamStart" @click="flyRoamStart">开始飞行漫游</div>
        <div class="skyline" id="flyRoamStop" @click="flyRoamStop">关闭飞行漫游</div>
      </div>

      <!-- 白模渲染面板 -->
      <div style="display: none" id="colorPanel">
        <div class="lyrRndrSelect">
          <el-select v-model="renderLyrsValue" placeholder="请选择渲染白模" @change="lyrRndrChange"
            @visible-change="lyrCheck({lyrToCheck:renderLyrs})">
            <el-option v-for="item in renderLyrs" :key="item.value" :label="item.label" :value="item.value"
              :disabled="item.disabled">
            </el-option>
          </el-select>
          <el-select v-model="renderOptionsValue" placeholder="请选择渲染方式" @change="RndrOptChange">
            <el-option v-for="item in renderOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="colorPicker">
          <div id="singleColor" style="display: none; margin-top: 30px;">
            <el-color-picker v-model="singleColorValue" color-format="rgb" show-alpha
              @change="singleColorChange"></el-color-picker>
          </div>
          <div id="colorRamp" style="display: none;">
            <el-select v-model="colorRampValue" placeholder="请选择渲染色带" @change="rampColorChange">
              <el-option v-for="item in colorRampOpts" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="skyline" id="saveRender" @click="saveRender">保存</div>
        <div class="skyline" id="cancleRender" @click="cancleRender">取消</div>

      </div>

      <!-- 二维热力图 -->
      <div style="display: none;" id="hotPanel">
        <el-select v-model="renderHotValue" placeholder="请选择二维热力渲染图层" @change="hotLyrChange"
          @visible-change="lyrCheck({lyrToCheck:hotLyrs})">
          <el-option v-for="item in hotLyrs" :key="item.value" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div class="skyline" id="hotRender" @click="hotRender">开始渲染热力图</div>
        <div class="skyline" id="removeRender" @click="removehotRender">移除热力图</div>
      </div>
      <!-- 格网分析 -->
      <div style="display: none;" id="gridPanel">
        <el-select v-model="renderGridValue" placeholder="请选择格网分析图层" @change="gridLyrChange"
          @visible-change="lyrCheck({lyrToCheck:gridLyrs})">
          <el-option v-for="item in gridLyrs" :key="item.value" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div class="skyline" id="gridDraw" @click="gridRenderDraw">绘制分析区域并开始分析</div>
        <!-- <div class="skyline" id="gridRender" @click="gridRender">开始分析</div> -->
        <div class="skyline" id="removeGridRender" @click="removeGridRender">移除分析结果</div>
      </div>
      <!-- 地图卷帘 -->
      <div style="display: none;" id="swipePanel">
        <div class="skyline" id="swipeStart" @click="swipeStart">启用卷帘</div>
        <el-select v-model="swipeLyrValue" placeholder="选择对比图层" @change="swipeChange"
          @visible-change="swipelyrCheck({lyrToCheck:swipeLyrs})">
          <el-option v-for="item in swipeLyrs" :key="item.value" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div class="skyline" id="removeSwipe" @click="removeSwipe">关闭卷帘</div>
      </div>
      <!-- 框选统计 -->
      <div style="display: none;" id="queryPanel">
        <el-select v-model="queryValue" placeholder="请选择统计图层"
          @visible-change="lyrCheck({lyrToCheck:queryLyrs,type:'query'})">
          <el-option v-for="item in queryLyrs" :key="item.value" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div class="skyline" id="queryDraw" @click="queryDrawBtn">框选范围</div>
        <!-- <div class="skyline" id="gridRender" @click="gridRender">开始分析</div> -->
        <div class="skyline" id="doQuery" @click="doQueryBtn">查询结果</div>
        <div class="esri-widget" id="queryRes"></div>
      </div>
      <!-- 虚实融合 -->
      <div style="display: none;" id="videoPanel">
        <!-- <el-select v-model="videoLyrValue" placeholder="请选择虚实融合图层" @visible-change="lyrCheck({lyrToCheck:videoLyrs})">
                <el-option v-for="item in videoLyrs" :key="item.value" :label="item.label" :value="item.value"
                  :disabled="item.disabled">
                </el-option>
              </el-select> -->
        <div
          style="margin-top: auto;font-weight: bolder;font-size: larger;color: white;">
          倾斜摄影底图
        </div>
        <div style="display: none;" id="single_mixControlPanel" class="esri-widget">
          <div class="skyline" id="videoToMap" @click="videoToMap('')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide" class="slider"></div>
      
          </div>
          <div class="skyline" id="removeVideo" @click="removeVideoMixWidget('')">关闭虚实融合</div>
        </div>
      
        <div id="multi_mixControlPanel" class="esri-widget">
          <div v-for="item in videoToMapRes" style=" border: 5px #fff;margin: 50px 2px;">
            <div
              style="display: flex;margin-top: auto;justify-content: space-between;font-weight: bolder;font-size: larger;color: white;">
              •{{item.groupName}}
              <div style="float: right; margin-top: auto;" class="skyline"
                @click="groupVideoToMap({groupName:item.groupName})">
                {{item.groupVideoToMapFlag?'关闭所有':'打开所有'}}</div>
            </div>
            <div
              style="max-height: 700px;overflow-y: auto; border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746); padding: 15px;margin-top: 20px;">
              <div v-for="videoItem in item.videoGroup">
                <div class="viewTitle">{{videoItem.name}}</div>
                <div class="skyline" :id="'multi_videoToMap'+videoItem.code" @click="videoToMap({videoItem:videoItem})">打开虚实融合
                </div>
                <div style="display: none;" :id="'mixControlPanel'+videoItem.code" class="esri-widget videoMix-border">
                  <div class="skyline" :id="'showViewLine'+videoItem.code">
                    <span class="esri-button-menu__item-label">展示视线</span>
                  </div>
                  <div class="skyline" :id="'drawViewLine'+videoItem.code">
                    <span class="esri-button-menu__item-label">绘制视线</span>
                  </div>
                  <div class="skyline" :id="'editViewLine'+videoItem.code">
                    <span class="esri-button-menu__item-label">编辑视线</span>
                  </div>
                  <div class="skyline" :id="'removeViewLine'+videoItem.code">
                    <span class="esri-button-menu__item-label">移除视线</span>
                  </div>
                  <div class="skyline" :id="'logConfig'+videoItem.code">
                    <span class="esri-button-menu__item-label">打印配置</span>
                  </div>
                  <label class="esri-feature-form__label">旋转角度：</label>
                  <div :id="'rotate'+videoItem.code" class="slider"></div>
                  <label class="esri-feature-form__label">近裁剪：</label>
                  <div :id="'near'+videoItem.code" class="slider"></div>
                  <label class="esri-feature-form__label">远裁剪：</label>
                  <div :id="'far'+videoItem.code" class="slider"></div>
                  <label class="esri-feature-form__label">视宽：</label>
                  <div :id="'wide'+videoItem.code" class="slider"></div>
                </div>
                <div class="skyline" :id="'removeVideo'+videoItem.code" @click="removeVideoMixWidget(videoItem.code)">关闭虚实融合
                </div>
              </div>
            </div>
          </div>
        </div>
      
        <div style="display: none;" id="multi_mixControlPanel_stadium" class="esri-widget">
          <div class="viewTitle">体育场马道东</div>
          <div class="skyline" id="multi_videoToMap11" @click="videoToMap('11')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel11" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine11">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine11">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine11">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine11">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig11">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate11" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near11" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far11" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide11" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo11" @click="removeVideoMixWidget('11')">关闭虚实融合</div>
      
      
          <div class="viewTitle">体育中心广场东</div>
          <div class="skyline" id="videoToMap12" @click="videoToMap('12')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel12" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine12">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine12">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine12">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine12">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig12">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate12" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near12" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far12" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide12" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo12" @click="removeVideoMixWidget('12')">关闭虚实融合</div>
      
      
          <div class="viewTitle">体育中心东门</div>
          <div class="skyline" id="videoToMap13" @click="videoToMap('13')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel13" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine13">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine13">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine13">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine13">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig13">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate13" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near13" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far13" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide13" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo13" @click="removeVideoMixWidget('13')">关闭虚实融合</div>
      
          <div class="viewTitle">体育中心东门进出口 </div>
          <div class="skyline" id="videoToMap14" @click="videoToMap('14')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel14" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine14">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine14">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine14">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine14">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig14">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate14" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near14" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far14" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide14" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo14" @click="removeVideoMixWidget('14')">关闭虚实融合</div>
      
          <div class="viewTitle">餐厅大门口外</div>
          <div class="skyline" id="videoToMap15" @click="videoToMap('15')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel15" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine15">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine15">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine15">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine15">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig15">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate15" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near15" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far15" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide15" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo15" @click="removeVideoMixWidget('15')">关闭虚实融合</div>
      
          <div class="viewTitle">体育中心南面_东面</div>
          <div class="skyline" id="videoToMap16" @click="videoToMap('16')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel16" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine16">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine16">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine16">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine16">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig16">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate16" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near16" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far16" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide16" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo16" @click="removeVideoMixWidget('16')">关闭虚实融合</div>
      
          <div class="viewTitle">体育中心北门进口</div>
          <div class="skyline" id="videoToMap17" @click="videoToMap('17')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel17" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine17">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine17">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine17">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine17">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig17">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate17" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near17" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far17" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide17" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo17" @click="removeVideoMixWidget('17')">关闭虚实融合</div>
      
          <div class="viewTitle">体育中心北门出口</div>
          <div class="skyline" id="videoToMap18" @click="videoToMap('18')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel18" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine18">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine18">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine18">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine18">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig18">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate18" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near18" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far18" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide18" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo18" @click="removeVideoMixWidget('18')">关闭虚实融合</div>
      
          <div class="viewTitle">游泳馆与体育馆交叉口</div>
          <div class="skyline" id="videoToMap19" @click="videoToMap('19')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel19" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine19">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine19">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine19">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine19">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig19">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate19" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near19" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far19" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide19" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo19" @click="removeVideoMixWidget('19')">关闭虚实融合</div>
      
          <div class="viewTitle">体育馆BRT朝南</div>
          <div class="skyline" id="videoToMap110" @click="videoToMap('110')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel110" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine110">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine110">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine110">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine110">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig110">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate110" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near110" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far110" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide110" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo110" @click="removeVideoMixWidget('110')">关闭虚实融合</div>
      
          <div class="viewTitle">体育馆BRT朝北</div>
          <div class="skyline" id="videoToMap111" @click="videoToMap('111')">打开虚实融合</div>
          <div style="display: none;" id="mixControlPanel111" class="esri-widget videoMix-border">
            <div class="skyline" id="showViewLine111">
              <span class="esri-button-menu__item-label">展示视线</span>
            </div>
            <div class="skyline" id="drawViewLine111">
              <span class="esri-button-menu__item-label">绘制视线</span>
            </div>
            <div class="skyline" id="editViewLine111">
              <span class="esri-button-menu__item-label">编辑视线</span>
            </div>
            <div class="skyline" id="removeViewLine111">
              <span class="esri-button-menu__item-label">移除视线</span>
            </div>
            <div class="skyline" id="logConfig111">
              <span class="esri-button-menu__item-label">打印配置</span>
            </div>
            <label class="esri-feature-form__label">旋转角度：</label>
            <div id="rotate111" class="slider"></div>
            <label class="esri-feature-form__label">近裁剪：</label>
            <div id="near111" class="slider"></div>
            <label class="esri-feature-form__label">远裁剪：</label>
            <div id="far111" class="slider"></div>
            <label class="esri-feature-form__label">视宽：</label>
            <div id="wide111" class="slider"></div>
          </div>
          <div class="skyline" id="removeVideo111" @click="removeVideoMixWidget('111')">关闭虚实融合</div>
      
        </div>
      </div>
      <!-- 多维查询 -->
      <div style="display: none;" id="multiQueryPanel">
        <el-select v-model="multiQueryLyrValue" placeholder="请选择多维查询图层"
          @visible-change="lyrCheck({lyrToCheck:queryLyrs,type:'query'})">
          <el-option v-for="item in queryLyrs" :key="item.name" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div style="display: flex;">
          <el-select v-model="queryFieldValue" placeholder="请选择查询字段" @visible-change="selectField()">
            <el-option v-for="item in queryFields" :key="item.label" :label="item.label" :value="item.label"
              :disabled="item.disabled">
            </el-option>
          </el-select>
          <el-input v-model="querymultiQueryInput" placeholder="请输入内容"></el-input>
        </div>
        <div class="skyline" id="doMultiQuery" @click="doMultiQuery">查询</div>
        <div class="skyline" id="resetMultiQuery" @click="resetMultiQuery">重置</div>

        <div class="info-box">
          <ul class="info-ul">
            <li v-for="item in multiQueryResult" style="cursor:pointer" @click="item.onclick">
              <div v-html="item.name" style="margin-top: 20px;"></div>
            </li>
          </ul>
        </div>

      </div>
      <!-- 建筑智能排布 -->
      <div style="display: none;" id="buildLayoutPanel">
        <div class="skyline" id="clickForSelect" @click="clickForSelect">点选地块</div>
        <div style="display: flex;">
          <p>建筑长度（米）：</p>
          <el-input v-model="buildLayout_length" placeholder="请输入内容"></el-input>
        </div>
        <div style="display: flex;">
          <p>建筑宽度（米）：</p>
          <el-input v-model="buildLayout_width" placeholder="请输入内容"></el-input>
        </div>
        <div style="display: flex;">
          <p>建筑层数（层）：</p>
          <el-input v-model="buildLayout_floorNum" placeholder="请输入内容"></el-input>
        </div>
        <div style="display: flex;">
          <p>每层高度（米）：</p>
          <el-input v-model="buildLayout_floorHeight" placeholder="请输入内容"></el-input>
        </div>
        <div style="display: flex;">
          <p>正面间距（米）：</p>
          <el-input v-model="buildLayout_foreDistance" placeholder="请输入内容"></el-input>
        </div>
        <div style="display: flex;">
          <p>侧面间距（米）：</p>
          <el-input v-model="buildLayout_backDistance" placeholder="请输入内容"></el-input>
        </div>
        <div class="skyline" id="autoLayout" @click="autoLayout">智能排布</div>
        <div class="info-box" id="autoLayout-info">
          <ul class="info-ul">
            <li>
              <p>指标</p>
              <p>经济技术指标</p>
            </li>
            <li v-for="item in autoLayoutResult">
              <p v-html="item.name"></p>
              <p v-html="item.value"></p>
            </li>
          </ul>
        </div>
        <div id="featureEditorWidget" style="display: none;">
          <div class="skyline" id="selectFeature" @click="selectFeature">选择要素</div>
          <div style="display: flex;">
            <p>输入高度（米）：</p>
            <el-input v-model="featureHeight" placeholder="" @change="featureHeightChange"></el-input>
          </div>
          <div class="skyline" id="updateFeature" @click="saveFeatureEdit">保存</div>
          <div style="margin-top: 30px; display: flex;">
            <span>自动模拟日照：</span>
            <div class="playBtn" @click="ctRzPlay()">
              <i :class="rzPlay?'el-icon-video-pause':'el-icon-video-play'"></i>
            </div>
          </div>
          <div class="rzSlider">
            <span>光照模拟时间：{{formatRzTime}}</span>
            <el-slider :max="1439" v-model="rzTime" :format-tooltip="formatTooltip"
              @change="lightsliderChange"></el-slider>
          </div>
        </div>
        <div class="skyline" id="removeLayout" @click="removeLayout">清除</div>
      </div>
      <!-- 场景特效模拟 -->
      <div style="display: none;" id="sceneEffectPanel">
        <div class="skyline" id="addFireEffect" @click="addPointEffect('fire')">启用火焰特效</div>
        <div v-if="effectFlag=='fire'" class="esri-widget" style="padding:12px">请在地图上点选特效位置</div>
        <div class="skyline" id="removeFireEffect" @click="removePointEffect('fire')">移除火焰特效</div>

        <div class="skyline" id="addFogEffect" @click="addPointEffect('fog')">启用烟雾特效</div>
        <div v-if="effectFlag=='fog'" class="esri-widget" style="padding:12px">请在地图上点选特效位置</div>
        <div class="skyline" id="removeFogEffect" @click="removePointEffect('fog')">移除烟雾特效</div>

        <div class="skyline" id="addFountainEffect" @click="addPointEffect('fountain')">启用喷泉特效</div>
        <div v-if="effectFlag=='fountain'" class="esri-widget" style="padding:12px">请在地图上点选特效位置</div>
        <div class="skyline" id="removeFountainEffect" @click="removePointEffect('fountain')">移除喷泉特效</div>
      </div>
      <!-- 城市小品 -->
      <div style="display: none;" id="addGltfLyrPanel">
        <el-select v-model="gltfLyrValue" placeholder="选择小品类型并绘制"
          @visible-change="gltfLyrChange(gltfLyrValue)">
          <el-option v-for="item in gltfLyrs" :key="item.name" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div class="skyline" id="addFireEffect" @click="startGltfLyr()">开始绘制</div>
        <div class="skyline" id="removeFogEffect" @click="stopGltfLyr()">暂停绘制</div>
        <div class="skyline" id="removeFountainEffect" @click="removeGltfLyr()">移除绘制内容</div>
      </div>
      <!-- 限高分析 -->
      <div style="display: none;" id="heightRestrictPanel">
        <el-select v-model="heightRestrictlyrValue" placeholder="请选择渲染白模" @change="heightRestrictlyrChange"
          @visible-change="lyrCheck({lyrToCheck:heightRestrictLyrs})">
          <el-option v-for="item in heightRestrictLyrs" :key="item.value" :label="item.label" :value="item.value"
            :disabled="item.disabled">
          </el-option>
        </el-select>
        <div class="skyline" id="drawHeightRestrict" @click="drawHeightRestrict()">绘制分析区域</div>
        <div class="esri-widget">建筑限高：{{heightRestrictValue}}米</div>
        <el-slider :max="500" :min="0" v-model="heightRestrictValue" @change="heightRestrictliderChange"></el-slider>
        <div class="skyline" id="startHeightRestrict" @click="startHeightRestrict()">开始限高分析</div>
        <div class="skyline" id="removeheightRestrict" @click="removeheightRestrict()">移除分析结果</div>
      </div>
      <!-- 统一销毁按钮 -->
      <div id="removeAllTools" class="skyline" style="text-align: center;" ></div>
    </div>
  </div>
</body>
<script type="module">
  import Slider from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/widgets/Slider.js";
  top.Slider = Slider
</script>
<script src="/static/citybrain/csdn/js/map_toolbar.js"></script>
</html>
