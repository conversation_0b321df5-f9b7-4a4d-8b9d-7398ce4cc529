<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>名称搜索地图位置</title>
    <script src="/static/citybrain/hjbh/js/vue.js"></script>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      body {
        margin: 0;
      }
      .souBox {
        width: 1130px;
        height: 1390px;
        background: rgba(3, 24, 39, 0.88);
        border: 2px solid #afdcfb;
        border-radius: 25px;
      }
      .souBox .topSou {
        width: 994px;
        height: 80px;
        margin: 50px auto 0;
      }
      .el-input-group {
        height: 100%;
        font-size: x-large;
      }
      .el-input-group--append .el-input__inner,
      .el-input-group__prepend {
        background: #132c4e;
        border: 0.3px solid #afdcfb;
        height: 100%;
        color: #fff;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }
      .el-input-group__append {
        width: 80px !important;
        background: #009ace;
        text-align: center;
        color: #fff !important;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }
      .el-input__suffix {
        margin-right: 20px;
      }
      .el-icon-circle-close:before {
        line-height: 80px;
        font-size: 30px;
      }
      .el-icon-search {
        font-size: 26px;
      }
      .el-input__clear {
        font-size: 26px;
      }
      /*  */
      .el-menu.el-menu--horizontal {
        background: transparent;
        border-color: #356596;
        display: flex;
        justify-content: space-between;
      }
      .el-menu-item {
        font-size: 30px !important;
      }
      .el-menu--horizontal > .el-menu-item.is-active {
        background: transparent !important;
        border-bottom: 4px solid #fff !important;
        color: #fff !important;
      }
      .el-menu--horizontal > .el-menu-item:hover {
        background: transparent !important;
        border-bottom: 4px solid #fff !important;
        color: #fff !important;
      }
      .main {
        position: relative;
        width: 935px;
        height: 1185px;
        background: url("/static/citybrain/csdn/img/ywt/sou-main-bg.png")
          no-repeat;
        background-size: 100% 100%;
        margin: 36px 0 0 30px;
        overflow: hidden;
      }
      .main > ul {
        width: 96%;
        max-height: 85%;
        margin-left: 15px;
        margin-top: 20px;
        padding: 0;
        list-style: none;
        overflow: auto;
      }
      .main > ul li {
        cursor: pointer;
        font-size: 30px;
        color: #fff;
        border-bottom: 1px dashed #006688;
      }
      .main > ul li .name {
        display: flex;
        align-items: center;
      }
      .main > ul li .sptel i {
        font-size: 27px;
        display: inline-block;
        padding: 1px 19px;
        font-size: 26px;
        margin: 3px 13px 0 0;
        background: url("/static/citybrain/csdn/img/ywt/spiconTel.png")
          no-repeat;
        background-size: 100% 100%;
      }
      .main > ul li:hover {
        background: #003e52;
      }
      .main > ul li:hover .name {
        background: linear-gradient(
          to bottom,
          #ffebce,
          #ffffff,
          #ffc559,
          #ffffff
        );
        -webkit-background-clip: text;
        color: transparent;
      }
      .click-no {
        opacity: 0.4;
        cursor: no-drop !important;
      }
      .click-no > div {
        pointer-events: none;
      }
      /*滚动条整体样式*/
      .main ul::-webkit-scrollbar {
        width: 6px;
        height: 1px;
      }
      .main ul::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: #0b4f76;
        height: 8px;
      }
      /* 多个ul */
      .main .dmdzUl {
      }
      .main .spjkUl .spjkicon {
        font-size: 27px;
        background-size: contain;
        padding: 2px 26px;
        background: url("/static/citybrain/csdn/img/ywt/iconspjk.png") no-repeat;
        background-size: 100% 100%;
      }
      .pointIcon {
        margin: 0 10px;
        width: 35px;
        height: 35px;
      }
      .pointIcon.active {
        background: url("/static/citybrain/csdn/img/ywt/sou-state-active.png")
          no-repeat;
        background-size: 100% 100%;
      }
      .pointIcon.none {
        background: url("/static/citybrain/csdn/img/ywt/sou-state-none.png")
          no-repeat;
        background-size: 100% 100%;
      }
      /* 分页 */
      .el-pagination {
        position: absolute;
        top: 1085px;
        left: 26%;
        margin-top: 33px;
        padding: 2px 1px !important;
      }
      .el_page_sjzx > .el-pagination button {
        height: 52px;
        width: 40px;
        background: transparent !important;
      }
      .el_page_sjzx .el-pagination button .el-icon {
        font-size: 24px !important;
        color: #c1e2fa;
        font-weight: 400;
      }
      .el_page_sjzx .el-pagination button .el-icon:hover {
        color: #20aeff;
      }
      .el_page_sjzx .el-pagination__jump {
        font-size: 22px !important;
        font-weight: 400;
        color: #c1e2fa;
        line-height: 45px;
        margin-left: 0 !important;
      }
      .el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor {
        width: 140px;
      }
      .el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor input {
        overflow: hidden;
        width: 96px;
        overflow: auto;
        height: 56px !important;
        color: #c1e2fa;
        font-size: 24px;
        border: 2px solid #a7a889;
        border-radius: 4px;
        background: transparent !important;
      }
      .el_page_sjzx ul {
        margin-top: 2px !important;
      }
      .el_page_sjzx ul li {
        border: 2px solid transparent;
        margin-left: 0px !important;
        height: 37px;
        padding: 0 3px !important;
        font-size: 20px !important;
        color: #c1e2fa !important;
        background: transparent !important;
        font-weight: 500;
        line-height: 36px !important;
        border-radius: 4px;
      }
      .el_page_sjzx li.active {
        margin: 0;
        padding: 0;
        color: #fff !important;
        /* border: 2px solid #035b86; */
        background-color: #00c0ff !important;
      }
      .el_page_sjzx {
        margin: 0 auto;
        text-align: center;
      }
      .el-pagination button,
      .el-pagination span:not([class*="suffix"]) {
        height: 42px !important;
        line-height: 42px !important;
      }
      .el-input {
        font-size: 26px !important;
      }
      .el-pagination__editor.el-input {
        width: 77px;
        margin: 0 10px;
      }
      .el-pagination__editor.el-input .el-input__inner {
        color: #fff;
        height: 33px;
        font-size: 24px;
        background: #132c4e;
        border: 1px solid;
        border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
      }
      /* 左导航 */
      .navBox {
        margin: 35px -32px 25px 50px;
        width: 87px;
        max-height: 100% !important;
        overflow-y: auto;
      }
      .navBox::-webkit-scrollbar {
        width: 0px;
        height: 1px;
      }
      .navBox::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: #0b4f76;
        height: 8px;
      }
      .el-menu {
        border: 0 !important;
        background: transparent !important;
        animation: 2s jumpBoxHandler1 linear;
        animation-iteration-count: 1;
      }
      .textLie {
        writing-mode: vertical-lr;
        text-orientation: upright;
        letter-spacing: 2px;
      }
      .countAll {
        font-size: 24px;
        writing-mode: rl-tb;
      }
      .el-menu > .el-menu-item {
        width: 85px;
        margin: 0 auto 26px !important;
        line-height: 34px;
        background: url("/static/citybrain/csdn/img/ywt/sou-none.png") no-repeat !important;
        background-size: 100% 100% !important;
        padding: 11px 0px !important;
      }
      .el-menu-item,
      .el-submenu__title {
        height: auto;
      }
      .el-menu > .el-menu-item.is-active {
        background: transparent !important;
        color: #fff !important;
        background: url("/static/citybrain/csdn/img/ywt/sou-active.png")
          no-repeat !important;
        background-size: 100% 100% !important;
      }
      .el-menu > .el-menu-item:hover {
        background: transparent !important;
        color: #fff !important;
        background: url("/static/citybrain/csdn/img/ywt/sou-active.png")
          no-repeat !important;
        background-size: 100% 100% !important;
      }
      .el-menu-item {
        text-align: center !important;
        color: #ccc !important;
        font-size: 28px !important;
      }
      @keyframes jumpBoxHandler1 {
        0% {
          transform: translate(0px, 0px); /*开始位置*/
        }
        50% {
          transform: translate(0px, -150px); /* 可配置跳动方向 */
        }
        100% {
          transform: translate(0px, 0px); /*结束位置*/
        }
      }
      /* 下拉框样式 */
      .select {
        display: inline-block;
        width: 200px;
        height: 55px;
        text-align: right;
        z-index: 100;
        line-height: 75px;
        margin-top: 10px;
      }
      .ul {
        width: 100%;
        height: 40px;
        text-align: center;
        font-size: 24px;
        color: #fefefe;
        background-color: #132c4e;
        border: 1px solid #359cf8;
        border-radius: 40px;
      }
      .ul > div {
        width: 100%;
        height: 40px;
        line-height: 40px;
      }
      .ul ul {
        display: none;
        margin: 5px;
        padding: 0;
      }
      .select ul > li {
        list-style: none;
        width: 100%;
        height: 40px;
        line-height: 40px;
        background-color: #132c4ef0;
        box-sizing: border-box;
      }

      .select ul > li:hover {
        background-color: #1384c6;
      }

      .ul-active {
        display: block !important;
      }

      .ul-active > li:last-of-type {
        border-radius: 0 0 20px 20px;
      }
      .select ul {
        overflow: hidden;
        max-height: 200px;
        overflow-y: auto;
      }
      .select ul::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 3px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .select ul::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 10px;
      }
      .flow-icon {
        width: 25px;
        position: absolute;
        top: 20px;
        right: 27px;
      }
      .flow-icon1 {
        top: 8px;
        transform: rotateX(180deg);
      }
      /* 错误提示框 */
      .el-notification__icon {
        width: 30px !important;
        height: 31px !important;
        font-size: 30px !important;
      }
      .el-notification__title {
        color: #fff !important;
        font-size: 30px !important;
      }
      .el-notification__content {
        color: #ccc !important;
        font-size: 26px !important;
      }
      .el-notification__closeBtn {
        font-size: 26px !important;
      }
      .el-notification {
        top: 130px !important;
        border: 0.5px solid #afdcf7 !important;
        background-color: #051938 !important;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="souBox">
        <!-- 搜索框 -->
        <div class="topSou">
          <el-input v-model="namePoint" clearable>
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="searchBtn()"
            ></el-button>
          </el-input>
        </div>
        <div style="display: flex; height: 86.5%">
          <!-- 导航条 mode="horizontal"横向布局-->
          <div class="navBox">
            <el-menu
              :default-active="activeIndex"
              class="el-menu-demo animated bounce"
              @select="handleSelect"
            >
              <el-menu-item index="地名地址">
                <span class="textLie">
                  地名地址<span class="countAll" v-if="dmdzData!==0">
                    ({{dmdzData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="视频监控">
                <span class="textLie">
                  视频监控<span class="countAll" v-if="spjkData!==0"
                    >({{spjkData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="POI">
                <span class="textLie">
                  POI<span class="countAll" v-if="poiData!==0"
                    >({{poiData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="物联感知设备">
                <span class="textLie">
                  物联感知设备<span class="countAll" v-if="wlgzsbData!==0"
                    >({{wlgzsbData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="网格数据">
                <span class="textLie">
                  网格数据<span class="countAll" v-if="wgsjData!==0"
                    >({{wgsjData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="数据指标">
                <span class="textLie">
                  数据指标<span class="countAll" v-if="sjzbData!==0"
                    >({{sjzbData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="接入系统">
                <span class="textLie">
                  接入系统<span class="countAll" v-if="jrxtData!==0"
                    >({{jrxtData.length}})</span
                  >
                </span>
              </el-menu-item>
              <el-menu-item index="更多" disabled>
                <span>...</span>
              </el-menu-item>
            </el-menu>
          </div>
          <!-- 列表展示盒子 -->
          <div class="main">
            <!-- 显示总数 -->
            <div
              v-if="activeIndex!='地名地址'"
              style="
                margin: 20px;
                display: flex;
                justify-content: space-between;
              "
            >
              <div class="countTit s-c-yellow-gradient1 s-font-40">
                {{activeIndex}}({{tabAllCount}})
              </div>
              <div class="select" @click="showSelct=!showSelct">
                <div class="flow-icon" :class="showSelct?'flow-icon1':''">
                  <img
                    src="/static/citybrain/hjbh/img/rkzt/up.png"
                    alt=""
                    width="20"
                  />
                </div>
                <div class="ul">
                  <div style="cursor: pointer">{{startName}}</div>
                  <ul :class="[showSelct?'ul-active':'']">
                    <li
                      style="cursor: pointer"
                      v-for="(item,index) in selectLi"
                      @click="selectChange(index,item)"
                    >
                      {{item}}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <!-- 视频 -->
            <ul class="spjkUl" v-if="activeIndex=='视频监控'">
              <li
                v-for="(item,index) in allData"
                :class="item.jwd.split(',')[0]=='' || item.jwd.split(',')[1]==''?'click-no':''"
              >
                <div
                  style="
                    padding: 20px 0 5px 8px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                  "
                  @click="clickList(item)"
                >
                  <div style="flex: 1">
                    <span style="display: flex" class="sptel">
                      <i v-if="item.device_type">{{item.device_type}}</i>
                      <span class="name">{{item.name}}</span>
                    </span>
                    <p style="margin: 5px 0 1px">
                      <span class="spjkicon" v-for="(ele,i) in item.iconArr">
                        <span class="s-c-blue-gradient1">{{ele}}</span>
                      </span>
                    </p>
                  </div>
                  <div
                    :class="item.jwd.split(',')[0]==''||item.jwd.split(',')[1]==''?'pointIcon none':'pointIcon active'"
                  ></div>
                </div>
              </li>
            </ul>
            <!-- poi && 物联感知 -->
            <ul
              class="poiUl"
              v-else-if="activeIndex=='POI' || activeIndex=='物联感知设备'"
            >
              <li
                v-for="(item,index) in allData"
                :class="item.jwd.split(',')[0]=='' || item.jwd.split(',')[1]==''?'click-no':''"
              >
                <div
                  style="
                    padding: 20px 0px 5px 8px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                  "
                  @click="clickList(item)"
                >
                  <div style="flex: 1">
                    <p style="margin: 0; display: flex">
                      <span
                        v-if="item.device_type!=''"
                        class="s-c-blue3-gradient"
                        style="
                          font-size: 26px;
                          white-space: nowrap;
                          margin-right: 10px;
                          padding-top: 2px;
                        "
                      >
                        [{{item.device_type}}]
                      </span>
                      <span class="name" style="flex: 1">{{item.name}}</span>
                    </p>
                    <p
                      style="
                        margin: 2px 0;
                        display: flex;
                        font-size: 25px;
                        justify-content: space-between;
                        color: #fff;
                      "
                    >
                      <span v-if="item.address!=''&&item.address!='--'">
                        <span class="">地址：</span>{{item.address}}
                      </span>
                      <span
                        v-if="item.addinfo!=''&&item.addinfo.deptname!=undefined"
                      >
                        <span class="">所属部门：</span>
                        {{item.addinfo.deptname}}
                      </span>
                    </p>
                  </div>
                  <!-- <div class="pointIcon active"></div> -->
                </div>
                <!-- {{index+1}}. {{item.name}} -->
              </li>
            </ul>
            <!-- 地面地址 -->
            <ul class="dmdzUl" v-else>
              <li
                v-for="(item,index) in allData"
                @click="clickList(item)"
                :title="item.name"
              >
                <span class="name" style="padding: 20px 0 5px 8px">
                  <span style="display: inline-block; margin-right: 10px">
                    {{index+1}}.
                  </span>
                  <span>{{item.name}}</span>
                </span>
              </li>
            </ul>

            <!-- 分页 -->
            <div
              class="el_page_sjzx"
              v-if="tabAllCount!=undefined&&tabAllCount!=''&&tabAllCount!=0"
            >
              <el-pagination
                @current-change="pageChange"
                layout="prev, pager, next,jumper"
                :pager-count="5"
                :page-size="15"
                :current-page.sync="currentPage1"
                :total="tabAllCount"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script>
    window.addEventListener("message", function (event) {
      if (event.data) {
        if (Object.prototype.toString.call(event.data) === "[object Object]") {
          if (
            event.data &&
            event.data.type == "pointClick" &&
            event.data.data
          ) {
            const item = JSON.parse(event.data.data.data);
            if (item.pointId == "sou") {
              console.log("sou-window====>", item);
              souName.getInfoData(item);
            }
          }
        } else if (
          Object.prototype.toString.call(event.data) === "[object String]"
        ) {
          let dataNew = JSON.parse(event.data);
          if (dataNew && dataNew.type == "pointClick" && dataNew.data) {
            if (dataNew.data.pointId == "video") {
              let iframe1 = {
                type: "openIframe",
                name: "video_main",
                src:
                  baseURL.url +
                  "/static/citybrain/csdn/commont/video_main.html",
                width: "100%",
                height: "100%",
                left: "0",
                top: "0",
                zIndex: "1000",
                argument: dataNew.data.obj,
              };
              window.parent.postMessage(JSON.stringify(iframe1), "*");
            }
          }
        }
        // if (event.data && event.data.type == "pointClick" && event.data.data) {
        //   const item = JSON.parse(event.data.data.data);
        //   // if (item.pointId == "video") {
        //   //   let iframe1 = {
        //   //     type: "openIframe",
        //   //     name: "video_main",
        //   //     src:
        //   //       baseURL.url + "/static/citybrain/csdn/commont/video_main.html",
        //   //     width: "100%",
        //   //     height: "100%",
        //   //     left: "0",
        //   //     top: "0",
        //   //     zIndex: "1000",
        //   //     argument: item,
        //   //   };
        //   //   window.parent.postMessage(JSON.stringify(iframe1), "*");
        //   // }
        //   if (item.pointId == "sou") {
        //     console.log("====>", item);
        //     // debugger;
        //     // let countStr = "";
        //     if (item.obj.type == "网格数据") {
        //       //       countStr += `<div
        //       //       class="item"
        //       //       style="display: flex; font-size: 32px; color: #2299e2; line-height: 70px"
        //       //     >
        //       //       <span style="margin-left:30px;white-space: nowrap; ">名  称 :</span>
        //       //       <span style="color: #fff; margin-left:30px;
        //       //       "
        //       //         >${item.obj.name}</span
        //       //       >
        //       //     </div>`;
        //       //       let str = `
        //       //     <div
        //       //   onclick=" this.style.display = 'none'"
        //       //   style="
        //       //     width: 800px;
        //       //     position: absolute;
        //       //     border-radius: 5px;
        //       //     background-color: rgba(10, 31, 53, 0.8);
        //       //     z-index: 999999;
        //       //     -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
        //       //     box-shadow: inset 0 0 40px 0 #5ba3fa;
        //       //     padding: 24px;
        //       //   "
        //       // >
        //       //   <div
        //       //     style="
        //       //       width: 0px;
        //       //       height: 0px;
        //       //       border-top: 70px solid rgba(10, 31, 53, 0.8);
        //       //       border-right: 60px solid transparent;
        //       //       position: absolute;
        //       //       bottom: -70px;
        //       //       left: 102px;
        //       //     "
        //       //   ></div>
        //       //   <div class="container">${countStr}</div>
        //       // </div>
        //       //     `;
        //       //       let objData = {
        //       //         funcName: "customPop",
        //       //         coordinates: item.obj.jwd.split(","),
        //       //         closeButton: true,
        //       //         html: str,
        //       //       };
        //       //       top.document
        //       //         .getElementById("map")
        //       //         .contentWindow.Work.funChange(JSON.stringify(objData));
        //     } else {
        //       souName.getInfoData(item);
        //     }
        //   }
        // }
      }
    });
    let souName = new Vue({
      el: "#app",
      data: {
        wgData: [], //网格总数据
        namePoint: "", //搜索框model
        activeIndex: "地名地址", //当前导航下标
        allData: [],
        listData: [],
        listAllData: [],
        currentPage1: 0,
        tabAllCount: 0,
        dmdzData: 0,
        spjkData: 0,
        poiData: 0,
        wlgzsbData: 0,
        sjzbData: 0,
        jrxtData: 0,
        wgsjData: 0,
        showSelct: false,
        startName: "金华市",
        selectLi: [
          "金华市",
          "婺城区",
          "金义新区",
          "东阳市",
          "义乌市",
          "永康市",
          "兰溪市",
          "浦江县",
          "武义县",
          "磐安县",
          "开发区",
        ],
      },
      created() {},
      mounted() {
        // document.getElementsByClassName(
        //   "el-pagination__jump"
        // )[0].childNodes[0].nodeValue = "跳转到";
      },
      methods: {
        selectChange(index, item) {
          this.startName = item;
          let name = item == "金东区" ? "金义新区" : item;
          if (item == "金华市") {
            this.listData = this.listAllData;
          } else {
            this.listData = this.listAllData.filter((ele) => ele.xzqx == name);
          }
          this.pageChange(1);
        },
        handleSelect(navName) {
          this.startName = "金华市";
          this.activeIndex = navName;
          navName == "地名地址"
            ? (this.listData = this.dmdzData)
            : navName == "视频监控"
            ? (this.listData = this.spjkData)
            : navName == "POI"
            ? (this.listData = this.poiData)
            : navName == "物联感知设备"
            ? (this.listData = this.wlgzsbData)
            : navName == "数据指标"
            ? (this.listData = this.sjzbData)
            : navName == "接入系统"
            ? (this.listData = this.jrxtData)
            : this.activeIndex == "网格数据"
            ? (this.listData = this.wgsjData)
            : "";
          this.listAllData = this.listData;
          this.allData = this.listData.slice(0, 15);
          this.tabAllCount = this.listData.length;
          // if (
          //   this.tabAllCount != undefined &&
          //   this.tabAllCount != "" &&
          //   this.tabAllCount != 0
          // ) {
          //   document.getElementsByClassName(
          //     "el-pagination__jump"
          //   )[0].childNodes[0].nodeValue = "跳转到";
          // }
          this.currentPage1 = 1;
        },
        searchBtn() {
          this.searchPlace(this.namePoint);
          this.searchOther(this.namePoint);
        },

        searchPlace(name) {
          axios({
            method: "get",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/tdt/search",
            params: {
              type: "query",
              tk: "86103f598bc5f9e7b1d3ea9479f48f7f",
              postStr: {
                keyWord: name,
                level: "11",
                mapBound: "119.14,28.32,120.46,29.41",
                queryType: "1",
                count: "20",
                start: "0",
              },
            },
          }).then((response) => {
            if (response.data.pois) {
              this.dmdzData = response.data.pois;
              this.listData = response.data.pois;
              this.activeIndex == "地名地址"
                ? (this.allData = this.listData.slice(0, 15))
                : "";
              this.tabAllCount = this.listData.length;
              this.listAllData = this.listData;
            }
          });
        },
        searchOther(name) {
          $api("csdnsy_gis03", { name: name }).then((res) => {
            // this.spjkData = res.filter((item) => item.type == "视频");
            this.spjkData = res.filter((item) => {
              item.iconArr = item.addinfo.labels
                ? item.addinfo.labels.split("|")
                : [];
              return item.type == "视频";
            });

            this.poiData = res.filter((item) => item.type == "POI");
            this.wlgzsbData = res.filter((item) => item.type == "物联感知");
            this.sjzbData = res.filter((item) => item.type == "数据指标");
            this.jrxtData = res.filter((item) => item.type == "接入系统");
            this.wgsjData = res.filter((item) => item.type == "网格数据");

            this.activeIndex == "视频监控"
              ? (this.listData = this.spjkData)
              : this.activeIndex == "POI"
              ? (this.listData = this.poiData)
              : this.activeIndex == "物联感知设备"
              ? (this.listData = this.wlgzsbData)
              : this.activeIndex == "数据指标"
              ? (this.listData = this.sjzbData)
              : this.activeIndex == "接入系统"
              ? (this.listData = this.jrxtData)
              : this.activeIndex == "网格数据"
              ? (this.listData = this.wgsjData)
              : "";

            if (this.activeIndex != "地名地址") {
              this.allData = this.listData.slice(0, 15);
              this.tabAllCount = this.listData.length;
              this.listAllData = this.listData;
            }
          });
        },
        clickList(item) {
          console.log("item==>", item);
          let e = item;
          let id = "";
          // item.type == "网格数据" (id = "sjzx-街面秩序")
          this.activeIndex == "地名地址"
            ? (id = "zhdd_map_hdz")
            : item.type == "视频"
            ? (id = "camera-load3")
            : item.type == "POI"
            ? (id = "rckz-兴趣点通用")
            : item.type == "物联感知"
            ? (id = "wlgzsb")
            : "";
          let icon = id;
          if (this.activeIndex == "地名地址") {
            let pointStr =
              e.lonlat.split(" ")[0] + "," + e.lonlat.split(" ")[1];
            let obj = [{ data: { pointId: "sou", obj: e }, point: pointStr }];
            let flyToPoint = e.lonlat.split(" ");
            this.pointTextMapFun(icon, obj, id, 0.4);
            this.flytoAdd(flyToPoint, 12.5);
          } else if (item.type == "视频" && e != undefined && e != "") {
            let pointStr = e.jwd;
            let obj = [
              {
                data: { pointId: "video", obj: e },
                point: pointStr,
                // code: e.addinfo.chncode,
              },
            ];
            let flyToPoint = e.jwd.split(",");
            this.pointTextMapFun(icon, obj, id, 0.4);
            this.flytoAdd(flyToPoint, 12.5);
          } else if (item.type == "POI" || item.type == "物联感知") {
            let pointStr = e.jwd;
            let obj = [{ data: { pointId: "sou", obj: e }, point: pointStr }];
            let flyToPoint = e.jwd.split(",");
            let iconSize = 1;
            this.pointTextMapFun(icon, obj, id, iconSize);
            this.flytoAdd(flyToPoint, 12.5);
          } else if (item.type == "数据指标") {
            console.log("数据指标==>", e.id);
            let leftData1 = {
              type: "openIframe",
              name: "cstz3-middle-diong",
              src:
                baseURL.url +
                "/static/citybrain/csdn/commont/cstz3-middle-diong.html",
              left: "2480px",
              top: "400px",
              width: "2602px",
              height: "1434px",
              zIndex: "998",
              argument: {
                id: e.id,
                status: "showEcharts",
              },
            };
            window.parent.postMessage(JSON.stringify(leftData1), "*");
          } else if (item.type == "接入系统") {
            console.log("接入系统==>", e.addinfo.height);
            top.commonObj.openWinHtml(
              e.addinfo.width,
              e.addinfo.height,
              e.addinfo.linkurl
            );
          } else if (item.type == "网格数据") {
            // this.rmShape();
            const test1 =
              "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/data/services/yksqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999";
            // const code = "&FILTER=adcode%3D'330784115218001'";
            const code = "&FILTER=adcode%3D'" + item.addinfo.adcode + "'";
            axios.get(test1 + code).then((res) => {
              console.log(res.data.features);
              if (res.data.features != "") {
                this.flytoAdd(
                  [
                    res.data.features[0].properties.center_x,
                    res.data.features[0].properties.center_y,
                  ],
                  15
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                  JSON.stringify({
                    funcName: "shape", //加载多边形功能
                    geojson: res.data.features[0],
                    colorOutline: [255, 50, 40, 0.9], //多边形边缘颜色 RGBA格式,
                    color: [193, 210, 240, 0.2], //多边形填充颜色 RGBA格式
                    name: "sou_wg", //多边形名称 可用于删除
                  })
                );
              } else {
                this.$notify.error({
                  title: "提示",
                  message: "暂无区域信息",
                  // duration: 0,
                });
              }
            });
            // let name = item.name.split("-")[item.name.split("-").length - 1];
            // let gaoJson = this.wgData.filter(
            //   (ele) => ele.properties.name == name
            // );
            // // console.log("网格数据============》",gaoJson );
            // if (gaoJson != [] && gaoJson[0].properties != undefined) {
            //   this.flytoAdd(
            //     [
            //       gaoJson[0].properties.center_x,
            //       gaoJson[0].properties.center_y,
            //     ],
            //     15
            //   );
            //   top.document.getElementById("map").contentWindow.Work.funChange(
            //     JSON.stringify({
            //       funcName: "shape", //加载多边形功能
            //       geojson: gaoJson[0],
            //       colorOutline: [255, 50, 40, 0.9], //多边形边缘颜色 RGBA格式,
            //       color: [193, 210, 240, 0.2], //多边形填充颜色 RGBA格式
            //       name: "sou_wg", //多边形名称 可用于删除
            //     })
            //   );
            // }
            // 打点
            // let pointStr = e.jwd;
            // let obj = [{ data: { pointId: "sou", obj: e }, point: pointStr }];
            // let flyToPoint = e.jwd.split(",");
            // let iconSize = 0.5;
            // this.pointTextMapFun(icon, obj, id, iconSize);
            // this.flytoAdd(flyToPoint);
          }
        },
        pageChange(e) {
          let arr = this.listData;
          let msg = [];
          if (e == 1) {
            this.allData = arr.slice(0, 15);
          } else {
            this.allData = arr.slice(15 * (e - 1), 15 * e);
          }
          this.tabAllCount = this.listData.length;
        },

        resetInput() {
          this.rmPoint("zhdd_map_hdz");
          this.rmPoint("camera-load3");
          this.rmPoint("wlgzsb");
          this.rmPoint("rckz-兴趣点通用");
          // this.rmPoint("sjzx-街面秩序");
        },
        // poi和物联感知查询详情
        getInfoData(item) {
          $api("csdnsy_gis02", {
            code: item.obj.subtype,
            id: item.obj.id,
          }).then((res) => {
            let arr = Object.keys(res[0]).map((ele) => {
              return {
                name: ele,
                value: res[0][ele],
              };
            });

            let countStr = "";
            for (let index = 0; index < arr.length; index++) {
              if (arr[index].name.indexOf("did") > -1) continue;
              else if (arr[index].name.indexOf("value") > -1) continue;
              countStr += `<div
              class="item"
              style="display: flex; font-size: 32px; color: #2299e2; line-height: 70px"
            >
              <span style="margin-left:30px;white-space: nowrap; ">${arr[index].name}  :</span>
              <span style="color: #fff; margin-left:30px;

              "
                >${arr[index].value}</span
              >
            </div>`;
            }
            let str = `
            <div
          onclick=" this.style.display = 'none'"
          style="
            width: 800px;
            position: absolute;

            border-radius: 5px;
            background-color: rgba(10, 31, 53, 0.8);
            z-index: 999999;
            -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
            box-shadow: inset 0 0 40px 0 #5ba3fa;
            padding: 24px;
          "
        >
          <div
            style="
              width: 0px;
              height: 0px;
              border-top: 70px solid rgba(10, 31, 53, 0.8);
              border-right: 60px solid transparent;
              position: absolute;
              bottom: -70px;
              left: 102px;
            "
          ></div>
          <div class="container">${countStr}</div>
        </div>
            `;

            let objData = {
              funcName: "customPop",
              coordinates: item.obj.jwd.split(","),
              closeButton: true,
              html: str,
            };

            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          });
        },

        // 飞行
        flytoAdd(obj, zoom) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "flyto",
              flyData: {
                center: obj,
                zoom: zoom, //大
                pitch: 2, //倾斜角
              },
            })
          );
        },
        // 添加点位方法
        pointTextMapFun(icon, pointData, pointId, iconSize) {
          this.rmPoint("zhdd_map_hdz");
          this.rmPoint("camera-load3");
          this.rmPoint("wlgzsb");
          this.rmPoint("rckz-兴趣点通用");
          this.rmShape();
          // this.rmPoint("sjzx-街面秩序");
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad", //功能名称
              pointType: icon, //点位类型图标
              pointId: "0" + pointId,
              setClick: true,
              pointData: pointData,
              imageConfig: { iconSize: iconSize },
              size: [0.01, 0.01, 0.01, 0.01],
              popup: {
                offset: [50, -100],
              },
            })
          );
          // 加载3D文字方法
          let str = {
            pos: [
              pointData[0].point.split(",")[0],
              pointData[0].point.split(",")[1],
              20,
            ],
            color: [0, 229, 238, 1],
            text: pointData[0].data.obj.name,
          };
          // top.document.getElementById("map").contentWindow.Work.funChange(
          //   JSON.stringify({
          //     funcName: "3Dtext",
          //     id: "00" + pointId,
          //     textData: [str],
          //     textSize: 30,
          //     zoomShow: true,
          //     color: [171, 196, 104, 1],
          //   })
          // );
        },
        // 清除网格
        rmShape() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmShape",
              shapeName: "sou_wg", //多边形名称  不传则清除全部
            })
          );
        },
        // 清除点位
        rmPoint(id) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "0" + id, //传id清除单类，不传清除所有
            })
          );
          try {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "rmPop",
              })
            );
          } catch (error) {}
          // top.document.getElementById("map").contentWindow.Work.funChange(
          //   JSON.stringify({
          //     funcName: "rm3DtextById",
          //     id: "00" + id,
          //   })
          // );
        },
      },
      beforeDestroy() {
        console.log("销毁===============》");
        this.rmShape();
      },
    });
  </script>
</html>
