<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>视频收藏分类--页面</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .topBox001 {
      width: 700px;
      height: 900px;
      padding: 25px 20px;
      box-sizing: border-box;
      overflow: hidden;
      background: url('/static/citybrain/csrk_3840/img/commont/top-bg.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
    }

    .hearder_h2 {
      margin-top: 10px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 40px;
      font-weight: 500;
      text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
      background: linear-gradient(180deg, #caffff 0%, #caffff 0%, #ffffff 0%, #00c0ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .hearder_h2>span {
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 20px;
      white-space: nowrap;
    }

    /* 按钮样式 */
    .el-input__inner {
      background-color: transparent;
      color: #fff;
      font-size: 30px;
      width: 96%;
    }

    .el-button {
      padding: 10px 10px !important;
      font-size: 30px !important;
    }

    /* 表格样式修改 */
    .el-table {
      max-height: 700px !important;
      overflow: hidden;
      overflow-y: auto;
      color: rgb(197, 192, 192);
      padding-right: 5px !important;
      background: transparent !important;
    }

    .el-table th,
    .el-table tr {
      font-size: 30px !important;
      background: rgba(11, 41, 77, 0.89) !important;
    }

    .el-table td,
    .el-table th.is-leaf {
      border: 0 !important;
      border-top: 1px solid rgba(87, 117, 214, 0.603) !important;
    }

    .el-table tbody tr:hover>td {
      background: #1d4a7acb !important;
    }

    .el-table::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 2px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .el-table::-webkit-scrollbar-thumb {
      border-radius: 2px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 10px;
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }

    .el-checkbox,
    .el-checkbox__input {
      zoom: 120%;
    }

    .el-icon-close {
      font-size: 40px;
      color: #fff;
      position: absolute;
      right: 15px;
      top: 15px;
    }

    .el-table .cell {
      line-height: 40px;
    }

    .el-input--mini .el-input__inner {
      height: 40px;
    }

    .el-input .el-input__count {
      font-size: 24px;
    }

    .el-input .el-input__count .el-input__count-inner {
      background: none;
    }

    .el-input__suffix {
      right: 30px;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="topBox001">
      <i class="el-icon-close" @click="closeIframe"></i>
      <p class="hearder_h2">
        <img src="/static/citybrain/csrk_3840/img/commont/tit-l.png" alt="" />
        <span>视频收藏</span>
        <img src="/static/citybrain/csrk_3840/img/commont/tit-r.png" alt="" />
      </p>
      <div class="s-flex s-row-evenly s-m-t-10 s-m-b-10">
        <el-input v-model="addName" placeholder="请输入添加分类名称.." maxlength="15" show-word-limit></el-input>
        <el-button icon="el-icon-plus" type="primary" @click="addClick()"></el-button>
      </div>
      <template>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%; margin-top: 10px"
          :show-header="false" @row-dblclick="dbclick" :cell-class-name="tableCellClassName">
          <el-table-column prop="dir_name" label="收藏名称"> <!-- show-overflow-tooltip -->
            <template slot-scope="scope">
              <el-input maxlength="15" @blur="hideInput(scope.row)" size="mini" :ref="scope.row.index"
                v-model="scope.row.dir_name" v-if="scope.row.index == currentCell">
              </el-input>
              <span v-else :title="scope.row.dir_name+'(双击可修改)'">{{scope.row.dir_name}}</span>
            </template>
          </el-table-column>
          <el-table-column width="250" label="操作">
            <template slot-scope="scope">
              <el-button v-show="scope.row.isExit" @click="cancelCollection(scope.row)" type="text" size="small"
                icon="el-icon-success" title="选择"></el-button>
              <el-button v-show="!scope.row.isExit" @click="handleClick(scope.row)" type="text" size="small"
                icon="el-icon-circle-check" title="选择"></el-button>
              <el-button :disabled="scope.row.isDelet" @click="delClick(scope.row)" type="text" size="small"
                icon="el-icon-delete" :title="scope.row.isDelet?'该文件夹下存在视频,无法删除':'删除'"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    let vm = new Vue({
      el: '#app',
      data: {
        addName: '',
        datas: [],
        tableData: [],
        paramsData: {
          dir_code: '', //收藏目录id
          chn_code: '', //通道编码:33079952001321089392
          user_access: top.commonObj.userId, //账号id
          user_nice: top.commonObj.userInfo.nickName, //昵称
          user_dept: top.commonObj.userInfo.deptName, //部门

          // user_access: "5", //账号id
          // user_nice: "大屏账号", //昵称
          // user_dept: "金华市数据局", //部门

          // user_access: "109", //账号id
          // user_nice: "李贇", //昵称
          // user_dept: "数字金华技术运营有限公司", //部门
        },
        paramsData1: {
          dir_code: '', //收藏目录id
          chn_code: '', //通道编码
          user_access: top.commonObj.userId, //账号id
        },
        paramsData2: {
          dir_name: '',
          id: '',
        },
        currentCell: null,
        authorization: top.commonObj.Authorization,
        // authorization:'eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NjkxOTUxNzYsImxvZ2luX3VzZXJfa2V5IjoiMGQ4OWVlNGEtZDdhOS00MjJjLWIzYTgtNGI0YWUyNzFmMWQ0In0.D99hrfgwnNeccdDESWNGS5RzOINEW3f8NsHKnf7xjlCeHXqW7CUaR9H0ydRNb2v3WYBIRG8LcPIkSe9xoXdusQ',
      },
      mounted () {

        let that = this
        window.addEventListener('message', function (event) {
          if ((event.data.status = 'collect_')) {
            console.log(event.data)
            that.paramsData.chn_code = event.data.videoCode
            that.init()
          }
        })
      },
      methods: {
        // 给单元格绑定横向和竖向的index，这样就能确定是哪一个单元格
        tableCellClassName ({ row, column, rowIndex, columnIndex }) {
          row.index = rowIndex
          column.index = columnIndex
        },
        // 拼接后类似这样："1,0","1,1",
        dbclick (row, column) {
          this.currentCell = row.index
          // 这里必须要setTimeout，因为在点击的时候，input才刚被v-if显示出来，不然拿不到dom
          setTimeout(() => {
            // 双击后自动获得焦点
            this.$refs[row.index].focus()
          })
        },
        // 当input失去焦点的时候，隐藏input
        hideInput (row) {
          this.currentCell = null

          if (row.dir_name != '') {
            this.paramsData2.id = row.pis
            this.paramsData2.dir_name = row.dir_name
            axios({
              method: 'post',
              url: baseURL.admApi + '/mis/dir/updateDirectory',
              params: this.paramsData2,
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                Authorization: this.authorization,
                ptid: 'PT0001',
              },
            }).then((res) => {
              this.init()
            })
          }
        },

        dedupe (arr) {
          let obj = {}
          arr = arr.reduce((newArr, next) => {
            obj[next.pis] ? "" : (obj[next.pis] = true && newArr.push(next))
            return newArr
          }, [])
          return arr
        },
        init () {
          $api('/video_dir', { userId: this.paramsData.user_access }).then((res) => {
            let names = []
            this.datas = res
            this.tableData = this.dedupe(res)

            console.log(this.tableData)


            res.forEach((item) => {
              if (item.chn_code == this.paramsData.chn_code) {
                this.tableData.forEach((el, index) => {
                  if (item.dir_name == el.dir_name) {
                    el.isExit = true
                    return
                  }
                })
              }
              if (item.chn_code) {
                this.tableData.forEach((el, index) => {
                  if (item.dir_name == el.dir_name) {
                    el.isDelet = true
                    return
                  }
                })
              }
            })

            let arr = []
            this.tableData.forEach((i) => {
              if (i.isExit) {
                arr.push(i)
              }
            })
            console.log(this.tableData)
            console.log(arr)
            if (arr.length == 0) {
              top.emiter.emit('showCollection', 0)
            }

          })
        },
        editCollection (row) {
          console.log(row)
          this.paramsData2.id = row.pis
          axios({
            method: 'post',
            url: baseURL.admApi + '/mis/dir/updateDirectory',
            params: this.paramsData2,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: this.authorization,
              ptid: 'PT0001',
            },
          }).then((res) => {
            this.init()
          })
        },
        cancelCollection (row) {
          this.paramsData1.chn_code = this.paramsData.chn_code
          this.paramsData1.dir_code = row.pis
          axios({
            method: 'post',
            url: baseURL.admApi + '/mis/dir/bindingVideo',
            params: this.paramsData1,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: this.authorization,
              ptid: 'PT0001',
            },
          }).then((res) => {
            this.$message('取消收藏!')
            this.$message({
              message: '取消收藏!',
            })
            // top.emiter.emit('showCollection', 0)
            this.init()
          })
        },
        handleClick (row) {
          console.log(row)
          this.paramsData.dir_code = row.pis
          axios({
            method: 'post',
            url: baseURL.admApi + '/mis/dir/bindingVideo',
            params: this.paramsData,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: this.authorization,
              ptid: 'PT0001',
            },
          }).then((res) => {
            this.$message({
              type: 'success',
              message: '收藏成功!',
            })
            this.init()
            // this.closeIframe()
            top.emiter.emit('showCollection', 1)
          })
        },
        delClick (row) {
          this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            axios({
              method: 'post',
              url: baseURL.admApi + '/mis/dir/delDirectory',
              params: {
                id: row.pis,
              },
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                Authorization: this.authorization,
                ptid: 'PT0001',
              },
            }).then((res) => {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.init()
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })

        },
        addClick () {
          if (this.addName == "" || this.addName == null) {
            this.$message({
              type: 'warning',
              message: '文件名不可为空！',
            })
          } else {
            axios({
              method: 'post',
              url: baseURL.admApi + '/mis/dir/createDirectory',
              params: {
                dir_name: this.addName,
                user_access: this.paramsData.user_access,
              },
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                Authorization: this.authorization,
                ptid: 'PT0001',
              },
            }).then((res) => {
              console.log('添加返回值axios=======', res)
              this.addName = ''
              this.init()
            })
          }
        },
        closeIframe () {
          window.parent.postMessage(
            JSON.stringify({
              type: 'closeIframe',
              name: 'collect_',
            }),
            '*'
          )
        },
      },
    })
  </script>
</body>

</html>