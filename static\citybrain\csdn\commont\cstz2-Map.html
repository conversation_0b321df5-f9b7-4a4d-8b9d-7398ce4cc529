<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />

    <script src="/static/citybrain/csdn/jquery/jquery-3.6.1.min.js"></script>

    <!-- <script src="./jquery/jquery-3.6.1.min.js"></script> -->
    <script src="/static/citybrain/hjbh/js/echarts.min.js"></script>

    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:100,300,400,500,700,900" /> -->

    <!-- <link rel="stylesheet" href="./css/common.css" /> -->
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <!-- <script src="./lib/components/core-min.js"></script>
    <script src="./lib/components/sha256.js"></script>
    <script src="./lib/components/md5.js"></script> -->
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/china.js"></script>

    <!-- <script src="./lib/base/vue-seamless-scroll.min.js"></script> -->
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <style>
      #cstz_map_app {
        width: 3420px;
        height: 1880px;
        position: absolute;
        top: 55px;
        left: 0;
        z-index: 1000;
        background-color: #002750;
      }
      #tab-first,
      #tab-second {
        height: 100px;
        font-size: 50px;
        line-height: 100px;
        padding-left: 20px;
      }
      .el-tabs__item,
      .is-top {
        color: #fff;
      }
      /* 省内省外tabs样式 */
      .el-tabs__active-bar {
        background-color: transparent !important;
      }

      /*去掉tabs底部的下划线*/
      .el-tabs__nav-wrap::after {
        position: static !important;
      }

      #tab-second {
        padding-right: 20px;
      }
      .el-tabs__item.is-active {
        color: #fff;
        background-color: #409eff;
      }
    </style>
  </head>

  <body>
    <div id="cstz_map_app" v-cloak>
      <div class="qyt-map" v-show="showMap">
        <el-tabs v-model="activeName" @tab-click="TabHandleClick">
          <el-tab-pane label="省内" name="first">
            <div
              id="mainMap"
              style="width: 3420px; height: 1680px; margin: 0 auto"
            ></div>
          </el-tab-pane>
          <el-tab-pane label="省外" name="second">
            <div
              id="mainMap2"
              style="width: 3420px; height: 1680px; margin: 0 auto"
            ></div>
          </el-tab-pane>
        </el-tabs>
        <img
          src="/static/citybrain/csdn/img/cstz2-middleTwo/close.png"
          @click="showMapClose"
          style="position: absolute; right: 30px; top: 30px; z-index: 999"
          alt=""
        />
      </div>
    </div>

    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- <script type="module" src="./js/cstz-left-new.js"></script> -->
    <script>
      var vm = new Vue({
        el: '#cstz_map_app',
        data: {
          myChart2: {},
          inToMapArr: [],
          outToMapArr: [],
          inProvince: 0,
          outProvince: 0,
          showMap: true,
          activeName: 'first',
          top10Data: [
            {
              top: '1',
              a: '江苏.南京',
              b: 'linear-gradient(360deg, #df8f30, #faff78)',
              c: '60851',
              d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
            },
            {
              top: '2',
              a: '四川.成都',
              b: 'linear-gradient(360deg, #df8f30, #faff78)',
              c: '52238',
              d: 'linear-gradient(360deg, #df8f30, #faff78)',
            },
            {
              top: '3',
              a: '北京',
              b: 'linear-gradient(360deg, #df8f30, #faff78)',
              c: '36048',
              d: 'linear-gradient(360deg, #304ddf, #7882ff)',
            },
            {
              top: '4',
              a: '上海',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '28256',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
            {
              top: '5',
              a: '浙江.杭州',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '25688',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
            {
              top: '1',
              a: '浙江.温州',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '35688',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
            {
              top: '2',
              a: '重庆',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '25688',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
            {
              top: '3',
              a: '安徽',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '25688',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
            {
              top: '4',
              a: '毕节',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '25688',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
            {
              top: '5',
              a: '安顺',
              b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
              c: '25688',
              d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
            },
          ],
          qgMapData: [
            {
              coord: [118.767413, 32.041544],
              name: '南京市',
              num: '8205',
              pm: '',
            },
            {
              coord: [114.085947, 22.547],
              name: '深圳市',
              num: '6475',
              pm: '',
            },
            {
              coord: [113.665412, 34.757975],
              name: '郑州市',
              num: '5099',
              pm: '',
            },
            {
              coord: [113.280637, 23.125178],
              name: '广州市',
              num: '42463',
              pm: '',
            },
            {
              coord: [115.819729, 32.896969],
              name: '阜阳市',
              num: '3875',
              pm: '',
            },
          ],
          // 全省的地图
          showQxMap: false,
          qxMapData: [
            {
              coord: [120.153576, 30.287459],
              name: '杭州市',
              num: '287358',
              pm: '',
            },
            {
              coord: [121.549792, 29.868388],
              name: '宁波市',
              num: '23054',
              pm: '',
            },
            {
              coord: [121.428599, 28.661378],
              name: '台州市',
              num: '11737',
              pm: '',
            },
            {
              coord: [120.582112, 29.997117],
              name: '绍兴市',
              num: '11694',
              pm: '',
            },
            {
              coord: [120.672111, 28.000575],
              name: '温州市',
              num: '11275',
              pm: '',
            },
            {
              coord: [118.87263, 28.941708],
              name: '衢州市',
              num: '10438',
              pm: '',
            },
            {
              coord: [119.921786, 28.451993],
              name: '丽水市',
              num: '8755',
              pm: '',
            },
            {
              coord: [120.750865, 30.762653],
              name: '嘉兴市',
              num: '7826',
              pm: '',
            },
            {
              coord: [120.102398, 30.867198],
              name: '湖州市',
              num: '4880',
              pm: '',
            },
            {
              coord: [122.106863, 30.016028],
              name: '舟山市',
              num: '3955',
              pm: '',
            },
          ],
        },
        created() {
          this.getInOrOut()
        },
        mounted() {
          this.drawQgMap()
          this.drawQxMap()
        },
        methods: {
          showMapClose() {},
          // 加载全国的echarts的地图
          drawQgMap() {
            let that = this
            let jhData = {
              coord: [119.653436, 29.084634],
              name: '金华',
              num: '',
              pm: '',
            }
            this.qgMapData.push(jhData)
            this.myChart2 = echarts.init(document.getElementById('mainMap2'))
            let series = []
            let chinaGeoCoordMap = this.qgMapData
            let len = this.qgMapData.length - 1
            let chinaDatas = this.qgMapData.slice(0, len)
            console.log(chinaDatas)
            console.log(this.qgMapData)
            let option = {
              title: {
                text: '省外城市人口迁移情况',
                left: 'center',
                top: '50px',
                textStyle: {
                  color: '#fff',
                  fontSize: '50',
                },
              },
              tooltip: {
                show: false,
                trigger: 'item',
                showDelay: 0,
                hideDelay: 0,
                enterable: true,
                transitionDuration: 0,
                extraCssText: 'z-index:100',
                formatter: function (params, ticket, callback) {
                  //根据业务自己拓展要显示的内容
                  var res = ''
                  var name = params.name
                  // var value = params.value[params.seriesIndex + 1];
                  var value = params.data.num
                  if (name != '金华') {
                    res = '<span>' + name + '</span><br/>数据：' + value
                  } else {
                    res = '<span>' + name + '</span>'
                  }
                  return res
                },
                textStyle: {
                  fontSize: '40',
                },
              },
              color: ['#00eaff', '#00eaff', '#ffde00', '#fc9700', '#f44336'],
              geo: {
                map: 'china',
                zoom: 1.2,
                label: {
                  emphasis: {
                    show: false,
                  },
                },
                roam: true, //是否允许缩放
                itemStyle: {
                  normal: {
                    color: 'rgba(51, 69, 89, .5)', //地图背景色
                    borderColor: '#00ffff', //省市边界线00fcff 516a89
                    borderWidth: 1,
                    opacity: 0.8,
                    shadowBlur: 20,
                    shadowColor: '#006dda',
                    shadowOffsetX: 5,
                    shadowOffsetY: 5,
                  },
                  emphasis: {
                    color: 'rgba(37, 43, 61, .5)', //悬浮背景
                  },
                },
              },
              backgroundColor: '#002750',
              series: series,
            }

            ;[['金华', chinaDatas]].forEach(function (item, i) {
              series.push(
                {
                  type: 'lines',
                  zlevel: 2,
                  effect: {
                    show: true,
                    period: 4, //箭头指向速度，值越小速度越快
                    trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
                    symbol: 'arrow', //箭头图标
                    symbolSize: 20, //图标大小
                  },
                  lineStyle: {
                    normal: {
                      width: 6, //尾迹线条宽度
                      opacity: 1, //尾迹线条透明度
                      curveness: 0.3, //尾迹线条曲直度
                    },
                  },
                  //       [
                  //   {
                  //     coord: fromCoord,
                  //   },
                  //   {
                  //     coord: toCoord,
                  //     value: dataItem[1].value, //线条颜色
                  //   },
                  // ]
                  data: that.convertData(
                    item[1],
                    chinaGeoCoordMap,
                    [119.653436, 29.084634]
                  ),
                },
                {
                  type: 'effectScatter',
                  coordinateSystem: 'geo',
                  zlevel: 2,
                  rippleEffect: {
                    //涟漪特效
                    period: 4, //动画时间，值越小速度越快
                    brushType: 'stroke', //波纹绘制方式 stroke, fill
                    scale: 4, //波纹圆环最大限制，值越大波纹越大
                  },
                  label: {
                    normal: {
                      show: true,
                      position: 'right', //显示位置
                      // offset: [5, -20], //偏移设置
                      formatter: function (params) {
                        //圆环显示文字
                        return (
                          params.data.pm +
                          '' +
                          params.data.name +
                          '{a| ' +
                          params.data.num +
                          '}人'
                        )
                      },
                      color: '#fff',
                      fontSize: 36,
                      rich: {
                        a: {
                          color: 'yellow',
                          fontSize: 36,
                        },
                      },
                    },
                    emphasis: {
                      show: true,
                    },
                  },
                  symbol: 'circle',
                  symbolSize: function (val) {
                    // return 10 + val[2] * 5; //圆环大小
                    return 10 //圆环大小
                  },
                  data: item[1].map(function (dataItem) {
                    return {
                      name: dataItem.name,
                      value: dataItem.coord,
                      num: dataItem.num,
                      pm: dataItem.pm,
                      // value: chinaGeoCoordMap[dataItem.name].concat([dataItem.num])
                    }
                  }),
                },

                //被攻击点
                {
                  type: 'scatter',
                  coordinateSystem: 'geo',
                  zlevel: 2,
                  rippleEffect: {
                    period: 4,
                    brushType: 'stroke',
                    scale: 4,
                  },
                  label: {
                    normal: {
                      show: true,
                      position: 'left',
                      //offset:[5, 0],
                      color: '#fff',
                      formatter: '{b}',
                      textStyle: {
                        color: '#f00',
                        fontSize: 40,
                      },
                    },
                    emphasis: {
                      show: true,
                      color: '#f60',
                    },
                  },
                  symbol: 'pin',
                  symbolSize: 60,
                  itemStyle: {
                    normal: {
                      color: '#f44336',
                    },
                  },
                  data: [
                    {
                      name: item[0],
                      value: chinaGeoCoordMap[len].coord,
                      num: chinaGeoCoordMap[len].num,
                      pm: chinaGeoCoordMap[len].pm,
                    },
                  ],
                }
              )
            })
            this.myChart2.setOption(option)
          },
          TabHandleClick(el) {
            // console.log(window.vm_left)

            if (el.label === '省外') {
              //   this.activeName = 'seconed'
              // console.log(1)
              let arr = []
              let arrAll = this.outToMapArr.slice(0, 10)
              console.log(arrAll)
              arrAll.forEach((item, i) => {
                let str = {
                  name: item.name,
                  coord: item.coord,
                  num: item.num,
                  pm: i + 1,
                }
                arr.push(str)
              })
              console.log(arr)
              this.qgMapData = arr

              this.drawQgMap()

              // this.isHot = true
              // this.showQgMap = !this.showQgMap
              //   this.showQgMap == true
              //     ? (this.showMiddleNew = false)
              //     : (this.showMiddleNew = true)
              //   this.openWind = ['showQgMap', 'showMiddleNew']
              //   this.colseOtherWin()
              //   this.closeWinZdqY()
            }
          },
          // 加载全省的echarts的地图
          drawQxMap() {
            let that = this
            let jhData = {
              coord: [119.653436, 29.084634],
              name: '金华',
              num: '',
              pm: '',
            }
            this.qxMapData.push(jhData)
            this.myChart3 =
              this.activeName == 'first'
                ? echarts.init(document.getElementById('mainMap'))
                : echarts.init(document.getElementById('mainMap2'))

            // this.myChart3 = echarts.init(document.getElementById('mainMap'))
            $.getJSON(
              '/static/citybrain/csdn/data/zj.json',
              function (geoJson) {
                echarts.registerMap('zj', geoJson)
                let data = []
                let series = []
                let chinaGeoCoordMap = that.qxMapData
                let len = that.qxMapData.length - 1
                let chinaDatas = that.qxMapData.slice(0, len)
                // geoJson.features.map(item=>{
                //   let str={
                //     name:item.properties.name,
                //     center:item.properties.center,
                //     value:120
                //   }
                //   data.push(str)
                // })

                let option = {
                  title: {
                    text: '省内人口迁移情况',
                    left: 'center',
                    top: '50px',
                    textStyle: {
                      color: '#fff',
                      fontSize: '50',
                    },
                  },
                  tooltip: {
                    trigger: 'item',
                    show: false,
                    formatter: function (params, ticket, callback) {
                      //根据业务自己拓展要显示的内容
                      var res = ''
                      var name = params.name
                      // var value = params.value[params.seriesIndex + 1];
                      var value = params.data.num
                      if (name != '金华') {
                        res = '<span>' + name + '</span><br/>数据：' + value
                      } else {
                        res = '<span>' + name + '</span>'
                      }
                      return res
                    },
                    textStyle: {
                      color: '#000',
                      fontSize: '40',
                    },
                  },
                  backgroundColor: '#002750',
                  color: [
                    '#00eaff',
                    '#00eaff',
                    '#ffde00',
                    '#fc9700',
                    '#f44336',
                  ],
                  visualMap: {
                    show: false,
                    // calculable: true,
                    seriesIndex: [0],
                    inRange: {},
                  },
                  geo: {
                    show: true,
                    map: 'zj',
                    label: {
                      normal: {
                        show: false,
                        fontSize: 40,
                        color: '#fff', //控制地图省市文字颜色
                      },
                      emphasis: {
                        show: false,
                        color: '#fff', //悬浮字体颜色
                      },
                    },
                    roam: true,
                    layoutSize: '85%',
                    layoutCenter: ['50%', '50%'],
                    itemStyle: {
                      normal: {
                        color: '#000',
                        areaColor: 'rgba(51, 69, 89, .5)', //地图背景颜色
                        borderColor: '#00ffff', //边界颜色
                        opacity: 0.8,
                        shadowBlur: 20,
                        shadowColor: '#006dda',
                        shadowOffsetX: 5,
                        shadowOffsetY: 5,
                      },
                      emphasis: {
                        color: '#000',
                        areaColor: 'skyblue', //悬浮背景颜色
                      },
                    },
                  },
                  series: series,
                  // series: [{
                  //     type: "map",
                  //     map: "zj",
                  //     geoIndex: 0,
                  //     aspectScale: 0.75, //长宽比
                  //     showLegendSymbol: false, // 存在legend时显示
                  //     roam: false,
                  //     animation: false,
                  //     data: data,
                  //     label: {
                  //       normal: {
                  //         show: true,
                  //         color:'#fff',
                  //         fontSize: 36
                  //       },
                  //       emphasis: {
                  //         show: true
                  //       }
                  //     },
                  // }]
                }

                ;[['金华', chinaDatas]].forEach(function (item, i) {
                  series.push(
                    {
                      type: 'lines',
                      zlevel: 2,
                      effect: {
                        show: true,
                        period: 4, //箭头指向速度，值越小速度越快
                        trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
                        symbol: 'arrow', //箭头图标
                        symbolSize: 20, //图标大小
                      },
                      lineStyle: {
                        normal: {
                          width: 6, //尾迹线条宽度
                          opacity: 1, //尾迹线条透明度
                          curveness: 0.3, //尾迹线条曲直度
                        },
                      },
                      //       [
                      //   {
                      //     coord: fromCoord,
                      //   },
                      //   {
                      //     coord: toCoord,
                      //     value: dataItem[1].value, //线条颜色
                      //   },
                      // ]
                      data: that.convertData(
                        item[1],
                        chinaGeoCoordMap,
                        [119.653436, 29.084634]
                      ),
                    },
                    {
                      type: 'effectScatter',
                      coordinateSystem: 'geo',
                      zlevel: 2,
                      rippleEffect: {
                        //涟漪特效
                        period: 4, //动画时间，值越小速度越快
                        brushType: 'stroke', //波纹绘制方式 stroke, fill
                        scale: 4, //波纹圆环最大限制，值越大波纹越大
                      },
                      label: {
                        normal: {
                          show: true,
                          position: 'right', //显示位置
                          // offset: [5, -20], //偏移设置
                          formatter: function (params) {
                            //圆环显示文字
                            return (
                              params.data.pm +
                              '' +
                              params.data.name +
                              '{a| ' +
                              params.data.num +
                              '}人'
                            )
                          },
                          color: '#fff',
                          fontSize: 36,
                          rich: {
                            a: {
                              color: 'yellow',
                              fontSize: 36,
                            },
                          },
                        },
                        emphasis: {
                          show: true,
                        },
                      },
                      symbol: 'circle',
                      symbolSize: function (val) {
                        // return 10 + val[2] * 5; //圆环大小
                        return 10 //圆环大小
                      },
                      data: item[1].map(function (dataItem) {
                        return {
                          name: dataItem.name,
                          value: dataItem.coord,
                          num: dataItem.num,
                          pm: dataItem.pm,
                          // value: chinaGeoCoordMap[dataItem.name].concat([dataItem.num])
                        }
                      }),
                    },
                    //被攻击点
                    {
                      type: 'scatter',
                      coordinateSystem: 'geo',
                      zlevel: 2,
                      rippleEffect: {
                        period: 4,
                        brushType: 'stroke',
                        scale: 4,
                      },
                      label: {
                        normal: {
                          show: true,
                          position: 'left',
                          //offset:[5, 0],
                          color: '#fff',
                          formatter: '{b}',
                          textStyle: {
                            color: '#f00',
                            fontSize: 40,
                          },
                        },
                        emphasis: {
                          show: true,
                          color: '#f60',
                        },
                      },
                      symbol: 'pin',
                      symbolSize: 60,
                      itemStyle: {
                        normal: {
                          color: '#f44336',
                        },
                      },
                      data: [
                        {
                          name: item[0],
                          value: chinaGeoCoordMap[len].coord,
                          num: chinaGeoCoordMap[len].num,
                          pm: chinaGeoCoordMap[len].pm,
                        },
                      ],
                    }
                  )
                })
                that.myChart3.setOption(option)
              }
            )
          },
          inPersonMap() {
            console.log('点击省内')
            console.log(top.vm)
            if (this.activeName === 'second') {
              console.log(1111111111)
              this.TabHandleClick()
              return
            }
            // let arr = this.inToMapArr.slice(0, 10)
            let arr = []
            let arrAll = this.inToMapArr.slice(0, 10)
            arrAll.forEach((item, i) => {
              let str = {
                name: item.name,
                coord: item.coord,
                num: item.num,
                pm: i + 1,
              }
              arr.push(str)
            })
            this.qxMapData = arr
            this.drawQxMap()
            this.isHot = true
            this.showMap = true
            // top.vm.showQxMap = !top.vm.showQxMap
            // top.vm.showQxMap==true?top.vm.showMiddleNew=false:top.vm.showMiddleNew=true
            // top.vm.openWind = ['showQxMap', 'showMiddleNew']
            // top.vm.colseOtherWin()
            // top.vm.closeWinZdqY()
          },
          getInOrOut() {
            $api('cstz_top5').then((res) => {
              var inTop10 = [],
                outTop10 = [],
                inAll = 0,
                outAll = 0

              for (let i = 0; i < res.length; i++) {
                var num = +res[i].addressCount
                var num1 = +res[i].addressLongitude
                var num2 = +res[i].addressLatitude
                var obj = {
                  name: res[i].addressName,
                  coord: [num1, num2],
                  num: res[i].addressCount,
                }
                if (res[i].topsState == 1) {
                  inTop10.push(res[i])
                  inAll += num
                  this.inToMapArr.push(obj)
                } else {
                  outTop10.push(res[i])
                  outAll += num
                  this.outToMapArr.push(obj)
                }
              }
              inTop10 = inTop10.slice(0, 5)
              outTop10 = outTop10.slice(0, 5)

              let top10Data = []

              inTop10.forEach((item, i) => {
                if (i === 0) {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #df8f30, #faff78)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
                    e: item.addressCount,
                  })
                } else if (i === 1) {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #df8f30, #faff78)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #df8f30, #faff78)',
                    e: item.addressCount,
                  })
                } else if (i === 2) {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #df8f30, #faff78)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #304ddf, #7882ff)',
                    e: item.addressCount,
                  })
                } else {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                    e: item.addressCount,
                  })
                }
              })

              outTop10.forEach((item, i) => {
                if (i === 0) {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #df8f30, #faff78)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
                    e: item.addressCount,
                  })
                } else if (i === 1) {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #df8f30, #faff78)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #df8f30, #faff78)',
                    e: item.addressCount,
                  })
                } else if (i === 2) {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #df8f30, #faff78)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #304ddf, #7882ff)',
                    e: item.addressCount,
                  })
                } else {
                  top10Data.push({
                    top: i + 1,
                    a: item.addressName,
                    b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                    c: parseInt(item.addressRate * 10000),
                    d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                    e: item.addressCount,
                  })
                }
              })

              this.top10Data = top10Data

              this.inProvince = inAll
              this.outProvince = outAll
            })
          },
          convertData(data, chinaGeoCoordMap, id) {
            var res = []
            for (var i = 0; i < data.length; i++) {
              var dataItem = data[i]
              var fromCoord = dataItem.coord
              var toCoord = id
              if (fromCoord && toCoord) {
                res.push([
                  {
                    coord: fromCoord,
                    value: dataItem.num,
                  },
                  {
                    coord: toCoord,
                  },
                ])
              }
            }
            return res
          },
        },
      })
    </script>
  </body>
</html>
