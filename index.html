<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>首页</title>
  <link rel="stylesheet" href="static/css/sigma.css" />
  <link rel="stylesheet" href="static/css/common.css" />
  <link rel="stylesheet" href="static/css/animate.css" />
  <!-- <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css" /> -->
  <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/dark/main.css" />
  <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/popu.css" />
  <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/utils/swipe/swipe.css" />
  <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
  <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
  <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
  <style>
    button {
      width: 500px;
      height: 200px;
      font-size: 50px;
    }

    .btn {
      width: 150px;
      height: 150px;
      left: 2150px;
      top: 1950px;
      position: absolute;
      background-image: url(./img/btn_click.png);
      background-size: 100% 100%;
      z-index: 9999;
      display: none !important;
      /* z-index: 80; */
    }

    .btn:hover {
      background-image: url(./img/btn_clicked_hover.png);
    }

    .btn-active {
      background-image: url(./img/btn_clicked_hover.png);
    }

    .switch_area {
      position: absolute;
      left: 4150px;
      top: 35px;
      z-index: 998;
    }

    .triangle {
      cursor: pointer;
      position: relative;
      z-index: 998;
    }

    .switch_menu {
      width: 80%;
      margin-left: 10%;
      height: auto;
      /* height: auto; */
      background: linear-gradient(180deg, #0e1a4095, #07487595);
      /* left: 90px; */
      /* top: 0; */
      z-index: 14;
      position: absolute;
    }

    .menu_2 {
      /* margin-top: 80px; */
    }

    .menu_3 {
      position: relative;
      /* left: 346px; */
    }

    ul.switch_menu li {
      font-size: 40px;
      color: white;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      width: 100%;
      height: 76px;
      line-height: 70px;
      text-align: center;
      background: linear-gradient(180deg, #ffffff 53%, #94aadb 1.8%, #c3d8ee 50%, #ebf2ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* ul.switch_menu li:first-of-type {
            border-radius: 16px 16px 0 0;
        }

        ul.switch_menu li:last-of-type {
            border-radius: 0 0 16px 16px;
        } */

    ul.switch_menu li:hover {
      background-image: linear-gradient(180deg, #ffffff 0%, #ffc460 50%, #ffeccb 100%);
      background-blend-mode: normal, normal;
      cursor: pointer;
    }

    .header_time {
      display: flex;
      align-items: center;
    }

    .header_time .week {
      cursor: pointer;
      height: 100px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      /* padding-right: 10px; */
    }

    .header_time .week img {
      margin-right: 25px;
      margin-top: -15px;
    }

    .headNowtitle {
      /* padding: 0px 20px;
            border-top: 5px solid #1f85c8;
            background-image: linear-gradient(to bottom, rgb(34 89 125), rgb(12 107 172 / 10%)); */
      background-image: url('/static/images/home/<USER>');
      /* background-size: 100%; */
      background-repeat: no-repeat;
    }

    .top-close {
      width: 120px;
      height: 120px;
      background-image: url('/static/images/common/components/close-1.png');
      background-size: 100%;
    }

    .details-modal-overlay {
      background: rgba(3, 24, 39, 0.6);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(4px);
      position: fixed;
      bottom: 0;
      right: 0;
      left: 0;
      top: 0;
      width: 7680px;
      height: 2160px;
      z-index: 0;
    }

    /* 退出弹窗 */
    .exit-system-css {
      position: absolute;
      left: 3120px;
      top: 750px;
      z-index: 10000;
      width: 1440px;
      height: 420px;
      background: #025e9a;
      box-shadow: inset 0px -3px 0px 0px rgba(255, 255, 255, 0.12);
      border-radius: 0px 0px 0px 0px;
      border: 3px solid;
      border-image: linear-gradient(270deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 3 3;
    }

    .exit-top {
      width: 100%;
      height: 132px;
      font-size: 48px;
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 50px;
      box-sizing: border-box;
    }

    .exit-bottom {
      width: 100%;
      height: calc(100% - 132px);
      font-size: 42px;
      color: #fff;
      border-top: 3px solid #2b76ac;
      padding: 50px;
      box-sizing: border-box;
    }

    .exit-system-text {
      width: 100%;
      color: rgba(255, 255, 255, 0.49);
      margin-bottom: 40px;
    }

    .close-btn {
      width: 48px;
      height: 48px;
      background-image: url('/static/images/common/components/close.png');
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
    }

    .close-btn:hover {
      background-image: url('/static/images/common/components/close-hover.png');
    }

    .exit-btns {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }

    .exit-btns>div {
      width: 222px;
      height: 96px;
      text-align: center;
      line-height: 96px;
      border-radius: 6px;
      border: 3px solid rgba(255, 255, 255, 0.2);
    }

    .exit-btns>div:first-child {
      margin-right: 30px;
    }

    .exit-btn-active {
      background-color: #177edd;
      border-color: transparent;
    }

    .exit-btn-active:hover {
      background-color: #1785eb;
    }

    #cancel:hover {
      background-color: #016bb1;
    }

    /* 展开和收起iframe */
    .close_left_right_iframe_btn {
      width: 74px;
      height: 74px;
      background-image: url('/img/oepn_iframe.png');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      position: absolute;
      z-index: 99;
      right: 20px;
      top: 150px;
      cursor: pointer;
    }

    .open_left_iframe_btn {
      display: inline-block;
      width: 15px;
      height: 1980px;
      background-image: url('/img/open_left_btn_bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      left: 0;
      z-index: 99;
    }

    .open_right_iframe_btn {
      width: 15px;
      height: 1980px;
      background-image: url('/img/open_right_btn_bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      right: 0;
      z-index: 99;
    }

    .open_left_iframe_btn>div {
      width: 58px;
      height: 297px;
      background-image: url('/img/open_left_btn.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      left: 15px;
      top: calc(50% - 148px);
      z-index: 99;
    }

    .open_right_iframe_btn>div {
      width: 58px;
      height: 297px;
      background-image: url('/img/open_right_btn.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      right: 15px;
      top: calc(50% - 148px);
      z-index: 99;
    }

    /* 移动中间图标的位置 */
    .map_mapIcon_move {
      left: 7455px !important;
      top: 200px !important;
    }

    .map_mapIcon_move2 {
      left: 7495px !important;
      top: 735px !important;
    }

    .btns_move {
      left: 30px !important;
    }

    #areaContainer {
      position: absolute;
      left: 4000px;
      top: 900px;
      z-index: 10;
      color: white;
      font-size: 30px;
    }

    #sliceContainer {
      position: absolute;
      left: 4890px;
      top: 1450px;
      width: 500px;
      z-index: 10;
      background: rgba(3, 24, 39, 0.88);
    }

    #lineOfSight {
      position: absolute;
      left: 4890px;
      top: 1540px;
      width: 500px;
      z-index: 10;
      background: rgba(3, 24, 39, 0.88);
    }

    #daylightWidget {
      position: absolute;
      left: 4890px;
      top: 1630px;
      z-index: 10;
      /* border: solid 1px lightblue; */
      background: rgba(3, 24, 39, 0.88);
    }

    .esri-slider-with-dropdown__list {
      color: white;
      font-size: 20px;
    }

    .esri-daylight .esri-slider.esri-slider--horizontal .esri-widget__anchor.esri-slider-with-dropdown__anchor {
      font-size: 30px;
    }

    .esri-daylight .esri-slider.esri-slider--horizontal .esri-slider__label {
      font-size: 30px;
    }

    .esri-daylight .esri-slider.esri-slider--horizontal .esri-slider__tick-label {
      font-size: 20px;
    }

    .esri-button,
    .esri-elevation-profile__header button {
      font-size: 30px;
      height: 60px;
    }

    .esri-date-picker__calendar {
      font-size: 20px;
    }

    .esri-slider-with-dropdown__anchor {
      width: 0;
      height: 0;
    }

    .mapPopup {
      position: fixed;
      z-index: 1;
      min-width: 300px;
      background: none;
      box-shadow: none;
      transform-origin: 50% 100%;
      transition: transform 0.3s ease-out;
    }

    .show {
      transform: translate(-50%, calc(-100% - 16px)) scale(1);
    }

    .hide {
      transform: translate(-50%, calc(-100% - 16px)) scale(0);
    }

    .mapPopup .header {
      display: none;
    }

    .mapPopup .body {
      position: relative;
    }

    .mapPopup .body::before {
      display: none;
    }

    .mapPopup .bodyContent {
      z-index: 1;
      padding: 0px;
    }

    .mapPopup .container {
      height: 100%;
      width: 100%;
    }

    .mapPopup ::-webkit-scrollbar {
      width: 6px;
      height: 1px;
      background-color: transparent;
    }
    .mapPopup ::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 20px;
    }

    /* 截图的样式 */
    #screenShotContainer {
      width: 7680px !important;
      left: 0 !important;
      z-index: 1000;
    }

    #toolPanel {
      min-width: 500px !important;
      height: 40px !important;
    }

    #toolPanel .item-panel {
      width: 35px !important;
      height: 35px !important;
    }

    /* 默认弹窗 */
    .esri-feature-fields__field-header {
      font-size: 24px !important;
    }

    .esri-widget__table {
      line-height: normal !important;
    }

    .esri-widget__table tr td {
      font-size: 24px !important;
    }

    .esri-popup__header-container--button {
      height: 100%;
    }

    .esri-popup__main-container {
      max-height: 660px !important;
      width: 500px !important;
    }

    .esri-popup__header-title {
      font-size: 26px !important;
    }

    .center-bottom {
      background-image: url('/static/citybrain/csdn/img/ywt/center-bc.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 点击三角形，六大板块切换 -->
    <!-- <div class="switch_area">
            <img class="triangle" src="./img/triangle.png" alt="" />
            <ul class="switch_menu">
            </ul>
        </div> -->

    <div class="header" style="position: relative">
      <!-- 标题左上角去掉 -->
      <!-- <div class="headNowtitle">
          <div style="height: 80px; line-height: 90px">
            <i class="text_linear_white" style="margin-right: 60px">当前位置</i>
          </div>
          <div style="height: 100px">
            <span class="lg-yellow nowTitle" style="margin-right: 60px"></span>
          </div>
        </div> -->
      <div class="header_time" id="headerTime1"></div>

      <img src="/static/images/common/header/fk.png"
        style="position: absolute; z-index: 99; right: 100px; width: 50px; height: 40px; top: 35px; cursor: pointer"
        alt="" onclick="commonObj.openFk()" />
      <img src="/static/images/common/header/tuichu.png"
        style="position: absolute; z-index: 99; right: 25px; width: 40px; height: 40px; top: 35px; cursor: pointer"
        onclick="commonObj.logOut()" alt="" />

      <div class="header_img"></div>
      <div class="header_title_text">
        <span class="text_linear_white header_text" id="headerTitle" onclick="commonObj.backIndex()" parentUrl="home">
          金华城市大脑•一网统管
        </span>
      </div>
      
      <ul class="nav_ul_home" id="navUlLeft"></ul>
      <ul class="nav_ul_home" style="left: 4850px" id="navUlRight"></ul>
      <ul class="nav_ul_two" style="left: 2000px; top: 20px" id="navUlTwo"></ul>
      <div class="weather" id="weather" style="z-index: 10"></div>
    </div>

    <!-- 剖切窗口 -->
    <div id="sliceContainer"></div>
    <!-- 通视分析窗口 -->
    <div id="lineOfSight"></div>
    <!-- 日照分析窗口 -->
    <div id="daylightWidget"></div>

    <!-- <iframe src="http://localhost:3001/#/chart/preview/emdn9gplr9k0000" name="indexTcglBtn" width="2100" height="1900" style="position: absolute;z-index: 999;left: 2200px;top: 210px"
        frameborder="0"></iframe> -->
    <!-- 测试验收地图点位聚合 按钮 -->
    <!-- <iframe
        src="/static/citybrain/0.html"
        style="width: 250px; height: 100px; position: absolute; left: 4000px; top: 250px; z-index: 999"
        scrolling="no"
        frameborder="0"
      ></iframe> -->
    <!-- 图层管理 -->
    <iframe src="/static/citybrain/tckz/IndexTcglBtn.html" class="index_frame animated fadeInLeft" name="indexTcglBtn"
      width="100px" height="230px" style="position: absolute; z-index: 8; left: 2100px; top: 200px" id="layerBar"
      frameborder="0"></iframe>
    <!-- 全局搜索 -->
    <!-- <iframe
        src="/static/citybrain/tckz/IndexVideoBtn.html"
        class="index_frame animated fadeInLeft"
        name="indexVideoBtn"
        width="100px"
        height="230px"
        style="position: absolute; z-index: 999; top: 630px; left: 2100px"
        frameborder="0"
      ></iframe> -->
    <!-- 搜索视频 -->
    <iframe src="/static/citybrain/tckz/IndexVideoBtnall.html" class="index_frame animated fadeInLeft"
      name="indexVideoBtnAll" width="100px" height="230px" id="videoBar"
      style="position: absolute; z-index: 8; top: 414px; left: 2100px" frameborder="0"></iframe>
    <!-- 无人机 -->
    <!-- <iframe src="/static/citybrain/tckz/IndexDroneBtn.html" class="index_frame animated fadeInLeft"
      name="indexVideoBtnAll" width="100px" height="230px"
      style="position: absolute; z-index: 8; top: 640px; left: 2100px" frameborder="0"></iframe> -->
    <!-- <iframe src="/static/citybrain/csdn/commont/index_name_sou.html" name="souMainBox" width="685px" height="360px" style="position: absolute;z-index: 999;left: 3513px;top: 200px" frameborder="0"></iframe> -->
    <!-- 左边小图标 -->
    <iframe class="index_main_mapIcon" style="position: absolute; z-index: 8; left: 5412px; top: 440px;transform: scale(1.5)" id="toolBar"
      name="indexMapIcon" src="/static/citybrain/csdn/commont/main_mapIcon.html" width="140px" height="1000px"
      frameborder="0" scrolling="no"></iframe>
    <!-- 物联感知 -->
    <!-- <iframe
        src="/static/citybrain/tckz/IndexWlgzBtn.html"
        class="index_frame animated fadeInLeft"
        name="indexWlgzBtn"
        width="100px"
        height="230px"
        style="position: absolute; z-index: 999; top: 630px; left: 2100px"
        frameborder="0"
      ></iframe> -->
    <!-- <div class="main-page" id="mainPage">
        <iframe class="page_center" src="./static/2Dmap/index.html" frameborder="0"></iframe>
      </div> -->

    <div class="main-page" id="mainPage"></div>
    <!-- <div class="page_center" id="map" style="background-color: #000"></div> -->

    <!-- <iframe class="page_center" src="/static/two-and-three-dimensional-integration/index.html" id="map" name="map" frameborder="0"></iframe> -->
    <!--iframe class="page_center" src="/static/EGS(v1.0.0)/index.html" id="map" name="map" frameborder="0"></iframe-->
    <div class="page_center" id="viewDiv"></div>
    <div style="z-index: 999; position: absolute; bottom: 100px; right: 2000px; display: none">
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1001">高标准农田-面</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1002">城镇开发边界</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1003">2009年地质灾害易发程度分区</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1004">2014年基本农田标志牌</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1005">白模</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1006">黄色白模</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1007">精模</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1008">倾斜摄影</button>
      <button style="width: 400px; height: 80px; margin-bottom: 10px" id="1009">湖海塘</button>
    </div>
    <div id="main-bg" style="
          background: url('/static/images/common/bg-main.png') no-repeat 100%;
          width: 7680px;
          height: 2160px;
          top: 0;
          z-index: 1;
          position: absolute;
        "></div>

    <!-- 模态框 -->
    <div id="modal-overlay" class="details-modal-overlay"></div>
    <div id="page_middle" class="page_middle" style="position: absolute; z-index: 99"></div>
    <div class="btn" id="btnClick"></div>
    <!-- <div id="page_middle1" class="page_middle1" style="position: absolute; z-index: 99"> -->

    <!-- 收起和展开的按钮 -->
    <div class="close_left_right_iframe_btn" onclick="lrFrameSHClick()"></div>
    <div class="open_left_iframe_btn" id="leftIframeShowBtn" style="display: none; cursor: pointer" onclick="lframeShow()">
      <div></div>
    </div>
    <div class="open_right_iframe_btn" id="rightIframeShowBtn" style="display: none; cursor: pointer" onclick="rframeShow()">
      <div></div>
    </div>
  </div>
  <script src="/static/js/jslib/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script>
  <script src="/static/js/jslib/iframeResizer.contentWindow.min.js"></script>
  <script src="/static/js/jslib/common.3.js"></script>
  <!-- <script src="/static/js/jslib/common.4.js"></script> -->
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  <script src="/static/js/jslib/listeners.js"></script>
  <script src="/static/js/jslib/watchmsg.js"></script>
  <script src="/static/js/jslib/arcgis-to-geojson.js"></script>
  <script src="/static/js/jslib/biz.min.js"></script>
  <!--script src="http://dev.arcgisonline.cn/jsapi/4.24/init.js"></script-->
  <!--script type="module" src="./static/js/jslib/ArcGisUtils.js"></script-->
  <script src="./static/js/jslib/ArcGisUtils/libs/three-r79.min.js"></script>
  <script src="./static/js/jslib/ArcGisUtils/libs/three-r116.min.js"></script>
  <script src="./static/js/jslib/ArcGisUtils/libs/three-r123.min.js"></script>
  <script src="./static/js/jslib/ArcGisUtils/libs/FBXLoader.r116.js"></script>
  <script src="./static/citybrain/csrk_3840/webjs/layer.js"></script>
  <script type="module" src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/index.js"></script>
  <script type="module" src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/lodash.min.js"></script>
  <script src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/turf.min.js"></script>
  <script src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/xgplayer.js"></script>
  <script src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/xgplayer-hls.js"></script>
  <!-- <script src="/static/js/jslib/vconsole.min.js"></script>
    <script type="text/javascript">
      let vConsole = new VConsole()
    </script> -->
  <!-- 这个js是截图用的 -->
  <script src="/static/citybrain/template_page/js/dist/screenShotPlugin.umd.js"></script>
  <script>
    // window.addEventListener('message',function (event){
    //   if (event.data.type === 'doVideoMeeting') {
    //     console.log('监听到doVideoMeeting')
    //     console.log(event.data)
    //     dd.createVideoMeeting({
    //       title: "视频会议",
    //       calleeStaffIds: event.data.accountIds, //人员列表
    //     }).then(res => {
    //       console.log('视频会议调用成功')
    //     }).catch(err => {
    //       console.log('视频会议调用失败', err)
    //     })
    //   }
    //   if (event.data.type === 'pointClick' && navigator.userAgent.toLowerCase().indexOf('dingtalk') > -1) {
    //     alert('监听到pointClick喽234')
    //   }
    // })

    // console.log(navigator.userAgent)
    // console.log('navigator.userAgent')
    window.onload = () => {
      // 缩放
      //top.lrFrameSHClick()
      // 地图初始视角
      let opts_home = {
        // 二环
        // x: 119.**************,
        // y: 28.**************,
        // z: 10280.***********,
        // heading: 354.*************,
        // tilt: 47.***************,
        // 市政府
        x: 119.**************,
        y: 29.***************,
        z: 607.*************,
        heading: 350.*************,
        tilt: 62.**************,
        basemap: 'image2023',
        isDefaultGoToFromGlobal: false,
        onload: () => {
          /*mapUtil.loadModelLayer({
          layerid: '精模',
          type: 'jm',
        })*/
          // mapUtil.loadTrafficLayer({layerid:"icon_road"})
          // 自动勾选路况
          // top.frames['indexMapIcon'].mainIconVm.showRoad = false
          // top.frames['indexMapIcon'].mainIconVm.openWid()
          // 流光线
          //mapUtil.tool.addEffect('flowline')
          // 倾斜摄影
          // mapUtil.loadModelLayer({
          //   layerid: 'qxLayer',
          //   type: 'qx',
          // })
          // localStorage.setItem('mapTypes', 'qx')
          localStorage.setItem('indexMapBm', 'qx')
          localStorage.setItem('QRCode', '')
          ArcGisUtils.startRoadFlicker(window.view, 40) //默认道路闪烁
          ArcGisUtils.addDEMToMap() //默认加载地形
          commonObj.indexLoadMap()
          top.document.getElementsByClassName('page_left')[0].style.backgroundImage = "url('/img/left-bg2.png')"
          top.document.getElementsByClassName('page_right')[0].style.backgroundImage = "url('/img/right-bg2.png')"
        },
      }
      mapUtil.initMap(opts_home)
      localStorage.setItem('mapType', '2')
      // 是否是大屏
      localStorage.setItem('isFull', 'true')
    }
  </script>
</body>

</html>