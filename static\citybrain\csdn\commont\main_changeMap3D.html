<!DOCTYPE html>
<html lang='en'>

<head>
  <meta charset='UTF-8' />
  <title>切换地图场景</title>
  <link rel='stylesheet' href='/static/css/animate.css' />
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script type="module">
    import FeatureLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/layers/FeatureLayer.js";
    window.FeatureLayer = FeatureLayer;
  </script>
  <script src='/static/citybrain/csdn/Vue/vue.js'></script>
  <script src="/elementui/js/elementui.js"></script>
  <script src="/static/citybrain/csrk_3840/webjs/layer.js"></script>
  <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
  <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
  <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
  <script src="/static/js/home_services/md5.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csrk_3840/webjs/layer.js"></script>
  <script src="/static/citybrain/csrk_3840/webjs/Emiter.js"></script>
  <script src="/static/js/jslib/turf.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    ::-webkit-scrollbar {
      width: auto !important;
      height: auto !important;
    }

    .mapClickNew {
      border-radius: 10px;
      background: #162b48;
      color: #ccc;
      font-size: 22px;
      cursor: pointer;
      padding: 5px;
      box-sizing: border-box;
      width: fit-content;
    }

    .mapClickNew .mapClick_img1,
    .mapClickNew .mapClick_img3,
    .mapClickNew .mapClick_img4,
    .mapClickNew .mapClick_img2 {
      position: relative;
      width: 181px;
      height: 181px;
      border-radius: 10px;
      margin: 5px;
    }

    .mapClickNew .mapClick_img1:hover span,
    .mapClickNew .mapClick_img2:hover span,
    .mapClickNew .mapClick_img3:hover span,
    .mapClickNew .mapClick_img4:hover span {
      color: #fff;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), #0E7AD9);
    }

    .mapClickNew .mapClick_img1 span,
    .mapClickNew .mapClick_img3 span,
    .mapClickNew .mapClick_img4 span,
    .mapClickNew .mapClick_img2 span {
      position: absolute;
      right: 0;
      top: 100px;
      background-color: #0006;
      /* padding: 0 0; */
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center;
    }

    .el-dropdown {
      display: inline-block;
      position: relative;
      font-size: 20px;
      width: 181px;
      height: 181px;
      color: white;
    }

    .el-dropdown-menu {
      position: absolute;
      top: 0;
      left: 0;
      width: 181px;
      padding: 10px 0;
      margin: 5px 0;
      background: rgba(0, 0, 0, 0.6);
      font-family: Source Han Sans CN, Source Han Sans CN;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .el-dropdown-menu__item {
      font-weight: 500;
      font-size: 20px;
      color: rgba(255, 255, 255, 0.85);
      margin: 5px 0 5px 0;
    }

    .el-dropdown-menu__item:hover {
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0), #0E7AD9) !important;
      font-weight: 500 !important;
      font-size: 20px !important;
      color: rgba(255, 255, 255, 0.85) !important;
    }

    .elDropdownMenuItemActive {
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0), #0E7AD9) !important;
      font-weight: 500 !important;
      font-size: 20px !important;
      color: rgba(255, 255, 255, 0.85) !important;
    }

    .mapClickNew .mapClick_img1 {
      background: url('/static/citybrain/tckz/img/commont/L4.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img2 {
      background: url('/static/citybrain/tckz/img/commont/L5.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img3 {
      background: url('/static/citybrain/tckz/img/commont/L6.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img4 {
      background: url('/static/citybrain/tckz/img/commont/L7.png') no-repeat;
      background-size: 100% 100%;
    }

    ::-webkit-scrollbar {
      width: auto !important;
      height: auto !important;
    }

    .mapPopContainer {
      width: 670px;
      height: auto;
      border-bottom: 2px solid #A5BFFF;
      margin: 15px 0 0 47px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
    }

    .mapPopTitle2 {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #4FD3FF;
      line-height: 48px;
      text-align: left;
    }

    .mapPopContent {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;

    }
  </style>
</head>

<body>
  <div id='app'>
    <div class='mapClickNew'>
      <div style='display: flex; justify-content: space-evenly; align-items: center;width: fit-content'>
        <div v-for="(item,i) in list" :key="i" :class="item.className">
          <el-dropdown trigger="click" @command="changeMap" placement="bottom">
            <div
              style="height: 181px;width: 181px;display: flex;flex-direction: column;justify-content: flex-end;align-items: center"
              class="el-dropdown-link">
              <span
                :style="CurrentMapLayerType==item.name ? 'background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), #0E7AD9)' : ''"
                style="
              position: unset;
              background-color: #0006;
              width: 100%;
              height: 60px;
              line-height: 60px;
              text-align: center;
              font-size: 28px;">{{item.label}}</span>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(childItem,index) in item.childList" :key="index" :command="childItem"
                :class="{elDropdownMenuItemActive:layerId == childItem.id}">{{childItem.name}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>

  <script type="module">
    import Point from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/Point.js";
    import Polygon from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/Polygon.js";
    import Graphic from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/Graphic.js";
    import * as geometryEngine from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/geometryEngine.js";
    var ltwg = new Vue({
      el: '#app',
      data: {
        // 当前行政区选择中的L4网格code
        currentXzqhCode: "",
        //当前选中的网格类型
        CurrentMapLayerType: "",
        //当前选中的图层id
        layerId: "",
        //网格图层服务列表
        list: [
          {
            id: "ltmx-1",
            name: "L4",
            label: "L4(1.85km)",
            className: "mapClick_img1",
            childList: [
              {
                id: "ltmx-1-1",
                name: "金华市域北斗立体单元网格场景",
                label: "金华市域北斗立体单元网格场景",
                type: "L4",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL4_jh/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 120.12879170091291,
                    "y": 29.079907240505413,
                    "z": 876259.8691175161
                  },
                  "heading": 350.10047426907846,
                  "tilt": 0.4396243915572348
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 19070.6162403496
                  },
                  "heading": 359.7691536792452,
                  "tilt": 0.498654497808037
                }
              }
            ]
          },
          {
            id: "ltmx-2",
            name: "L5",
            label: "L5(123.69m)",
            className: "mapClick_img2",
            childList: [
              {
                id: "ltmx-2-1",
                name: "软件园区场景-网络经济中心",
                label: "网络经济中心",
                type: "L5",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL5_wljjzx/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64201483962988,
                    "y": 29.06917199668523,
                    "z": 287.4639280559495
                  },
                  "heading": 349.6479278690715,
                  "tilt": 78.79613543401558
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 970.8321295250207
                  },
                  "heading": 0.4796113715866231,
                  "tilt": 0.49992899975282157
                }
              },
              {
                id: "ltmx-2-2",
                name: "产业园区场景-金华之心",
                label: "金华之心",
                type: "L5",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL5_jhzx/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.6381093122306,
                    "y": 29.068850709114106,
                    "z": 429.9266258114949
                  },
                  "heading": 351.1896709424258,
                  "tilt": 76.40908254648048
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 5255.47137390729
                  },
                  "heading": 354.21033071644484,
                  "tilt": 1.2152264575339278
                }
              },
              {
                id: "ltmx-2-3",
                name: "医院场景-中心医院",
                label: "中心医院",
                type: "L5",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL5_jhzxyy/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.65479542054288,
                    "y": 29.09664188172944,
                    "z": 679.5597031889483
                  },
                  "heading": 1.5954660941723067,
                  "tilt": 67.8152184343289
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 5687.3349986914545
                  },
                  "heading": 0.3870520833848267,
                  "tilt": 0.49957389619080456
                }
              },
              {
                id: "ltmx-2-4",
                name: "商业场景-金华世贸城市广场",
                label: "金华世贸城市广场",
                type: "L5",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL5_smdl/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64774344167269,
                    "y": 29.06738396488418,
                    "z": 1154.197718942538
                  },
                  "heading": 351.6642234442552,
                  "tilt": 63.506261336106995
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 2418.8591800723225
                  },
                  "heading": 351.0360867712431,
                  "tilt": 0.4998211181100887
                }
              },
              {
                id: "ltmx-2-5",
                name: "住宅社区场景-雅苑社区",
                label: "雅苑社区",
                type: "L5",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL5_yyxq/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64313211429734,
                    "y": 29.077283215402347,
                    "z": 504.197718942538
                  },
                  "heading": 351.6634400452235,
                  "tilt": 63.49868469445509
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 2418.8591800723225
                  },
                  "heading": 351.0360867712431,
                  "tilt": 0.4998211181100887
                }
              }
            ]
          },
          {
            id: "ltmx-3",
            name: "L6",
            label: "L6(61.84m)",
            className: "mapClick_img3",
            childList: [
              {
                id: "ltmx-3-1",
                name: "软件园区场景-网络经济中心",
                label: "网络经济中心",
                type: "L6",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL6_wljjzx/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64201483962988,
                    "y": 29.06917199668523,
                    "z": 287.4639280559495
                  },
                  "heading": 349.6479278690715,
                  "tilt": 78.79613543401558
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 1043.0165497604758
                  },
                  "heading": 0.21102299273180822,
                  "tilt": 0.4999277241999839
                }
              },
              {
                id: "ltmx-3-2",
                name: "产业园区场景-金华之心",
                label: "金华之心",
                type: "L6",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL6_jhzx/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.63891033206305,
                    "y": 29.068845382588286,
                    "z": 380.95805413182825
                  },
                  "heading": 349.64587916535845,
                  "tilt": 78.796301782103
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 3423.773941337131
                  },
                  "heading": 0.47961047694813685,
                  "tilt": 0.49974627502294244
                }
              },
              {
                id: "ltmx-3-3",
                name: "医院场景-中心医院",
                label: "中心医院",
                type: "L6",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL6_smdl/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.65493040144462,
                    "y": 29.098022658626757,
                    "z": 747.3164111776277
                  },
                  "heading": 356.570910459727,
                  "tilt": 61.86480171052263
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 1396.8335762042552
                  },
                  "heading": 0.330621623511585,
                  "tilt": 0.49990367585278894
                }
              },
              {
                id: "ltmx-3-4",
                name: "商业场景-金华世贸城市广场",
                label: "金华世贸城市广场",
                type: "L6",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL6_smdl/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64774344167269,
                    "y": 29.06738396488418,
                    "z": 1154.197718942538
                  },
                  "heading": 351.6642234442552,
                  "tilt": 63.506261336106995
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 2418.8591800723225
                  },
                  "heading": 351.0360867712431,
                  "tilt": 0.4998211181100887
                }
              },
              {
                id: "ltmx-3-5",
                name: "住宅社区场景-雅苑社区",
                label: "雅苑社区",
                type: "L6",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL6_yyxq/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64313211429734,
                    "y": 29.077283215402347,
                    "z": 504.197718942538
                  },
                  "heading": 351.6634400452235,
                  "tilt": 63.49868469445509
                },
                clickCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": "",
                    "y": "",
                    "z": 2418.8591800723225
                  },
                  "heading": 351.0360867712431,
                  "tilt": 0.4998211181100887
                }
              }
            ]
          },
          {
            id: "ltmx-4",
            name: "L7",
            label: "L7(7.73m)",
            className: "mapClick_img4",
            childList: [
              {
                id: "ltmx-4-1",
                name: "软件园区场景-网络经济中心",
                label: "网络经济中心",
                type: "L7",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL7_wljjzx/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64197173525476,
                    "y": 29.072522550894,
                    "z": 180.71130117587745
                  },
                  "heading": 342.8440365467332,
                  "tilt": 80.3909719777578
                }
              },
              {
                id: "ltmx-4-2",
                name: "产业园区场景-金华之心",
                label: "金华之心",
                type: "L7",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL7_jhzx/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.63773905546152,
                    "y": 29.071546451713964,
                    "z": 274.0226293988526
                  },
                  "heading": 348.8172549934383,
                  "tilt": 78.71999097154114
                }
              },
              {
                id: "ltmx-4-3",
                name: "医院场景-中心医院",
                label: "中心医院",
                type: "L7",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL7_jhzxyy/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64866637882155,
                    "y": 29.117206701893437,
                    "z": 875.9521861216053
                  },
                  "heading": 151.87076386840656,
                  "tilt": 53.98736183938371
                }
              },
              {
                id: "ltmx-4-4",
                name: "商业场景-金华世贸城市广场",
                label: "金华世贸城市广场",
                type: "L7",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL7_smdl/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64377330580486,
                    "y": 29.079985695788118,
                    "z": 267.1001231605187
                  },
                  "heading": 8.224355064491375,
                  "tilt": 70.14657205766309
                }
              },
              {
                id: "ltmx-4-5",
                name: "住宅社区场景-雅苑社区",
                label: "雅苑社区",
                type: "L7",
                url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL7_yyxq/SceneServer",
                loadCamera: {
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 119.64313211429734,
                    "y": 29.077283215402347,
                    "z": 504.197718942538
                  },
                  "heading": 351.6634400452235,
                  "tilt": 63.49868469445509
                }
              }
            ]
          }
        ],
        //点位类型分类表
        fCodeList: [
          {
            label: "影剧院",
            codes: ['180202'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-影剧院.png",
            unit: "家"
          },
          {
            label: "公园广场",
            codes: ['180303', '180402', '180404', '180405', '180307'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-公园广场.png",
            unit: "个"
          },
          {
            label: "住宅区",
            codes: ['120101'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-住宅区.png",
            unit: "个"
          },
          {
            label: "博物馆",
            codes: ['160204'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/szwh-博物馆.png",
            unit: "家"
          },
          {
            label: "学校",
            codes: ['160101', '160102', '160103', '160104', '160105', '160106', '160107'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-学校.png",
            unit: "家"
          },
          {
            label: "医院",
            codes: ['170101', '170102', '170103', '170104', '170105'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-医院.png",
            unit: "家"
          },
          {
            label: "公交车站",
            codes: ['230105'],
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/rckz-公交车站.png",
            unit: "个"
          },
          {
            label: "视频监控",
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-load3.png",
            unit: "个",
          },
          {
            label: "工业企业",
            icon: "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/工业企业信息.png",
            unit: "个",
          },
        ],
        isScrolling: false,
        scrollInterval: null,
        label: ""
      },
      mounted() {
        this.creditAuth()
        this.clearAllLayers()
        //如果之前选中网格图层过则读取缓存加载图层
        // if (localStorage.getItem('layerId')) {
        //   this.CurrentMapLayerType = localStorage.getItem('mapType3D')
        //   // // 避免重复遍历，提前过滤并处理可能的空数组结果
        //   const filteredItemList = this.list.find(item => item.name === this.CurrentMapLayerType).childList;
        //   const childItem = filteredItemList.find(item => item.id === localStorage.getItem('layerId'))
        //   this.changeMap(childItem)
        // } else {
        //   //清除选中效果
        //   this.CurrentMapLayerType = ''
        // }

        this.CurrentMapLayerType = ''

        top.window.startScrolling = this.startScrolling.bind(this);
        top.window.stopScrolling = this.stopScrolling.bind(this);
        top.window.loadPoint = this.loadPoint.bind(this);
        top.window.resetWg = this.resetWg.bind(this);
        top.window.clearPointLayer = this.clearPointLayer.bind(this);
        top.window.closePointPop = this.closePointPop.bind(this);
        top.window.showL7CompanyDetail = this.showL7CompanyDetail.bind(this);
        // 监听wggl中发来的消息 过滤L4网格
        window.addEventListener('message', function (event) {
          if (event.data) {
            const data = JSON.parse(event.data);
            if (data.hasOwnProperty('name') && data.hasOwnProperty('type') && data.name === 'main_changeMap3D' && data.type === 'closeIframe') {
              let data = JSON.stringify({
                type: 'closeIframe',
                name: 'wwgl'
              })
              top.postMessage(data, '*')
              top.closeIframeByNames(['wwgl'])
            }
            if (data.hasOwnProperty('type') && data.type === 'updateL4wgLayer') {
              if (data.data.features === "全市") {
                top.L4wgLayer.definitionExpression = '';
                top.mapUtil.mapview.goTo({
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": 120.12879170091291,
                    "y": 29.079907240505413,
                    "z": 876259.8691175161
                  },
                  "heading": 350.10047426907846,
                  "tilt": 0.4396243915572348
                })
              } else {
                // 更新L4wgLayer
                // 循环遍历features 拿到每个里面的attributes.code 然后拼成'code=features[0].attributes.code AND code=features[1].attributes.code'这样
                this.currentXzqhCode = data.data.features.map(feature => `code='${feature.attributes.code}'`).join(' OR ');
                localStorage.setItem('currentL4Code', JSON.stringify(data.data.features))
                localStorage.setItem('currentXzqhCode', this.currentXzqhCode)
                if (top.L4wgLayer) {
                  top.L4wgLayer.definitionExpression = this.currentXzqhCode;
                }
                const polygon = turf.polygon([data.data.extents.coordinates?.[0] || data.data.extents.rings?.[0]]);
                // 计算中心点
                const center = turf.center(polygon);
                top.mapUtil.mapview.goTo({
                  "position": {
                    "spatialReference": {
                      "latestWkid": 4490,
                      "wkid": 4490
                    },
                    "x": center.geometry.coordinates[0],
                    "y": center.geometry.coordinates[1],
                    "z": 376259.8691175161
                  },
                  "heading": 350.10047426907846,
                  "tilt": 0.4396243915572348
                },)
                console.log('收到查询结果:', data.features);
              }
            }
          }
        });
      },
      methods: {
        //关闭iframe
        closeIframe() {
          top.frames['indexMapIcon'].mainIconVm.show3Dkuai = false
          let data = JSON.stringify({
            type: 'closeIframe',
            name: 'main_changeMap3D'
          })
          window.parent.postMessage(data, '*')
        },
        //切换网格图层
        changeMap(item) {
          this.clearAllLayers()
          //判断是否重复点击 是:清除图层(clearAllLayers) 否:加载图层
          if (this.layerId == item.id) {
            this.layerId = ''
            this.CurrentMapLayerType = ''
            localStorage.removeItem("mapType3D")
            localStorage.removeItem("layerId")
            if (item.type == 'L4' && item.name == '金华市域北斗立体单元网格场景') {
              this.openOrCloseQuhuaL4()
            }
          } else {
            this.CurrentMapLayerType = item.type
            localStorage.setItem("mapType3D", item.type)
            localStorage.setItem("layerId", item.id)
            this.loadLayer(item)
            if (this.layerId == "") {
              if (item.type == 'L4' && item.name == '金华市域北斗立体单元网格场景') {
                this.showQuhuaL4 = false
                this.openOrCloseQuhuaL4()
              }
            } else {
              if (this.showQuhuaL4) {
                this.openOrCloseQuhuaL4()
              }
              if (item.type == 'L4' && item.name == '金华市域北斗立体单元网格场景') {
                this.openOrCloseQuhuaL4()
              }
            }
            this.layerId = item.id
          }
        },
        // 打开L4区划选择框
        openOrCloseQuhuaL4() {
          let that = this
          that.showQuhuaL4 = !that.showQuhuaL4
          let moveLeft = '4815px'
          let moveTop = '800px'
          if (that.showQuhuaL4) {
            let iframe1 = {
              type: 'openIframe',
              name: 'wwgl',
              src: '/static/citybrain/tckz/wggl.html', //
              width: '600px',
              height: '950px',
              left: moveLeft,
              top: '550px',
              zIndex: 990,
              argument: {},
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else {
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'wwgl'
            })
            top.postMessage(data, '*')
            top.closeIframeByNames(['wwgl'])
          }
        },
        //打开L7面板
        OpenL7View(obj) {
          top.postMessage(JSON.stringify({
            type: 'openIframe',
            name: 'L7ltwgDialog',
            src: '/static/citybrain/csdn/commont/ltwgDialogL7.html',
            left: '4926px',
            top: '552px',
            width: '500px',
            height: '1155px',
            zIndex: '999',
            argument: {
              result: obj
            }
          }), '*')
        },
        //加载网格图层
        loadLayer(obj) {
          top.window.view.goTo(obj.loadCamera);
          top.mapUtil.loadTileLayer({
            type: "scene",
            layerid: "ltwg" + obj.id,
            url: obj.url,
            id: String(obj.id),
            onclick: (e) => this.layerClick(e, obj),
            layerCallback: (layer) => {
              // 请注意 因为L4网格需求比较多 涉及iframe多 所以这里索性直接挂在top上 方便使用
              if (this.CurrentMapLayerType == 'L4') {
                top.L4wgLayer = layer
              }
              layer.outFields = ["*"]
            }
          })
          this.label = obj.label
          if (this.CurrentMapLayerType == 'L7') {
            //打开L7右侧面板
            this.OpenL7View(obj)
            //清除之前的L7网格高亮
            this.clearL7LayerHighlight()
          }
        },
        //图层点击事件
        layerClick(e, layerObj) {
          let obj = e[0].mapPoint
          //保存当前点击网格以便后续恢复弹窗展示调用
          localStorage.setItem('currentWg', JSON.stringify(e))
          //清除地图点位
          this.clearPointLayer()
          //添加视角飞行
          if (layerObj.clickCamera) {
            let camera = layerObj.clickCamera
            camera.position["x"] = obj.x;
            camera.position["y"] = obj.y;
            top.window.view.goTo(camera)
          }
          //恢复网格镂空
          this.resetWg()
          //显示详情弹窗
          this.showDetailDialog(e)


          //L7级点机某个网格设置高亮
          if (this.CurrentMapLayerType == 'L7') {
            this.clearL7LayerHighlight()
            this.setL7LayerHighlight(e[0].graphic)
          }
        },
        //L7级点击某个网格设置高亮
        setL7LayerHighlight(graphic) {
          top.mapUtil.mapview.whenLayerView(graphic.layer).then((layerView) => {
            this.highlight = layerView.highlight(graphic)
          })
        },
        //L7级清除高亮
        clearL7LayerHighlight() {
          if (this.highlight) {
            this.highlight.remove(); // 移除高亮
            this.highlight = null; // 清空高亮对象引用
          }
        },
        //立体网格详情弹窗
        async showDetailDialog(e) {
          let that = this
          let clickObj = e[0];
          console.log(clickObj, "layerClick");

          //网格级别
          let wg = this.CurrentMapLayerType
          //网格编码
          let code = clickObj.graphic.attributes.code
          //实时人数(目前只有L4级别有实时人数) （设置仅L4调用）
          let peopleNumber = this.CurrentMapLayerType == "L4" ? await this.getRealTimePeopleCount(code) : "暂无数据"
          //获取归类后的点位数据以及归类后的模板字符串(目前只有L4级别有poi)（设置仅L4调用,非L4设置默认值[]和""避免资源消耗）
          let formatPointObj = this.CurrentMapLayerType == "L4" ? await this.formatPointArrStr(clickObj) : { formatArr: [], listContainers: `` }
          //归类后的点位数据
          let formatArr = formatPointObj.formatArr
          //归类后的模板字符串
          let listContainers = formatPointObj.listContainers
          //企业信息(目前只有L7级别有企业信息)
          let danwei = clickObj.graphic.attributes.danwei ? this.formatQiyeStr(clickObj.graphic.attributes.danwei.replace(/，/g, ',')) : "暂无数据"
          //创建地图pop弹窗
          let str = this.generateContent(formatArr, wg, code, peopleNumber, danwei, listContainers)
          let objData = {
            layerid: e[0].layer.title,
            position: [e[0].mapPoint.x, e[0].mapPoint.y],
            offset: [-250, 150],
            closeButton: true,
            content: str,
          }
          if (this.CurrentMapLayerType == "L4") {
            //将该网格设置为镂空
            let excludeCodes = JSON.parse(localStorage.getItem('currentL4Code'))
              .filter(feature => feature.attributes.code !== code)
              .map(feature => `code='${feature.attributes.code}'`)
              .join(' OR ');
            top.L4wgLayer.definitionExpression = excludeCodes;
            let result = {
              wg,
              code,
              peopleNumber,
              formatPointObj,
              clickObj
            }
            top.postMessage(JSON.stringify({
              type: 'openIframe',
              name: 'L4ltwgDialog',
              src: '/static/citybrain/csdn/commont/ltwgDialog.html',
              left: '4926px',
              top: '552px',
              width: '500px',
              height: '1155px',
              zIndex: '999',
              argument: {
                result: result
              }
            }), '*')
          } else {
            top.mapUtil._createPopup(objData)
          }
        },
        //生成网格弹窗模板字符串
        generateContent(arr, wg, code, peopleNumber, danwei, listItems) {
          //根据图层级别显示不同内容
          let L7Text = this.label == "中心医院" ? "科室信息" : this.label == "金华世贸城市广场" ? "商铺信息" : this.label == "雅苑社区" ? "居民信息" : "企业信息"
          switch (this.CurrentMapLayerType) {
            case "L4":
              //判断数据量是否超过一页出现左右页滑动切换箭头
              if (arr.length > 1) {
                return `
          <div id="ltwgDialog" style="
            width: 868px;
            height: 1170px;
            background: url('/img/ltwgBackground.png') no-repeat;
            background-size: 100% 100%;
            overflow: hidden;
          ">
            <div style="margin: 65px 0 0 53px;">
              <div style="
                width: 762px;
                height: 102px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
                <div style="
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 500;
                  font-size: 48px;
                  color: #FFFFFF;
                  line-height: 48px;
                  text-align: left;
                  margin-left: 20px;
                ">北斗立体单元网格</div>
                <div style="
                  width: 48px;
                  height: 48px;
                  background: url('/img/ltwgClose.png') no-repeat;
                  background-size: cover;
                  margin-right: 20px;
                cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.style.display = 'none';window.clearPointLayer();window.resetWg();
                "></div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">网格级别：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${wg}</div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">全球编码：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${code}</div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">实时人数：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${peopleNumber}</div>
              </div>
              <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;
      margin: 31px 0 0 47px;">数据集装箱</div>
              <div style="display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      margin-left: -53px">
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #4FD3FF;
      line-height: 48px;
      text-align: center;">POI</div>
                <div style="display: flex;justify-content: space-around;align-items: center;margin-top: 30px;z-index: 999">
                    <img src="/img/arrowLeft.png" style="width: 36px;
                height: 73px;
                margin-right: 25px;
                cursor: pointer;" onmousedown="window.startScrolling('left')" onmouseup="window.stopScrolling()" onmouseleave="window.stopScrolling()">
                    <div id='scrollContainer' style="width: 500px;
                    overflow-x: scroll;
                    display: flex;
                    justify-content: space-evenly;
                    align-items: center;">${listItems}</div>
                    <img src="/img/arrowRight.png" style="width: 36px;
                height: 73px;
                margin-left: 25px;
                cursor: pointer;" onmousedown="window.startScrolling('right')" onmouseup="window.stopScrolling()" onmouseleave="window.stopScrolling()">
                </div>
                <div style="
                  background: url('/img/poiBox.png') no-repeat;
                  background-size: 100% 100%;
                  width: 766px;
                  height: 328px;
                  position: absolute;
                  top: 680px;"></div>
              </div>
            </div>
          </div>
        `
              } else {
                return `
          <div id="ltwgDialog" style="
            width: 868px;
            height: 1170px;
            background: url('/img/ltwgBackground.png') no-repeat;
            background-size: 100% 100%;
            overflow: hidden;
          ">
            <div style="margin: 65px 0 0 53px;">
              <div style="
                width: 762px;
                height: 102px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
                <div style="
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 500;
                  font-size: 48px;
                  color: #FFFFFF;
                  line-height: 48px;
                  text-align: left;
                  margin-left: 20px;
                ">北斗立体单元网格</div>
                <div style="
                  width: 48px;
                  height: 48px;
                  background: url('/img/ltwgClose.png') no-repeat;
                  background-size: cover;
                  margin-right: 20px;
                cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.style.display = 'none';window.clearPointLayer();window.resetWg();
                "></div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">网格级别：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${wg}</div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">全球编码：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${code}</div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">实时人数：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${peopleNumber}</div>
              </div>
              <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;
      margin: 31px 0 0 47px;">数据集装箱</div>
              <div style="display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      margin-left: -53px">
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #4FD3FF;
      line-height: 48px;
      text-align: center;">POI</div>
                <div style="display: flex;justify-content: space-around;align-items: center;margin-top: 30px;z-index: 999">
                    <div id='scrollContainer' style="width: 500px;
                    overflow-x: scroll;
                    display: flex;
                    justify-content: space-evenly;
                    align-items: center;">${listItems}</div>
                </div>
                <div style="
                  background: url('/img/poiBox.png') no-repeat;
                  background-size: 100% 100%;
                  width: 766px;
                  height: 328px;
                  position: absolute;
                  top: 680px;"></div>
              </div>
            </div>
          </div>
        `
              }
            case "L5":
              return `
          <div id="ltwgDialog" style="
            width: 868px;
            height: 1170px;
            background: url('/img/ltwgBackground.png') no-repeat;
            background-size: 100% 100%;
            overflow: hidden;
          ">
            <div style="margin: 65px 0 0 53px;">
              <div style="
                width: 762px;
                height: 102px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
                <div style="
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 500;
                  font-size: 48px;
                  color: #FFFFFF;
                  line-height: 48px;
                  text-align: left;
                  margin-left: 20px;
                ">北斗立体单元网格</div>
                <div style="
                  width: 48px;
                  height: 48px;
                  background: url('/img/ltwgClose.png') no-repeat;
                  background-size: cover;
                  margin-right: 20px;
                cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.style.display = 'none';window.clearPointLayer();window.resetWg();
                "></div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">网格级别：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${wg}</div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">全球编码：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${code}</div>
              </div>
            </div>
          </div>
        `
            case "L6":
              return `
          <div id="ltwgDialog" style="
            width: 868px;
            height: 1170px;
            background: url('/img/ltwgBackground.png') no-repeat;
            background-size: 100% 100%;
            overflow: hidden;
          ">
            <div style="margin: 65px 0 0 53px;">
              <div style="
                width: 762px;
                height: 102px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
                <div style="
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 500;
                  font-size: 48px;
                  color: #FFFFFF;
                  line-height: 48px;
                  text-align: left;
                  margin-left: 20px;
                ">北斗立体单元网格</div>
                <div style="
                  width: 48px;
                  height: 48px;
                  background: url('/img/ltwgClose.png') no-repeat;
                  background-size: cover;
                  margin-right: 20px;
                cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.style.display = 'none';window.clearPointLayer();window.resetWg();
                "></div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">网格级别：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${wg}</div>
              </div>
              <div style="
                 width: 670px;
                  height: 68px;
                  border-bottom: 2px solid #A5BFFF;
                  margin: 15px 0 0 47px;
                  display: flex;
                  justify-content: flex-start;
                  align-items: center;
                ">
                <div style="
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        font-size: 32px;
                        color: #4FD3FF;
                        line-height: 48px;
                        text-align: left;
                      ">全球编码：</div>
                <div style="font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 48px;
      text-align: left;">${code}</div>
              </div>
            </div>
          </div>
        `
            case "L7":
              return `
          <div id="ltwgDialog" style="
            width: 868px;
            height: 1170px;
            background: url('/img/ltwgBg2.png') no-repeat;
            background-size: 100% 100%;
            overflow: hidden;
          ">
            <div style="margin: 65px 0 0 53px;">
              <div style="
                width: 762px;
                height: 102px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
                <div style="
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 500;
                  font-size: 48px;
                  color: #FFFFFF;
                  line-height: 48px;
                  text-align: left;
                  margin-left: 20px;
                ">北斗立体单元网格</div>
                <div style="
                  width: 48px;
                  height: 48px;
                  background: url('/img/ltwgClose.png') no-repeat;
                  background-size: cover;
                  margin-right: 20px;
                cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.style.display = 'none';window.clearPointLayer();window.resetWg();
                "></div>
              </div>
              <div style="overflow-y: scroll;height: 893px;">
                  <div style="
                     width: 670px;
                      height: 68px;
                      border-bottom: 2px solid #A5BFFF;
                      margin: 15px 0 0 47px;
                      display: flex;
                      justify-content: flex-start;
                      align-items: center;
                    ">
                    <div style="
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            font-size: 32px;
                            color: #4FD3FF;
                            line-height: 48px;
                            text-align: left;
                          ">网格级别：</div>
                    <div style="font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 48px;
          text-align: left;">${wg}</div>
                  </div>
                  <div style="
                     width: 670px;
                      height: 68px;
                      border-bottom: 2px solid #A5BFFF;
                      margin: 15px 0 0 47px;
                      display: flex;
                      justify-content: flex-start;
                      align-items: center;
                    ">
                    <div style="
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            font-size: 32px;
                            color: #4FD3FF;
                            line-height: 48px;
                            text-align: left;
                          ">全球编码：</div>
                    <div style="font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 48px;
          text-align: left;">${code}</div>
                  </div>
                  <div style="
                         width: 670px;
                          height: auto;
                          border-bottom: 2px solid #A5BFFF;
                          margin: 15px 0 0 47px;
                          display: flex;
                          justify-content: flex-start;
                          align-items: flex-start;
                          flex-wrap: wrap;
                          flex-direction: column;
                    ">
                    <div style="
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            font-size: 32px;
                            color: #4FD3FF;
                            line-height: 48px;
                            text-align: left;
                          ">${L7Text}：</div>
                    <div style="font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 48px;
          text-align: left;">${danwei}</div>
                  </div>
                </div>
              </div>
          </div>
        `
          }
        },
        //恢复镂空网格显示
        resetWg() {
          if (top.L4wgLayer) {
            top.L4wgLayer.definitionExpression = localStorage.getItem('currentXzqhCode')
          } else {
            top.mapUtil.layers[`ltwg${localStorage.getItem('layerId')}`].definitionExpression = ""
          }
        },
        //清除所有图层
        clearAllLayers() {
          // 使用常量定义正则表达式，以提高代码的可维护性
          const LayerRegex = /ltwg/;

          // 获取所有层的键，并筛选出匹配 MapWarningList 的层
          const layerKeys = Object.keys(top.mapUtil.layers).filter(item => LayerRegex.test(item));

          // 安全地移除匹配的层
          try {
            if (layerKeys && layerKeys.length) {
              top.mapUtil.removeAllLayers(layerKeys);
            } else {
              return
            }
          } catch (error) {
            console.error('Failed to remove ltwgLayers:', error);
          }
        },
        //加载点位图层
        loadPoint(obj, wg) {
          console.log(obj, wg, "point");
          let layerId = `ltwgPoint${obj.id}`
          let PointData = []
          obj.points.forEach(ele => {
            if (ele.attributes.point_x && ele.attributes.point_y) {
              let marker = {
                lng: ele.attributes.point_x,
                lat: ele.attributes.point_y,
                data: ele
              }
              PointData.push(marker)
            }
          })
          if (top.mapUtil.layers[layerId]) {
            this.clearPointLayer(layerId)
          } else {
            top.mapUtil.loadPointLayer({
              data: PointData,
              layerid: layerId,
              iconcfg: { image: `${baseURL.url + obj.icon}`, iconSize: 1 },
              cluster: true, //是否聚合
              onclick: (e) => this.showPointPop(e, layerId),
              popcfg: {
                offset: [50, -100],
                show: false,
              },
            })
          }
        },
        //创建点位弹窗
        createPops(arr) {
          let countStr = ''
          for (let index = 0; index < arr.length; index++) {
            countStr += `<p style="
                      width: 650px;
                      font-size: 30px;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                    ">
                    ${arr[index].name} :<span style="color: #ffff" title="${arr[index].value}">${arr[index].value}</span>
                  </p>`
          }
          let str = `<div
              style="
                position: relative;
                background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                background-size: 100% 100%;
                width: max-content;
                min-height: 250px;
              ">
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-end;
                  padding-bottom: 10px;
                  margin: 0 20px;
                  border-bottom: 1px solid;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                  padding-left: 20px;
                ">
                <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">${arr[0].value}</h2>
                <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none';window.closePointPop()">
                  <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
                </span>
              </nav>
              <header
                style="
                  padding-bottom: 15%;
                  margin: 10px 20px 0;
                  display: flex;
                  justify-content: space-between;
                  font-size: 25px;
                ">
                <div style="margin-left: 40px;color:#fff">${countStr}</div>
              </header>
            </div>`
          return str
        },
        //点位弹窗打开
        showPointPop(e, id) {
          console.log(e, id, "point");
          let key = [{ name: "名称", key: "shortname" }, { name: "全称", key: "name" }, { name: "地址", key: "address" }];
          let data = e.data.attributes
          let arr = key.map((a, i) => {
            return { name: a.name, value: data[a.key] }
          })
          let str = this.createPops(arr)
          let objData = {
            layerid: id,
            position: [data.point_x, data.point_y],
            popup: {
              offset: [50, -50],
              closeButton: true,
            },
            content: str,
          }

          top.mapUtil._createPopup(objData)
        },
        //关闭点位弹窗后处理操作
        closePointPop() {
          //恢复网格弹窗显示
          this.showDetailDialog(JSON.parse(localStorage.getItem('currentWg')))
        },
        //清除点位
        clearPointLayer(layerid) {
          if (layerid) {
            top.mapUtil.removeLayer(layerid)
          } else {
            // 清除所有点位图层
            const LayerRegex = /ltwgPoint/;
            const layerKeys = Object.keys(top.mapUtil.layers).filter(item => LayerRegex.test(item));
            try {
              if (layerKeys && layerKeys.length) {
                top.mapUtil.removeAllLayers(layerKeys);
              } else {
                return
              }
            } catch (error) {
              console.error('Failed to remove ltwgPointLayers:', error);
            }
          }
        },
        //通用飞行方法
        FlyTo(e) {
          top.mapUtil.flyTo({
            destination: [e.esX, e.esY],
            zoom: e.zoom,
            offset: e.offest || []
          })
        },
        //获取网格对应点位数据
        getPoint(e) {
          return new Promise((resolve, reject) => {
            const layer = new FeatureLayer({
              url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/poi_sbj_bdL4/FeatureServer'
            });
            const query = {
              where: `code = '${e.graphic.attributes.code}'`,
              returnGeometry: true,
              outFields: ["*"],
            };
            layer.queryFeatures(query)
              .then(function (results) {
                // 成功时解析 Promise
                resolve(results.features);
              })
              .catch(function (err) {
                // 失败时拒绝 Promise
                reject(err);
              });
          })
        },
        // 获取网格视频监控点位               //由于数据接口不支持范围查询返回 所以这里继续以圆的形式查询 并再做数据筛选
        async getWgVideoPoints(e) {
          let code = e.graphic.attributes.code
          const { coordinates, data } = await this.getCoordinates(code)
          // 创建一个turf多边形
          const polygon = turf.polygon([coordinates]);
          // 计算边界框
          const bbox = turf.bbox(polygon);

          // 计算中心点
          const center = turf.center(polygon);
          const centerCoordinates = center.geometry.coordinates;

          // 计算近似半径（取边界框对角线的一半）
          const corner1 = turf.point([bbox[0], bbox[1]]);
          const corner2 = turf.point([bbox[2], bbox[3]]);
          const radius = turf.distance(corner1, corner2) * 1000 / 2; // 转换为米
          console.log('圆心坐标:', centerCoordinates);
          console.log('半径(米):', radius);

          // 使用圆心坐标和半径进行后续操作
          const point = `${centerCoordinates[0]},${centerCoordinates[1]}`;
          const distance = (radius / 1000).toFixed(3); // 转换为千米并保留3位小数
          const that = this;
          return new Promise((resolve, reject) => {
            axios({
              method: 'get',
              url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
              params: {
                type: 'zbjk',
                distance: distance,
                point: point,
              },
            }).then(function (response) {
              let dataArr = [];
              response.data.data.zbjk.pointData.forEach((each) => {
                let test = each.point.split(",").map(Number);
                let point = new Point({
                  x: test[0],
                  y: test[1],
                  spatialReference: { wkid: 4490 }
                });
                let cameraType = each.isHighAltitude === 1 ? '高点' : each.cameraType;
                let obj = {
                  cameraType: cameraType,
                  data: each.data,
                  is_online: each.is_online,
                  lat: each.lat,
                  lng: each.lng,
                  pointType: that.getPointType(each.is_online, cameraType, each.data.warningType),
                };
                // 判断点是否在网格框内
                // 创建 turf 的 Point 对象
                let turfPoint = turf.point(test);
                const pointInPolygon = turf.booleanPointInPolygon(turfPoint, polygon);
                if (pointInPolygon) {
                  dataArr.push(obj);
                }
              });

              resolve(dataArr); // 使用resolve返回dataArr
            }).catch(error => {
              reject(error); // 如果请求失败，使用reject传递错误
            });
          });
        },
        //获取网格对应工业企业点位数据
        async getGongyePoint(e) {
          let code = e.graphic.attributes.code
          const { coordinates, data } = await this.getCoordinates(code)
          return new Promise((resolve, reject) => {
            const layer = new FeatureLayer({
              url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/GYQYXX_NEW/FeatureServer'
            });
            const polygon = new Polygon({
              rings: coordinates,
              spatialReference: { wkid: 4490 }
            });
            const query = {
              geometry: polygon,
              returnGeometry: true,
              spatialRelationship: 'intersects',
              outFields: ["*"],
            };
            layer.queryFeatures(query)
              .then(function (results) {
                // 成功时解析 Promise
                resolve(results.features);
              })
              .catch(function (err) {
                // 失败时拒绝 Promise
                reject(err);
              });
          })
        },
        getPointType(is_online, cameraType, warningType) {
          if (warningType && warningType == 1) {
            return '告警视频'
          } else {
            let arr = is_online + '-' + cameraType
            let obj = {
              枪机在线: '在线-枪机',
              枪机离线: '离线-枪机',
              球机在线: '在线-球机',
              球机离线: '离线-球机',
              半球机在线: '在线-半球机',
              半球机离线: '离线-半球机',
              高点在线: '在线-高点',
              高点离线: '离线-高点',
              未知在线: '在线-未知',
              未知离线: '离线-未知',
              铁塔在线: '在线-铁塔',
              铁塔离线: '离线-铁塔'
            }
            for (var key in obj) {
              if (obj[key] == arr) {
                return key
              }
            }
          }
        },
        //将点位数据进行归类并返回归类后的模板字符串内容
        async formatPointArrStr(clickObj) {
          //整理不同类型点位数据
          let arr = await this.getPoint(clickObj)
          //视频点位单独处理
          let videoArr = await this.getWgVideoPoints(clickObj)
          // 工业企业信息
          let GongyeArr = await this.getGongyePoint(clickObj)
          let formatArr = [];
          //将点位根据字典表中的code进行归类
          formatArr = this.fCodeList.map(item => ({
            name: item.label,
            points: item.label == "视频监控" ? videoArr : item.label == "工业企业" ? GongyeArr : arr.filter(obj => item.codes.includes(obj.attributes.fcode)),
            icon: item.icon,
            unit: item.unit,
            pointType: item.label == "视频监控" ? 'video' : item.label == "工业企业" ? 'gongye' : 'common'
          })).filter(item => item.points.length > 0)
          //归类后将数组分割为五个元素一组的二维数组
          formatArr = this.chunkArray(formatArr, 5)
          console.log(formatArr, "featureFormat");
          //弹窗内容展示赋值 listContainers(poi L4显示)
          //listContainers(分割后的一页,一页5个poi类型点位)
          let listContainers = formatArr.map((list, i) => {
            //listItems(对应listContainers页中的某个poi类型集合)
            let listItems = list.map(item => `
            <div class="poi-item"
                  style="cursor: pointer;
                  flex-shrink: 0;
                  width: 130px;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;" onclick="window.loadPoint(${JSON.stringify(item).replace(/\"/g, "'")},${JSON.stringify(clickObj).replace(/\"/g, "'")})">
              <img src="${item.icon}" alt="" style="width: 100px;height: 100px;">
              <div style="font-family: Source Han Sans CN, Source Han Sans CN;
                          font-weight: 400;
                          font-size: 32px;
                          color: #FFFFFF;
                          line-height: 48px;
                          text-align: center;
                          ">${item.name}</div>
            </div>`).join('');
            return `
            <div class="container-item" style="width: 100%;
                  flex-shrink: 0;
                  height: 320px;
                  display: flex;
                  justify-content: space-evenly;
                  align-items: center;
                  flex-wrap: wrap;">
              ${listItems}
            </div>
          `
          }).join('');
          return { listContainers, formatArr };
        },
        //将企业信息格式化后塞入弹窗使其可点击展示企业详细信息
        formatQiyeStr(str) {
          let listItems = str.split(",").map(item => `
            <div onclick='window.showL7CompanyDetail(${JSON.stringify(item)})' style="cursor: pointer">
              ${item}
            </div>`).join('');
          return listItems;
        },
        //获取医师清单模板字符串
        getDoctorListStr(str) {
          return $api('/zzyx_ksxq', { ks: str }).then(res => {
            let listItems = ""
            if (res.length > 0 && res[0].yss) {
              listItems = res.map(item => `
            <div>
              ${item.yss}
            </div>`).join('');
            } else {
              listItems = "暂无医师信息"
            }
            return listItems;
          }).catch(error => {
            console.error('请求医生列表失败:', error);
            throw '请求医生列表失败';
          });
        },
        //获取科室详情模板字符串
        getKsDetailStr(str) {
          return $api('/zzyx_ksxq', { ks: str }).then(res => {
            let listItems = ""
            if (res.length > 0 && res[0].jj) {
              listItems = res.map(item => `
            <div>
              ${item.jj}
            </div>`).join('');
            } else {
              listItems = "暂无科室信息"
            }
            return listItems;
          }).catch(error => {
            console.error('请求科室信息失败:', error);
            throw '请求科室信息失败';
          });
        },
        //获取床位数模板字符串
        getKsBedNumberStr(str) {
          return $api('/zzyx_ksxq', { ks: str }).then(res => {
            let listItems = ""
            if (res.length > 0 && res[0].cws) {
              listItems = res.map(item => `
            <div>
              ${item.cws}
            </div>`).join('');
            } else {
              listItems = "暂无床位个数信息"
            }
            return listItems;
          }).catch(error => {
            console.error('请求床位个数失败:', error);
            throw '请求床位个数失败';
          });
        },
        //将医生信息添加至弹窗
        appendDoctorListStr(CompanyName) {
          //医师信息模板字符串
          let doctorListStr = ""
          this.getDoctorListStr(CompanyName).then(res => {
            doctorListStr = res

            //如果医师信息元素已添加过则改变值
            if (top.$("#infoMessage").length > 0) {
              top.$("#infoMessage").children().last().html(doctorListStr)
            } else {
              // 使用 jQuery 创建新元素
              let newElement = $(`  <div id="infoMessage">
                <div class="mapPopTitle2">医师信息：</div>
                <div class="mapPopContent">${doctorListStr}</div>
              </div>`);

              // 设置样式
              newElement.css({
                'width': '670px',
                'height': 'auto',
                'border-bottom': '2px solid #A5BFFF',
                'margin': '15px 0 0 47px',
                'display': 'flex',
                'justify-content': 'flex-start',
                'align-items': 'flex-start',
                'flex-wrap': 'wrap',
                'flex-direction': 'column',
              });
              newElement.find('.mapPopTitle2').css({
                'font-family': 'Source Han Sans CN, Source Han Sans CN',
                'font-weight': '400',
                'font-size': '32px',
                'color': '#4FD3FF',
                'line-height': '48px',
                'text-align': 'left',
              });
              newElement.find('.mapPopContent').css({
                'font-family': 'Source Han Sans CN, Source Han Sans CN',
                'font-weight': '400',
                'font-size': '32px',
                'color': '#FFFFFF',
                'line-height': '48px',
                'text-align': 'left',
              });
              top.$('#ltwgDialog').children().children().last().append(newElement);
            }
          })
        },
        //将科室详情添加至弹窗
        appendKsDetailStr(CompanyName) {
          //科室详情模板字符串
          let KsDetailStr = ""
          this.getKsDetailStr(CompanyName).then(res => {
            KsDetailStr = res

            //如果科室详情元素已添加过则改变值
            if (top.$("#infoMessage2").length > 0) {
              top.$("#infoMessage2").children().last().html(KsDetailStr)
            } else {
              // 使用 jQuery 创建新元素
              let newElement = $(`  <div id="infoMessage2">
                <div class="mapPopTitle2">科室详情：</div>
                <div class="mapPopContent">${KsDetailStr}</div>
              </div>`);

              // 设置样式
              newElement.css({
                'width': '670px',
                'height': 'auto',
                'border-bottom': '2px solid #A5BFFF',
                'margin': '15px 0 0 47px',
                'display': 'flex',
                'justify-content': 'flex-start',
                'align-items': 'flex-start',
                'flex-wrap': 'wrap',
                'flex-direction': 'column',
              });
              newElement.find('.mapPopTitle2').css({
                'font-family': 'Source Han Sans CN, Source Han Sans CN',
                'font-weight': '400',
                'font-size': '32px',
                'color': '#4FD3FF',
                'line-height': '48px',
                'text-align': 'left',
              });
              newElement.find('.mapPopContent').css({
                'font-family': 'Source Han Sans CN, Source Han Sans CN',
                'font-weight': '400',
                'font-size': '32px',
                'color': '#FFFFFF',
                'line-height': '48px',
                'text-align': 'left',
              });
              top.$('#ltwgDialog').children().children().last().append(newElement);
            }
          })
        },
        //将床位数添加至弹窗
        appendKsBedNumberStr(CompanyName) {
          //床位数模板字符串
          let KsBedNumberStr = ""
          this.getKsBedNumberStr(CompanyName).then(res => {
            KsBedNumberStr = res

            //如果床位数元素已添加过则改变值
            if (top.$("#infoMessage3").length > 0) {
              top.$("#infoMessage3").children().last().html(KsBedNumberStr)
            } else {
              // 使用 jQuery 创建新元素
              let newElement = $(`  <div id="infoMessage3">
                <div class="mapPopTitle2">床位数：</div>
                <div class="mapPopContent">${KsBedNumberStr}</div>
              </div>`);

              // 设置样式
              newElement.css({
                'width': '670px',
                'height': 'auto',
                'border-bottom': '2px solid #A5BFFF',
                'margin': '15px 0 0 47px',
                'display': 'flex',
                'justify-content': 'flex-start',
                'align-items': 'flex-start',
                'flex-wrap': 'wrap',
                'flex-direction': 'column',
              });
              newElement.find('.mapPopTitle2').css({
                'font-family': 'Source Han Sans CN, Source Han Sans CN',
                'font-weight': '400',
                'font-size': '32px',
                'color': '#4FD3FF',
                'line-height': '48px',
                'text-align': 'left',
              });
              newElement.find('.mapPopContent').css({
                'font-family': 'Source Han Sans CN, Source Han Sans CN',
                'font-weight': '400',
                'font-size': '32px',
                'color': '#FFFFFF',
                'line-height': '48px',
                'text-align': 'left',
              });
              top.$('#ltwgDialog').children().children().last().append(newElement);
            }
          })
        },
        //跳转企业详情页
        showL7CompanyDetail(CompanyName) {
          const that = this;
          if (this.label == "网络经济中心" || this.label == "金华之心") {
            axios({
              method: 'post',
              url: baseURL.admApi + '/mis/irs/actuator',
              headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                Authorization: sessionStorage.getItem('Authorization'),
              },
              data: {
                name: '金华企业画像企业搜索',
                url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012229/dataSharing/0cSJbwb7r4uh7H4e.htm',
                params: {
                  name: CompanyName,
                },
              },
            }).then((res) => {
              let arr = res.data.datas.data ? res.data.datas.data : []
              if (arr.length > 0) {
                top.postMessage(JSON.stringify({
                  type: 'openIframe',
                  name: 'qyhx-xq-index',
                  src: 'https://csdn.dsjj.jinhua.gov.cn:8101/static/citybrain/qyhx/pages/qyhx-xq-index.html',
                  left: '0px',
                  top: '0',
                  width: '7680px',
                  height: '2160px',
                  zIndex: '999',
                  argument: {
                    allMessage: arr,
                    status: 'detail',
                  }
                }), '*')
              } else {
                console.log(arr, "未查询到企业信息")
                alert("非金华本地企业")
                return false
              }
            })
          } else if (this.label == "中心医院") {
            if (top.$('#ltwgDialog').length > 0) {
              this.appendDoctorListStr(CompanyName)
              this.appendKsDetailStr(CompanyName)
              this.appendKsBedNumberStr(CompanyName)
            }
          }
        },
        //将arr按照size大小划分为一个二维数组
        chunkArray(arr, size) {
          return arr.reduce((result, element, index) => {
            if (index % size === 0) {
              result.push([]);
            }
            result[Math.floor(index / size)].push(element);
            return result;
          }, []);
        },
        //开始滚动
        startScrolling(direction) {
          let scrollContainer = top.document.getElementById("scrollContainer");
          if (!scrollContainer) return
          this.isScrolling = true;
          this.scrollInterval = setInterval(() => {
            if (direction === 'left') {
              scrollContainer.scrollLeft -= 40; // 调整滚动速度
            } else if (direction === 'right') {
              scrollContainer.scrollLeft += 40; // 调整滚动速度
            }
          }, 100); // 调整滚动间隔时间
        },
        //停止滚动
        stopScrolling() {
          clearInterval(this.scrollInterval);
          this.isScrolling = false;
        },
        //获取实时人数方法集合总入口
        async getRealTimePeopleCount(code) {
          const { coordinates, data } = await this.getCoordinates(code)
          const wgid = await this.createWLGZ(coordinates);
          const peopleNumber = await this.getCount(wgid);
          return peopleNumber;
        },
        //获取L4点击网格边界数据
        async getCoordinates(code) {
          let url = "";
          const urlList = [
            {
              type: "L4",
              url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BDL4_jh_2d/FeatureServer/0"
            },
            {
              type: "L5",
              url: ""
            },
            {
              type: "L6",
              url: ""
            },
            {
              type: "L7",
              url: ""
            }
          ]
          url = urlList.find(item => item.type === this.CurrentMapLayerType).url;
          let queryUrl = `${url}/query`
          try {
            // 构建请求参数
            const params = {
              f: 'json', // 返回格式
              where: `code='${code}'`, // 查询条件
              returnGeometry: true, // 返回几何信息
              outFields: '*', // 返回所有字段
              outSR: 4490, // 输出坐标系
            };

            // 发起 GET 请求
            const response = await axios.get(queryUrl, { params });
            console.log(response, "Coordinates");
            // 检查响应状态码
            if (response.status === 200) {
              // 处理返回的数据
              const data = response.data;
              if (data.features && data.features.length > 0) {
                const coordinates = data.features[0].geometry.rings[0];
                console.log('Grid Coordinates:', coordinates);
                return { coordinates, data };
              } else {
                console.warn(`No data found for grid code: ${code}`);
                return null;
              }
            } else {
              console.error(`Request failed with status code: ${response.status}`);
              return null;
            }
          } catch (error) {
            console.error('Error fetching grid coordinates:', error);
            return null;
          }
        },
        //获取网格区域id
        async createWLGZ(coordinates) {
          let str = '';
          coordinates.forEach((ele) => {
            str += `${ele[0]},${ele[1]}|`;
          });
          const faceStr = str.slice(0, -1); // Remove the last pipe symbol

          try {
            const response = await axios({
              method: 'post',
              url: `${baseURL.url}/typeq/api/getu/project/create`,
              data: {
                shape: faceStr,
                precision: 7,
              },
              headers: {
                'Access-Token': window.accessToken
              }
            });
            return response.data;
          } catch (error) {
            console.error('Error creating WLGZ:', error);
            throw error; // Optionally throw or handle the error
          }
        },
        //根据网格区域id获取实时人数
        async getCount(id) {
          //  优化时间格式化过程，使用模板字符串和辅助函数
          const formatTime = (date) => {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
          };

          const d = new Date();
          const start = formatTime(d);

          // 修正减小时的错误，确保符合业务逻辑（假设减3小时是需求，这里保持不变）
          const frontOneHour = new Date(d.getTime() - 3 * 60 * 60 * 1000);
          const end = formatTime(frontOneHour);

          try {
            // 使用async/await处理异步请求，增加错误处理
            const res = await axios({
              method: 'post',
              url: baseURL.url + '/typeq/api/getu/project/get', // 确保baseURL.url是安全的，这里假设是安全的
              data: {
                id: id.data, // 假设id.data已经过验证
                type: 2,
                start_time: end,
                end_time: start,
              },
              headers: {
                'Access-Token': window.accessToken
              }
            });
            return res.data.data;
          } catch (error) {
            // 异常处理逻辑
            console.error("Error getCount:", error);
            throw error;
          }
        },
        //获取网格区域id以及实时人数接口所需鉴权token
        creditAuth() {
          var appKey = 'zj_jh-API',
            masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

          var timestamp = Number(Math.round(new Date().getTime() / 1000).toString())

          var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

          var o_str = appKey + time_md5 + masterSecret,
            sha256_str = CryptoJS.SHA256(o_str).toString()

          var sign = sha256_str + masterSecret

          const reqParams = {
            appKey: appKey,
            sign: sign,
            timestamp: timestamp,
            version: 'v1.0',
          }

          return axios({
            method: 'post',
            url: baseURL.url + '/typeq/api/auth/creditAuth',
            data: reqParams,
          }).then(function (res) {
            if (res.data.errno === 0) {
              window.accessToken = res.data.data.accessToken
            }
          })
        },
      }
    })
    //大模型打开对应图层
    top.emiter.on('AiOpenWgMapLayer', (res) => {
      if (res === 'L7-1') {
        ltwg.changeMap(ltwg.list.filter(item => item.name == "L7")[0].childList[0]);
      }
      if (res === 'L7-2') {
        ltwg.changeMap(ltwg.list.filter(item => item.name == "L7")[0].childList[1]);
      }
      if (res === 'L7-3') {
        ltwg.changeMap(ltwg.list.filter(item => item.name == "L7")[0].childList[2]);
      }
      if (res === 'L7-4') {
        ltwg.changeMap(ltwg.list.filter(item => item.name == "L7")[0].childList[3]);
      }
      if (res === 'L7-5') {
        ltwg.changeMap(ltwg.list.filter(item => item.name == "L7")[0].childList[4]);
      }
    })
    //大模型关闭对应图层
    top.emiter.on('AiCloseWgMapLayer', (res) => {
      if (localStorage.getItem("mapType3D") == res) {
        ltwg.changeMap(ltwg.list.filter(item => item.name == res)[0].childList[0]);
      } else {
        return
      }
    })
  </script>
</body>

</html>