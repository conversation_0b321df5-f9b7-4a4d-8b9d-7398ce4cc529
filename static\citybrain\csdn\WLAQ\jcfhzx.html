<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>监测防护中心</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/animate.css" />
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <script src="/Vue/vue.js"></script>

  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/elementui/js/index.js"></script>
  <!-- <link href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script> -->

  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <style>
    #jcfh {
      width: 3840px;
      height: 2160px;
      background-image: url('../img/jcfhimg/bg.png');
      background-size: cover;
    }

    .top-title {
      width: 3840px;
      height: 229px;
      background-image: url('../img/jcfhimg/topbg.png');
      background-size: cover;
      display: flex;
      align-items: flex-start;
      justify-content: center;
    }

    .tt-l {
      display: flex;
      align-items: center;
      margin-top: 62px;
    }

    .tt-l>div {
      margin-left: 24px;
      font-family: SourceHanSansCN-Medium;
      font-size: 50px;
      font-weight: normal;
      font-stretch: normal;
      margin-top: -12px;
      background: linear-gradient(180deg, #ebf2ff 0, #ffffff 25%, #c3d8ee 50%, #94a9db 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-right: 82px;
    }

    .tt-m {
      width: 1694px;
      height: 154px;
      font-family: SourceHanSansCN-Bold;
      font-size: 80px;
      font-weight: bold;
      font-stretch: normal;
      text-align: center;
      line-height: 148px;
      background: linear-gradient(180deg, #c2e5ff 0, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .tt-r {
      display: flex;
      align-items: center;
      margin-top: 62px;
      margin-left: 82px;
    }

    .tt-r>div {
      margin-left: 24px;
      font-family: SourceHanSansCN-Medium;
      font-size: 50px;
      font-weight: normal;
      font-stretch: normal;
      margin-top: -12px;
      background: linear-gradient(180deg, #ebf2ff 0, #ffffff 25%, #c3d8ee 50%, #94a9db 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

    }

    .jcfh-content {
      height: 1960px;
      width: 3840px;
      margin-top: -29px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20px;
    }

    .left {
      width: 1000px;
      height: 1960px;
      margin-right: 18px;
    }

    .middle {
      width: 1760px;
      height: 1960px;
      margin-right: 20px;
    }

    .right {
      width: 1000px;
      height: 1960px;
    }

    .contentItem {
      background-image: url('../img/jcfhimg/smallbg.png');
      background-size: cover;
      width: 1000px;

    }

    .title {
      background-image: url('../img/jcfhimg/titlebg.png');
      background-size: cover;
      width: 780px;
      height: 70px;
      font-family: SourceHanSansCN-Bold;
      font-size: 40px;
      font-weight: bold;
      font-stretch: normal;
      text-indent: 64px;
      color: #fff;
      line-height: 70px;
    }

    .leftItem {
      height: 633px;
      margin-bottom: 20px;
    }

    .rightItem1 {
      height: 600px;
      margin-bottom: 20px;
    }

    .rightItem2 {
      height: 609px;
      margin-bottom: 20px;
    }

    .rightItem3 {
      height: 691px;
      margin-bottom: 20px;
    }

    .leftcontent1 {
      display: flex;
    }

    .leftcontent1-1-1 {
      background-image: url('../img/jcfhimg/lxbt.png');
      background-size: cover;
      width: 520px;
      height: 70px;
      margin-top: 40px;
      margin-left: 75px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding-left: 110px;
    }

    .leftcontent1-1-1-1 {
      font-family: SourceHanSansCN-Medium;
      font-size: 30px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #ffffff;
      margin-right: 24px;
      white-space: nowrap;
    }

    .leftcontent1-1-1-2 {
      width: 32px;
      height: 46px;
      background-image: url('../img/jcfhimg/num-bg.png');
      background-size: cover;
      margin-right: 13px;
    }

    .leftcontent1-1-1-2>div {
      font-family: BebasNeue;
      font-size: 40px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 46px;
      width: 32px;
      text-align: center;
      font-weight: 520;
    }

    .leftcontent1-1-1-3 {
      font-family: SourceHanSansCN-Medium;
      font-size: 30px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 50px;
    }

    .leftcontent1-1-2 {
      display: flex;
    }

    .leftcontent1-1-2-1 {
      width: 243px;
      height: 345px;
      margin-left: 62px;
      margin-top: 55px;
    }

    .leftcontent1-1-2-1-1 {
      width: 243px;
      text-align: center;
      font-family: SourceHanSansCN-Medium;
      font-size: 36px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #ffffff;
    }

    .leftcontent1-1-2-1-2 {
      background-image: url('../img/jcfhimg/gjjczb.png');
      background-size: cover;
      width: 243px;
      height: 282px;
      margin-top: 29px;
      box-sizing: border-box;
      padding-top: 47px;
    }

    .leftcontent1-1-2-1-2-1 {
      display: flex;
      align-items: flex-end;
      height: 56px;
      margin-left: 70px;
    }

    .leftcontent1-1-2-1-2-1>div:nth-child(1) {
      font-family: BebasNeue;
      font-size: 56px;
    }

    .leftcontent1-1-2-1-2-1>div:nth-child(2) {
      font-family: BebasNeue;
      font-size: 36px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      height: 56px;
    }

    .leftcontent1-2 {
      width: 326px;
      margin-left: 56px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 54px;
    }

    .leftcontent1-2-1 {
      background-image: url('../img/jcfhimg/zbbg.png');
      background-size: cover;
      width: 219px;
      height: 160px;
    }

    .leftcontent1-2-1-1 {
      font-family: MicrosoftYaHei;
      font-size: 33px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 2px;
      color: #d6e7f9;
      text-align: center;
      margin-top: 28px;
    }

    .leftcontent1-2-1-2 {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      margin-top: 12px;
    }

    .leftcontent1-2-1-2>div:first-child {
      font-family: BebasNeue;
      font-size: 60px;
      line-height: 60px;
      margin-right: 22px;
    }

    .middle-top {
      display: flex;
      margin-top: 37px;
    }

    .middle-top-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 329px;
      height: 317px;
      cursor: pointer;
    }

    .middle-top-item>img {
      width: 210px;
      height: 164px;

    }

    .middle-top-item>div {
      font-family: PangMenZhengDao;
      font-size: 40px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 30px;
      letter-spacing: 0px;
      color: #ffffff;
      margin-top: 16px;
    }

    .middle-top-item-active {
      width: 379px;
      height: 317px;
      background-image: url('../img/jcfhimg/xz1.png');
      background-size: cover;
    }

    .mbox1 {
      width: 1760px;
      height: 683px;
      background-color: rgb(10, 32, 68, 0.7);
      border: 1px solid rgba(0, 198, 255, 0.5);
      margin-top: -41px;
      display: flex;
      /* align-items: center; */
    }

    .mbox1left {
      margin-left: 45px;
      margin-top: 157px;
    }

    .mbox1left>div {
      display: flex;
      align-items: center;
      margin-bottom: 42px;
    }

    .mbox1left>div>img {
      margin-right: 38px;
    }

    .mbox1right {
      margin-left: 74px;
      margin-top: 77px;
    }

    .mrtop {
      width: 1131px;
      height: 80px;
      background-color: #00396f;
      display: flex;
      align-items: center;
    }

    .mrtop>div {

      font-family: SourceHanSansCN-Regular;
      font-size: 32px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 80px;
      letter-spacing: 0px;
      color: #77b3f1;
      text-align: center;
    }

    .mrcon {
      height: 450px;
      overflow-y: auto;
    }

    .mrconitem {
      margin-top: 10px;
      /* width: 1131px; */
      height: 80px;
      background-color: #0f2b4d;
      display: flex;
      align-items: center;
    }

    .mrconitem>div {
      font-family: SourceHanSansCN-Regular;
      font-size: 32px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 80px;
      letter-spacing: 0px;
      color: #fff;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mrconitem-bq {
      height: 50px;
      background-color: #11406B;
      border-radius: 6px;
      border: solid 1px #1a95e3;
      font-family: SourceHanSansCN-Regular;
      font-size: 32px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 50px;
      letter-spacing: 0px;
      padding: 0 10px;
      color: #ffffff;
      margin-right: 10px;
    }

    .mbox2 {
      width: 1760px;
      height: 921px;
      margin-top: 18px;


    }

    .mbox2con {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .mbox2-1 {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mbox2-2 {
      width: 1423px;
      height: 619px;
      background-image: url('../img/jcfhimg/zxys.png');
      background-size: cover;
      margin-top: 79px;
      /* display: flex; */

    }

    .m2item {
      display: flex;
      width: 100%;
      justify-content: space-between;
    }

    .m2item-1 {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .m2item-1>div:first-child {
      font-family: BebasNeue;
      font-size: 78px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: -5px;
      color: #9aa9bf;
    }

    .m2item-1>div:nth-child(2) {
      font-family: SourceHanSansCN-Regular;
      font-size: 42px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 56px;
      letter-spacing: 0px;
      color: #ffffff;
    }

    .content {
      display: flex;
      justify-content: space-evenly;
      flex-flow: row wrap;
    }

    .right1 {
      width: 461px;
      height: 178px;
      margin-top: 60px;
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      align-items: center;
    }

    .right1img {
      width: 227px;
      height: 178px;
    }

    .txtcontainer {
      border-top: 4px solid #0a9ac8;
    }

    .right1txt {
      width: 219px;
      height: 140px;
      background-color: rgb(26, 149, 227, 0.08);
      border-radius: 0px 0px 20px 0px;
      /* opacity: 0.08; */
      /* 子元素会继承父元素的opacity,所以改用rbga */
      padding-top: 25px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
    }

    .right1txt .txtnumber {
      font-family: BebasNeue;
      font-size: 60px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      /* color: #9aa9bf; */
      /* 字体颜色渐变 */
      background: linear-gradient(to bottom, #ffcdcd, #fff, #ff4949, #fff);
      -webkit-background-clip: text;
      color: transparent;
    }

    .right1txt .txttitle {
      font-family: SourceHanSansCN-Regular;
      font-size: 30px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 30px;
      letter-spacing: 1px;
      color: #ffffff;
    }

    .right2 {
      width: 360px;
      height: 193px;
      margin-top: 50px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .right2title {
      display: flex;
      align-items: center;
    }

    .right2title div {
      font-family: SourceHanSansCN-Bold;
      font-size: 36px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #ffffff;
      margin-left: 20px;
    }

    .right2txt {
      width: 360px;
      height: 126px;
      background-color: rgb(26, 149, 227, 0.08);
      border-radius: 0px 0px 20px 0px;
      padding: 20px 30px 0 30px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      position: relative;
    }

    .right2txt .smalltxtcontainer {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-around;
    }

    .right2txt .smalltxtcontainer .txttitle {
      font-family: SourceHanSansCN-Regular;
      font-size: 30px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 30px;
      letter-spacing: 0px;
      color: #ffffff;
      opacity: 0.95;
    }

    .right2txt .smalltxtcontainer1 .txtnumber {
      font-family: BebasNeue;
      font-size: 50px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      background: linear-gradient(to bottom, #ffeccb, #fff, #ffc460, #ffe2b0, #fff);
      -webkit-background-clip: text;
      color: transparent;
    }

    .right2txt .smalltxtcontainer2 .txtnumber {
      font-family: BebasNeue;
      font-size: 50px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      background: linear-gradient(to bottom, #caffff, #fff, #22e8e8, #91f4f4, #fff);
      -webkit-background-clip: text;
      color: transparent;
    }

    .right2txt .columnline {
      width: 3px;
      height: 60px;
      background-image: linear-gradient(to bottom, transparent, rgb(81, 196, 255, 0.8), transparent);
      opacity: 0.57;
      position: absolute;
      left: 190px;
      top: 40px;
    }

    .mrcon::-webkit-scrollbar { width: 0 !important }


    /* 金色渐变 */
    .godLine {
      background: linear-gradient(180deg, #ffeccb 0, #ffffff 25%, #ffc460 50%, #ffe2b0 75%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* 青色渐变 */
    .qingLine {
      background: linear-gradient(180deg, #caffff 0, #ffffff 25%, #22e8e8 50%, #91f4f4 75%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* 红色渐变 */
    .redLine {
      background: linear-gradient(180deg, #ffcdcd 25%, #ffffff 50%, #ff4949 75%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>

<body>
  <div id="jcfh">
    <div class="top-title">
      <div class="tt-l" @click="pageJump('aqgz')">
        <img src="../img/jcfhimg/ttl.png" alt="">
        <div style="font-weight: 600;cursor: pointer;">安全感知中心</div>
      </div>
      <div class="tt-m">监测防护中心</div>
      <div class="tt-r" @click="pageJump('aqyy')">
        <img src="../img/jcfhimg/rightBtn-icon.png" alt="">
        <div style="font-weight: 600;cursor: pointer;">安全运营中心</div>
      </div>
    </div>

    <div class="jcfh-content">
      <div class="left">
        <div class="contentItem leftItem">
          <div class="title">攻击监测</div>
          <div class="leftcontent1">
            <div class="leftcontent1-1">
              <div class="leftcontent1-1-1" style="padding-left: 35px;">
                <div class="leftcontent1-1-1-1">攻击总数</div>
                <div class="leftcontent1-1-1-2" v-for="(item,i) in gongji.value1">
                  <div class="godLine">{{item}}</div>
                </div>
                <div class="leftcontent1-1-1-3 godLine">次</div>
              </div>
              <div class="leftcontent1-1-2">
                <div class="leftcontent1-1-2-1">
                  <div class="leftcontent1-1-2-1-1">近7日攻击</div>
                  <div class="leftcontent1-1-2-1-2">
                    <div class="leftcontent1-1-2-1-2-1">
                      <div class="godLine" v-if="gongji.value2.length >= 5">{{(gongji.value2/10000).toFixed(0)}}</div>
                      <div class="godLine" v-if="gongji.value2.length >= 5">万次</div>
                      <div class="godLine" v-if="!gongji.value2.length >= 5">{{gongji.value2}}</div>
                      <div class="godLine" v-if="!gongji.value2.length >= 5">次</div>
                    </div>
                  </div>
                </div>
                <div class="leftcontent1-1-2-1">
                  <div class="leftcontent1-1-2-1-1">通报及时率</div>
                  <div class="leftcontent1-1-2-1-2">
                    <div class="leftcontent1-1-2-1-2-1">
                      <div class="godLine">{{gongji.value3}}</div>
                      <div class="godLine">次</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="leftcontent1-2">
              <div class="leftcontent1-2-1">
                <div class="leftcontent1-2-1-1">今日新增</div>
                <div class="leftcontent1-2-1-2">
                  <div class="qingLine">{{gongji.value4}}</div>
                  <img src="../img/jcfhimg/add.png" alt="">
                </div>
              </div>
              <img style="margin-top:66px" src="../img/jcfhimg/gjjc.png" alt="">
            </div>
          </div>
        </div>
        <div class="contentItem leftItem">
          <div class="title">隐患监测</div>
          <div class="leftcontent1">
            <div class="leftcontent1-1">
              <div class="leftcontent1-1-1">
                <div class="leftcontent1-1-1-1">隐患总数</div>
                <div class="leftcontent1-1-1-2" v-for="(item,i) in yinhuan.value1.toString()">
                  <div class="godLine">{{item}}</div>
                </div>
                <div class="leftcontent1-1-1-3 godLine">次</div>
              </div>
              <div id="myEcahrts1" style="width:620px;height:400px"></div>
            </div>
            <div class="leftcontent1-2">
              <div class="leftcontent1-2-1">
                <div class="leftcontent1-2-1-1">今日新增</div>
                <div class="leftcontent1-2-1-2">
                  <div class="qingLine">{{yinhuan.value2}}</div>
                  <img src="../img/jcfhimg/add.png" alt="">
                </div>
              </div>
              <img style="margin-top:66px" src="../img/jcfhimg/yhjc.png" alt="">
            </div>
          </div>
        </div>
        <div class="contentItem leftItem">
          <div class="title">事件监测</div>
          <div class="leftcontent1">
            <div class="leftcontent1-1">
              <div class="leftcontent1-1-1" style="padding-left: 80px">
                <div class="leftcontent1-1-1-1">事件总数</div>
                <div class="leftcontent1-1-1-2" v-for="(item,i) in shijian.value1.toString()">
                  <div class="godLine">{{item}}</div>
                </div>
                <div class="leftcontent1-1-1-3 godLine">次</div>
              </div>
              <div id="myEcahrts2" style="width:620px;height:400px"></div>
            </div>
            <div class="leftcontent1-2">
              <div class="leftcontent1-2-1">
                <div class="leftcontent1-2-1-1">今日新增</div>
                <div class="leftcontent1-2-1-2">
                  <div class="qingLine">{{shijian.value2}}</div>
                  <img src="../img/jcfhimg/add.png" alt="">
                </div>
              </div>
              <img style="margin-top:66px" src="../img/jcfhimg/sjjc.png" alt="">
            </div>
          </div>
        </div>
      </div>
      <div class="middle">
        <div class="middle-top">
          <div class="middle-top-item" v-for="(item,i) in data0" :key="i"
            :class="{'middle-top-item-active':activeNum==i}" @click="middleClick(i)">
            <img :src="'../img/jcfhimg/mt'+(i+1)+'.png'" alt="">
            <div>{{item.name}}</div>
          </div>
        </div>
        <div class="mbox1">
          <div class="mbox1left">
            <div>
              <img style="width:210px;height:164px" :src="'../img/jcfhimg/mt'+(activeNum+1)+'.png'" alt="">
              <div class="leftcontent1-2-1">
                <div class="leftcontent1-2-1-1">引擎数</div>
                <div class="leftcontent1-2-1-2">
                  <div class="qingLine">{{middleTopIndexs.num1}}</div>
                </div>
              </div>
            </div>
            <div>
              <img style="width:210px;height:164px" src="../img/jcfhimg/jrgjs.png" alt="">
              <div class="leftcontent1-2-1">
                <div class="leftcontent1-2-1-1">今日告警数</div>
                <div class="leftcontent1-2-1-2">
                  <div class="redLine" style="letter-spacing:-6px;">{{middleTopIndexs.num2}}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="mbox1right">
            <div class="mrtop">
              <div style="flex:0.2">设备名称</div>
              <div style="flex:0.2">设备IP</div>
              <div style="flex:0.4">标签</div>
              <div style="flex:0.2">联动</div>
            </div>
            <div class="mrcon">
              <div class="mrconitem" v-for="(item,i) in data1" :key="i">
                <div style="flex:0.2">{{item.name}}</div>
                <div style="flex:0.2">{{item.ip}}</div>
                <div style="flex:0.4">
                  <div v-for="(m,n) in item.bq" :key="n" class="mrconitem-bq">{{m}}</div>
                </div>
                <div style="flex:0.2;cursor: pointer;" class="godLine">进入</div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentItem mbox2">
          <div class="title">响应情况</div>
          <div class="mbox2con">
            <div class="mbox2-1">
              <div class="leftcontent1-1-1" style="margin-left:0;margin-right:103px">
                <div class="leftcontent1-1-1-1">{{index1.name}}</div>
                <div class="leftcontent1-1-1-2" v-for="(item,i) in index1.value">
                  <div class="godLine">{{item}}</div>
                </div>
                <div class="leftcontent1-1-1-3 godLine">%</div>
              </div>
              <div class="leftcontent1-1-1" style="margin-left:0;">
                <div class="leftcontent1-1-1-1">{{index2.name}}</div>
                <div class="leftcontent1-1-1-2" v-for="(item,i) in index2.value">
                  <div class="godLine">{{item}}</div>
                </div>
                <div class="leftcontent1-1-1-3 godLine">%</div>
              </div>
            </div>
            <div class="mbox2-2">
              <div class="m2item" style="margin-top:-40px">
                <div class="m2item-1" style="margin-left:100px">
                  <div class="godLine">{{index3.value}}</div>
                  <div>{{index3.name}}</div>
                </div>
                <div class="m2item-1" style="margin-right:100px">
                  <div class="godLine">{{index4.value}}</div>
                  <div>{{index4.name}}</div>
                </div>
              </div>
              <div class="m2item" style="margin-top:140px">
                <div class="m2item-1" style="margin-left:100px">
                  <div class="godLine">{{index5.value}}</div>
                  <div>{{index5.name}}</div>
                </div>
                <div class="m2item-1" style="margin-right:100px">
                  <div class="godLine">{{index6.value}}</div>
                  <div>{{index6.name}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="contentItem rightItem1">
          <div class="title">三高一弱监测</div>
          <div class="content">
            <div class="right1" v-for="(item,i) in right1" :key="i">
              <img class="right1img" :src="item.img"></img>
              <!-- border与borderradius不能共存 -->
              <div class="txtcontainer">
                <div class="right1txt">
                  <div class="txttitle">{{item.name}}</div>
                  <div class="txtnumber">{{item.value}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentItem rightItem2">
          <div class="title">攻击面监测</div>
          <div class="content">
            <div class="right2" v-for="(item,i) in right2" :key="i">
              <div class="right2title">
                <img src="../img/jcfhimg/yms.png" width="50px" height="50px">
                <div>{{item.name}}</div>
              </div>
              <div class="txtcontainer">
                <div class="right2txt">
                  <div class="smalltxtcontainer smalltxtcontainer1">
                    <div class="txttitle">总量</div>
                    <div>
                      <div class="txtnumber">{{item.value1}}<span style="font-size: 32px;">个</span></div>
                    </div>
                  </div>
                  <span class="columnline"></span>
                  <div class="smalltxtcontainer smalltxtcontainer2">
                    <div class="txttitle">本日新增</div>
                    <div class="txtnumber">{{item.value2}}<span style="font-size: 32px;">个</span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="contentItem rightItem3">
          <div class="title">威胁情报</div>
          <div class="mrtop" style="width:940px;margin-left: 30px;margin-top:33px">
            <div style="flex:1">时间</div>
            <div style="flex:1">情报信息</div>
            <div style="flex:1">发布单位</div>
          </div>
          <div class="mrcon" style="width:940px;margin-left: 30px;">
            <div class="mrconitem" v-for="(item,i) in data2" :key="i">
              <div style="flex:1">{{item.time}}</div>
              <div style="flex:1" :title="item.msg">{{item.msg.length>9?item.msg.slice(0,9)+"...":item.msg}}</div>
              <div style="flex:1;">{{item.dw}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
 <script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  let vm = new Vue({
    el: "#jcfh",
    data: {
      middleTopIndexs:{num1:"",num2:""}, //引擎数与今日告警数
      index1:{}, //六项指标
      index2:{},
      index3:{},
      index4:{},
      index5:{},
      index6:{},
      gongji:{value1:"",value2:"",value3:"",value4:""},
      yinhuan:{value1:0,value2:""},
      shijian:{value1:0,value2:""},
      right1:[
        {name:"高危漏洞",value:"",img:"../img/jcfhimg/gwld.png"},
        {name:"高危端口",value:"",img:"../img/jcfhimg/gwdk.png"},
        {name:"高危操作",value:"",img:"../img/jcfhimg/gwcz.png"},
        {name:"弱口令",value:"",img:"../img/jcfhimg/rkl.png"}
      ],
      right2:[
        {name:"域名数",value1:"",value2:"",img:"../img/jcfhimg/yms.png"},
        {name:"Web资产数(域名)",value1:"",value2:"",img:"../img/jcfhimg/wbzcsym.png"},
        {name:"Web资产数(IP)",value1:"",value2:"",img:"../img/jcfhimg/wbzcsip.png"},
        {name:"开放端口数",value1:"",value2:"",img:"../img/jcfhimg/kfdks.png"}
      ],
      data0: [
        { name: '云安全引擎' },
        { name: '应用安全引擎' },
        { name: '网络安全引擎' },
        { name: '数据安全引擎' },
        { name: '终端安全引擎' },
        { name: '视频安全引擎' },
      ],
      data1: [],
      data2: [],
      activeNum: 0,
    },
    created() {
    },
    mounted() {
      this.getApiData()
      this.middleClick(this.activeNum)
    },
    methods: {
      getApiData() {
        $api("wlaq_jcfh_C21").then(res => {
          this.index1 = res[0]
          this.index2 = res[1]
          this.index3 = res[2]
          this.index4 = res[3]
          this.index5 = res[4]
          this.index6 = res[5]
        })

        //攻击监测数
        $api("wlaq_jcfh_L11").then(res => {
          this.gongji.value1 = res[0].value
          this.gongji.value2 = res[1].value
          this.gongji.value3 = res[2].value
          this.gongji.value4 = res[3].value
        })

        //隐患监测
        $api("wlaq_jcfh_L21").then(res => {
          this.yinhuan.value2 = res[4].num
          let arr = []
          res.forEach((item,i) => {
            if (item.code == '1') {
              arr.push(item)
              this.yinhuan.value1 += Number(item.num)
            }
          })
          let result = arr.map(item => {
            return {name:item.label,value:item.num}
          })
          this.getEcharts1(result, "myEcahrts1")
        })

        //事件监测
        $api("wlaq_jcfh_L31").then(res => {
          console.log(res);
          this.shijian.value2 = res[4].num
          let arr = []
          res.forEach((item,i) => {
            if (item.code == '1') {
              arr.push(item)
              this.shijian.value1 += Number(item.num)
            }
          })
          let result = arr.map(item => {
            return {name:item.label,value:item.num}
          })
          this.getEcharts1(result, "myEcahrts2")
        })

        //三高一弱监测
        $api("wlaq_jcfh_R11").then(res => {
          this.right1[0].value = res[0].value
          this.right1[1].value = res[1].value
          this.right1[2].value = res[2].value
          this.right1[3].value = res[3].value
        })

        //攻击面监测
        $api("wlaq_jcfh_R21").then(res => {
          this.right2[0].value1 = res[0].num
          this.right2[0].value2 = res[1].num
          this.right2[1].value1 = res[2].num
          this.right2[1].value2 = res[3].num
          this.right2[2].value1 = res[4].num
          this.right2[2].value2 = res[5].num
          this.right2[3].value1 = res[6].num
          this.right2[3].value2 = res[7].num
        })

        //威胁情报
        $api("wlaq_jcfh_R31").then(res => {
          console.log(res);
          this.data2 = res.map(item => {
            return { time: item.sj, msg: item.qbxx, dw: item.fbdw }
          })
        })
      },
      pageJump(value) {
        if (value == 'aqgz') {
          location.href = '/static/citybrain/csdn/WLAQ/GZZX.html'
        } else {
          location.href = '/static/citybrain/csdn/WLAQ/YYZX.html'
        }
      },
      middleClick(i) {
        this.activeNum = i;
        //顶部指标
        $api("wlaq_jcfh_C11").then(res => {
          res.forEach((item,i) => {
            if (Number(item.code) == this.activeNum + 1 && item.label == '今日告警数') {
              this.middleTopIndexs.num2 = item.num
            }
            if (Number(item.code) == this.activeNum + 1 && item.label == '引擎数') {
              this.middleTopIndexs.num1 = item.num
            }
          })
        })
        $api("wlaq_jcfh_C12",{code:this.activeNum + 1}).then(res => {
          this.data1 = res.map(item => {
            return { name: item.sbmc, ip: item.sbip, bq: item.sbbq.split(","), url:item.url }
          })
        })
      },
      getEcharts1(data, dom) {
        let echarts0 = echarts.init(document.getElementById(dom));
        let that = this;

        let xData = [];
        let yData = [];
        for (let item of data) {
          xData.push(item.name);
          yData.push(item.value.split("人")[0]);
        }
        let option = {
          grid: {
            top: "14%",
            left: "1%",
            bottom: "2%",
            right: "1%",
            containLabel: true,
          },
          tooltip: {
            show: true,
            borderWidth: 0,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "#fff",
              fontSize: 30,
            },
            formatter: "{b}: <br/> {c}",
          },
          animation: false,
          xAxis: [
            {
              type: "category",
              data: xData,
              axisTick: {
                alignWithLabel: true,
              },
              nameTextStyle: {
                color: "#82b0ec",
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#82b0ec",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                },
                formatter: function (params) {
                  var newParamsName = ''
                  var paramsNameNumber = params.length
                  var provideNumber = 2 // 一行显示几个字
                  var rowNumber = Math.ceil(paramsNameNumber / provideNumber)
                  if (paramsNameNumber > provideNumber) {
                    for (var p = 0; p < rowNumber; p++) {
                      var tempStr = ''
                      var start = p * provideNumber
                      var end = start + provideNumber
                      if (p === rowNumber - 1) {
                        tempStr = params.substring(start, paramsNameNumber)
                      } else {
                        tempStr = params.substring(start, end) + '\n'
                      }
                      newParamsName += tempStr
                    }
                  } else {
                    newParamsName = params
                  }
                  return newParamsName
                },
                margin: 30,
              },
            },
          ],
          yAxis: [
            {
              show: false,
              type: "value",
              axisLabel: {
                textStyle: {
                  color: "#fff",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#0c2c5a",
                },
              },
              axisLine: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: "",
              type: "pictorialBar",
              symbolSize: [70, 10],
              symbolOffset: [0, -6], // 上部椭圆
              symbolPosition: "end",
              z: 12,
              // "barWidth": "0",
              label: {
                normal: {
                  show: true,
                  position: "top",
                  formatter: "{c}",
                  fontSize: 28,
                  fontWeight: "bold",
                  color: "#ffdb9b",
                },
              },
              itemStyle: {
                normal: {
                  //这里是颜色
                  color: function (params) {
                    //注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
                    var colorList = ['#ff3d3d', '#ff623d', '#ffb638', '#00c0ff'];
                    return colorList[params.dataIndex]
                  }
                }
              },
              data: yData,
            },
            {
              name: "",
              type: "pictorialBar",
              symbolSize: [70, 10],
              symbolOffset: [0, 7], // 下部椭圆
              // "barWidth": "20",
              z: 12,
              itemStyle: {
                normal: {
                  //这里是颜色
                  color: function (params) {
                    //注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
                    var colorList = ['rgba(255,61,61,0.7)', 'rgba(255,98,61,0.7)', 'rgba(255,182,56,0.7)', 'rgba(0,192,255,0.7)'];
                    return colorList[params.dataIndex]
                  }
                }
              },
              data: yData,
            },
            {
              name: "",
              type: "pictorialBar",
              symbolSize: function (d) {
                return d > 0 ? [110, 20] : [0, 0];
              },
              symbolOffset: [-2, 18], // 下部内环
              z: 10,
              itemStyle: {
                normal: {
                  color: "transparent",
                  borderColor: "#25759c",
                  borderType: "solid",
                  borderWidth: 4,
                },
              },
              data: yData,
            },
            {
              name: "",
              type: "pictorialBar",
              symbolSize: [150, 30],
              symbolOffset: [-2, 25], // 下部外环
              z: 10,
              itemStyle: {
                normal: {
                  color: "transparent",
                  borderColor: "#25759c",
                  borderType: "solid",
                  borderWidth: 4,
                },
              },
              data: yData,
            },
            {
              type: "bar",
              //silent: true,
              barWidth: "70",
              barGap: "10%", // Make series be overlap
              barCateGoryGap: "10%",
              itemStyle: {
                normal: {
                  color: function (params) {
                    var colors = [
                      "#4587E7",
                      "#35AB33",
                      "#F5AD1D",
                      "#ff7f50",
                      "#da70d6",
                      "#32cd32",
                      "#6495ed",
                    ];
                    // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                    if (params.dataIndex == 0) {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: "rgba(255,61,61,0)", //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: "rgba(255,61,61,0.7)", //指100%处的颜色
                          },
                        ],
                        false
                      );
                    } else if (params.dataIndex == 1) {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: "rgba(255,98,61,0)", //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: "rgba(255,98,61,0.7)", //指100%处的颜色
                          },
                        ],
                        false
                      );
                    } else if (params.dataIndex == 2) {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: "rgba(255,182,56,0)", //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: "rgba(255,182,56,0.7)", //指100%处的颜色
                          },
                        ],
                        false
                      );
                    } else {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: "rgba(0,192,255,0)", //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0.7)", //指100%处的颜色
                          },
                        ],
                        false
                      );
                    }
                    // return colors[params.dataIndex];
                  },
                }

              },
              data: yData,
            },
          ],
        };
        echarts0.setOption(option);
        echarts0.getZr().on("mousemove", (param) => {
          echarts0.getZr().setCursorStyle("default");
        });
      },
    }
  });
</script>

</html>
