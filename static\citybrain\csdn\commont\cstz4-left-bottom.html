<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>城市体征</title>
    <link rel="shortcut icon" href="#" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <link rel="stylesheet" href="/static/css/xiaoguo.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/css/cstz4-left-bottom.css" />
  </head>

  <body>
    <div id="app" class="content">
      <div class="s-c-blue-gradient s-font-50 title s-w7 pointer" @click="openIframe('经济生态')">经济生态</div>
      <div style="position: relative">
        <div class="bgContent"></div>
        <div class="icon"></div>
        <div class="item-box" style="position: absolute; top: 35px; left: 310px">
          <div class="title-box">
            <div>{{jjstData[0].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[0].value}}{{jjstData[0].unit}}</div>
          </div>
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[0].level1}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 230px; left: 249px">
          <div class="title-box">
            <div>{{jjstData[1].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[1].value}}{{jjstData[1].unit}}</div>
          </div>
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[1].level1}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 420px; left: 272px">
          <div class="title-box">
            <div>{{jjstData[2].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[2].value}}{{jjstData[2].unit}}</div>
          </div>
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[2].level1}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 610px; left: 406px">
          <div class="title-box">
            <div>{{jjstData[3].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[3].value}}{{jjstData[3].unit}}</div>
          </div>
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[3].level1}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 35px; right: 178px">
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[4].level1}}</div>
          </div>
          <div class="title-box">
            <div>{{jjstData[4].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[4].value}}{{jjstData[4].unit}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 230px; right: 92px">
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[5].level1}}</div>
          </div>
          <div class="title-box">
            <div>{{jjstData[5].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[5].value}}{{jjstData[5].unit}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 420px; right: 162px">
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[6].level1}}</div>
          </div>
          <div class="title-box">
            <div>{{jjstData[6].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[6].value}}{{jjstData[6].unit}}</div>
          </div>
        </div>
        <div class="item-box" style="position: absolute; top: 610px; right: 340px">
          <div class="box">
            <div class="s-font-35 s-c-white">{{jjstData[7].level1}}</div>
          </div>
          <div class="title-box">
            <div>{{jjstData[7].name}}</div>
            <div class="s-c-yellow-gradient">{{jjstData[7].value}}{{jjstData[7].unit}}</div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script>
  var vm = new Vue({
    el: '#app',
    data() {
      return {
        jjstData: [
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
          {
            level0: '经济生态',
            level1: '-',
            level2: '-',
            name: '-',
            value: '-',
            unit: '',
            orderid: 101,
          },
        ],
      }
    },
    mounted: function () {
      this.initFun()
    },
    methods: {
      initFun() {
        $api('cstz_gy_jjst').then((res) => {
          this.jjstData = res
        })
      },
      openIframe(name) {
        let leftData = {
          type: 'openIframe',
          name: 'djtl-page',
          src: '/static/citybrain/djtl/pages/djtl-page.html',
          width: '7680px',
          height: '2005px',
          left: '0',
          top: '115px',
          zIndex: 100,
          argument: {
            name: name,
            type: '城市体征下钻',
          },
        }
        top.postMessage(JSON.stringify(leftData), '*')
      },
    },

    beforeDestroy() {},
  })
</script>
