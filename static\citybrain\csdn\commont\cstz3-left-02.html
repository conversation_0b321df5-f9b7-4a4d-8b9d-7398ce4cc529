<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />

    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="../jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/auto-tooltip.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>

    <style>
      body {
        margin: 0;
      }
      ul {
        margin: 0;
        padding: 0;
      }
      #cstz3_left1 {
        width: 900px;
        height: 1660px;
        background-color: #092c4e;
        /* padding-top: 20px; */
        overflow: hidden;
        box-sizing: border-box;
      }
      .text {
        color: #fff !important;
        font-size: 55px;
      }

      .djtl_container {
        width: 100%;
        height: 1660px;
        /* overflow-y: scroll; */
        box-sizing: border-box;
      }
      .djtl_item {
        width: 97%;
        height: 400px;
        /* background-color: #132d57; */
        margin: 0 15px;
      }

      .djtl_container::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .djtl_container::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }
      .djtl_item_title {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 32px;
        text-align: center;
        line-height: 46px;
        margin: 25px 0;
      }
      .djtl_item_title .name {
        line-height: 48px;
      }

      .item_small_title {
        color: #fff;
        font-size: 38px;
        text-align: center;
      }
      .djtl_item_content {
        display: flex;
      }
      .right_content,
      .left_content {
        width: 50%;
        height: 330px;
      }
      .echarts_content {
        display: flex;
      }
      .header::after {
        content: '';
        display: inline-block;
        width: 35px;
        height: 35px;
        /* background: url('/sta/img/zlglzx-right/headerght.png') no-repeat; */
        background: url('/static/citybrain/zlglzx/img/zlglzx-right/headerght.png');
      }
      .lineEcharts {
        width: 100%;
        height: 73px;
        /* background: url('../img/zlglzx-right/bg-ec.png') no-repeat; */
        background: url('/static/citybrain/zlglzx/img/zlglzx-right/bg-ec.png');

        background-size: 100% 100%;
      }
      .tb {
        margin-right: 50px;
      }
      .upDown {
        vertical-align: middle;
        margin-left: 20px;
      }
      .mainTel {
        display: flex;
        justify-content: space-between;
        font-size: 34px;
        margin: 20px 0;
      }
      .divbox {
        width: 867px;
        height: 100px;
        background: linear-gradient(
          180deg,
          rgba(0, 69, 255, 0.15) 0%,
          rgba(0, 186, 255, 0.15) 100%
        );
        display: flex;
        justify-content: space-around;
        align-items: center;
      }
      * {
        margin: 0;
        padding: 0;
      }

      .line {
        display: flex;
        justify-content: space-around;
        align-items: center;
        flex-wrap: wrap;
        height: 162px;
        margin-top: -100px;
      }

      .allline1 {
        display: flex;
        justify-content: space-between;
        align-content: space-around;
      }

      .line_1 {
        width: 863px;
        /* position: relative; */
        /* bottom: 220px; */
        /* right: 50px; */
      }

      .line_2 {
        width: 943px;
        position: relative;
        bottom: 170px;
      }

      .line_3 {
        width: 943px;
        position: relative;
        bottom: 140px;
      }

      .line_4 {
        width: 943px;
        position: relative;
        bottom: 240px;
        left: 310px;
      }
      .line1_title {
        text-align: center;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 2px;
        color: #d6e7f9;
        margin-left: 20px;
      }

      .number1 {
        text-align: center;
        font-family: BebasNeue;
        font-size: 45px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 2px;
        position: relative;
        margin-left: 20px;
        background: linear-gradient(to bottom, #ffffff, #ffc460);
        -webkit-background-clip: text;
        color: transparent;
      }

      .unit1 {
        text-align: center;
        font-family: SourceHanSansCN-Medium;
        font-size: 27px;
        font-weight: normal;
        letter-spacing: 1px;
        line-height: 45px;
      }

      .icon {
        display: inline-block;
        width: 20px;
        height: 50px;
        margin-right: -20px;
      }
      .icon img {
        width: 100%;
        height: 100%;
      }
      .line_1 div {
        margin-bottom: 0px;
      }
      .text5 {
        font-family: SourceHanSansCN-Regular;
        font-size: 28px;
        line-height: 40px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 40px;
        letter-spacing: 0px;
        color: #d6e7f9;
        white-space: nowrap;
        text-align: center;
        position: relative;
        right: 5px;
        top: 55px;
      }
      .number4 {
        height: 52px;
        font-family: BebasNeue;
        font-size: 50px;
        font-weight: 800;
        font-stretch: normal;
        line-height: 50px;
        letter-spacing: 0px;
        text-align: center;
        white-space: nowrap;
        background: linear-gradient(to bottom, #dcf6ff, #40a4de);
        -webkit-background-clip: text;
        color: transparent;
        position: relative;
        /* top: 60px; */
      }
    </style>
  </head>

  <body>
    <div id="cstz3_left1">
      <!-- <nav style="padding: 0px 0 0 10px">
        <s-header-title-2 title="党建统领" />
      </nav> -->

      <ul class="djtl_container">
        <li class="djtl_item" style="height: 380px">
          <div class="djtl_item_title">
            <span class="name"> 地区生产总值 </span>
          </div>
          <div id="chart01" style="width: 100%; height: 280px"></div>
        </li>
        <li class="djtl_item" style="height: 520px">
          <div class="djtl_item_title">
            <span class="name"> 12315维权 </span>
          </div>
          <div id="box6" style="margin-bottom: 100px">
            <div class="line" style="margin-bottom: 40px; margin-top: -48px">
              <div>
                <div class="text5">维权总数</div>

                <div
                  style="
                    display: flex;
                    justify-content: center;
                    position: relative;
                    top: 60px;
                    width: 280px;
                  "
                >
                  <div class="number4" style="font-size: 40px">{{wqSum}}</div>
                  
                <!-- <div style="height: 52px; flex: 1">
                  <dv-digital-flop :config="{number:[wqSum],style:{
                        fontSize: 50,
                        gradientColor: ['#dcf6ff', '#40a4de'],
                        gradientParams: [0, 0, 0, 67],
                        gradientWith: 'fill',
                        fontWeight: 800,
                      }}" style="height: 52px" />
                </div> -->

                  <div
                    class="number4"
                    style="font-size: 24px; top: 0; margin-right: 20px"
                  >
                    件
                  </div>
                </div>

                <div>
                  <img src="/static/citybrain/scjg/img/sy/Base5.png" alt="" />
                </div>
              </div>

              <div>
                <div class="text5">投诉量</div>
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    position: relative;
                    top: 60px;
                    width: 280px;
                  "
                >
                  <div class="number4" style="font-size: 40px">{{tsSum}}</div>
                  <div
                    class="number4"
                    style="font-size: 24px; top: 0; margin-right: 20px"
                  >
                    件
                  </div>
                </div>

                <div>
                  <img src="/static/citybrain/scjg/img/sy/Base5.png" alt="" />
                </div>
              </div>

              <div>
                <div class="text5">举报量</div>
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    position: relative;
                    top: 60px;
                    width: 280px;
                  "
                >
                  <div class="number4" style="font-size: 40px">{{jbSum}}</div>
                  <div
                    class="number4"
                    style="font-size: 24px; top: 0; margin-right: 20px"
                  >
                    件
                  </div>
                </div>

                <div>
                  <img src="/static/citybrain/scjg/img/sy/Base5.png" alt="" />
                </div>
              </div>
            </div>
            <div
              id="charts2"
              class="animated fadeInRight"
              style="width: 872px; height: 286px"
            ></div>
          </div>
        </li>
        <li class="djtl_item" style="height: 250px">
          <div class="djtl_item_title">
            <span class="name"> 市场主体概况 </span>
          </div>
          <div style="width: 100%; height: 350px; margin-top: 127px">
            <div class="line line_1">
              <div v-for="(item,index) in scztData" style="display: flex; width: 40%">
                <div class="img">
                  <img :src="`/static/citybrain/scjg/img/sy/sczt_${index+1}.png`" alt="" />
                </div>
                <div class="bazaar-container">
                  <div class="line1_title">{{item.name}}</div>
                  <div class="number1">
                    {{item.value}}
                    <span v-if="index!=2&&index!=3" class="unit1">万</span>
                    <span v-else class="unit1">家</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
        <li class="djtl_item" style="height: 300px; margin-top: 96px">
          <div class="djtl_item_title">
            <span class="name"> 今日空气质量 </span>
          </div>
          <div style="width: 100%; display: flex">
            <div id="chart04" style="width: 50%; height: 330px"></div>
            <div id="chart05" style="width: 50%; height: 330px"></div>
          </div>
        </li>
      </ul>
    </div>
  </body>
</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
  var vm = new Vue({
    el: '#cstz3_left1',
    data: {
      data1: 84848,
      yjCity: [
        {
          main1Tel: '同比2018',
          main1falg: false,
          main1num: '8.8%',
          main2Tel: '同比2019',
          main2falg: false,
          main2num: '4.98%',
          main3Tel: '同比2020',
          main3falg: false,
          main3num: '10.47%',
          nowNum: '5.9',
          nowTel: '2021',
          nowUnit: '万元',
          tagNum: '5.39',
          tagTel: '目标',
          tagUnit: '万元',
          tel: '居民人均可支配收入',
        },
      ],
      // 12315维权
      wqSum:"",
      tsSum:"",
      jbSum:"",
      // 市场主体概括
      scztData:[{
        name:"",
        value:""
      }]
    },
    mounted() {
      this.initFun()

      this.getChart04('chart04', '今日AQI值', 55)
      this.getChart04('chart05', 'PM2.5', 39)

      let y = ['2014', '2015', '2016', '2017', '2018', '2019', '2020', '2021']
      // let x1 = [4569.5, 4704, 5355, 5855, 6155]
      let x2 = [
        3206.64, 3406.48, 3635.01, 3848.62, 4100.23, 4559.91, 4703.95, 5355.44,
      ]
      let zx = [8.3, 7.8, 7.5, 6.2, 5.5, 6.5, 2.8, 9.8]
      this.lineCharts('chart01', y, x2, zx)
    },
    methods: {
      initFun(){
        let that=this
        //横向柱状图
        // this.getChart01('chart01')
        //柱状图
        // this.getChart02('chart02')
        //
        // this.getChart03('chart03')

        // 12315维权
        $api('/scjg/scjg011').then((res) => {
          this.wqSum = +res[0].rights_number
          this.tsSum = +res[0].complaints_number //投诉量
          this.jbSum = +res[0].report_number //举报量
        })

        $api('/scjg/scjg012').then((res) => {        
          let xData = res.map((o) => {
            return o.particular_year
          })
          let wqData=tsData=jbData=[]
          tsData = res.map((o) => {
            return o.complaints_number
          })
          jbData = res.map((o) => {
            return o.report_number
          })
          wqData = res.map((o) => {
            return o.rights_number
          })
          // 12315维权
          this.getERchart(xData,wqData,tsData,jbData)

        })
        //市场主体概括
        $api('/scjg/scjg003').then((res) => {
            console.log("res==>",res);
            that.scztData=res.map(item=>{
              let str={
                name:item.sczt_name,
                value:item.sczt_price
              }
              return str
            })
        })

      },
      getChart01(id) {
        let myEc = echarts.init(document.getElementById(id))
        var myColor = ['#00e9db', '#00c0e9', '#0096f3', '#33CCFF', '#33FFCC']
        option = {
          // backgroundColor: "#0e2147",
          grid: {
            left: '10%',
            top: '12%',
            right: '10%',
            bottom: '10%',
            containLabel: true,
          },
          xAxis: [
            {
              show: false,
            },
          ],
          yAxis: [
            {
              axisTick: 'none',
              axisLine: 'none',
              offset: '27',
              axisLabel: {
                textStyle: {
                  color: '#ffffff',
                  fontSize: '24',
                },
              },
              data: [
                '南昌转运中心',
                '广州转运中心',
                '杭州转运中心',
                '宁夏转运中心',
                '兰州转运中心',
              ],
            },
            {
              axisTick: 'none',
              axisLine: 'none',
              axisLabel: {
                textStyle: {
                  color: '#ffffff',
                  fontSize: '28',
                },
              },
              data: ['5', '4', '3', '2', '1'],
            },
            {
              name: '分拨延误TOP 10',
              nameGap: '50',
              nameTextStyle: {
                color: '#ffffff',
                fontSize: '24',
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0,0,0,0)',
                },
              },
              data: [],
            },
          ],
          series: [
            {
              name: '条',
              type: 'bar',
              yAxisIndex: 0,
              data: [4, 13, 25, 29, 38],
              label: {
                normal: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#ffffff',
                    fontSize: '20',
                  },
                },
              },
              barWidth: 12,
              itemStyle: {
                normal: {
                  color: function (params) {
                    var num = myColor.length
                    return myColor[params.dataIndex % num]
                  },
                },
              },
              z: 2,
            },
            {
              name: '白框',
              type: 'bar',
              yAxisIndex: 1,
              barGap: '-100%',
              data: [99, 99.5, 99.5, 99.5, 99.5],
              barWidth: 20,
              itemStyle: {
                normal: {
                  color: '#0e2147',
                  barBorderRadius: 5,
                },
              },
              z: 1,
            },
            {
              name: '外框',
              type: 'bar',
              yAxisIndex: 2,
              barGap: '-100%',
              data: [100, 100, 100, 100, 100],
              barWidth: 24,
              itemStyle: {
                normal: {
                  color: function (params) {
                    var num = myColor.length
                    return myColor[params.dataIndex % num]
                  },
                  barBorderRadius: 5,
                },
              },
              z: 0,
            },
            {
              name: '外圆',
              type: 'scatter',
              hoverAnimation: false,
              data: [0, 0, 0, 0, 0],
              yAxisIndex: 2,
              symbolSize: 35,
              itemStyle: {
                normal: {
                  color: function (params) {
                    var num = myColor.length
                    return myColor[params.dataIndex % num]
                  },
                  opacity: 1,
                },
              },
              z: 2,
            },
          ],
        }
        myEc.setOption(option)
        myEc.getZr().on('mousemove', param => {
          myEc.getZr().setCursorStyle('default')
        })
      },

      getChart02(id) {
        let myChart = echarts.init(document.getElementById(id))
        let xdata = ['婺城区', '金义新区', '兰溪市', '东阳市', '义乌市']
        let data = [100, 80, 60, 40, 20]
        let data1 = [80, 60, 40, 20, 40]
        var option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '24',
            },
          },
          legend: {
            orient: 'horizontal',
            itemWidth: 18,
            itemHeight: 18,
            top: '8%',
            // icon: 'rect',
            itemGap: 45,
            textStyle: {
              color: '#D6E7F9',
              fontSize: 24,
            },
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '10%',
            top: '30%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: xdata,
              offset: 20,
              axisLine: {
                lineStyle: {
                  color: '#77b3f1',
                  opacity: 0.3,
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                // rotate: -40,
                textStyle: {
                  fontSize: 24,
                  color: 'white',
                },
              },
            },
          ],
          yAxis: [
            {
              name: '单位：个',
              type: 'value',
              nameTextStyle: {
                fontSize: 28,
                color: '#D6E7F9',
                padding: [0, 0, 20, 0],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#77b3f1',
                  opacity: 0.1,
                  width: 2,
                },
              },
              axisTick: {
                show: true,
                lineStyle: {
                  color: '#77b3f1',
                  opacity: 0.5,
                  width: 2,
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 24,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '县(市、区)建设数量',
              type: 'bar',
              barWidth: '25%',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#CBF2FF',
                    },
                    {
                      offset: 0.5,
                      color: '#00C0FF',
                    },
                    {
                      offset: 1,
                      color: '#004F69',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: data,
              label: {
                normal: {
                  show: true,
                  lineHeight: 10,
                  formatter: '{c}',
                  position: 'top',
                  textStyle: {
                    color: '#fff',
                    fontSize: 20,
                  },
                },
              },
            },
            {
              name: '市级接入数量',
              type: 'bar',
              barWidth: '25%',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#CDFFFF',
                    },
                    {
                      offset: 0.5,
                      color: '#22E8E8',
                    },
                    {
                      offset: 1,
                      color: '#006B6B',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: data1,
              label: {
                normal: {
                  show: true,
                  lineHeight: 10,
                  formatter: '{c}',
                  position: 'top',
                  textStyle: {
                    color: '#fff',
                    fontSize: 20,
                  },
                },
              },
            },
          ],
        }
        myChart.setOption(option)
        tools.loopShowTooltip(myChart, option, { loopSeries: true })
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      },

      getChart03(id) {
        let myEcharts = echarts.init(document.getElementById(id))
        let option = {
          color: ['#e86056', '#F5CC53', '#6AE4B2'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          legend: {
            selectedMode: false,
            top: '0px',
            right: '24%',
            textStyle: {
              color: '#fff',
              fontSize: 28,
              fontFamily: 'SourceHanSansCN-Medium',
            },
            itemWidth: 18,
            itemHeight: 8,
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '10%',
            top: '30%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: [
                '8月1日',
                '8月2日',
                '8月3日',
                '8月4日',
                '8月5日',
                '8月6日',
              ],
              splitLine: { show: false },
              axisTick: {
                //y轴刻度线
                show: false,
              },
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisLabel: {
                interval: 0,
                // rotate: 40,
                textStyle: {
                  color: '#ccc',
                  fontSize: 28,
                  fontFamily: 'SourceHanSansCN-Medium',
                },
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              name: '单位：个',
              nameTextStyle: {
                fontSize: 28,
                color: '#D6E7F9',
                padding: [0, 0, 20, 0],
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  color: '#ccc',
                  fontSize: 28,
                  fontFamily: 'SourceHanSansCN-Medium',
                },
              },
            },
          ],
          series: [
            {
              cursor: 'auto',
              name: '',
              symbol: 'circle', //设定为实心点
              symbolSize: 15, //设定实心点的大小
              type: 'line',
              data: [65, 50, 70, 90, 68, 75],
              barWidth: 20,
              itemStyle: {
                normal: {
                  color: 'rgb(0,136,212)',
                  borderColor: 'rgba(0,136,212,0.2)',
                  borderWidth: 30,
                },
              },
              label: {
                show: false, //开启显示
                position: 'top', //在上方显示
                textStyle: {
                  //数值样式
                  color: '#FFFFFF',
                  fontFamily: 'SourceHanSansCN-Regular',
                  fontSize: 28,
                },
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: 'rgba(0, 136, 212, 0.3)',
                      },
                      {
                        offset: 0.8,
                        color: 'rgba(0, 136, 212, 0)',
                      },
                    ],
                    false
                  ),
                  shadowColor: 'rgba(0, 0, 0, 0.1)',
                  shadowBlur: 40,
                },
              },
            },
          ],
        }
        myEcharts.setOption(option)
        myEcharts.getZr().on('mousemove', param => {
          myEcharts.getZr().setCursorStyle('default')
        })
      },

      getChart04(id, kqName, num) {
        let myEc = echarts.init(document.getElementById(id))
        // let color = ['#0D9BCA', '#0bbef9']
        // let data = data;
        var dataArr = num

        option = {
          // backgroundColor: '#fff',
          tooltip: {
            show: false,
          },

          series: [
            {
              name: '内部进度条',
              type: 'gauge',
              radius: '35%',
              center: ['50%', '30%'],
              splitNumber: 10,
              max: 500,
              axisLine: {
                lineStyle: {
                  color: [
                    [dataArr / 1000, '#fefe00'],
                    [1, '#111F42'],
                  ],
                  width: 8,
                },
              },
              axisLabel: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              itemStyle: {
                show: false,
              },
              detail: {
                formatter: function () {
                  return kqName
                },
                offsetCenter: [0, 130],
                textStyle: {
                  padding: [0, 0, 0, 0],
                  fontSize: 20,
                  fontWeight: '700',
                  color: '#fff',
                },
              },
              title: {
                //标题
                show: true,
                offsetCenter: [0, 90], // x, y，单位px
                textStyle: {
                  color: '#fefe00',
                  fontSize: 40, //表盘上的标题文字大小
                  fontWeight: 700,
                  fontFamily: 'PingFangSC',
                },
              },
              data: [
                {
                  name: dataArr,
                  value: dataArr / 2,
                },
              ],
              pointer: {
                show: true,
                length: '75%',
                radius: '20%',
                width: 10, //指针粗细
              },
              animationDuration: 4000,
              color: '#fefe00',
            },
            {
              name: '外部刻度',
              type: 'gauge',
              radius: '50%',
              center: ['50%', '30%'],

              min: 0,
              max: 500,
              splitNumber: 10, //刻度数量
              startAngle: 225,
              endAngle: -45,
              axisLine: {
                show: true,
                lineStyle: {
                  width: 1,
                  color: [[1, 'rgba(0,0,0,0)']],
                },
              }, //仪表盘轴线
              axisLabel: {
                show: true,
                color: '#fff',
                distance: -15,
                formatter: function (v) {
                  switch (v + '') {
                    case '0':
                      return '0'
                    case '100':
                      return '100'
                    case '200':
                      return '200'
                    case '300':
                      return '300'
                    case '400':
                      return '400'
                    case '500':
                      return '500'
                    case '600':
                      return '600'
                    case '700':
                      return '700'
                    case '800':
                      return '800'
                    case '900':
                      return '900'
                    case '1000':
                      return '1000'
                  }
                },
              }, //刻度标签。
              axisTick: {
                show: true,
                splitNumber: 7,
                lineStyle: {
                  color: '#fff', //用颜色渐变函数不起作用
                  width: 1,
                },
                length: -8,
              }, //刻度样式
              splitLine: {
                show: true,
                length: -20,
                lineStyle: {
                  color: '#fff', //用颜色渐变函数不起作用
                },
              }, //分隔线样式
              detail: {
                show: false,
              },
              pointer: {
                show: false,
              },
            },
          ],
        }

        myEc.setOption(option)
        myEc.getZr().on('mousemove', param => {
          myEc.getZr().setCursorStyle('default')
        })
      },
      // 社会矛盾纠纷图表
      lineCharts(dom, name, dataTj, dataLu) {
        const myChart = echarts.init(document.getElementById(dom))
        let colors = [
          '#00C0FF',
          '#22E8E8',
          '#A9DB52',
          '#73DEBD',
          '#26C978',
          '#8CDF6C',
          '#FBD657',
          '#F56679',
          '#E07BCE',
          '#9D50E0',
        ]
        let option = {
          color: colors,
          legend: {
            top: 0,
            left: '180',
            itemWidth: 25,
            itemHeight: 25,
            itemGap: 80,
            textStyle: {
              fontSize: 24,
              color: '#fff',
              padding: [3, 0, 0, 0],
            },
            data: ['地区生产总值', '增速'],
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            offset: 10,
            axisLabel: {
              interval: 0,
              // rotate: -30,
              fontSize: 25,
              color: '#D6E7F9',
            },
            axisLine: {
              lineStyle: {
                color: '#D6E7F9',
              },
              width: 5,
            },
            axisTick: {
              show: false,
            },
            data: name,
          },
          yAxis: [
            {
              name: '单位：亿元',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [0, 0, 0, -15],
              },
              type: 'value',
              position: 'left',

              axisLabel: {
                fontSize: 30,
                color: '#D6E7F9',
              },
              id: 0,
              axisLine: {
                lineStyle: {
                  color: '#D6E7F9',
                },
                width: 5,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(150, 164, 244, 0.3)',
                },
              },
            },
            {
              name: '%',
              position: 'right',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [0, 0, 0, -15],
              },
              id: 1,
              type: 'value',
              axisLabel: {
                fontSize: 30,
                color: '#D6E7F9',
              },
              axisLine: {
                lineStyle: {
                  color: '#D6E7F9',
                },
                width: 5,
              },
              axisTick: {
                show: true,
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(150, 164, 244, 0.3)',
                },
              },
            },
          ],
          series: [
            // {
            //   name: '市区地区生产总值',
            //   type: 'bar',
            //   stack: '总量',
            //   barWidth: '30%',
            //   label: {
            //     show: false,
            //     position: 'insideRight',
            //   },
            //   data: dataJf,
            //   markLine: {
            //     symbol: 'none',
            //     silent: true,
            //     lineStyle: {
            //       color: '#63cf77',
            //     },
            //     label: {
            //       position: 'insideMiddleBottom', //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
            //       formatter: '2022目标：1106亿元，增速9%，占全市比20.55%',
            //       color: '#63cf77',
            //     },
            //     data: [
            //       {
            //         yAxis: 90,
            //       },
            //       {
            //         yAxis: 80,
            //       },
            //     ],
            //   },
            // },
            {
              name: '地区生产总值',
              type: 'bar',
              // stack: "总量",
              barWidth: '30%',
              label: {
                show: false,
                position: 'insideRight',
              },
              data: dataTj,
            },
            {
              name: '增速',
              yAxisIndex: 1,
              type: 'line',
              // stack: "总量",
              barWidth: '30%',
              label: {
                show: false,
                position: 'insideRight',
              },
              data: dataLu,
            },
          ],
        }
        // for (let i = 0; i < data.legend.length; i++) {
        //     option.series.push({
        //         name: data.legend[i],
        //         type: "bar",
        //         stack: "总量",
        //         barWidth: "30%",
        //         label: {
        //             show: false,
        //             position: "insideRight",
        //         },
        //         data: data.data[i],
        //     });
        // }
        myChart.setOption(option)
        tools.loopShowTooltip(myChart, option, { loopSeries: true })
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      },
      setTopEcharts(dom, num, target, all) {
        let myEc = echarts.init(document.getElementById(dom))

        let data = [
          {
            name: '',
            value: num,
            count: all,
          },
        ]

        let yaxisData = []
        let countList = []
        data.map((item) => {
          yaxisData.push(item.name)
          countList.push(item.count)
        })
        let max = Math.max(...countList)

        let xData = []
        let yData = []
        let markPointData = []
        let backgroundData = []
        data.map((item, index) => {
          xData.push(item.name)
          yData.push({
            value: (item.value / item.count) * max,
            symbolBoundingData: max,
          })
          backgroundData.push(max)
        })

        let option = {
          grid: {
            left: '0%',
            right: '1%',
            containLabel: true,
          },
          xAxis: {
            show: false,
            max: max,
            type: 'value',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          yAxis: {
            type: 'category',
            // data: data,
            barWidth: 20,
            inverse: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: 'rgba(134, 208, 255, 1)',
                fontSize: 13,
              },
            },
            data: yaxisData,
          },
          series: [
            {
              type: 'bar',
              data: data,
              barWidth: 14,
              itemStyle: {
                normal: {
                  color: 'rgba(252,252,252,0)',
                },
              },
              z: 1,
            },
            {
              name: '预计达成',
              type: 'line',
              symbol: 'rect',
              symbolSize: [5, 40], // 竖线宽高
              color: '#FFC400',
              data: [target],
            },
            {
              type: 'pictorialBar',
              z: 3,
              symbol: 'rect',
              symbolSize: [10, 30],
              symbolMargin: 2,
              symbolClip: true,
              symbolBoundingData: max,
              data: yData,
              symbolRepeat: 'fixed',
              symbolPosition: 'start',
              itemStyle: {
                normal: {
                  color: {
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#4FACFE',
                      },
                      {
                        offset: 1,
                        color: '#00F2FE',
                      },
                    ],
                  },
                },
              },
            },
            {
              type: 'pictorialBar',
              z: 2,
              symbol: 'rect',
              symbolSize: [10, 30],
              symbolMargin: 2,
              symbolClip: true,
              symbolBoundingData: max,
              data: backgroundData,
              symbolRepeat: 'fixed',
              symbolPosition: 'start',
              itemStyle: {
                normal: {
                  color: 'rgba(84, 217, 255, 0.3)',
                },
              },
            },
          ],
        }

        myEc.setOption(option)
        myEc.getZr().on('mousemove', param => {
          myEc.getZr().setCursorStyle('default')
        })
      },
      getERchart(xData,wqData,tsData,jbData) {
        let charts4 = echarts.init(document.getElementById('charts2'))
        let option4 = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '27',
            },
          },
          grid: {
            bottom: '10%',
          },
          xAxis: {
            type: 'category',
            data: xData,
            axisLine: {
              lineStyle: {
                color: '#fff',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 25,
              },
            },
          },
          yAxis: [
            {
              name: '单位: 件',
              type: 'value',
              nameTextStyle: {
                fontSize: 30,
                padding: [0, 65, 10, 0],
              },
              axisLine: {
                lineStyle: {
                  color: '#fff',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 24,
                },
              },
            },
          ],
          legend: {
            top: '1%',
            itemGap: 32,
            textStyle: {
              fontSize: 27,
              color: 'white',
            },
          },
          series: [
            {
              type: 'bar',
              name: '投诉量',
              barWidth: 30,
              color: 'deepskyblue',
              data: tsData,
            },
            {
              type: 'bar',
              name: '举报量',
              barWidth: 30,
              color: '#ffd75d',
              data: jbData,
            },
            {
              type: 'line',
              name: '维权总数',
              symbol: 'circle',
              symbolSize: 12,
              color: '#ffd75d',
              data: wqData,
            },
          ],
        }

        charts4.setOption(option4)
        tools.loopShowTooltip(charts4, option4, { loopSeries: true })
        charts4.getZr().on('mousemove', param => {
          charts4.getZr().setCursorStyle('default')
        })
      },
    },
  })
</script>
