<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>卫星遥感分析</title>
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>
  <style>
    #yg {
      width: 1150px;
      height: 165px;
      position: relative;
    }

    .title {
      width: 350px;
      height: 70px;
      position: absolute;
      top: 10px;
      left: 32%;
      background-image: url(/static/citybrain/csdn/img/ywt/title-active.png);
      background-size: 100% 100%;
      /* display: none; */
      text-align: center;
      color: rgb(255, 255, 255);
      font-size: 48px;
      line-height: 70px;
    }

    .time-con {
      width: 1100px;
      height: 75px;
      margin: 0px auto;
      position: relative;
      left: 0;
      top: 90px;
    }

    .time-top {
      width: 100%;
      height: 50px;
      margin: 0 auto;
      display: flex;
    }

    .navBar {
      width: 73%;
      height: 40px;
      /* margin: 0 auto; */
      position: relative;
      left: 112px;
    }

    .navBar .el-tabs__item {
      padding: 0 15px !important;
      position: relative;
      height: 40px !important;
      line-height: 40px !important;
      font-size: 34px;
      width: 145px;
      color: #02afef;
      text-align: center;
    }

    .el-tabs__active-bar {
      display: none;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__nav-scroll {
      background-image: url(/static/citybrain/csdn/img/ywt/time-bg.png);
      background-size: 100% 100%;
      width: 720px;
      height: 40px;
      margin: 0 auto;
    }

    .navBar .el-tabs__nav-next {
      background-image: url(/static/citybrain/csdn/img/ywt/time-right.png);
      background-size: 100% 100%;
      animation: fade-in-out-heart-right 1.2s infinite;
      width: 35px;
      margin-top: 2px;
      height: 35px;
    }

    .navBar .is-disabled {
      animation: none;
    }

    .el-icon-arrow-right,
    .el-icon-arrow-left {
      display: none;
    }

    .navBar .is-disabled i {
      display: none;
    }

    .navBar .el-tabs__nav-prev {
      background-image: url(/static/citybrain/csdn/img/ywt/time-left.png);
      background-size: 100% 100%;
      animation: fade-in-out-heart-left 1.2s infinite;
      width: 35px;
      margin-top: 2px;
      height: 35px;
    }

    .navBar .is-active {
      background-image: url(/static/citybrain/csdn/img/ywt/time-active.png);
      background-size: 100% 100%;
      color: #15e5e3;
    }

    .navBar .el-tabs__nav {
      border: none !important;
    }

    .navBar .el-tabs__header {
      border: none !important;
    }

    .time-bottom {
      width: 100%;
      height: 50px;
      margin: 20px auto;
      display: flex;
      justify-content: space-around;
    }

    .time-bottom li {
      width: 180px;
      height: 60px;
      background-image: url(/static/citybrain/csdn/img/ywt/title.png);
      background-size: 100% 100%;
      color: #fff;
      line-height: 60px;
      text-align: center;
      list-style: none;
      font-size: 28px;
    }

    .tab-active {
      background-image: url(/static/citybrain/csdn/img/ywt/title-active.png) !important;
    }
  </style>
</head>

<body>
  <div id="yg">
    <div class="title">{{ygName}}</div>
    <div class="time-con optiontop">
      <div class="time-top">
        <div class="navBar">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane :label="item.text" :name="item.text" :id="item.id" :index="index"
              v-for="(item,index) in timeList" :key="item.id"></el-tab-pane>
          </el-tabs>
        </div>
        <img src="/static/citybrain/csdn/img/ywt/time-t.png" width="35" height="35" title="暂停" v-if="run"
          style="position: absolute; right: 135px; top: 2px" @click="endChange" />
        <img src="/static/citybrain/csdn/img/ywt/time-start.png" width="35" height="35" title="播放" v-else
          style="position: absolute; right: 135px; top: 2px" @click="startChange" />
      </div>
    </div>
    <!-- <div class="time-bottom">
      <li v-for="(item,index) in tabList" :key="index" @click="clickTab(item,index)"
        :class="ygName===item?'tab-active':''">{{item}}</li>
    </div> -->
  </div>
  <script>
    var vm = new Vue({
      el: '#yg',
      data: {
        ygName: 'PM2.5浓度',
        ygId: '33',
        timeList: [
          {
            id: '2010',
            text: '2010年',
          },
          {
            id: '2011',
            text: '2011年',
          },
          {
            id: '2012',
            text: '2012年',
          },
          {
            id: '2013',
            text: '2013年',
          },
          {
            id: '2014',
            text: '2014年',
          },
          {
            id: '2015',
            text: '2015年',
          },
          {
            id: '2016',
            text: '2016年',
          },
          {
            id: '2017',
            text: '2017年',
          },
          {
            id: '2018',
            text: '2018年',
          },
          {
            id: '2019',
            text: '2019年',
          },
          {
            id: '2020',
            text: '2020年',
          },
          {
            id: '2021',
            text: '2021年',
          },
          {
            id: '2022',
            text: '2022年',
          },
          {
            id: '2023',
            text: '2023年',
          }
        ],
        activeName: '2010年',
        activeId: '2010',
        player: null,
        tabList: ['PM2.5浓度', '地表水域', '植被覆盖', '城市裸土', '土壤详情', '地面沉降'],
        run: false,
        currentIndex: 0,
        removeId: 0,
      },
      mounted() {
        // this.getMapData()
        let that = this
        window.addEventListener('message', function (e) {
          if (e.data && e.data.status == 'ygtm') {
            that.ygName = e.data.ygName
            that.ygId = e.data.ygId
            that.getTimeList(e.data.ygName)
          }
          // 配合图层添加时间轴
          if (e.data && e.data.tcglMeg) {
            that.ygName = e.data.tcglMeg.ygName
            that.timeList = []
            for (let index = 0; index < 24; index++) {
              that.timeList.push({
                // id: 'tcgl' + (index + 1),
                id: 2000 + index,
                text: 2000 + index + '年',
                url: e.data.tcglMeg.list[0].url,
                maplayid: 2000 + index + '年',
              })
            }
            // that.timeList = e.data.tcglMeg.list.map((a) => {
            //   return { text: a.name.substr(-4) + '年', url: a.url, id: 'tcgl' + a.id, maplayid: a.name }
            // })
            that.ygId = that.timeList[0].id
            that.activeId = that.timeList[0].id
            that.activeName = that.timeList[0].text
            that.getMapData()
          }
          that.currentIndex = 0
          that.run = false
          clearInterval(that.player)
        })
        top.emiter &&
          top.emiter.on('removeMap', (res) => {
            if (res) {
              that.removeLayer(that.removeId)
            }
          })
      },
      methods: {
        // clickTab(item, index) {
        //   this.ygName = item;
        //   this.getTimeList(item)
        // },
        startChange() {
          this.run = true
          this.player = setInterval(() => {
            if (this.currentIndex < this.timeList.length - 1) {
              this.currentIndex++
              this.activeId = this.timeList[this.currentIndex].id
              this.activeName = this.timeList[this.currentIndex].text
            } else {
              clearInterval(this.player)
              this.run = false
              this.currentIndex = 0
              this.activeId = this.timeList[this.currentIndex].id
              this.activeName = this.timeList[this.currentIndex].text
            }
            this.getMapData()
          }, 5000) //定时函数，根据给的值跳动频率变化，值越大跳动越快
        },
        endChange() {
          this.run = false
          clearInterval(this.player)
        },
        //点击tab切换
        handleClick(tab, event) {
          if (this.player) {
            this.run = false
            clearInterval(this.player)
          }
          this.activeName = tab.name
          this.activeId = tab.$attrs.id
          this.currentIndex = tab.$attrs.index
          this.getMapData()
        },
        //根据图层名字确定时间数组
        getTimeList(name) {
          if (name == 'PM2.5浓度') {
            this.timeList = [
              {
                id: '2024',
                text: '2024年',
              },
              {
                id: '2023',
                text: '2023年',
              },
              {
                id: '2022',
                text: '2022年',
              },
              {
                id: '2021',
                text: '2021年',
              },
              {
                id: '2020',
                text: '2020年',
              },
              {
                id: '2019',
                text: '2019年',
              },
              {
                id: '2018',
                text: '2018年',
              },
              {
                id: '2017',
                text: '2017年',
              },
              {
                id: '2016',
                text: '2016年',
              },
              {
                id: '2015',
                text: '2015年',
              },
              {
                id: '2014',
                text: '2014年',
              },
              {
                id: '2013',
                text: '2013年',
              },
              {
                id: '2012',
                text: '2012年',
              },
              {
                id: '2011',
                text: '2011年',
              },
              {
                id: '2010',
                text: '2010年',
              },
            ]
          } else if (name == '地面沉降') {
            this.timeList = [
              {
                id: '2025-1',
                text: '2025-1Q',
              },
              {
                id: '2024-4',
                text: '2024-4Q',
              },
              {
                id: '2024-3',
                text: '2024-3Q',
              },
              {
                id: '2024-2',
                text: '2024-2Q',
              },
              {
                id: '2024-1',
                text: '2024-1Q',
              },
              {
                id: '2023-4',
                text: '2023-4Q',
              },
              {
                id: '2023-3',
                text: '2023-3Q',
              },
              {
                id: '2023-2',
                text: '2023-2Q',
              },
              {
                id: '2023-1',
                text: '2023-1Q',
              },
              {
                id: '2022-4',
                text: '2022-4Q',
              },
              {
                id: '2022-3',
                text: '2022-3Q',
              },
              {
                id: '2022-2',
                text: '2022-2Q',
              },
              {
                id: '2022-1',
                text: '2022-1Q',
              },
            ]
          } else {
            this.timeList = [
              {
                id: '202503',
                text: '2025.03',
              }, {
                id: '202502',
                text: '2025.02',
              }, {
                id: '202501',
                text: '2025.01',
              }, {
                id: '202412',
                text: '2024.12',
              }, {
                id: '202411',
                text: '2024.11',
              }, {
                id: '202410',
                text: '2024.10',
              }, {
                id: '202409',
                text: '2024.09',
              }, {
                id: '202408',
                text: '2024.08',
              }, {
                id: '202407',
                text: '2024.07',
              }, {
                id: '202406',
                text: '2024.06',
              }, {
                id: '202405',
                text: '2024.05',
              }, {
                id: '202404',
                text: '2024.04',
              }, {
                id: '202403',
                text: '2024.03',
              }, {
                id: '202402',
                text: '2024.02',
              }, {
                id: '202401',
                text: '2024.01',
              }, {
                id: '202312',
                text: '2023.12',
              }, {
                id: '202311',
                text: '2023.11',
              },
              {
                id: '202310',
                text: '2023.10',
              },
              {
                id: '202309',
                text: '2023.09',
              },
              {
                id: '202308',
                text: '2023.08',
              },
              {
                id: '202307',
                text: '2023.07',
              },
              {
                id: '202306',
                text: '2023.06',
              },
              {
                id: '202305',
                text: '2023.05',
              },
              {
                id: '202304',
                text: '2023.04',
              },
              {
                id: '202303',
                text: '2023.03',
              },
              {
                id: '202302',
                text: '2023.02',
              },
              {
                id: '202301',
                text: '2023.01',
              },
              {
                id: '202212',
                text: '2022.12',
              },
              {
                id: '202211',
                text: '2022.11',
              },
              {
                id: '202210',
                text: '2022.10',
              },
              {
                id: '202209',
                text: '2022.09',
              },
              {
                id: '202208',
                text: '2022.08',
              },
              {
                id: '202207',
                text: '2022.07',
              },
              {
                id: '202206',
                text: '2022.06',
              },
              {
                id: '202205',
                text: '2022.05',
              },
              {
                id: '202204',
                text: '2022.04',
              },
              {
                id: '202203',
                text: '2022.03',
              },
              {
                id: '202202',
                text: '2022.02',
              },
              {
                id: '202201',
                text: '2022.01',
              },
            ]
            //城市裸土暂无六、七、八月数据
            // if (name == '城市裸土') {
            //   this.timeList = this.timeList.slice(3, this.timeList.length)
            // }
          }
          this.activeName = this.timeList[0].text
          this.activeId = this.timeList[0].id
          this.getMapData()
        },
        rmLayer(id) {
          top.mapUtil.removeLayer(id)
        },
        //添加地图覆盖物
        //添加地图覆盖物
        getMapData() {
          this.rmLayer(this.removeId)
          $api('/layer_structure', { name: this.ygName + '分析', time: this.activeId }).then((res) => {
            if (res.length > 0) {
              let url = res[0].token
              this.removeId = res[0].four + res[0].above_name
              localStorage.setItem('removeId', this.removeId)
              top.mapUtil.loadTileLayer({
                layerid: this.removeId,
                url: url,
                id: res[0].two || '',
                type: (res[0].four == '历史影像分析' && 'tile') || '',
              })
            }
          })
        },
      },
    })
  </script>
</body>

</html>