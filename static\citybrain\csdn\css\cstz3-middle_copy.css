[v-cloak] {
  display: none;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.op-5 {
  opacity: 0.5;
}
.w-8 {
  width: 1600px !important;
}
.w-2 {
  width: 1200px !important;
}

.w-4 {
  width: 1400px !important;
}

.flex-8 {
  flex: 0.8 !important;
}

.flex-5 {
  flex: 0.5 !important;
}

.flex-3 {
  flex: 0.3 !important;
}

.cursor {
  cursor: pointer;
}
.cursor-no {
  /* cursor: no-drop; */
  cursor: default;
}

.left-2370 {
  height: 800px !important;
  left: 2370px !important;
}

/* 切换每周和每日的图例 */
.work-day {
  width: 800px;
  position: absolute;
  top: 120px;
  left: 2970px;
}

.work-day-item {
  width: 400px;
  height: 60px;
  font-size: 32px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-sizing: border-box;
  color: #fff;
  background-color: #05132c89;
}

.work-day-item i {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-color: #ffc460;
  border-radius: 50%;
}

.work-day-item span {
  flex: 0.9;
}

.work-day-item input {
  width: 40px;
  height: 40px;
}
/* 轮播的文字 */
.loopText {
  width: 500px;
  height: 200px;
  font-size: 80px;
  position: absolute;
  top: 100px;
  left: 10px;
}

.loopText > .alt-body {
  display: flex;
  align-content: center;
}

.loopTime {
  display: inline-block;
  width: 100%;
  text-align: center;
  font-size: 58px;
  line-height: 80px;
  font-weight: 500;
  word-break: keep-all;
  white-space: nowrap;
}
/* 底部时间轴 */

/* 24小时 */
.time-24 {
  width: 3065px;
  height: 400px;
  padding: 0 40px;
  box-sizing: border-box;
  background-color: #05132c;
  border-radius: 20px;
  position: absolute;
  top: 1488px;
  left: 180px;
}

.time-top {
  width: 100%;
  height: 84px;
  line-height: 84px;
  text-align: center;
  position: relative;
  margin-top: -20px;
}

.time-title {
  width: 420px;
  height: 100%;
  margin: 0 auto;
  margin-top: 50px;
  border-top: solid 2px #22e8e8;
  border-bottom: solid 2px #22e8e8;
  font-size: 50px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select {
  display: inline-block;
  width: 300px;
  height: 55px;
  text-align: right;
  position: absolute;
  right: 120px;
  top: 0;
  z-index: 100;
}

.flow-icon {
  width: 25px;
  position: absolute;
  top: 20px;
  right: 14px;
}

.flow-icon > img {
  width: 25px;
}

.ul > div {
  width: 100%;
  height: 60px;
  line-height: 60px;
}

.select ul {
  width: 100%;
  height: 200px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  overflow-y: auto;
  display: none;
  /* background-color: #132c4e; */
}

.select > span {
  display: block;
  font-size: 30px;
  color: #fff;
  position: absolute;
  top: -40px;
  left: 65px;
}

.ul {
  width: 100%;
  height: 60px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  border-radius: 40px;
  margin-top: 25px;
}

.select ul > li {
  width: 100%;
  height: 62px;
  line-height: 62px;
  background-color: #132c4ec2;
  padding-right: 20px;
  box-sizing: border-box;
}

.select ul > li:hover {
  background-color: #359cf8;
}

.ul-active {
  display: block !important;
}

.select > ul > li:first-of-type {
  border-radius: 40px 40px 0 0;
}

.select ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.select ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.select > select {
  width: 300px;
  height: 60px;
  border-radius: 40px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  outline: none !important;
  margin-right: 40px;
}

.select > select > option {
  outline: none !important;
  border-color: transparent !important;
  padding: 20px 0;
}
/* 专题图层 */
.zttc-class {
  min-width: 400px !important;
  height: 500px;
  position: absolute;
  left: 250px;
  overflow-y: auto;
}

.map-img {
  margin-right: 10px;
}

.zttc-class .ul-list {
  /* padding: 20px; */
  box-sizing: border-box;
}

.zttc-class::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.zttc-class::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.zttc-class .text-list {
  margin-right: 20px;
}

.right-item {
  min-width: 340px;
  position: absolute;
  top: 1000px;
  left: 2990px;
}

.bg-blue-gradient_0 {
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.ul-list input[type='checkbox'] {
  width: 30px;
  height: 30px;
  outline: none;
}

.ul-list {
  width: 100%;
  list-style: none;
  /* padding:30px; */
  box-sizing: border-box;
  color: #fff;
  background-color: #091e35;
  border-radius: 5px;
}

.ul-list > li {
  width: 100%;
  padding: 20px 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.ul-list > li:last-child {
  margin-bottom: 0;
}

.ul-list > li:hover {
  background-color: #244477;
}

.text-list {
  display: flex;
  align-items: center;
}

.text-list .icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 20px;
  background-color: #abdb4c;
}

.text-list > span {
  font-size: 34px;
}

/* 地质灾害的弹窗 */
.three-alt-class {
  width: 1600px !important;
  height: 500px !important;
  left: 500px !important;
  top: 850px !important;
}

.search-list {
  flex: 0.3;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0 30px 0;
}

.bg-red-gradient_0 {
  background: linear-gradient(to bottom, #ffcece, #f4f1ff, #ff4949, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.search-item {
  height: 100%;
  font-size: 32px;
  line-height: 60px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.bg-yellow-gradient_0 {
  background: linear-gradient(to bottom, #ffeccb, #f4f1ff, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.btn-class {
  width: 145px;
  height: 54px;
  font-size: 32px;
  padding: 5px 10px;
  color: #fff;
  text-align: center;
  line-height: 54px;
  border-radius: 10px;
  background-color: #11486d;
}

.bg-green-gradient_0 {
  background: linear-gradient(to bottom, #f0ffd7, #f4f1ff, #a9db52, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.s-text-40 {
  font-size: 40px;
}

.btn-class-active {
  background-color: #1755c4;
}

.s-text-28 {
  font-size: 28px;
}
.s-text-32 {
  font-size: 28px;
}

/* 医疗就诊人数的样式 */
.right-content {
  display: flex;
  justify-content: space-between;
}

.right-content > div {
  width: 48%;
}

.right-content span {
  font-size: 32px;
  color: #fff;
}

.right-content span::before {
  display: inline-block;
  content: '';
  width: 20px;
  height: 20px;
  margin-right: 27px;
  border-radius: 3px;
  border: solid 2px #41a3be;
  -webkit-transform: rotate(45deg);
  background-color: #0f3854;
}

.right_2 {
  width: 100%;
  height: 500px;
  margin-top: 10px;
  overflow-y: auto;
  box-sizing: border-box;
}

.right_2::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.right_2::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.right_2_0 {
  margin-bottom: 25px;
}

.right_2_0_0 {
  display: flex;
}

.right_2_0_0_0 {
  display: flex;
  font-family: SourceHanSansCN-Medium;
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  /* line-height: 46px; */
  letter-spacing: 0px;
  /* background-image: linear-gradient(360deg, #ffeaae, #ffdb85, #ffcb5b); */
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  flex: 1;
}

.right_2_0_0_0 > div {
  color: #d6e7f9;
  font-size: 32px;
  margin-left: 20px;
}

.right_2_0_0_1 {
  font-family: BebasNeue;
  font-size: 30px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 48px;
  letter-spacing: 1px;
  color: #c0cedd;
}

.right_2_0_1 {
  width: 100%;
  height: 6px;
  background-color: #2e3f53;
}

.right_2_0_1 > div {
  width: 586px;
  height: 8px;
  background-image: linear-gradient(360deg, #df3c30, #ff9b78);
  margin-top: -1px;
}

.right_2_0_0_2 {
  font-size: 24px !important;
  font-weight: 700;
  color: #00aaf8 !important;
}

.selectImg {
  margin-left: 10px;
}

.dialog {
  opacity: 0.9;
}

.dialog .dialog-title {
  height: 84px;
  line-height: 86px;
  padding: 8px 20px;
  box-sizing: border-box;
  background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.9) 0%, rgba(0, 32, 52, 0.9) 100%),
    linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
  background-blend-mode: normal, normal;
}

.dialog .dialog-title span {
  font-size: 48px;
  line-height: 45px;
  color: #d6e7f9;
}

.dialog .dialog-title i {
  position: absolute;
  right: 20px;
  /* top: 21px; */
  cursor: pointer;
}

.medical-dialog {
  position: absolute;
  left: 1700px;
  top: 100px;
  width: 860px;
  height: 920px;
  z-index: 20;
  background: rgba(9, 30, 53, 0.9);
  box-sizing: border-box;
}

.point-dialog .dialog-tabs {
  margin: 30px 0 0 40px;
}

.point-dialog .dialog-tabs li {
  width: 210px;
  background-image: url('../img/cstz2-middle/border.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  padding-left: 36px;
}

.point-dialog .dialog-tabs li.active {
  color: rgb(26, 213, 253);
  background-image: url('../img/cstz2-middle/border-active.png');
}

.dialog-tabs {
  display: flex;
  font-size: 32px;
  color: rgba(176, 204, 233, 0.6);
  margin: 30px 0 0 60px;
}

.dialog-tabs li {
  width: 178px;
  height: 60px;
  line-height: 60px;
  cursor: pointer;
}

.medical-dialog .dialog-tabs li {
  width: 220px;
  margin-right: 50px;
}

.dialog-tabs li.active {
  color: #fff;
}

.dialog-tabs-content {
  margin-top: 40px;
  padding: 0 20px;
}

.dialog-tabs-content-item {
  height: 600px;
}

.selectTwo {
  position: absolute;
  left: 500px;
  top: 190px;
  width: 220px;
  padding: 0 40px;
  border: 1px solid #afdcfb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #ffffff;
  border-radius: 40px;
  background-color: rgb(19, 44, 78, 0.6);
}

.order-dialog {
  position: absolute;
  left: 2590px;
  top: 440px;
  width: 620px;
  height: 350px;
  z-index: 20;
  background: rgba(9, 30, 53, 0.9);
  box-sizing: border-box;
}

.order-dialog p {
  height: 40px;
  margin: 10px 0 16px 20px;
  line-height: 40px;
  color: #fff;
  font-size: 32px;
}

.order-dialog p:nth-child(3) {
  height: 80px;
}

.order-dialog button {
  width: 100px;
  height: 50px;
  margin-left: 20px;
  background-color: #359cf8;
  font-size: 32px;
  line-height: 50px;
  color: #fff;
}

.case-dialog {
  position: absolute;
  left: 1700px;
  top: 100px;
  width: 620px;
  height: 560px;
  z-index: 20;
  background: rgba(9, 30, 53, 0.9);
  box-sizing: border-box;
}

.case-dialog .table .table-body {
  height: 386px;
  overflow-y: auto;
}

.case-dialog .table .table-body::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.case-dialog .table .table-body::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.case-dialog .table .table-body .table-body-col {
  width: 50%;
}

.case-dialog .table .table-header div {
  width: 50%;
}

.table .table-header {
  display: flex;
  height: 61px;
  margin-bottom: 9px;
  background-color: rgba(15, 60, 103, 0.8);
}

.table .table-header div {
  width: 20%;
  font-family: SourceHanSansCN-Medium;
  font-size: 32px;
  color: #77b3f1;
  text-align: center;
  line-height: 61px;
}

.table .table-header div:last-child {
  width: 40%;
  font-family: SourceHanSansCN-Medium;
  font-size: 32px;
  color: #77b3f1;
  text-align: center;
  line-height: 61px;
}

.table .table-body {
  height: 189px;
  overflow: hidden;
}

.table .table-body .table-body-row {
  display: flex;
  height: 66px;
  margin-bottom: 8px;
  background-color: rgba(21, 50, 86, 0.3);
  line-height: 66px;
  font-family: SourceHanSansCN-Medium;
  font-size: 32px;
  color: #d6e7f9;
}

.table .table-body .table-body-col {
  width: 20%;
  text-align: center;
}

.table .table-body .table-body-col:last-child {
  width: 40%;
}

.yel-icon {
  background-color: #ffc561 !important;
}

/* 悬浮效果 */
.red-list > li:hover {
  background-image: url('../img/cstz2-middle/red-click.png');
}

.red-list > li:hover span {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

.yel-list > li:hover {
  background-image: url('../img/cstz2-middle/yel-click.png');
}

.yel-list > li:hover span {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

/* 普通的悬浮效果 */
.item-list > li:hover {
  background-image: url('../img/cstz2-middle/blue-clicked.png');
}

.item-list > li:hover span {
  font-size: 32px;
  background-image: linear-gradient(#00c2ff 10%, #e5ffff 40%, #caffff 20%);
  -webkit-background-clip: text;
  color: transparent;
}

.blue-ul > li:hover {
  /* background-image: url('../img/cstz2-middle/blue-clicked-img.png'); */
  -moz-box-shadow: 10px 10px 10px black;
  -webkit-box-shadow: 2px 2px 10px black;
  box-shadow: 5px 5px 10px 2px black;
  transition: all 0.5s;
}

.blue-ul > li span {
  font-size: 32px;
  color: #ffffff;
}

.blue-ul > li:hover div {
  background-image: linear-gradient(#00e5ff 10%, #ffffff 60%, #00e5ff 10%);
  -webkit-background-clip: text;
  color: transparent;
}

.blue-ul > li:hover div > span {
  background-image: linear-gradient(#00e5ff 10%, #ffffff 60%, #00e5ff 10%);
  -webkit-background-clip: text;
  color: transparent;
}

/* 红色左边弹窗悬浮效果 */
.red-clicked-img span {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff6363 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

/* .red-ul>li:hover {
  background-image: url('../img/cstz2-middle/red-clicked-img.png')
} */

.red-ul > li span {
  font-size: 32px;
  color: #ffffff;
}

.red-ul > li:hover div {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

.red-ul > li:hover div > span {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

/* 黄色左边弹窗悬浮效果 */
/* .yel-ul>li:hover {
  background-image: url('../img/cstz2-middle/yel-cliched-img.png')
} */

.yel-ul > li span {
  font-size: 32px;
  color: #ffffff;
}

.yel-ul > li:hover div {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

.yel-ul > li:hover div > span {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}

/* 效果 */
.red-color {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.red-click-img {
  background-image: url('../img/cstz2-middle/red-click.png') !important;
}

.red-clicked-img {
  background-image: url('../img/cstz2-middle/red-clicked.png') !important;
}

.yel-color {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.yel-click-img {
  background-image: url('../img/cstz2-middle/yel-click.png') !important;
}

.yel-clicked-img {
  background-image: url('../img/cstz2-middle/yel-clicked.png') !important;
}

.alt-red-click {
  background-image: url('../img/cstz2-middle/red-clicked-img.png') !important;
}

.alt-red-clicked {
  background-image: url('../img/cstz2-middle/red-click-img.png') !important;
}

.alt-yel-clicked {
  background-image: url('../img/cstz2-middle/yel-click-img.png') !important;
}

.blue-clicked {
  background-image: url('../img/cstz2-middle/blue-clicked.png') !important;
}

.alt-blue-clicked {
  background-image: url('../img/cstz2-middle/blue-clicked-img.png') !important;
}

.blue-color {
  background-image: linear-gradient(#00e5ff 10%, #ffffff 60%, #00e5ff 10%);
  -webkit-background-clip: text;
  color: transparent;
}

/* 城市交通单独样式 */
/* 疫情概括 */
.yqgk .yqgk-top {
  width: 100%;
  height: 111px;
  background-image: url('../img/cstz2-middle/yqfk_1.png');
  background-repeat: no-repeat;
  background-size: contain;
  font-size: 32px;
  color: #fff;
  margin-top: 10px;
  padding: 0 40px 0 120px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.yqgk {
  height: 700px !important;
}
.yqgk-center {
  width: 100%;
  height: 200px;
  background-image: url('../img/cstz2-middle/yqfk_4.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0 100px;
  margin-top: 50px;
  padding: 0 80px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}
.yqgk-center-item {
  height: 100%;
  font-size: 32px;
  color: #fff;
  text-align: center;
  background-image: url('../img/cstz2-middle/yqfk_5.png');
  background-repeat: no-repeat;
  background-size: auto;
  background-position: center;
}
.yqgk-center-item p {
  font-size: 60px;
  margin-bottom: 100px;
}
.yqgk-bottom {
  width: 100%;
  height: 200px;
  margin-top: 50px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-content: center;
}
.yqgk-bottom-item {
  height: 100%;
  font-size: 32px;
  padding-left: 220px;
  color: #fff;
}
.yqgk-bottom-item > span {
  display: inline-block;
  margin-top: 80px;
}
.yqgk-bottom-item > p > span {
  display: inline-block;
  font-size: 60px;
}
.yqgk-bottom-item:first-child {
  background-image: url('../img/cstz2-middle/yqfk_2.png');
  background-repeat: no-repeat;
  background-size: auto;
  background-position: 0 20px;
}
.yqgk-bottom-item:last-child {
  background-image: url('../img/cstz2-middle/yqfk_3.png');
  background-repeat: no-repeat;
  background-size: auto;
  background-position: 0 20px;
}
/* 总体设置 */
#app {
  position: absolute;
  /* left: 2120px; */
  left: 0;
}

.bottom {
  width: 3420px;
  position: absolute;
  top: 1550px;
  display: flex;
  justify-content: space-between;
}
.bottom1 {
  width: 3379px;
  height: 372px;
  background: url('../img/cstz3/cstz3-bottom-back1.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 1500px;
  display: flex;
  justify-content: space-between;
  padding: 132px 30px 0;
  box-sizing: border-box;
}
.b-t-color {
  color: #d6e7f9;
}
.bottom-title {
  width: 98%;
  font-size: 40px;
  color: #9ab0c7;
  text-align: center;
  position: absolute;
  top: 70px;
  left: 34px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* background: url(../img/cstz3/title-line.png) no-repeat 22px 23px; */
}
.bottom-title > img {
  width: 1114px;
  height: 10px;
}
.title-list {
  display: flex;
}
.bottom-btn {
  width: 34px;
  height: 210px;
}
.left-btn {
  background: url(../img/cstz3/btn-left-unClik.png);
  background-size: 100% 100%;
}
.left-btn:active {
  background: url(../img/cstz3/btn-left-clik.png);
  background-size: 100% 100%;
}
.right-btn {
  background: url(../img/cstz3/btn-right-unClik.png);
  background-size: 100% 100%;
}
.right-btn:active {
  background: url(../img/cstz3/btn-right-clik.png);
  background-size: 100% 100%;
}
.bottom-center {
  height: 210px;
  width: 96%;
  padding-top: 40px;
  overflow: hidden;
}
.bottom-cont {
  display: flex;
}
.bottom-cont > div {
  margin-right: 35px;
}
.bottom-cont > div:last-child {
  margin-right: 0;
}
.bItem_box {
  /* width:284px; */
  height: 140px;
  line-height: 140px;
  text-align: center;
  font-size: 34px;
  color: #d6e7f9;
  padding: 0 75px;
}
.red-box {
  cursor: pointer;
  background: url(../img/cstz3/btn-red.png) no-repeat;
  background-size: 100% 100%;
}
.red-box-active {
  background: url(../img/cstz3/btn-red-active.png) no-repeat;
  background-size: 100% 100%;
}
.yellow-box {
  background: url(../img/cstz3/btn-yellow.png) no-repeat;
  background-size: 100% 100%;
}
.yellow-box-active {
  background: url(../img/cstz3/btn-yellow-active.png) no-repeat;
  background-size: 100% 100%;
}
.blue-box {
  background: url(../img/cstz3/btn-blue.png) no-repeat;
  background-size: 100% 100%;
}
.blue-box-active {
  background: url(../img/cstz3/btn-blue-active.png) no-repeat;
  background-size: 100% 100%;
}

.item {
  width: 846px;
  height: 283px;
  padding: 20px;
  box-sizing: border-box;
  background-image: url('../img/cstz2-middle/item-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.title {
  width: 420px;
  height: 90px;
  margin: 0 auto;
  text-align: center;
  line-height: 90px;
  background-image: url('../img/cstz2-middle/title-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-size: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title span {
  background-image: linear-gradient(#00c2ff 10%, #e5ffff 40%, #caffff 20%);
  -webkit-background-clip: text;
  color: transparent;
}

.title-img {
  display: inline-block;
  width: 36px;
  height: 36px;
  background-color: #ff4949;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.755);
  font-weight: bold;
  font-size: 30px;
  text-align: center;
  line-height: 36px;
  position: absolute;
  top: -2px;
  right: 10px;
}

.item-list {
  width: 100%;
  height: 82px;
  display: flex;
  justify-content: space-between;
}

.item-list > li {
  width: 156px;
  height: 82px;
  text-align: center;
  line-height: 82px;
  margin-top: 50px;
  background-image: url('../img/cstz2-middle/blue-click.png');
  position: relative;
}
.title-item {
  position: relative;
  width: 250px;
}
.title-item-img {
  width: 30px;
  height: 30px;
  background-color: #ff4949;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  color: rgba(255, 255, 255, 0.755);
  font-size: 26px;
  position: absolute;
  top: -10px;
  right: -5px;
  z-index: 5;
}

.item-list > li span {
  font-size: 32px;
  background-image: linear-gradient(#00c2ff 10%, #e5ffff 40%, #caffff 20%);
  -webkit-background-clip: text;
  color: transparent;
}

.font-30 {
  font-size: 30px !important;
}

.font-26 {
  font-size: 26px !important;
}

/* 左边弹窗 */
.left {
  width: 542px;
  max-height: 1650px;
  position: absolute;
  top: 100px;
  left: 2px;
}

.head {
  width: 100%;
  height: 70px;
  line-height: 100px;
  background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.7) 0%, rgba(0, 32, 52, 0.7) 100%),
    linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
  background-blend-mode: normal, normal;
  padding: 10px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.head > span {
  font-size: 36px !important;
  line-height: 45px;
  font-weight: 500 !important;
  /* color: #7f91a7 !important; */
  color: #fff !important;
}

.img {
  display: inline-block;
  margin: 20px;
  float: right;
  width: 34px;
  height: 34px;
  background-image: url('../img/cstz2-middle/close.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.img:hover {
  background-image: url('../img/cstz2-middle/close-hover.png');
}

.left-body,
.alt-body {
  width: 100%;
  height: calc(100% - 70px);
  box-sizing: border-box;
}

.left-body {
  overflow-y: auto;
}

.left-body::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.left-body::-webkit-scrollbar-thumb {
  /* border-radius: 4px; */
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  /* background: #20aeff; */
  height: 8px;
}

.left-ul {
  width: 100%;
  height: 100%;
}

.left-ul > li {
  position: relative;
  width: 561px;
  height: 170px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
  padding: 10px 0;
  background: #05132c;
  opacity: 0.9;
}

.left-ul > li img {
  display: inline-block;
  margin-left: -175px;
}

/* background-image: url('../img/cstz2-middle/red-clicked-img.png') !important; */
.left-ul > li > span {
  position: absolute;
  top: 31px;
  left: -5px;
  display: inline-block;
  margin-top: 14px;
  margin-left: 165px;
  width: 68%;
  text-align: left;
  font-size: 32px;
}

.left-body ul li .dian::after {
  content: ' ';
  display: inline-block;
  margin-left: 10px;
  width: 20px;
  height: 20px;
  background: url('../img/cstz2-middle/点击.png');
}

.left-ul > li div {
  width: 70%;
  font-size: 60px;
  margin: 0 0 0 80px;
  /* line-height: 35px; */
  background-image: linear-gradient(#caffff 10%, #e5ffff 70%, #1cb4e4 10%);
  -webkit-background-clip: text;
  color: transparent;
  position: absolute;
  top: 83px;
  left: 69px;
  text-align: left;
}

.left-ul > li div > span {
  font-size: 32px;
  background-image: linear-gradient(#caffff 10%, #e5ffff 70%, #1cb4e4 10%);
  -webkit-background-clip: text;
  color: transparent;
}

.left-ul > li div > img {
  width: 17px;
  height: 35px;
  margin-left: 8px;
}

/* 修改弹窗样式 */
.alt-box {
  width: 930px;
  max-height: 800px;
  background-color: #091e35;
  opacity: 0.9;
}
.qyrk-box {
  width: 3304px !important;
  height: 1374px !important;
  max-height: 1374px !important;
  left: 40px !important;
  background: rgba(5, 19, 44, 0.75) !important;
}
.qyrk-box .head {
  height: 84px;
}
.qyrk-box .head > span {
  line-height: 84px !important;
  font-size: 48px !important;
  color: #fff !important;
}
.qyrk-box .bg-img {
  display: flex;
  align-items: center;
  justify-content: center;
}

.alt-box01 {
  width: 930px;
}

.alt-box01 .head span {
  font-size: 40px;
  color: #d6e7f9;
}

.alt-box ul {
  margin-top: 0;
  max-height: 692px;
  overflow-y: auto;
}

.alt-box ul::-webkit-scrollbar {
  width: 4px;
  height: 2px;
}

.alt-box ul::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #20aeff;
  height: 8px;
}

.alt-box li {
  background: #05132c;
  opacity: 0.7;
  padding: 20px;
  color: #fff;
  font-size: 40px;
  position: relative;
  border-bottom: 1px dashed #ccc;
}

.alt-box li:last-child {
  border: none;
}

.alt-box ul li i {
  position: absolute;
  top: 36px;
  left: 16px;
  display: inline-block;
  font-style: normal;
  width: 60px;
  height: 60px;
}

.alt-box ul li i img {
  width: 100%;
}

.alt-box .alt-body .leftUl01 li p .titileArea {
  margin-left: 70px;
  display: inline-block;
  width: 330px;
  font-weight: normal;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.alt-box ul li img {
  margin-left: 30px;
  /* position: absolute;
  top: 25px;
  left: 370px; */
}

.alt-box ul li b {
  font-style: normal;
  font-weight: 400;
  position: absolute;
  top: 25px;
  right: 70px;
}

.alt-box ul li .pone {
  font-size: 32px;
  margin-top: 7px;
  margin-left: 70px !important;
}

.alt-box ul li .pone img {
  margin-left: 20px;
}

.alt-box ul li .ptwo {
  font-size: 28px;
  color: #ccc;
}

.alt-box ul li .ptwo span:nth-child(1) {
  margin-left: 120px;
}

.alt-box ul li .ptwo span {
  margin-right: 70px;
}

/* 第二个弹窗 */
.alt-box {
  width: 930px;
  height: 764px;
  background-color: #091e35;
  border-style: solid;
  border-width: 2px;
  border-image-source: linear-gradient(-32deg, #359df8c9 0%, #afddfbc0 100%);
  border-image-slice: 1;
  position: absolute;
  top: 100px;
  left: 550px;
}

.alt2-box {
  left: 1500px;
}

.text {
  font-size: 32px;
  color: #77b3f1;
  background-color: #00396f;
  padding: 20px;
}

.text:nth-child(2n) {
  background-color: #014789;
}

.table-th {
  width: 100%;
  height: 64px;
  background-color: #00396f;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.th {
  flex: 0.2;
  font-size: 32px;
  text-align: center;
  color: #77b3f1;
}

.table-tr {
  width: 100%;
  height: 500px;
  overflow-y: auto;
}

.table-tr::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.table-tr::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.tr {
  width: 100%;
  /* height: 64px; */
  padding: 12px 0;
  background-color: #0f2b4dc8;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.td {
  flex: 0.2;
  font-size: 32px;
  color: #fff;
  text-align: center;
}

/* ------------------------- 重点区域人流量 start ------------------------- */
.flow-dialog {
  position: absolute;
  top: 900px;
  width: 570px;
  /* height: 150px; */
  max-height: 500px;
  z-index: 20;
  box-sizing: border-box;
}

.flow-dialog p {
  width: 550px;
  height: 160px;
  padding-left: 30px;
  margin: 20px 0 20px 20px;
  font-size: 34px;
  color: #fff;
  line-height: 45px;
  background: #05132c;
  opacity: 0.7;
  position: relative;
  line-height: 65px;
  padding-top: 15px;
}

.flow-dialog p img {
  position: absolute;
  top: 35px;
}

.flow-dialog span {
  margin-left: 118px;
  font-size: 32px;
}

.flow-dialog p b {
  display: block;
  font-size: 56px;
  margin-left: 118px;
  background-image: linear-gradient(#a4ecf4 10%, #ffffff 60%, #00e5ff 10%);
  -webkit-background-clip: text;
  color: transparent;
}

.flow-dialog i {
  position: absolute;
  top: 0;
  right: 30px;
  font-size: 36px;
  font-style: normal;
  color: #fff;
}

.flow-dialog i:hover {
  cursor: pointer;
}

.point-dialog {
  position: absolute;
  top: 56px;
  width: 860px;
  height: 850px;
  z-index: 20;
  background: rgba(9, 30, 53, 0.9);
  opacity: 0.9;
  border: 2px solid;
  -o-border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 2 2;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 2 2;
  box-shadow: 0 3px 35px 0 #000;
  box-sizing: border-box;
}

.point-dialog .dialog-title {
  height: 80px;
  line-height: 100px;
  background: linear-gradient(0deg, rgba(0, 32, 52, 0.9), rgba(0, 89, 147, 0.9));
}

.point-dialog .dialog-title span {
  font-size: 48px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  margin-left: 52px;
  background: linear-gradient(0deg, #caffff, #caffff 0, #fff 0, #8eccf5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* .point-dialog .dialog-title i.close {
  position: absolute;
  top: -15px;
  right: 40px;
  font-size: 62px;
  cursor: pointer;
  font-style: normal;
  font-family: Source Han Sans CN;
  font-weight: 500;
  background: linear-gradient(0deg, #caffff, #caffff 0, #fff 0, #8eccf5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
} */

.sex-chart-content {
  margin-top: 80px;
}

.sex-chart-content > div {
  height: 150px;
  margin-bottom: 40px;
}

.sex-chart-content span {
  display: inline-block;
  height: 100px;
  line-height: 100px;
  vertical-align: bottom;
  font-size: 28px;
  color: #fff;
}

/* .sex-chart-content span:first-child {
  margin-right: 20px;
} */

.sex-chart-content span:last-child {
  margin-left: 20px;
}

.sex-chart-content img {
  width: 40px;
  margin: 0 10px;
}

/* ------------------------- 重点区域人流量 end ------------------------- */

/* ------------------------- 生产指数弹窗 start ------------------------- */
.product-dialog {
  position: absolute;
  left: 1000px;
  top: 500px;
  width: 1473px;
  height: 657px;
  z-index: 20;
  background: rgba(9, 30, 53, 0.9);
  box-sizing: border-box;
}

/* .product-dialog .dialog-title i.close {
  position: absolute;
  top: -10px;
  right: 40px;
  font-size: 80px;
  cursor: pointer;
  font-style: normal;
  font-family: Source Han Sans CN;
  font-weight: 500;
  background: linear-gradient(0deg, #caffff, #caffff 0, #fff 0, #8eccf5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
} */

.product-dialog .dialog-content {
  display: flex;
  align-items: center;
  height: calc(100% - 84px);
}

.product-dialog .dialog-content-left .container {
  width: 292px;
  height: 340px;
  opacity: 0.8;
  margin-left: 20px;
  background: url('/static/citybrain/csdn/img/cstz-2771.png') no-repeat;
}

.product-dialog .dialog-content-left .container span {
  font-family: BebasNeue;
  font-size: 45px;
}

.product-dialog .dialog-content-left .container > div {
  position: relative;
  padding-top: 45px;
  text-align: center;
}

.product-dialog .dialog-content-left .container img {
  position: absolute;
  left: 230px;
  top: 50px;
}

.product-dialog .dialog-content-left .container .c-text {
  display: inline-block;
  width: 100%;
  margin-top: 100px;
  text-align: center;
  font-family: SourceHanSansCN-Regular;
  font-size: 36px;
}

.product-dialog .dialog-content-right {
  flex: 1;
  height: 100%;
  margin-left: 60px;
}

.product-dialog .dialog-content-right ul {
  height: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.product-dialog .dialog-content-right li {
  width: 50%;
  height: 120px;
  background: url('/static/citybrain/csdn/img/cstz2-middle/exponent_bg_unchecked.png') 0 100% no-repeat;
  font-family: SourceHanSansCN-Normal;
}

.product-dialog .dialog-content-right li.active {
  width: 50%;
  height: 120px;
  background: url('/static/citybrain/csdn/img/cstz2-middle/exponent_bg_checked.png') 0 100% no-repeat;
  font-family: SourceHanSansCN-Normal;
}

.product-dialog .dialog-content-right .s-title_1 {
  padding-left: 50px;
  font-size: 32px;
  color: #d6e7f9;
}

.product-dialog .dialog-content-right div {
  width: 480px;
  margin-top: 15px;
  text-align: center;
  cursor: pointer;
}

.product-dialog .dialog-content-right div span {
  font-weight: 600;
  font-family: BebasNeue;
}

.product-dialog .dialog-content-right div img {
  vertical-align: bottom;
}

/* ------------------------- 生产指数弹窗 end ------------------------- */

/* ------------------------- 生产指数图表弹窗 end ------------------------- */

.indicator-chart-dialog {
  position: absolute;
  left: 1500px;
  top: 200px;
  width: 1473px;
  height: 657px;
  z-index: 20;
  background: rgba(9, 30, 53, 0.9);
  box-sizing: border-box;
}

/* 
.indicator-chart-dialog .dialog-title i.close {
  position: absolute;
  top: -10px;
  right: 40px;
  font-size: 80px;
  cursor: pointer;
  font-style: normal;
  font-family: Source Han Sans CN;
  font-weight: 500;
  background: linear-gradient(0deg, #caffff, #caffff 0, #fff 0, #8eccf5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
} */

.indicator-chart-dialog .dialog-content {
  padding-top: 40px;
}

.indicator-chart-dialog #indicatorChart {
  height: 500px;
}

/* ------------------------- 生产指数图表弹窗 end ------------------------- */
