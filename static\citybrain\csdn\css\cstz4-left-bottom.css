* {
  padding: 0;
  margin: 0;
}

.pointer {
  cursor: pointer;
}
.content {
  width: 1923px;
  height: 917px;
  background: url('/static/citybrain/csdn/img/cstz4/bg.png') no-repeat;
  overflow: hidden;
}

.title {
  line-height: 85px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.bgContent {
  width: 100%;
  height: 832px;
  background: url('/static/citybrain/csdn/img/cstz4/circle-bg.png') no-repeat;
  background-position: center center;
  animation: roate 15s infinite linear;
}

.icon {
  width: 139px;
  height: 139px;
  background: url('/static/citybrain/csdn/img/cstz4/icon-jj.png') no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes roate {
  0% {
    transform: rotateZ(0);
    -ms-transform: rotateZ(0);
    -moz-transform: rotateZ(0);
    -webkit-transform: rotateZ(0);
    -o-transform: rotateZ(0);
  }

  100% {
    transform: rotateZ(360deg);
    -ms-transform: rotateZ(360deg);
    -moz-transform: rotateZ(360deg);
    -webkit-transform: rotateZ(360deg);
    -o-transform: rotateZ(360deg);
  }
}

.box {
  width: 227px;
  height: 177px;
  text-align: center;
  background: url('/static/citybrain/csdn/img/cstz4/left-bg.png') no-repeat;
}

.title-box {
  font-size: 35px;
  color: #fff;
}
.item-box {
  width: 585px;
  display: flex;
  align-items: center;
}
