<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>地图工具栏</title>
  <script src="/static/citybrain/hjbh/js/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <!-- 引入样式 -->
  <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> -->
  <!-- 引入组件库 -->
  <!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
  <style>
    body,
    html {
      margin: 0;
    }

    li,
    ul {
      list-style: none;
    }

    [v-cloak] {
      display: none;
    }

    /* 设置滚动条的样式 */
    ::-webkit-scrollbar {
      width: 10px;
    }

    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      border-radius: 5px;
    }

    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(35, 144, 207, 0.1);
    }

    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(27, 146, 215, 0.4);
    }

    .toolbar {
      width: 820px;
      height: 500px;
      background: rgba(3, 24, 39, 0.88);
      border: 2px solid #afdcfb;
      border-radius: 25px;
      box-sizing: border-box;
      position: relative;
      padding: 20px;
      box-sizing: border-box;
    }

    .el-cascader-menu {
      color: #b6d1f0;
      font-size: 26px;
    }

    .el-cascader-node.in-active-path,
    .el-cascader-node.is-active,
    .el-cascader-node.is-selectable.in-checked-path {
      font-size: 26px;
    }

    .el-cascader-panel {
      height: 450px;
    }

    .el-scrollbar__wrap {
      overflow-y: scroll !important;
      overflow-x: hidden;
      height: 100%;
    }

    .el-cascader-menu {
      width: 258px;
    }

    .el-cascader-node__prefix {
      left: 3px;
    }

    .el-cascader-node {
      padding: 10px 30px 10px 20px;
    }

    .el-cascader-node:not(.is-disabled):focus,
    .el-cascader-node:not(.is-disabled):hover {
      background: #133f81;
    }

    .el-cascader-panel.is-bordered {
      border: none
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <div class="toolbar">
      <el-cascader-panel :options="options" :show-all-levels="false" @change="change"></el-cascader-panel>
    </div>
  </div>
</body>

<script>
  var souName = new Vue({
    el: '#app',
    data() {
      return {
        options: [
          {
            value: '天气',
            label: '天气',
            children: [{
              value: '天气效果',
              label: '天气效果',
            }]
          }, {
            value: '绘图工具',
            label: '绘图工具',
            children: [
              {
                value: '绘点',
                label: '绘点',
              }, {
                value: '绘面',
                label: '绘面',
              }, {
                value: '绘线',
                label: '绘线',
              }, {
                value: '绘圆',
                label: '绘圆',
              }, {
                value: '绘矩形',
                label: '绘矩形',
              }, {
                value: '清除',
                label: '清除',
              }
            ]
          }, {
            value: '工具类',
            label: '工具类',
            children: [
              {
                value: '放大',
                label: '放大',
              }, {
                value: '缩小',
                label: '缩小',
              }
            ]
          }, {
            value: '气象',
            label: '气象',
            children: [
              {
                value: '湿度',
                label: '湿度',
                children: [
                  {
                    value: '未来一小时',
                    label: '未来一小时',
                  }, {
                    value: '未来3小时',
                    label: '未来3小时',
                  }, {
                    value: '未来6小时',
                    label: '未来6小时',
                  }, {
                    value: '未来12小时',
                    label: '未来12小时',
                  }, {
                    value: '未来一小时(移除)',
                    label: '未来一小时(移除)',
                  }
                ]
              }, {
                value: '温度',
                label: '温度',
                children: [
                  {
                    value: '未来一小时',
                    label: '未来一小时',
                  }, {
                    value: '未来3小时',
                    label: '未来3小时',
                  }, {
                    value: '未来6小时',
                    label: '未来6小时',
                  }, {
                    value: '未来12小时',
                    label: '未来12小时',
                  }
                ]
              }, {
                value: '云量',
                label: '云量',
                children: [
                  {
                    value: '未来一小时',
                    label: '未来一小时',
                  }, {
                    value: '未来3小时',
                    label: '未来3小时',
                  }, {
                    value: '未来6小时',
                    label: '未来6小时',
                  }, {
                    value: '未来12小时',
                    label: '未来12小时',
                  }
                ]
              }, {
                value: '风速',
                label: '风速',
                children: [
                  {
                    value: '未来一小时',
                    label: '未来一小时',
                  }, {
                    value: '未来3小时',
                    label: '未来3小时',
                  }, {
                    value: '未来6小时',
                    label: '未来6小时',
                  }, {
                    value: '未来12小时',
                    label: '未来12小时',
                  }
                ]
              }
            ]
          }, {
            value: '添加热网',
            label: '添加热网',
            children: [
              {
                value: '三维热力图',
                label: '三维热力图',
              }, {
                value: '清除三维热力图',
                label: '清除三维热力图',
              }, {
                value: '添加道路效果',
                label: '添加道路效果',
              }, {
                value: '移除道路效果',
                label: '移除道路效果',
              }, {
                value: '添加路网',
                label: '添加路网',
              }, {
                value: '移除路网',
                label: '移除路网',
              }
            ]
          }, {
            value: '测量工具',
            label: '测量工具',
            children: [
              {
                value: '面积测量',
                label: '面积测量',
              }, {
                value: '面积测量清除',
                label: '面积测量清除',
              }
            ]
          }, {
            value: '设置透明度',
            label: '设置透明度',
            children: [
              {
                value: '添加图层',
                label: '添加图层',
                children: [
                  {
                    value: '添加气象图层',
                    label: '添加气象图层',
                  }, {
                    value: '面积测量清除',
                    label: '面积测量清除',
                  }
                ]
              }, {
                value: '设置值',
                label: '设置值(0-1)',
                children: [
                  {
                    value: '-',
                    label: '-',
                  }, {
                    value: '+',
                    label: '+',
                  }
                ]
              }
            ]
          }, {
            value: '设置倾斜度',
            label: '设置倾斜度',
            children: [
              {
                value: '添加倾斜摄影(不)',
                label: '添加倾斜摄影(不)',
              }, {
                value: '添加倾斜摄影',
                label: '添加倾斜摄影',
              }, {
                value: '移除倾斜摄影',
                label: '移除倾斜摄影',
              }
            ]
          }



        ]
      }
    },

    mounted() {

    },
    methods: {
      change(val) {
        let index = val.length - 1
        console.log(val[index]);
      },
    },
  })
</script>

</html>