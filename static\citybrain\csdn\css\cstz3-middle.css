/* 总体设置 */
#app {
  position: absolute;
  /* left: 2120px; */
  left: 0;
}
#main01 {
  /* display: none; */
  position: relative;
  left: 2130px;
}
.img-btn {
  position: absolute;
  left: -730px;
  width: 70px;
  height: 70px;
  background-color: #132c4e;
  background-image: url('/static/citybrain/csdn/img/cstz3/城市体征.png');
  background-position: center;
  background-repeat: no-repeat;
  border: 1px solid #fff;
  border-right: transparent;
  cursor: pointer;
}
.bottom-title {
  width: 3172px;
  height: 100px;
  background-color: #1d4664;
  position: absolute;
  top: 1555px;
  text-align: center;
  font-size: 50px;
  color: #fff;
  line-height: 104px;
  left: 127px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 110px;
  box-sizing: border-box;
}
p {
  padding: 0;
  margin: 0;
}
ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.opacity-5 {
  opacity: 0.5;
}
.mouse-no {
  pointer-events: none;
}
.mouse-pointer {
  cursor: pointer;
}
.mouse-not {
  /* cursor: not-allowed; */
  cursor: default;
}
/* 效果 */
.red-color {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
/* 轮播 */

.el-carousel {
  width: 100%;
  overflow: hidden;
}

.el-carousel__item {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.el-carousel__indicators {
  display: flex;
  justify-content: center;
}

.el-carousel__button {
  display: none;
}
/* 拥堵的弹窗样式 */
.alt-box {
  width: 930px;
  height: 764px;
  background: rgba(3, 24, 39, 0.88);
  /* background-color: #091e35; */
  border: 1px solid #359df8c9;
  /* border-style: solid;
  border-width: 2px;
  border-image-source: linear-gradient(-32deg, #359df8c9 0%, #afddfbc0 100%);
  border-image-slice: 1; */
  position: absolute;
  top: 200px;
  left: 630px;
  border-radius: 50px 50px 50px 0px;
}
.alt-box01 {
  overflow: hidden;
  width: 930px;
}

.alt-box01 .head span {
  font-size: 40px;
  color: #d6e7f9;
}

.alt-box ul {
  margin-top: 0;
  max-height: 688px;
  overflow-y: auto;
}

.alt-box ul::-webkit-scrollbar {
  width: 4px;
  height: 2px;
}

.alt-box ul::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #20aeff;
  height: 8px;
}

.alt-box li {
  background: #0f2b4d;
  opacity: 0.7;
  padding: 10px;
  color: #fff;
  font-size: 40px;
  position: relative;
  border-bottom: 1px dashed #ccc;
  margin-bottom: 0px;
  cursor: pointer;
}

.alt-box li:last-child {
  border: none;
}

.alt-box ul li i {
  position: absolute;
  top: 22px;
  left: 16px;
  display: inline-block;
  font-style: normal;
  width: 80px;
}

.alt-box ul li i img {
  width: 100%;
}

.alt-box .alt-body .leftUl01 li p .titileArea {
  margin-left: 70px;
  display: inline-block;
  width: 330px;
  font-weight: normal;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.alt-box ul li b {
  font-style: normal;
  font-weight: 400;
  position: absolute;
  top: 20px;
  right: 70px;
}

.alt-box ul li .pone {
  font-size: 32px;
  margin-top: 7px;
  margin-left: 70px !important;
}

.alt-box ul li .pone img {
  margin-left: 20px;
}

.alt-box ul li .ptwo {
  font-size: 28px;
  color: #ccc;
  padding-left: 110px;
  padding-right: 40px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

/* 拥堵的区县 */

.twoLong {
  position: absolute;
  top: 200px;
  left: 2830px;
}

.table-content {
  display: none;
}

.table {
  width: 440px;
  background-color: #091e35;
  opacity: 0.9;
}

.twoLong #infoTable {
  background-color: #132c4e;
}

.titletext img:first-child {
  width: 26px;
  height: 30px;
  margin: 0 20px 0 10px;
}

.titletext img.flag-img {
  position: absolute;
  top: 0;
  right: 0;
}
#tab-first,
#tab-second {
  height: 100px;
  font-size: 50px;
  line-height: 100px;
  padding-left: 20px;
}
.el-tabs__item,
.is-top {
  color: #fff;
}
.tabletitle {
  width: 440px;
  height: 60px;
  background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.5) 0%, rgba(0, 32, 52, 0.5) 100%),
    linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
  background-blend-mode: normal, normal;
  display: flex;
  text-align: center;
  opacity: 0.8;
}

.tableline {
  width: 440px;
  min-height: 50px;
  max-height: 300px;
  overflow-y: auto;
  /* border-bottom: 1px solid #a8d8fb; */
  /* margin: 30px 0 0 72px; */
  /* display: flex; */
  justify-content: space-between;
  align-items: center;
}

.tableline::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.tableline::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.tableline img {
  width: 50px;
}

.titletext {
  margin-left: 30px;
  width: 400px;
  text-align: center;
  line-height: 65px;
  font-family: SourceHanSansCN-Bold;
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #d6e7f9;
  text-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.6);
  position: relative;
}

.titlenumber {
  font-family: BebasNeue;
  font-size: 62px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 45px;
  letter-spacing: 1px;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0px 2px 27px 0px rgba(0, 0, 0, 0.67);
  margin-top: 12px;
}

.linename {
  flex: 1;
  height: 60px;
  /* margin: 0px 10px 0px 30px; */
  padding: 10px 0 6px 40px;
  font-family: SourceHanSansCN-Regular;
  font-size: 30px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #d6e7f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.linenumber {
  font-family: SourceHanSansCN-Medium;
  font-size: 36px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 34px;
  letter-spacing: 0px;
  background: linear-gradient(to bottom, #c2e5ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.6);
}

.register-table {
  background-color: #132c4e;
}

.linename .check-btn {
  position: relative;
  float: right;
  width: 26px;
  height: 26px;
  margin: -28px 33px 0 0;
  /* background: url('./img/cstz2-middle/span.png') no-repeat;
background-size: 100%; */
}

.linename .check-btn img {
  width: 100%;
}

.linename.active {
  width: 440px;
  background: #244477;
}

.line-text {
  display: inline-block;
  width: 80%;
  text-align: left;
  margin-left: 10px;
}

.linename.active .line-text {
  background: linear-gradient(to bottom, #f0b55f, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.linename .check-img {
  position: relative;
  float: right;
  width: 26px;
  height: 26px;
  background: url('/static/citybrain/scjg/img/sy/table_uncheck_bg.png') no-repeat;
  margin: 8px 33px 0 0;
}

.linename.active .check-img {
  background: url('/static/citybrain/scjg/img/sy/table_check_bg.png') no-repeat;
}

.three-table {
  background-color: #132c4e;
}

.four-table {
  background-color: #132c4e;
}

/* 左边弹窗 */
.left {
  width: 542px;
  max-height: 1650px;
  position: absolute;
  top: 200px;
  left: 50px;
}

.head {
  width: 100%;
  height: 70px;
  line-height: 100px;
  /* background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.7) 0%, rgba(0, 32, 52, 0.7) 100%),
    linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
  background-blend-mode: normal, normal; */
  background: linear-gradient(0deg, #00aae2, #073446);
  padding: 10px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.head > span {
  font-size: 34px !important;
  line-height: 45px;
  font-weight: 500 !important;
  /* color: #7f91a7 !important; */
  color: #fff !important;
}

.img {
  display: inline-block;
  margin: 20px;
  float: right;
  width: 34px;
  height: 34px;
  background-image: url('../img/cstz2-middle/close-hover.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.left-body,
.alt-body {
  width: 100%;
  height: calc(100% - 84px);
  box-sizing: border-box;
}

.left-body {
  overflow-y: auto;
}

.left-body::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.left-body::-webkit-scrollbar-thumb {
  /* border-radius: 4px; */
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  /* background: #20aeff; */
  height: 8px;
}

.left-ul {
  width: 100%;
  height: 100%;
}

.left-ul > li {
  position: relative;
  width: 561px;
  height: 170px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
  padding: 10px 0;
  background: #05132c;
  opacity: 0.9;
}

.left-ul > li img {
  display: inline-block;
  margin-left: -175px;
}

/* background-image: url('../img/cstz2-middle/red-clicked-img.png') !important; */
.left-ul > li > span {
  position: absolute;
  top: 31px;
  left: -5px;
  display: inline-block;
  margin-top: 14px;
  margin-left: 165px;
  width: 68%;
  text-align: left;
  font-size: 32px;
}

.left-body ul li .dian::after {
  content: ' ';
  display: inline-block;
  margin-left: 10px;
  width: 20px;
  height: 20px;
  background: url('../img/cstz2-middle/点击.png');
}

.left-ul > li div {
  width: 70%;
  font-size: 60px;
  margin: 20px 0 0 80px;
  /* line-height: 35px; */
  background-image: linear-gradient(#caffff 10%, #e5ffff 70%, #1cb4e4 10%);
  -webkit-background-clip: text;
  color: transparent;
  position: absolute;
  top: 83px;
  left: 69px;
  text-align: left;
}

.left-ul > li div > span {
  font-size: 32px;
  background-image: linear-gradient(#caffff 10%, #e5ffff 70%, #1cb4e4 10%);
  -webkit-background-clip: text;
  color: transparent;
}

.left-ul > li div > img {
  width: 17px;
  height: 35px;
  margin-left: 8px;
}
.blue-ul > li span {
  color: #fff;
}
.left_img {
  width: 337px;
  height: 103px;
}
/* 底部样式 */

.bottom {
  width: 3420px;
  height: 284px;
  background: url('/static/citybrain/csdn/img/cstz3-middle/bottom_bg.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 1630px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 200px;
  box-sizing: border-box;
}
.bottom-btn {
  width: 100px;
  height: 100px;
}
.left-btn {
  background: url('/static/citybrain/csdn/img/cstz3-middle/left.png');
  background-size: 100% 100%;
}
.right-btn {
  background: url('/static/citybrain/csdn/img/cstz3-middle/right.png');
  background-size: 100% 100%;
}
.bItem_item {
  margin: 0 20px;
  position: relative;
}
.bItem_item:first-child {
  margin-left: 0;
}
.bItem_item:last-child {
  margin-right: 0;
}
.bItem_box {
  width: 284px;
  height: 140px;
  line-height: 140px;
  text-align: center;
  font-size: 34px;
  color: #d6e7f9;
  padding: 0 70px;
}
.red-box {
  cursor: pointer;
  background: url(/static/citybrain/csdn/img/cstz3/btn-red.png) no-repeat;
  background-size: 100% 100%;
}
.yellow-box {
  background: url(/static/citybrain/csdn/img/cstz3/btn-yellow.png) no-repeat;
  background-size: 100% 100%;
}
.blue-box {
  background: url(/static/citybrain/csdn/img/cstz3/btn-blue.png) no-repeat;
  background-size: 100% 100%;
}

.bottom-center {
  width: 96%;
  overflow: hidden;
}
.bottom-cont {
  display: flex;
}
.title-img {
  display: inline-block;
  width: 45px;
  height: 45px;
  background-color: #ff4949;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.755);
  font-weight: bold;
  font-size: 30px;
  text-align: center;
  line-height: 45px;
  position: absolute;
  top: -2px;
  right: 10px;
}

/* 金华市特色指标 */

.top-alt-list {
  width: 3000px;
  height: 180px;
  background-image: url('/static/citybrain/csdn/img/cstz3/btn3-middle-top-bg.png');
  background-size: 100% 100%;
  font-size: 32px;
  position: absolute;
  left: 189.5px;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
}
.top-alt-name {
  width: 100%;
  font-size: 40px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-alt-name > img {
  flex: 1;
}
.top-alt-name > span {
  width: 300px;
  text-align: center;
  display: inline-block;
}
.top-alt-list > ul {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.top-li-active {
  color: #fff !important;
  background-image: url('/static/citybrain/csdn/img/cstz3/btn3-middle-top-active.png') !important;
}
.top-alt-list > ul > li {
  height: 90px;
  line-height: 90px;
  text-align: center;
  padding: 0 25px;
  color: #d2f6fe;
  background-image: url('/static/citybrain/csdn/img/cstz3/btn3-middle-top.png');
  background-size: 100% 100%;
}

/* 下拉框 */
.qxzb-02 {
  position: relative;
  left: 3470px;
  top: 200px;
}
.select02 {
  display: inline-block;
  width: 500px;
  height: 70px;
  text-align: right;
  position: absolute;
  right: 160px;
  top: 0;
  z-index: 100;
  border: 1px solid #fff;
}
.select02 .flow-icon {
  width: 25px;
  position: absolute;
  top: 15px;
  right: 32px;
}
.select02 .flow-icon1 {
  top: 5px;
  transform: rotateX(180deg);
}
.select02 .ul {
  height: 100%;
  font-size: 40px;
  border-radius: 0;
  border: 0px solid transparent;
}
.select02 .ul > div {
  height: 100%;
  line-height: 70px;
}
.select02 .ul > ul > li:first-of-type {
  border-radius: 0;
}
.select02 .ul ul > li:hover {
  background-color: #244377;
}
.select02 .ul ul > li:hover > span {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.select02 .ul ul > li {
  background-color: #122b4a;
}
