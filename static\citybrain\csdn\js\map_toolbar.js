var mapTool = new Vue({
    el: '#app',
    data() {
        return {
            spatialAnalToolItems: [
                { name: '剖切工具', },
                { name: '地表透明', },
                { name: '通视分析', },
                { name: '穹顶分析', },
                { name: '日照分析', },
                { name: '城市天际线', },
                { name: '缓冲区分析', },
                { name: '淹没分析', },
                { name: 'BIM剖析', },
                { name: '环点漫游', },
                { name: '飞行漫游', },
                { name: '白模渲染', },
                { name: '二维热力', },
                { name: '格网分析', },
                { name: '仿真视域', },
                { name: '地图卷帘', },
                { name: '框选统计', },
                { name: '虚实融合', },
                { name: '多维查询', },
                { name: '建筑智能排布', },
                { name: '场景特效模拟', },
                { name: '城市小品', },
                { name: '限高分析', },
            ],
            mapIndex: -1,
            currDraw: null,//标绘按钮互斥控制变量
            drawFlag: '',
            sliderValue: 10,
            min: 0,
            max: 10,
            rzPlay: false,
            rzDate: new Date(),
            rzTime: new Date().getHours() * 60 + new Date().getMinutes() * 1, //分钟数
            timeString: new Date().toLocaleString(),
            interval: null,
            //淹没分析滑块
            baseSliderValue: 0,
            baseMin: 0,
            baseMax: 200,
            floodTimeSliderValue: 0,
            floodTimeMin: 0,
            floodTimeMax: 100,

            tableData: [
                {
                    name: '医院',
                    code: '医院',
                    icon: '医院c',
                    num: '',
                    unit: '家',
                    avg: '',
                    arr: [],
                },
                {
                    name: '学校',
                    icon: '学校c',
                    num: '',
                    code: '学校',
                    unit: '所',
                    avg: '',
                    arr: [],
                },
                {
                    name: '公交车站',
                    code: '公交站',
                    icon: '公交车站c',
                    num: '',
                    unit: '个',
                    avg: '',
                    arr: [],
                },
                {
                    name: '公共厕所',
                    code: '公厕',
                    icon: '公共厕所c',
                    num: '',
                    unit: '个',
                    avg: '',
                    arr: [],
                },
                {
                    name: '运动场馆',
                    code: '游泳场池,高尔夫球场,场馆',
                    icon: '运动场馆c',
                    num: '',
                    unit: '个',
                    avg: '',
                    arr: [],
                },
                {
                    name: '公园广场',
                    code: '植物园,公园,广场,动物园',
                    icon: '公园广场c',
                    num: '',
                    unit: '个',
                    avg: '',
                    arr: [],
                },
            ],
            renderLyrsValue: '',
            renderLyrs: [
                {
                    checkOrNot: true,
                    value: 'map婺城区白模',
                    label: '婺城区白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map金义新区白模',
                    label: '金义新区白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map兰溪市白模',
                    label: '兰溪市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map东阳市白模',
                    label: '东阳市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map义乌市白模',
                    label: '义乌市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map永康市白模',
                    label: '永康市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map浦江县白模',
                    label: '浦江县白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map武义县白模',
                    label: '武义县白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map磐安县白模',
                    label: '磐安县白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map开发区白模',
                    label: '开发区白模',
                    disabled: true,
                },
            ],
            renderOptionsValue: '',
            renderOptions: [
                {
                    value: 'single',
                    label: '单色渲染',
                },
                {
                    value: 'ramp',
                    label: '分级渲染',
                },
            ],
            colorRampOpts: [
                {
                    value: 'ramp1',
                    label: '红黄蓝',
                },
                {
                    value: 'ramp2',
                    label: '橙紫',
                },
                {
                    value: 'ramp3',
                    label: '粉彩',
                },
                {
                    value: 'ramp4',
                    label: '青蓝',
                },
                {
                    value: 'ramp5',
                    label: '橙变',
                },
            ],
            colorRampValue: '',
            singleColorValue: 'rgb(64, 158, 255)',
            singleRenderer: '',
            rampRenderer: '',
            hotLyrs: [
                {
                    checkOrNot: true,
                    value: 'tcgl学校',
                    label: '学校',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl医院',
                    label: '医院',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl公交车站',
                    label: '公交车站',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl避难场所',
                    label: '避难场所',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl应急物资',
                    label: '应急物资',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl住宅区[2925]-点',
                    label: '住宅区',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'overflow',
                    label: '……',
                    disabled: true,
                },
            ],
            renderHotValue: '',
            gridLyrs: [
                {
                    checkOrNot: true,
                    value: 'tcgl学校',
                    label: '学校',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl医院',
                    label: '医院',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'tcgl公交车站',
                    label: '公交车站',
                    disabled: true,
                },
                // {
                //   value: 'tcgl避难场所',
                //   label: '避难场所',
                //   disabled: true,
                // },
                // {
                //   value: 'tcgl应急物资',
                //   label: '应急物资',
                //   disabled: true,
                // },
                {
                    checkOrNot: true,
                    value: 'tcgl住宅区[2925]-点',
                    label: '住宅区',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'overflow',
                    label: '……',
                    disabled: true,
                },
            ],
            renderGridValue: '',
            swipeLyrs: [
                // {
                //     checkOrNot: true,
                //     value: 'black',
                //     label: '矢量底图',
                //     disabled: true,
                // },
                {
                    checkOrNot: true,
                    value: 'img2008',
                    label: '2008年历史卫星影像',
                    disabled: false,
                },
            ],
            swipeLyrValue: '',
            queryValue: '',
            queryLyrs: [],
            videoLyrs: [
                // {
                //     value: 'map金华市体育中心',
                //     label: '金华市体育中心',
                //     disabled: true,
                //     checkOrNot: true,
                //     channelId: {
                //         11: '33070291581314000733',
                //         12: '33079954011328680120',
                //         13: '33079955031311088274',
                //         14: '33079955031311085254',
                //         15: '33070291581314000381',
                //         16: '33079955031311006442',
                //         17: '33079955031311089765',
                //         18: '33079955031311008834',
                //         19: '33070291581314000377',
                //         110: '33079955001320082005',
                //         111: '33079955001320082006',
                //     },
                //     heightOffset: 49.7,
                //     zoomTo: {
                //         11: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         12: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         13: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         14: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         15: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         16: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         17: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         18: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         19: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         110: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //         111: {
                //             x: 119.64102979556569,
                //             y: 29.02875012616247,
                //             z: 962.7947349455208,
                //             heading: 350.09506537981497,
                //             tilt: 62.308368046366894,
                //         },
                //     },
                //     geo: [
                //         [119.6375, 29.0443],
                //         [119.6388, 29.0451],
                //         [119.6393, 29.0429],
                //         [119.6386, 29.0423],
                //     ],
                //     videoType: 'HLS',
                // },
                // {
                //     value: 'map倾斜摄影_浙师大',
                //     label: '倾斜摄影_浙师大',
                //     disabled: true,
                //     checkOrNot: true,
                //     channelId: '33070255111328001188',
                //     heightOffset: 70,
                //     zoomTo: {
                //         x: 119.64517319171227,
                //         y: 29.13089265702911,
                //         z: 871.4335835920647,
                //         heading: 354.2371656363017,
                //         tilt: 48.38892849216801,
                //     },
                //     geo: [
                //         [119.6451, 29.1381],
                //         [119.6441, 29.1379],
                //         [119.6436, 29.14],
                //         [119.6446, 29.1402],
                //     ],
                //     videoType: 'HLS',
                // },
                {
                    value: '底图',
                    label: '倾斜摄影底图',
                    disabled: false,
                    checkOrNot: false,
                    // channelId: '33079952001321087131',
                    channelId: {
                        1: '33079952001321087131',
                        2: '33070252001320080009',
                        3: '33070299041311041639',
                    },
                    zoomTo: {
                        1: {
                            x: 119.64248728622745,
                            y: 29.082393211161403,
                            z: 239.3099555131048,
                            heading: 165.26566089871625,
                            tilt: 40.18155422708016,
                        },
                        2: {
                            x: 119.64477681805529,
                            y: 29.108673350741128,
                            z: 337.9310757247731,
                            heading: 167.37350531080918,
                            tilt: 36.99753820286347,
                        },
                        3: {
                            x: 119.64627430418847,
                            y: 29.105571155233253,
                            z: 373.3052971418947,
                            heading: 16.953658811478554,
                            tilt: 19.27400097150425,
                        },
                    },
                    geo: [
                        [119.6436, 29.08027],
                        [119.6424, 29.08007],
                        [119.64224, 29.0815],
                        [119.6432, 29.0817],
                    ],
                    heightOffset: 41,
                    videoType: 'HLS',
                },
            ],
            videoToMapRes: [
                {
                    groupName: '示范点位',
                    videoGroup: [
                        {
                            name: '市民广场',
                            code: '33079952001321087131',
                            flyTo: {
                                x: 119.64248728622745,
                                y: 29.082393211161403,
                                z: 239.3099555131048,
                                heading: 165.26566089871625,
                                tilt: 40.18155422708016,
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '解放西路与回溪街交叉口',
                            code: '33070252001320080009',
                            flyTo: {
                                x: 119.64477681805529,
                                y: 29.108673350741128,
                                z: 337.9310757247731,
                                heading: 167.37350531080918,
                                tilt: 36.99753820286347,
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '八一北街与解放东路路口南',
                            code: '33070299041311041639',
                            flyTo: {
                                x: 119.64627430418847,
                                y: 29.105571155233253,
                                z: 373.3052971418947,
                                heading: 16.953658811478554,
                                tilt: 19.27400097150425,
                            },
                            videoType: 'HLS',
                        },
                    ],
                    groupVideoToMapFlag:false,
                },
                {
                    groupName: '世贸中心周边',
                    videoGroup: [
                        {
                            name: '双龙南街李渔路东',
                            code: '33079999001321042140',
                            flyTo: {
                                "x": 119.64539327357713,
                                "y": 29.083812550538845,
                                "z": 617.6128753386438,
                                "heading": 261.6159202187536,
                                "tilt": 38.904805074658064
                            },
                            videoType: 'HLS',
                        },
                    ],
                    groupVideoToMapFlag:false,
                },
                {
                    groupName: '江北银泰周边',
                    videoGroup: [
                        {
                            name: '婺城八一北街与解放东路',
                            code: '33070251001311081085',
                            flyTo: {
                                "x": 119.64855442087952,
                                "y": 29.106531261529952,
                                "z": 414.409316342324,
                                "heading": 271.49378489787233,
                                "tilt": 31.692354556583552
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '婺城解放东路与新华街路口',
                            code: '33070251001311085306',
                            flyTo: {
                                "x": 119.64749916989484,
                                "y": 29.106377716496034,
                                "z": 384.71030215360224,
                                "heading": 88.65064666450576,
                                "tilt": 15.990305609493458
                            },
                            videoType: 'HLS',
                        },
                        // {
                        //     name: '婺城解放东路与新华街路口1',
                        //     code: '33070251001311087058',
                        //     flyTo: {
                        //         "x": 119.6465668635783,
                        //         "y": 29.106491733983034,
                        //         "z": 333.8342643752694,
                        //         "heading": 101.34492045467844,
                        //         "tilt": 33.81472673011827
                        //     },
                        //     videoType: 'HLS',
                        // },
                        {
                            name: '火腿大楼（解放东路新华街）',
                            code: '33070299001321049914',
                            flyTo: {
                                "x": 119.64830944698313,
                                "y": 29.106564488859025,
                                "z": 276.4599846508354,
                                "heading": 183.23095776586095,
                                "tilt": 12.383123200446033
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '婺城新华街与中山路',
                            code: '33070251001321082472',
                            flyTo: {
                                "x": 119.64668284174107,
                                "y": 29.103135385152484,
                                "z": 387.5584816429764,
                                "heading": 97.82371573381572,
                                "tilt": 24.053586999184457
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '东市街将军路南',
                            code: '33070299041311048143',
                            flyTo: {
                                "x": 119.66272002686205,
                                "y": 29.105140210332337,
                                "z": 778.2630534097552,
                                "heading": 347.41066691396065,
                                "tilt": 6.754035824335343
                            },
                            videoType: 'HLS',
                        },
                        // {
                        //     name: '东市街将军路北',
                        //     code: '33070299041320040350',
                        //     flyTo: {
                        //         "x": 119.6620266002008,
                        //         "y": 29.107678391829356,
                        //         "z": 594.9694565478712,
                        //         "heading": 167.5938629996045,
                        //         "tilt": 23.20397394226806
                        //     },
                        //     videoType: 'HLS',
                        // },
                        // {
                        //     name: '东市街将军路西',
                        //     code: '33070299041320040349',
                        //     flyTo: {
                        //         "x": 119.6626250052509,
                        //         "y": 29.105512342041695,
                        //         "z": 667.0804749829695,
                        //         "heading": 87.08166470613601,
                        //         "tilt": 0.49995315121159717
                        //     },
                        //     videoType: 'HLS',
                        // },
                    ],
                    groupVideoToMapFlag:false,
                },
                {
                    groupName: '万达广场周边',
                    videoGroup: [
                        {
                            name: '光南路东市南街北',
                            code: '33070399041320040266',
                            flyTo: {
                                "x": 119.66846666396033,
                                "y": 29.095185835724944,
                                "z": 824.3521468983963,
                                "heading": 143.49990550597605,
                                "tilt": 10.472279826390475
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '东市南街丹溪路北',
                            code: '33071099001321080119',
                            flyTo: {
                                "x": 119.67376177864078,
                                "y": 29.085749010516857,
                                "z": 397.4420589329675,
                                "heading": 157.6269142123379,
                                "tilt": 0.499973150302767
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '金东多湖万达东北角',
                            code: '33070351001320080094',
                            flyTo: {
                                "x": 119.6742290072268,
                                "y": 29.09428929627451,
                                "z": 229.11181917507201,
                                "heading": 316.6746637590928,
                                "tilt": 54.71141364204638
                            },
                            videoType: 'HLS',
                        },
                    ],
                    groupVideoToMapFlag:false,
                },
                {
                    groupName: '金华之心',
                    videoGroup: [
                        // {
                        //     name: '李渔路江南逸江南逸1幢1单元',
                        //     code: '33079999001321043356',
                        //     flyTo: {
                        //         "x": 119.625761,
                        //         "y": 29.0807,
                        //         "z": 824.3521468983963,
                        //         "heading": 143.49990550597605,
                        //         "tilt": 10.472279826390475
                        //     },
                        //     videoType: 'HLS',
                        // },
                        {
                            name: '永康街李渔路东',
                            code: '33079999051320040321',
                            flyTo: {
                                "x": 119.637752,
                                "y": 29.082633,
                                "z": 397.4420589329675,
                                "heading": 157.6269142123379,
                                "tilt": 0.499973150302767
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '永康街李渔路北',
                            code: '33079999051320040326',
                            flyTo: {
                                "x": 119.63703,
                                "y": 29.082849,
                                "z": 229.11181917507201,
                                "heading": 316.6746637590928,
                                "tilt": 54.71141364204638
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '永康街李渔路南',
                            code: '33079999051320040325',
                            flyTo: {
                                "x": 119.637225,
                                "y": 29.082252,
                                "z": 229.11181917507201,
                                "heading": 316.6746637590928,
                                "tilt": 54.71141364204638
                            },
                            videoType: 'HLS',
                        },
                        {
                            name: '永康街李渔路西',
                            code: '33079999051320040324',
                            flyTo: {
                                "x": 119.636843,
                                "y": 29.082431,
                                "z": 229.11181917507201,
                                "heading": 316.6746637590928,
                                "tilt": 54.71141364204638
                            },
                            videoType: 'HLS',
                        },
                    ],
                    groupVideoToMapFlag:false,
                },
            ],
            videoLyrValue: '底图',
            queryFieldDict: {},//多维查询图层alias与name的字典，图层返回name存在重复现象，因此使用不会重复的alias字段将二者对应
            queryFieldValue: '',
            queryFields: [],
            querymultiQueryInput: '',
            multiQueryLyrValue: '',
            multiQueryResult: [],
            mixControlPanel: false,
            buildLayout_length: 20,
            buildLayout_width: 20,
            buildLayout_foreDistance: 20,
            buildLayout_backDistance: 20,
            buildLayout_floorNum: 10,
            buildLayout_floorHeight: 3,
            autoLayoutResult: [],
            featureHeight: '',
            effectFlag: '',
            fireEffectlocations: [],
            fogEffectlocations: [],
            fountainEffectlocations: [],
            coordPickResult: [],
            //城市小品参数
            gltfLyrValue: '',
            gltfLyrs: [
                { disabled: false, label: '标牌-减速慢行', value: '标牌-减速慢行.glb', symbol: { height: 40, width: 30 } },
                { disabled: false, label: '标牌-限速40', value: '标牌-限速40.glb', symbol: { height: 40, width: 30 } },
                { disabled: false, label: '标牌-向左', value: '标牌-向左.glb', symbol: { height: 40, width: 30 } },
                { disabled: false, label: '树木', value: 'AcerPlatanoides.glb', symbol: { height: 40, width: 30 } },
                { disabled: false, label: '建筑', value: 'building.gltf', symbol: { height: 80, width: 60 } },
            ],
            heightRestrictLyrs: [
                // {
                //     checkOrNot: true,
                //     value: 'map婺城区白模',
                //     label: '婺城区白模',
                //     disabled: true,
                // },
                // {
                //     checkOrNot: true,
                //     value: 'map金义新区白模',
                //     label: '金义新区白模',
                //     disabled: true,
                // },
                {
                    checkOrNot: true,
                    value: 'map兰溪市白模',
                    label: '兰溪市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map东阳市白模',
                    label: '东阳市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map义乌市白模',
                    label: '义乌市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map永康市白模',
                    label: '永康市白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map浦江县白模',
                    label: '浦江县白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map武义县白模',
                    label: '武义县白模',
                    disabled: true,
                },
                {
                    checkOrNot: true,
                    value: 'map磐安县白模',
                    label: '磐安县白模',
                    disabled: true,
                },
                // {
                //     checkOrNot: true,
                //     value: 'map开发区白模',
                //     label: '开发区白模',
                //     disabled: true,
                // },
            ],
            heightRestrictlyrValue:'',
            heightRestrictValue:10,
            heightRestrictRegionClear:false,
        }
    },
    watch: {
        // videoLyrValue(val) {
        //     //监听视频融合图层的改变
        //     this.removeVideoMixWidget('')
        //     $('#multi_mixControlPanel').css({ display: 'none' })
        //     $('#single_mixControlPanel').css({ display: 'none' })
        //     $('#multi_mixControlPanel_stadium').css({ display: 'none' })
        //     if (val) {
        //         if ('底图' == val) {
        //             $('#multi_mixControlPanel').css({ display: 'block' })
        //         } else if ('map金华市体育中心' == val) {
        //             $('#multi_mixControlPanel_stadium').css({ display: 'block' })
        //         } else {
        //             $('#single_mixControlPanel').css({ display: 'block' })
        //         }
        //     }
        // },
    },
    computed: {
        formatRzTime() {
            return this.formatTooltip(this.rzTime)
        },
    },
    mounted() {
        this.beforeClose()
        let that = this
        window.addEventListener('message', function (e) {
            let res = e.data
            console.log(e, 'iframe传递过来的数据')
            that.toolChange(res)
        })
        this.creditAuth()
        top.window.mixPlayers = {}
    },
    methods: {
        changeRzTime(val) {
            this.timeString = `${new Date(this.rzDate).toLocaleDateString()} ${this.formatTooltip(this.rzTime)}`
            top.window.mapUtil.mapview.environment.lighting.date = new Date(this.timeString)
            clearInterval(this.interval)
            this.rzPlay = false
        },

        lightsliderChange(val) {
            this.timeString = `${new Date(this.rzDate).toLocaleDateString()} ${this.formatTooltip(this.rzTime)}`
            top.window.mapUtil.mapview.environment.lighting.date = new Date(this.timeString)
            top.window.mapUtil.mapview.environment.lighting.directShadowsEnabled = true //具体根据checkbox来定
            // top.window.mapUtil.mapview.environment.lighting.type = "sun"
        },

        ctRzPlay() {
            this.rzPlay = !this.rzPlay
            if (this.rzPlay) {
                this.interval = setInterval(() => {
                    this.rzTime++
                    this.lightsliderChange(this.rzTime)
                    if (this.rzTime == 1439) {
                        this.rzTime = 0
                    }
                }, 10)
            } else {
                clearInterval(this.interval)
                this.interval = null
            }
        },

        formatTooltip(val) {
            let h = parseInt((val / 60) % 24)
            let m = parseInt(val % 60)
            h < 10 ? (h = '0' + h) : h
            m < 10 ? (m = '0' + m) : m
            return h + ':' + m
        },

        toolChange(res) {
            let that = this
            let title = {
                测量: { toolTitle: '测量工具', panelName: '#measureMainPanel', removeBtn: '移除测量结果' },
                标绘: { toolTitle: '标绘工具', panelName: '#drawMainPanel', removeBtn: '移除标绘内容' },
                空间分析: { toolTitle: '空间分析工具', panelName: '#spatialMainPanel', removeBtn: '移除空间分析结果' },
            }
            if (res.data) {
                //切换工具面板内容
                $('#header').html(`<span>${title[res.data.toolName].toolTitle}</span>`)
                $('#mainPanel').html(``)
                $('#mainPanel').append($(title[res.data.toolName].panelName))
                $(title[res.data.toolName].panelName).css({ display: 'block' })

                //停止绘制工具
                top.window.drawTool?.cancel()

                //移除工具按钮
                $('#removeAllTools').html(`<span>${title[res.data.toolName].removeBtn}</span>`)
                $('#removeAllTools').click(() => {
                    that.cleanall({ toolPanel: title[res.data.toolName].toolTitle })
                })
            }
        },

        measureFun(name) {
            let that = this
            switch (name) {
                case '面积测量':
                    top.window.areaMeasurementWidget?.destroy()
                    top.window.areaMeasurementWidget = null
                    if ($('#area-box > div > div')[0].innerHTML == '使用') {
                        $('#area-box > div > div')[0].innerHTML = '关闭'
                        if (!document.getElementById('areaTool')) {
                            let widget = top.document.createElement('div')
                            widget.id = 'areaTool'
                            $('#area-box').append(widget)
                        }
                        top.window.areaMeasurementWidget = top.ArcGisUtils.createAreaMeasurementWidget(
                            top.mapUtil.mapview,
                            document.getElementById('areaTool')
                        )
                    } else {
                        $('#area-box > div > div')[0].innerHTML = '使用'
                    }
                    // if (!top.window.areaMeasurementWidget) {
                    //     if (!document.getElementById('areaTool')) {
                    //         let widget = top.document.createElement('div')
                    //         widget.id = 'areaTool'
                    //         $('#area-box').append(widget)
                    //     }
                    //     top.window.areaMeasurementWidget = top.ArcGisUtils.createAreaMeasurementWidget(
                    //         top.mapUtil.mapview,
                    //         document.getElementById('areaTool')
                    //     );
                    // } else {
                    //     top.window.areaMeasurementWidget?.destroy()
                    //     top.window.areaMeasurementWidget = null
                    // }
                    break
                case '距离测量':
                    top.window.distanceMeasurementWidget?.destroy()
                    top.window.distanceMeasurementWidget = null
                    if ($('#dis-box > div > div')[0].innerHTML == '使用') {
                        $('#dis-box > div > div')[0].innerHTML = '关闭'
                        if (!document.getElementById('disTool')) {
                            let widget = top.document.createElement('div')
                            widget.id = 'disTool'
                            $('#dis-box').append(widget)
                        }
                        top.window.distanceMeasurementWidget = top.ArcGisUtils.measuringDistance(
                            top.mapUtil.mapview,
                            document.getElementById('disTool')
                        )
                    } else {
                        $('#dis-box > div > div')[0].innerHTML = '使用'
                    }
                    // if (!top.window.distanceMeasurementWidget) {
                    //     if (!document.getElementById('disTool')) {
                    //         let widget = top.document.createElement('div')
                    //         widget.id = 'disTool'
                    //         $('#dis-box').append(widget)
                    //     }
                    //     top.window.distanceMeasurementWidget = top.ArcGisUtils.measuringDistance(
                    //         top.mapUtil.mapview,
                    //         document.getElementById('disTool')
                    //     );
                    // } else {
                    //     top.window.distanceMeasurementWidget?.destroy()
                    //     top.window.distanceMeasurementWidget = null
                    // }
                    break
                case '角度测量':
                    this.stopAngleMeasure()
                    if ($('#angle-box > div > div')[0].innerHTML == '使用') {
                        $('#angle-box > div > div')[0].innerHTML = '关闭'
                        top.window.angleMeasurementWidget = new top.ArcGisUtils.AngleMeasure({ view: top.mapUtil.mapview })
                        $('#angleTool').html(`
                        <div id="angleValue" class="esri-widget"></div>
                        <div class="esri-button" onclick="mapTool.angleBtnFun()">新测量</div>
                        `)
                    } else {
                        $('#angle-box > div > div')[0].innerHTML = '使用'
                    }
                    break
                case '坐标拾取':
                    if ($('#coordPick-box > div > div')[0].innerHTML == '使用') {
                        $('#coordPick-box > div > div')[0].innerHTML = '关闭'
                        $('#coordInfoTitle').html(`请在地图上点选位置`)
                        if (!top.window.drawTool) top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
                        top.window.drawTool.draw('point')
                        top.window.drawTool.sketchVM.on('create', function (e) {
                            if (e.state == 'start') {
                                let geometry = e.graphic.geometry
                                $('#coordInfoTitle').html(`CGCS2000坐标：`)
                                that.coordPickResult.push({
                                    coord: `点${that.coordPickResult.length + 1}<br>${Math.round(geometry.x * 1000000) / 1000000}<br>${Math.round(geometry.y * 1000000) / 1000000
                                        }`,
                                })
                            }
                        })
                    } else {
                        this.stopcoordPick()
                    }
                    break
            }
        },
        angleBtnFun() {
            top.window.angleMeasurementWidget?.destroy()
            top.window.angleMeasurementWidget = null
            if ($('#angleValue')) $('#angleValue').html('请在地图上绘制夹角')
            top.window.angleMeasurementWidget = new top.ArcGisUtils.AngleMeasure({ view: top.mapUtil.mapview })
            top.window.angleMeasurementWidget.measure((val) => {
                console.log(val)
                $('#angleTool').html(`<div id="angleValue" class="esri-widget">角度为${val}°</div>
          <div class="esri-button" onclick="mapTool.angleBtnFun()">新测量</div>
          `)
            })
        },
        stopAngleMeasure() {
            $('#angleTool').html(``)
            top.window.angleMeasurementWidget?.destroy()
            top.window.angleMeasurementWidget = null
        },
        stopcoordPick() {
            $('#coordPick-box > div > div')[0].innerHTML = '使用'
            $('#coordInfoTitle').html(``)
            this.coordPickResult = []
            this.drawToolDestroy()
        },
        // async drawFun(type) {
        //     let that = this
        //     let drawType = {
        //         点: { drawName: 'point', btnId: 'drawPoint-box', pnlId: 'drawPoint-box-panel', symbol: null },
        //         线: { drawName: 'polyline', btnId: 'drawPolyline-box', pnlId: 'drawPolyline-box-panel', symbol: null },
        //         面: { drawName: 'polygon', btnId: 'drawPolygon-box', pnlId: 'drawPolygon-box-panel', symbol: null },
        //         圆: { drawName: 'circle', btnId: 'drawCircle-box', pnlId: 'drawCircle-box-panel', symbol: null },
        //         矩形: { drawName: 'rectangle', btnId: 'drawRect-box', pnlId: 'drawRect-box-panel', symbol: null },
        //         虚线: {
        //             drawName: 'polyline',
        //             btnId: 'drawdotline-box',
        //             pnlId: 'drawdotline-box-panel',
        //             symbol: {
        //                 color: [255, 0, 0],
        //                 width: 5,
        //                 style: "dash",
        //                 type: "simple-line",
        //             },
        //         },
        //         条纹面: {
        //             drawName: 'polygon',
        //             btnId: 'drawlinePolygon-box',
        //             pnlId: 'drawlinePolygon-box-panel',
        //             symbol: {
        //                 style: "vertical",
        //                 type: "simple-fill",
        //                 color: [255, 0, 0, 0.9],
        //                 outline: {
        //                     color: [255, 0, 0],
        //                     width: 5,
        //                 },
        //             },
        //         },
        //         X标记: {
        //             drawName: 'point',
        //             btnId: 'drawdotSignal-box',
        //             pnlId: 'drawdotSignal-box-panel',
        //             symbol: {
        //                 color: [255, 255, 0],
        //                 outline: {  // autocasts as new SimpleLineSymbol()
        //                     color: [255, 0, 0],
        //                     width: 5
        //                 },
        //                 size: 30,
        //                 style: "x",
        //                 type: "simple-marker",
        //             },
        //         },
        //         三角标记: {
        //             drawName: 'point',
        //             btnId: 'drawTriSignal-box',
        //             pnlId: 'drawTriSignal-box-panel',
        //             symbol: {
        //                 color: [255, 0, 0],
        //                 size: 30,
        //                 style: "triangle",
        //                 type: "simple-marker",
        //             },
        //         },
        //         方形标记: {
        //             drawName: 'point',
        //             btnId: 'drawCubicSignal-box',
        //             pnlId: 'drawCubicSignal-box-panel',
        //             symbol: {
        //                 color: [255, 0, 0],
        //                 size: 30,
        //                 style: "square",
        //                 type: "simple-marker",
        //             },
        //         },
        //         态势箭头: {
        //             drawName: 'general_arrow',
        //             btnId: 'drawArrow-box',
        //             pnlId: 'drawArrow-box-panel',
        //             symbol: null,
        //         },
        //         燕尾箭头: {
        //             drawName: 'swallowtail_arrow',
        //             btnId: 'drawSwallowTail-box',
        //             pnlId: 'drawSwallowTail-box-panel',
        //             symbol: null,
        //         },
        //     }
        //     top.window.drawTool?.cancel()
        //     $(`#${drawType[type].btnId} > div > div`)[0].innerHTML = $(`#${drawType[type].btnId} > div > div`)[0].innerHTML == '使用' ? '关闭' : '使用'

        //     if (this.currDraw != type) {//第二次点击其他按钮时
        //         // this.drawToolDestroy()
        //         // top.window.drawTool?.cancel()
        //         this.currDraw = type  //按钮互斥控制变量
        //         Object.keys(drawType).forEach((key) => {
        //             if (key != type) {  //关闭除第二次点击之外的按钮
        //                 $(`#${drawType[key].btnId} > div > div`)[0].innerHTML = '使用'
        //                 $(`#${drawType[key].pnlId}`).html(``)
        //             }
        //         })
        //         $(`#${drawType[type].pnlId}`).html(`<div style="padding:12px;"><p>请在地图上绘制${type}</p></div>`)
        //         if (!top.window.drawTool) top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
        //         if (type == "态势箭头" || type == "燕尾箭头") {
        //             while (1) { //此处需要一直循环，且异步，用来保证箭头可以重复绘制
        //                 await top.window.drawTool.draw(drawType[type].drawName, drawType[type].symbol)
        //             }
        //         }
        //         await top.window.drawTool.draw(drawType[type].drawName, drawType[type].symbol)
        //         if (type == '圆') {
        //             top.window.drawTool.sketchVM.on('create', function (e) {
        //                 if (e.state == 'active') {
        //                     let graphic = e.graphic
        //                     let la1 = graphic.geometry.centroid.latitude
        //                     let ln1 = graphic.geometry.centroid.longitude
        //                     let la2 = graphic.geometry.rings[0][0][1]
        //                     let ln2 = graphic.geometry.rings[0][0][0]

        //                     // 调用 return的距离单位为km

        //                     let radiu = that.GetDistance(la1, ln1, la2, ln2)
        //                     let cent = { lat: la1, lng: ln1 }
        //                     let are = Math.PI * Math.pow(radiu, 2)
        //                     let circleresult = { radius: radiu, center: cent, area: are, coordinates: graphic.geometry.rings }
        //                     $(`#${drawType[type].pnlId}`).html(`<div style="padding:12px;"><p>当前圆半径为${Math.round(circleresult.radius * 100) / 100}公里</p></div>`)
        //                 }
        //             });
        //         }
        //         if (type == '线') {
        //             top.window.drawTool.sketchVM.on('create', function (e) {
        //                 if (e.state == 'active') {
        //                     let graphic = e.graphic
        //                     let totalLen = 0
        //                     for (let i = 0; i < graphic.geometry.paths[0].length - 1; i++) {
        //                         let la1 = graphic.geometry.paths[0][i][1]
        //                         let ln1 = graphic.geometry.paths[0][i][0]
        //                         let la2 = graphic.geometry.paths[0][i + 1][1]
        //                         let ln2 = graphic.geometry.paths[0][i + 1][0]

        //                         totalLen += that.GetDistance(la1, ln1, la2, ln2)
        //                     }
        //                     $(`#${drawType[type].pnlId}`).html(`<div style="padding:12px;"><p>当前折线总长度为${Math.round(totalLen * 100) / 100}公里</p></div>`)
        //                 }
        //             });
        //         }

        //     } else {
        //         $(`#${drawType[type].pnlId}`).html(``)
        //     }
        //     // if (this.currDraw == type) {//两次点击同一个按钮时
        //     //     // top.window.drawTool.destroy()
        //     //     // top.window.drawTool = null
        //     //     $(`#${drawType[type].pnlId}`).html(``)
        //     // } else {
        //     // }
        // },
        async drawFun(type) {
            let that = this
            let drawType = {
                点: { drawName: 'point', btnId: 'drawPoint-box', pnlId: 'drawPoint-box-panel', symbol: null },
                线: { drawName: 'polyline', btnId: 'drawPolyline-box', pnlId: 'drawPolyline-box-panel', symbol: null },
                面: { drawName: 'polygon', btnId: 'drawPolygon-box', pnlId: 'drawPolygon-box-panel', symbol: null },
                圆: { drawName: 'circle', btnId: 'drawCircle-box', pnlId: 'drawCircle-box-panel', symbol: null },
                矩形: { drawName: 'rectangle', btnId: 'drawRect-box', pnlId: 'drawRect-box-panel', symbol: null },
                虚线: {
                    drawName: 'polyline',
                    btnId: 'drawdotline-box',
                    pnlId: 'drawdotline-box-panel',
                    symbol: {
                        color: [255, 0, 0],
                        width: 5,
                        style: 'dash',
                        type: 'simple-line',
                    },
                },
                条纹面: {
                    drawName: 'polygon',
                    btnId: 'drawlinePolygon-box',
                    pnlId: 'drawlinePolygon-box-panel',
                    symbol: {
                        style: 'vertical',
                        type: 'simple-fill',
                        color: [255, 0, 0, 0.9],
                        outline: {
                            color: [255, 0, 0],
                            width: 5,
                        },
                    },
                },
                X标记: {
                    drawName: 'point',
                    btnId: 'drawdotSignal-box',
                    pnlId: 'drawdotSignal-box-panel',
                    symbol: {
                        color: [255, 255, 0],
                        outline: {
                            // autocasts as new SimpleLineSymbol()
                            color: [255, 0, 0],
                            width: 5,
                        },
                        size: 30,
                        style: 'x',
                        type: 'simple-marker',
                    },
                },
                三角标记: {
                    drawName: 'point',
                    btnId: 'drawTriSignal-box',
                    pnlId: 'drawTriSignal-box-panel',
                    symbol: {
                        color: [255, 0, 0],
                        size: 30,
                        style: 'triangle',
                        type: 'simple-marker',
                    },
                },
                方形标记: {
                    drawName: 'point',
                    btnId: 'drawCubicSignal-box',
                    pnlId: 'drawCubicSignal-box-panel',
                    symbol: {
                        color: [255, 0, 0],
                        size: 30,
                        style: 'square',
                        type: 'simple-marker',
                    },
                },
                态势箭头: {
                    drawName: 'general_arrow',
                    btnId: 'drawArrow-box',
                    pnlId: 'drawArrow-box-panel',
                    symbol: null,
                },
                燕尾箭头: {
                    drawName: 'swallowtail_arrow',
                    btnId: 'drawSwallowTail-box',
                    pnlId: 'drawSwallowTail-box-panel',
                    symbol: null,
                },
            }
            top.window.drawTool?.cancel()
            if (this.drawFlag == '') {
                $(`#${drawType[type].pnlId}`).html(``)
                return
            }
            if (!top.window.drawTool) top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
            if (type == '态势箭头' || type == '燕尾箭头') {
                while (1) {
                    await top.window.drawTool.draw(drawType[type].drawName, drawType[type].symbol)
                }
            }
            top.window.drawTool.draw(drawType[type].drawName, drawType[type].symbol)
            if (type == '圆') {
                top.window.drawTool.sketchVM.on('create', function (e) {
                    if (e.state == 'active') {
                        let graphic = e.graphic
                        let la1 = graphic.geometry.centroid.latitude
                        let ln1 = graphic.geometry.centroid.longitude
                        let la2 = graphic.geometry.rings[0][0][1]
                        let ln2 = graphic.geometry.rings[0][0][0]

                        // 调用 return的距离单位为km

                        let radiu = that.GetDistance(la1, ln1, la2, ln2)
                        let cent = { lat: la1, lng: ln1 }
                        let are = Math.PI * Math.pow(radiu, 2)
                        let circleresult = { radius: radiu, center: cent, area: are, coordinates: graphic.geometry.rings }
                        $(`#${drawType[type].pnlId}`).html(
                            `<div style="padding:12px;"><p>当前圆半径为${Math.round(circleresult.radius * 100) / 100}公里</p></div>`
                        )
                    }
                })
            }
            if (type == '线') {
                top.window.drawTool.sketchVM.on('create', function (e) {
                    if (e.state == 'active') {
                        let graphic = e.graphic
                        let totalLen = 0
                        for (let i = 0; i < graphic.geometry.paths[0].length - 1; i++) {
                            let la1 = graphic.geometry.paths[0][i][1]
                            let ln1 = graphic.geometry.paths[0][i][0]
                            let la2 = graphic.geometry.paths[0][i + 1][1]
                            let ln2 = graphic.geometry.paths[0][i + 1][0]

                            totalLen += that.GetDistance(la1, ln1, la2, ln2)
                        }
                        $(`#${drawType[type].pnlId}`).html(
                            `<div style="padding:12px;"><p>当前折线总长度为${Math.round(totalLen * 100) / 100}公里</p></div>`
                        )
                    }
                })
            }
            // await this.drawFun(type)
        },
        GetDistance(lat1, lng1, lat2, lng2) {
            let radLat1 = (lat1 * Math.PI) / 180.0
            let radLat2 = (lat2 * Math.PI) / 180.0
            let a = radLat1 - radLat2
            let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
            let s =
                2 *
                Math.asin(
                    Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2))
                )
            s = s * 6378.137 // EARTH_RADIUS;
            s = Math.round(s * 10000) / 10000
            return s
        },

        spatialAnal(type) {
            $('#spatialMainPanel').css({ display: 'none' })
            $('#toolbar').append($('#spatialMainPanel'))

            let that = this
            $('#header').html(`<span>${type}</span>`)
            document.getElementById(
                'mainPanel'
            ).innerHTML = `<div id="backBtn" onclick="mapTool.cleanall({toolPanel:''});mapTool.toolChange({data:{toolName:'空间分析'}});">返回</div><div id="buffer-sub" style="float:right;"></div><div id="toolPanel"></div>`
            // document.getElementById('mainPanel').innerHTML = `<div id="backBtn" onclick="mapTool.toolChange({data:{toolName:'空间分析'}});">返回</div><div id="buffer-sub" style="float:right;"></div><div id="toolPanel"></div>`

            let spatialToolHtmlFunc = {
                剖切工具: function () {
                    top.window.sliceWidget = top.ArcGisUtils.createSiceWidget(
                        top.mapUtil.mapview,
                        document.getElementById('toolPanel')
                    )
                },
                地表透明: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#groundSlider'))
                    $('#groundSlider').css({ display: 'block' })
                    $('#groundSlider >div').css({ display: 'flex' })
                },
                通视分析: function () {
                    top.window.lineOfSightWidget = top.ArcGisUtils.createLineOfSightWidget(
                        top.mapUtil.mapview,
                        document.getElementById('toolPanel')
                    )
                },
                穹顶分析: function () {
                    $('#toolPanel').html(``)
                    $('#toolPanel').append($('#qdAnalysis'))
                    $('#qdAnalysis').css({ display: 'block' })
                },
                日照分析: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#daylight'))
                    $('#daylight').css({ display: 'block' })
                    top.window.mapUtil.mapview.environment.lighting.directShadowsEnabled = true
                },
                城市天际线: function () {
                    $('#toolPanel').html(
                        `<div class="skyline" id="skyLineLocatePos1">定位到默认视角一</div><div class="skyline" id="skyLineLocatePos2">定位到默认视角二</div><div class="skyline" id="skyLineCreate">获取天际线</div>`
                    )
                    let pos1 = {
                        position: {
                            spatialReference: {
                                latestWkid: 4490,
                                wkid: 4490,
                                vcsWkid: 5773,
                                latestVcsWkid: 5773,
                            },
                            // "x": 119.62673987984279,
                            // "y": 29.09376745563141,
                            // "z": 83.71922224014997,
                            x: 119.62256640822459,
                            y: 29.090506361385394,
                            z: 56.878645340912044,
                        },
                        // "heading": 68.41530212680792,
                        heading: 68.47284913323257,
                        // "tilt": 91.11314196130647,
                        tilt: 93.9859202016013,
                    }
                    let pos2 = {
                        position: {
                            spatialReference: {
                                latestWkid: 4490,
                                wkid: 4490,
                                vcsWkid: 5773,
                                latestVcsWkid: 5773,
                            },
                            x: 119.64679051808322,
                            y: 29.09957445931217,
                            z: 63.80571464076638,
                        },
                        heading: 166.74651453947655,
                        tilt: 92.32759752194713,
                    }
                    top.window.skylineRef = new top.ArcGisUtils.Skyline({ view: top.mapUtil.mapview })
                    top.window.skylineRef.on('loading', (e) => {
                        console.log(e)
                    })
                    $('#skyLineLocatePos1').click(function () {
                        top.mapUtil.mapview.goTo(pos1)
                    })
                    $('#skyLineLocatePos2').click(function () {
                        top.mapUtil.mapview.goTo(pos2)
                    })
                    $('#skyLineCreate').click(function () {
                        if (!top.document.getElementById('skyLine')) {
                            let widget = top.document.createElement('div')
                            widget.id = 'skyLine'
                            top.$('.container').append(widget)
                        }
                        if (!top.document.getElementById('skyLineMask')) {
                            //创建蒙版
                            let widget = top.document.createElement('div')
                            widget.id = 'skyLineMask'
                            top.$('.container').append(widget)
                        }
                        top.$('#skyLineMask').css({
                            display: 'block',
                            width: '100%',
                            height: '100%',
                            position: 'absolute',
                            background: ' rgba(0, 0, 0, 0.8)',
                            top: '0',
                            'z-index': '5',
                        })
                        top.$('#skyLine').html(`
                    <p>绘制中</p>
                    <div class="top-close" id="skyLineClose" onclick="this.parentNode.style.display = 'none';console.log(top.window.skylineRef,'success!')")></div>
                    <img src="" alt="" style="width:2600px;" id="skyLineImg">`)
                        top.$('#skyLine').css({
                            display: 'block',
                            position: 'absolute',
                            right: '2874px',
                            top: '540px',
                            width: '2600px',
                            'z-index': '997',
                            'font-size': '80px',
                            color: 'white',
                            'text-align': 'center',
                            border: 'solid 1px lightblue',
                            background: 'rgba(3, 24, 39, 0.88)',
                            height: '731.25px',
                        })
                        top.$('#skyLine p').css({
                            display: 'block',
                            position: 'absolute',
                            left: '1300px',
                            top: '310px',
                        })
                        top.$('#skyLineClose').css({
                            position: 'absolute',
                            right: '0',
                            top: '0',
                            'z-index': '997',
                            cursor: 'pointer',
                        })
                        top.window.skylineRef.start().then((url) => {
                            top.$('#skyLineMask').css({ display: 'none' })
                            top.$('#skyLine p').css({ display: 'none' })
                            if (url) {
                                top.$('#skyLineImg').attr({ src: url })
                            }
                        })
                    })
                },
                缓冲区分析: function () {
                    $('#buffer-sub').html(`单位：米`)
                    $('#toolPanel').html(`
                    <div class="skyline" id="buffer-draw-pts">绘点</div>
                    <div class="skyline" id="buffer-draw-line">绘线</div>
                    <div class="skyline" id="buffer-draw-polygon">绘面</div>
                    <div class="skyline" id="buffer-draw-clear">清除绘制</div>
                    <div class="skyline" id="buffer-draw-region">框选分析对象</div>
                    <div class="skyline" id="buffer-analyse">开始分析</div>
                    <div class="skyline" id="buffer-analyse-clear">清除分析</div>
                    `)
                    $('#buffer-draw-pts').click(function () {
                        if (!top.window.drawTool) {
                            top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
                        }
                        top.window.drawTool.draw('point')
                    })
                    $('#buffer-draw-line').click(function () {
                        if (!window.drawTool) {
                            top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
                        }
                        top.window.drawTool.draw('polyline')
                    })
                    $('#buffer-draw-polygon').click(function () {
                        if (!window.drawTool) {
                            top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
                        }
                        top.window.drawTool.draw('polygon')
                    })
                    $('#buffer-draw-clear').click(function () {
                        if (!window.drawTool) {
                            top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
                        }
                        top.window.drawTool.clear()
                    })
                    $('#buffer-draw-region').click(function () {
                        top.window.bufferWidget?.destroy()
                        top.window.bufferWidget = null
                        const layer = top.mapUtil.mapview.map.findLayerById('drawLayer')
                        top.window.bufferWidget = new top.ArcGisUtils.Buffer({
                            view: top.mapUtil.mapview,
                        })
                        top.window.bufferWidget.setTargetLayer(layer)
                        top.window.bufferWidget.start()
                    })
                    $('#buffer-analyse').click(function () {
                        if (!top.window.bufferWidget) return
                        top.window.bufferWidget
                            .analysis(50, {
                                unionResults: true,
                            })
                            .catch((err) => { })
                    })
                    $('#buffer-analyse-clear').click(function () {
                        top.window.bufferWidget?.destroy()
                        top.window.bufferWidget = null
                    })
                },
                淹没分析: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#floodPanel'))
                    $('#floodPanel').css({ display: 'block' })
                },
                BIM剖析: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#BIMPanel'))
                    $('#BIMPanel').css({ display: 'block' })
                },
                环点漫游: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#RoamPanel'))
                    $('#RoamPanel').css({ display: 'block' })
                },
                飞行漫游: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#flyRoamPanel'))
                    $('#flyRoamPanel').css({ display: 'block' })
                    $('#buffer-sub').html(`航点数量：0个`)
                },
                白模渲染: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#colorPanel'))
                    $('#colorPanel').css({ display: 'block' })
                },
                二维热力: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#hotPanel'))
                    $('#hotPanel').css({ display: 'block' })
                },
                格网分析: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#gridPanel'))
                    $('#gridPanel').css({ display: 'block' })
                },
                仿真视域: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#stimuRoamPanel'))
                    $('#stimuRoamPanel').css({ display: 'block' })
                },
                地图卷帘: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#swipePanel'))
                    $('#swipePanel').css({ display: 'block' })
                },
                框选统计: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#queryPanel'))
                    $('#queryPanel').css({ display: 'block' })
                },
                虚实融合: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#videoPanel'))
                    $('#videoPanel').css({ display: 'block' })
                },
                多维查询: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#multiQueryPanel'))
                    $('#multiQueryPanel').css({ display: 'block' })
                },
                建筑智能排布: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#buildLayoutPanel'))
                    $('#buildLayoutPanel').css({ display: 'block' })
                },
                场景特效模拟: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#sceneEffectPanel'))
                    $('#sceneEffectPanel').css({ display: 'block' })
                },
                城市小品: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#addGltfLyrPanel'))
                    $('#addGltfLyrPanel').css({ display: 'block' })
                },
                限高分析: function () {
                    document.getElementById('toolPanel').innerHTML = ''
                    $('#toolPanel').append($('#heightRestrictPanel'))
                    $('#heightRestrictPanel').css({ display: 'block' })
                },
            }
            spatialToolHtmlFunc[type]()
        },

        floodSimulate() {
            let view = top.window.mapUtil.mapview
            if (top.window.rainFail) {
                top.window.rainFail.stop()
                top.window.rainFail = null
                this.baseSliderValue = 0
                this.floodTimeSliderValue = 0
                $('#floodArea').html(``)
            }
            if (top.frames['person_count']) {
                top.frames['person_count'].postMessage({ clear_point: '清除poi点位' }, '*')
                window.parent.postMessage(
                    JSON.stringify({
                        type: 'closeIframe',
                        name: 'person_count',
                    }),
                    '*'
                )
            }
            top.window.rainFail = new top.ArcGisUtils.RainfallAnalysis({ view })
            top.window.rainFail.addWaterRenderLayer(view).then((res) => {
                if (res.rings.length > 1) {
                    $('#floodArea').html(`<p>绘制面不可交叉，请重新绘制！</p>`)
                    return
                }
                let geojson = { geometry: top.ArcgisToGeojsonUtils.arcgisToGeoJSON(res) }

                this.createWLGZ(geojson)

                top.window.rainFail.getWaterBaseSurface().then((value) => {
                    view.environment.weather = {
                        type: 'rainy', // autocasts as new RainyWeather({ cloudCover: 0.7, precipitation: 0.3 })
                        cloudCover: 0.7,
                        precipitation: 0.3,
                    }
                    setTimeout(() => {
                        top.window.rainFail.setWaterElevationOffset(value * 1)
                        top.window.rainFail.setRainfall(20)
                        top.window.rainFail.start()
                    }, 3000)
                })
            })
        },

        floodSliderChange() {
            let view = top.window.mapUtil.mapview
            if (top.window.rainFail) {
                view.environment.weather = {
                    type: 'rainy', // autocasts as new RainyWeather({ cloudCover: 0.7, precipitation: 0.3 })
                    cloudCover: 0.7,
                    precipitation: 0.3,
                }
                setTimeout(() => {
                    top.window.rainFail.setWaterElevationOffset(this.baseSliderValue * 1)
                    top.window.rainFail.setRainfall(20)
                    top.window.rainFail.start()
                }, 3000)
            }
        },
        //穹顶分析方法
        qdAnalysisFun() {
            top.window.domAnalysisRef?.removeAll()
            top.window.domAnalysisRef = null
            top.window.domAnalysisRef = new top.ArcGisUtils.DomAnalysis(
                {
                    // 创建实例，并传入监听回调
                    View: top.mapUtil.mapview,
                    isViewUpdating: (val) => {
                        console.log(val + '正在更新')
                    },
                },
                (total, visible, invisible, percent) => {
                    console.log(total, visible, invisible, percent)
                    $('#analysisRes').html(`
                <p>分析总数：${total}</p>
                <p>可见总数：${visible}</p>
                <p>不可见总数：${invisible}</p>
                <p>开敞度：${percent || '0%'}</p>
                `)
                }
            )
            top.window.domAnalysisRef.autoDrawSphere()
        },
        //地表透明度方法
        groundSliderChange() {
            const { ground } = top.window.mapUtil.mapview.map
            ground.opacity = this.sliderValue / 10
        },
        createWLGZ(faceMap) {
            let this_ = this
            // 创建区域
            if (faceMap.geometry.coordinates[0] != []) {
                let str = ''
                faceMap.geometry.coordinates[0].forEach((ele) => {
                    str += ele[0] + ',' + ele[1] + '|'
                })
                let faceStr = str.slice(0, str.length - 1)
                axios({
                    method: 'post',
                    url: baseURL.url + '/typeq/api/getu/project/create',
                    data: {
                        shape: faceStr,
                        precision: 7,
                    },
                }).then(function (res) {
                    this_.getCount(res.data, faceMap)
                })
            }
        },
        getCount(id, faceMap) {
            let this_ = this
            let d = new Date()
            let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d.getHours().toString().padStart(2, '0')}:${d
                .getMinutes()
                .toString()
                .padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
            var frontOneHour = new Date(d.getTime() - 3 * 60 * 60 * 1000)
            let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1}-${frontOneHour.getDate()} ${frontOneHour
                .getHours()
                .toString()
                .padStart(2, '0')}:${frontOneHour.getMinutes().toString().padStart(2, '0')}:${frontOneHour
                    .getSeconds()
                    .toString()
                    .padStart(2, '0')}`

            axios({
                method: 'post',
                url: baseURL.url + '/typeq/api/getu/project/get',
                data: {
                    id: id.data,
                    type: 2,
                    start_time: end,
                    end_time: start,
                },
            }).then(async (res) => {
                //此处需要异步获取，否则总是第二次访问时才获取到
                let person_count_data = { mapGeoJson: faceMap, countAll: res.data.data }
                this_.serchFun(person_count_data)
                console.log(
                    `淹没面积：${(top.turf.area(faceMap.geometry) / 1000000).toFixed(2)}km²影响人数：${res.data.data}人`
                )
            })
        },
        async serchFun(data) {
            let _this = this
            let obj = { type: 'FeatureCollection', features: [data.mapGeoJson] } //暂时注释、21日接口问题
            $('#floodArea').html(
                `<p>淹没面积：${(top.turf.area(data.mapGeoJson.geometry) / 1000000).toFixed(2)}km²</p><p>影响人数：${data.countAll
                }人</p>`
            )
            for (let index = 0; index < _this.tableData.length; index++) {
                !(function (index) {
                    axios({
                        method: 'post',
                        url: baseURL.url + '/api2.0/solr-provider/api/data-sources/solr-search',
                        data: {
                            pageInfo: {
                                current: 1,
                                size: 100,
                                totalSize: 0,
                            },
                            text: _this.tableData[index].code,
                            tableNames: 'poi',
                            returnGeo: true,
                            sortField: '',
                            order: '',
                            geometry: JSON.stringify(obj),
                        },
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((res) => {
                        console.log('res==>', res.data.data.dataList)
                        _this.tableData[index].arr = res.data.data
                        _this.tableData[index].num = res.data.pageInfo.totalSize
                        console.log(`${_this.tableData[index].name}：${_this.tableData[index].num}${_this.tableData[index].unit}`)
                        $('#floodArea').append(
                            `<p>${_this.tableData[index].name}：${_this.tableData[index].num}${_this.tableData[index].unit}</p>`
                        )
                    })
                })(index)
            }
        },

        //鉴权
        creditAuth() {
            var appKey = 'zj_jh-API',
                masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

            var timestamp = Number(Math.round(new Date().getTime() / 1000).toString())

            var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

            var o_str = appKey + time_md5 + masterSecret,
                sha256_str = CryptoJS.SHA256(o_str).toString()

            var sign = sha256_str + masterSecret

            const reqParams = {
                appKey: appKey,
                sign: sign,
                timestamp: timestamp,
                version: 'v1.0',
            }

            return axios({
                method: 'post',
                url: baseURL.url + '/typeq/api/auth/creditAuth',
                data: reqParams,
            }).then(function (res) {
                if (res.data.errno === 0) {
                    window.accessToken = res.data.data.accessToken

                    axios.defaults.headers.common['Access-Token'] = res.data.data.accessToken
                }
            })
        },

        //BIM剖析加载
        async BIMAnalysis() {
            let view = top.window.mapUtil.mapview
            if (!window.BIMlayer1) {
                window.BIMlayer1 = await top.ArcGisUtils.loadArcgisLayer(view, {
                    type: 'building-scene',
                    // url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_ALL_001/SceneServer",
                    url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_JHZX_001_ALL_4490_NEW/SceneServer',
                    // url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_01/SceneServer",
                    elevationInfo: {
                        mode: 'absolute-height',
                        offset: 38.2,
                    },
                })
            }
            if (!top.window.BIMwidget) {
                if (!document.getElementById('BIMWidgetpanel')) {
                    let widget = top.document.createElement('div')
                    widget.id = 'BIMWidgetpanel'
                    $('#BIMPanel').append(widget)
                }
                let BIMPos = {
                    x: 119.63384392993423,
                    y: 29.076555707208282,
                    z: 302.53924650885165,
                    heading: 4.114277186019171,
                    tilt: 62.92452773781252,
                }
                top.mapUtil.flyTo(BIMPos)
                top.window.BIMwidget = top.ArcGisUtils.createBuildingExplorer({
                    view,
                    layers: [BIMlayer1],
                    container: document.getElementById('BIMWidgetpanel'),
                })
            }
        },
        //BIM剖析清除
        async BIMClear() {
            let view = top.window.mapUtil.mapview
            view.map.remove(await window.BIMlayer1)
            window.BIMlayer1 = null
            await top.window.BIMwidget?.destroy()
            top.window.BIMwidget = null
            $('#BIMWidgetpanel').remove()
        },
        //环点漫游
        RoamStart() {
            let view = top.window.mapUtil.mapview
            this.RoamStop()
            top.window.ArcRoam = new top.ArcGisUtils.RoamByHeading({ view })
            top.window.ArcRoam.start()
        },
        RoamStop() {
            top.window.ArcRoam?.stop(true)//是否返回初始视角false
            top.window.ArcRoam = null
        },

        //飞行漫游
        flyRoamDraw() {
            let view = top.window.mapUtil.mapview

            if (!top.window.ArcflyRoam) top.window.ArcflyRoam = new top.ArcGisUtils.Fly({ view })
            top.window.ArcflyRoam.store((_, length) => {
                $('#buffer-sub').html(`航点数量：${length}个`)
            })
        },
        flyRoamStart() {
            top.window.ArcflyRoam.fly(false)
        },
        flyRoamStop() {
            top.window.ArcflyRoam?.stop(true)
            top.window.ArcflyRoam = null
            if ($('#header > span')[0].innerHTML == '飞行漫游') $('#buffer-sub').html(`航点数量：0个`)
        },

        //白模渲染
        lyrCheck(param) {
            let view = top.window.mapUtil.mapview
            let { lyrToCheck, type } = param
            let _this = this
            switch (type) {
                case 'query': //区域查询、多维查询
                    this.queryLyrs = []
                    // console.log(top.mapUtil.layers);
                    Object.keys(top.mapUtil.layers).forEach((item) => {
                        // console.log(top.mapUtil.layers[item]);
                        let lyr = top.mapUtil.layers[item]
                        let newName = item.includes('map')
                            ? item.replace(new RegExp('map', 'g'), '')
                            : item.replace(new RegExp('tcgl', 'g'), '')
                        if (lyr.type == 'feature') {
                            this.queryLyrs.push({
                                value: item,
                                label: newName,
                                disabled: false,
                            })
                        }
                    })
                    break
                default:
                    lyrToCheck.forEach((thisItem) => {
                        if (thisItem.checkOrNot) {
                            //判断是否需要检查上图状态
                            thisItem.disabled = true
                            Object.keys(top.mapUtil.layers).forEach((item) => {
                                if (item == thisItem.value) {
                                    thisItem.disabled = false
                                }
                            })
                        }
                    })
                    break
            }
        },
        lyrRndrChange() {
            console.log(this.renderLyrsValue)
        },
        RndrOptChange() {
            $('#singleColor').css({ display: 'none' })
            $('#colorRamp').css({ display: 'none' })
            let renderOpt = {
                single: () => {
                    $('#singleColor').css({ display: 'block' })
                    this.rampRenderer = null
                },
                ramp: () => {
                    $('#colorRamp').css({ display: 'block' })
                    this.singleRenderer = null
                },
            }
            renderOpt[this.renderOptionsValue]()
        },
        singleColorChange() {
            let view = top.window.mapUtil.mapview
            let colorArr = this.singleColorValue
                .substring(5, this.singleColorValue.length - 1)
                .split(', ')
                .map((component) => {
                    return parseFloat(component)
                })
            console.log(this.renderLyrsValue, colorArr)
            if (!top.mapUtil.layers[`${this.renderLyrsValue}`]) return

            this.singleRenderer = {
                type: 'simple',
                symbol: {
                    type: 'mesh-3d',
                    symbolLayers: [
                        {
                            type: 'fill',
                            material: {
                                color: colorArr,
                                colorMixMode: 'replace',
                            },
                        },
                    ],
                },
            }
            top.mapUtil.layers[`${this.renderLyrsValue}`].renderer = this.singleRenderer
        },
        rampColorChange() {
            if (!top.mapUtil.layers[`${this.renderLyrsValue}`]) return

            let colorRampSwitch = {
                ramp1: [
                    [44, 123, 182],
                    [171, 217, 233],
                    [255, 255, 191],
                    [253, 174, 97],
                    [215, 25, 27],
                ],
                ramp2: [
                    [230, 97, 2],
                    [252, 184, 99],
                    [247, 247, 247],
                    [178, 172, 210],
                    [94, 60, 154],
                ],
                ramp3: [
                    [251, 180, 174],
                    [179, 206, 227],
                    [204, 234, 196],
                    [222, 204, 228],
                    [254, 217, 165],
                ],
                ramp4: [
                    [240, 249, 232],
                    [186, 228, 188],
                    [123, 204, 197],
                    [66, 162, 202],
                    [7, 104, 172],
                ],
                ramp5: [
                    [254, 236, 222],
                    [252, 190, 133],
                    [253, 140, 60],
                    [229, 85, 14],
                    [166, 54, 4],
                ],
            }

            let cfg = ''
            let sym2 = {
                type: 'mesh-3d',
                symbolLayers: [
                    {
                        type: 'fill',
                        material: {
                            color: colorRampSwitch[this.colorRampValue][1],
                            colorMixMode: 'replace',
                        },
                        edges: {
                            type: 'solid',
                            color: cfg.edgesColor || [0, 0, 0, 0],
                            size: cfg.edgesSize || 0.7,
                        },
                    },
                ],
            }
            let sym3 = {
                type: 'mesh-3d',
                symbolLayers: [
                    {
                        type: 'fill',
                        material: {
                            color: colorRampSwitch[this.colorRampValue][2],
                            colorMixMode: 'replace',
                        },
                        edges: {
                            type: 'solid',
                            color: cfg.edgesColor || [0, 0, 0, 0],
                            size: cfg.edgesSize || 0.7,
                        },
                    },
                ],
            }
            let sym4 = {
                type: 'mesh-3d',
                symbolLayers: [
                    {
                        type: 'fill',
                        material: {
                            color: colorRampSwitch[this.colorRampValue][3],
                            colorMixMode: 'replace',
                        },
                        edges: {
                            type: 'solid',
                            color: cfg.edgesColor || [0, 0, 0, 0],
                            size: cfg.edgesSize || 0.7,
                        },
                    },
                ],
            }
            let sym5 = {
                type: 'mesh-3d',
                symbolLayers: [
                    {
                        type: 'fill',
                        material: {
                            color: colorRampSwitch[this.colorRampValue][4],
                            colorMixMode: 'replace',
                        },
                        edges: {
                            type: 'solid',
                            color: cfg.edgesColor || [0, 0, 0, 0],
                            size: cfg.edgesSize || 0.7,
                        },
                    },
                ],
            }
            this.rampRenderer = {
                type: 'class-breaks',
                field: 'floor',
                classBreakInfos: [
                    {
                        minValue: 0,
                        maxValue: 10,
                        symbol: {
                            type: 'mesh-3d',
                            symbolLayers: [
                                {
                                    type: 'fill',
                                    material: {
                                        color: colorRampSwitch[this.colorRampValue][0],
                                        colorMixMode: 'replace',
                                    },
                                    edges: {
                                        type: 'solid',
                                        color: cfg.edgesColor || [0, 0, 0, 0],
                                        size: cfg.edgesSize || 0.7,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        minValue: 10,
                        maxValue: 20,
                        symbol: sym2,
                    },
                    {
                        minValue: 20,
                        maxValue: 30,
                        symbol: sym3,
                    },
                    {
                        minValue: 30,
                        maxValue: 40,
                        symbol: sym4,
                    },
                    {
                        minValue: 40,
                        maxValue: 100,
                        symbol: sym5,
                    },
                ],
            }

            top.mapUtil.layers[`${this.renderLyrsValue}`].renderer = this.rampRenderer
        },
        saveRender() {
            if (!this.renderLyrsValue) return

            let saveRenderer = {
                ramp: this.rampRenderer,
                single: this.singleRenderer,
            }

            //读取
            axios({
                method: 'get',
                url: baseURL.admApi + '/screen/layer/getUserPerspective/' + top.commonObj.userId,
                headers: {
                    'Content-Type': 'application/json',
                    ptid: top.commonObj.ptid,
                    Authorization: top.commonObj.Authorization,
                },
            }).then((renderRes) => {
                let renderData = null
                if (renderRes.data.data != undefined) {
                    renderData = JSON.parse(renderRes.data.data.json)//将数据库上的数据解析
                }

                // let renderData = JSON.parse(res.data.data.json)
                if (!renderData) {
                    renderData = {
                        map婺城区白模: {},
                        map金义新区白模: {},
                        map兰溪市白模: {},
                        map东阳市白模: {},
                        map义乌市白模: {},
                        map永康市白模: {},
                        map浦江县白模: {},
                        map武义县白模: {},
                        map磐安县白模: {},
                        map开发区白模: {},
                    }
                }

                renderData[this.renderLyrsValue] = saveRenderer[this.renderOptionsValue]//将对应图层的渲染方案存入renderData，同时不改变其他图层
                axios({
                    method: 'post',
                    url: baseURL.admApi + '/screen/layer/userPerspective',
                    data: {
                        userid: top.commonObj.userId,
                        json: renderData, //将当前选中图层的渲染方案与其他未修改方案存入数据库
                    },
                    headers: {
                        'Content-Type': 'application/json',
                        ptid: top.commonObj.ptid,
                        Authorization: top.commonObj.Authorization,
                    },
                })
            })
        },
        /**
         * @description: 将对应白模渲染置空
         * @return {*}
         */
        async cancleRender() {
            if (!this.renderLyrsValue) return
            await axios({
                method: 'get',
                url: baseURL.admApi + '/screen/layer/getUserPerspective/' + top.commonObj.userId,
                headers: {
                    'Content-Type': 'application/json',
                    ptid: top.commonObj.ptid,
                    Authorization: top.commonObj.Authorization,
                },
            }).then((res) => {
                let renderData = JSON.parse(res.data.data.json)
                if (!renderData) {
                    renderData = {
                        map婺城区白模: {},
                        map金义新区白模: {},
                        map兰溪市白模: {},
                        map东阳市白模: {},
                        map义乌市白模: {},
                        map永康市白模: {},
                        map浦江县白模: {},
                        map武义县白模: {},
                        map磐安县白模: {},
                        map开发区白模: {},
                    }
                }
                renderData[this.renderLyrsValue] = {}
                axios({
                    method: 'post',
                    url: baseURL.admApi + '/screen/layer/userPerspective',
                    data: {
                        userid: top.commonObj.userId,
                        json: renderData,
                    },
                    headers: {
                        'Content-Type': 'application/json',
                        ptid: top.commonObj.ptid,
                        Authorization: top.commonObj.Authorization,
                    },
                })
            })

            //移除渲染之后图层选项置空
            this.renderLyrsValue = ''
        },

        //热力图渲染
        hotLyrChange() {
            // console.log(this.renderHotValue);
        },
        hotRender() {
            let view = top.window.mapUtil.mapview

            this.removehotRender()
            let hotRenderData = []
            top.mapUtil.layers[this.renderHotValue].source._items.forEach((item) => {
                let lng = item.attributes.lng * 1
                if (lng != '' && lng != 0.0 && lng != null && lng != undefined && !isNaN(lng)) {
                    hotRenderData.push({ lat: item.attributes.lat * 1, lng: item.attributes.lng * 1, count: 1 })
                }
            })
            console.log(hotRenderData)
            top.window.heatmapLayer = top.ArcGisUtils.loadHeatmapLayer({
                data: hotRenderData,
                colorStops: [
                    { color: 'rgba(63, 40, 102, 0)', ratio: 0 },

                    { color: '#00AFFF', ratio: 0.1 },

                    { color: '#14B441', ratio: 0.3 },

                    { color: '#FFFA00', ratio: 0.7 },

                    { color: '#FF4600', ratio: 1 },
                ],
                maxDensity: 0.0003,
                referenceScale: null,
                radius: 70, // 查询半径单位pt
                minScale: 9, // 可添参数，热力图展示的最小zoom级别
            })
        },
        removehotRender() {
            let view = top.window.mapUtil.mapview
            if (top.window.heatmapLayer) {
                view.map.remove(top.window.heatmapLayer)
                top.window.heatmapLayer = null
                this.renderHotValue = ''
            }
        },

        //格网分析
        gridRenderDraw() {
            this.removeGridRender()
            let ptsCata = {
                tcgl学校: '学校',
                tcgl医院: '医院',
                tcgl公交车站: '公交站',
                tcgl住宅区: '房产小区',
            }
            let view = top.window.mapUtil.mapview

            top.window.drawTool = new top.ArcGisUtils.Draw({ view: top.mapUtil.mapview })
            top.window.drawTool.draw('polygon').then((e) => {
                top.window.drawTool.destroy()
                top.window.drawTool = null
                let textgeojson = JSON.stringify({
                    type: 'FeatureCollection',
                    features: [{ geometry: top.ArcgisToGeojsonUtils.arcgisToGeoJSON(e.geometry) }],
                })
                axios({
                    method: 'post',
                    url: baseURL.url + '/api2.0/solr-provider/api/data-sources/solr-search',
                    data: {
                        pageInfo: {
                            current: 1,
                            size: 10000,
                            totalSize: 0,
                        },
                        text: ptsCata[this.renderGridValue],
                        tableNames: 'poi',
                        returnGeo: true,
                        sortField: '',
                        order: '',
                        geometry: textgeojson,
                        // geoType:'wkt',
                    },
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }).then((res) => {
                    let pointData = []
                    res.data.data.forEach((item) => {
                        let str = {
                            count: 1,
                            lng: item.x * 1,
                            lat: item.y * 1,
                        }
                        if (item.x != '' && item.x != null && item.x != undefined && item.x != 'null' && item.x != 'undefined') {
                            pointData.push(str)
                        }
                    })
                    // let partData = pointData.slice(0, 10000);
                    top.mapUtil.layers['格网渲染'] = top.ArcGisUtils.addHexGridLayer({
                        view: view,
                        points: pointData,//点数据
                        // filed: 'count', // 统计的字段 ，如不传，按照点的个数统计
                        type: 'hexGrid', //hexGrid、squareGrid
                        cellSide: 100, // 六边形或者四边形的边长
                        units: 'meters', //单位
                        colorsForClassBreaks: [
                            //可以不传，按照系统默认配色，如果传
                            {
                                colors: [
                                    [246, 229, 207, 5],
                                    [204, 113, 180, 255],
                                    [118, 42, 121, 255],
                                ],
                                numClasses: 3,
                            },
                            {
                                colors: [
                                    [246, 229, 207, 5],
                                    [221, 159, 191, 255],
                                    [204, 113, 180, 255],
                                    [118, 42, 121, 255],
                                ],
                                numClasses: 4,
                            },
                            {
                                colors: [
                                    [246, 229, 207, 5],
                                    [221, 159, 191, 255],
                                    [204, 113, 180, 255],
                                    [161, 78, 150, 255],
                                    [118, 42, 121, 255],
                                ],
                                numClasses: 5,
                            },
                        ],
                        label: true, //是否显示数值标签
                        labelSymbol: {
                            type: 'text', // autocasts as new TextSymbol3DLayer()
                            material: { color: [0, 0, 0] },
                            size: 40, // Defined in points
                        }, //标签配置
                    })
                })
            })
        },
        gridLyrChange() { },
        removeGridRender() {
            let view = top.window.mapUtil.mapview

            // this.drawToolDestroy()
            top.window.drawTool?.cancel()

            top.mapUtil.removeLayer('格网渲染')
        },
        //仿真视域
        stimuRoamStart() {
            let view = top.window.mapUtil.mapview

            if (top.window.StimuRoam) return
            top.window.StimuRoam = view.on('click', ({ mapPoint }) => {
                if (!top.window.stimuRoamStartCamera) {
                    top.window.stimuRoamStartCamera = view.camera.clone().toJSON()
                }
                const { x, y, z } = mapPoint
                view
                    .goTo({
                        position: {
                            x,
                            y,
                            z,
                            spatialReference: {
                                wkid: 4490,
                            },
                        },
                        tilt: 90,
                    })
                    .then(() => {
                        const v = view.camera.toJSON()
                        setTimeout(() => {
                            view.camera = {
                                ...v,
                                heading: v.heading + 180,
                            }
                        }, 500)
                    })
            })
        },
        stimuRoamStop() {
            top.window.StimuRoam?.remove()
            top.view.goTo(top.window.stimuRoamStartCamera)
            top.window.stimuRoamStartCamera = null
            top.window.StimuRoam = null
        },

        //地图卷帘
        swipeStart() {
            let view = top.window.mapUtil.mapview

            if (top.window.swipeTool) return
            top.window.swipeTool = new top.ArcGisUtils.Swipe({
                view,
                newSceneViewCallback: (view) => {
                    top.window.swipeview = view
                    top.$('#swipeMapDom').css({
                        width: '7680px',
                        height: '2160px',
                        top: '0',
                        'z-index': '5',
                        position: 'absolute',
                    })
                    top.ArcGisUtils.addDEMToMap(top.window.swipeview)
                },
            })
        },
        swipelyrCheck(param) {
            let view = top.window.mapUtil.mapview
            let { lyrToCheck } = param

            // lyrToCheck.forEach(thisItem => {
            //     thisItem.disabled = false
            //     if (top.mapUtil._baseMapType == thisItem.value) thisItem.disabled = true
            // })
        },

        swipeChange() {
            let swipeLyrCfg = {
                black: () => {
                    top.ArcGisUtils.exchangeMap(top.window.swipeview, 'vector')
                },
                img2008: () => {
                    // top.ArcGisUtils.exchangeMap(top.window.swipeview, 'image')
                    top.ArcGisUtils.loadArcgisLayer(top.window.swipeview, {
                        title: '金华2008年历史影像',
                        // type: "map-image",
                        // url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/historyImg_2008/MapServer"
                        type: 'tile',
                        url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/historyImg_2008/MapServer',
                        // type: 'wms',
                        // url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/services/historyImg_2008/MapServer/WMSServer',
                    })
                    // top.ArcGisUtils.loadArcgisLayer(top.window.swipeview,{
                    //     title: "金华2008年历史影像",
                    //     type: "map-image",
                    //     url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/historyImg_2008/MapServer"
                    // })
                },
            }
            // swipeLyrCfg[this.swipeLyrValue]()
            swipeLyrCfg['img2008']()
        },
        removeSwipe() {
            top.window.swipeTool?.destroy()
            top.window.swipeTool = null
            top.window.swipeview = null
            this.swipeLyrValue = ''
        },

        //区域查询
        async queryDrawBtn() {
            let view = top.window.mapUtil.mapview
            $('#queryRes').html(``)
            if (this.queryValue == '') {
                //若当前无feature类型图层，则下拉列表无子项，且不可查询
                $('#queryRes').html(`请先选择查询图层`)
                return
            }
            if (top.window.highLight) {
                //清除高亮
                top.window.highLight.remove()
                top.window.highLight = null
            }
            // this.drawToolDestroy()
            top.window.drawTool?.cancel()

            console.log(top.mapUtil.layers[this.queryValue])
            top.window.drawTool = new top.ArcGisUtils.Draw({ view })
            await top.window.drawTool.draw('polygon').then((res) => {
                top.window.graphic = res
                top.window.drawTool.cancel()
            })
            top.window.queryGeometry = top.window.graphic.geometry.clone()
        },
        doQueryBtn() {
            if (!top.window.queryGeometry) return
            // this.drawToolDestroy()
            top.window.drawTool?.cancel()

            if (this.queryValue == '') {
                //若当前无feature类型图层，则下拉列表无子项，且不可查询
                $('#queryRes').html(`请先绘制查询区域`)
                return
            }

            let view = top.window.mapUtil.mapview
            let layer = top.mapUtil.layers[this.queryValue]

            view.whenLayerView(layer).then((layerView) => {
                const params = layer.createQuery()
                params.outFields = ['*']
                params.where = '1=1'
                params.geometry = top.window.queryGeometry
                layer.queryFeatures(params).then(({ features: res }) => {
                    if (top.window.highLight) {
                        top.window.highLight.remove()
                        top.window.highLight = null
                    }
                    top.window.highLight = layerView.highlight(res)
                    let newName = this.queryValue.includes('map')
                        ? this.queryValue.replace(new RegExp('map', 'g'), '')
                        : this.queryValue.replace(new RegExp('tcgl', 'g'), '')
                    $('#queryRes').html(`${newName}图层要素统计：${res.length}个`)
                })
            })
        },

        //视频融合
        groupVideoToMap({ groupName } = {}) {
            // console.log(videoGroup);
            this.videoToMapRes.forEach(item => {
                if (item.groupName == groupName) {
                    !item.groupVideoToMapFlag && item.videoGroup.forEach((videoItem, index) => {
                        this.videoToMap({ videoItem, flyTo: index == 0 ? true : false })
                    })
                    item.groupVideoToMapFlag && item.videoGroup.forEach((videoItem, index) => {
                        this.removeVideoMixWidget(videoItem.code)
                    })
                    item.groupVideoToMapFlag = !item.groupVideoToMapFlag
                }
            })
        },
        videoToMap({ videoItem,  flyTo = true } = {}) {
            if (!this.videoLyrValue) return
            $(`#mixControlPanel${videoItem.code}`).css({ display: 'block' })
            let view = top.view
            let that = this
            let curVideo = []
            this.videoLyrs.forEach((item) => {
                if (item.value == that.videoLyrValue) {
                    console.log(that.videoLyrValue)
                    curVideo = item
                }
            })
            if (top.window[`videoMixWidget${videoItem.code}`]) return
            top.window[`videoMixWidget${videoItem.code}`]?.removeViewLine()
            axios({
                method: 'get',
                url: baseURL.url + '/jhyjzh-server/screen_api/zhdd/video/login',
            }).then((res) => {
                axios({
                    method: 'get',
                    url: baseURL.url + '/jhyjzh-server/screen_api/zhdd/video/getRealMonitor',
                    params: {
                        channelId: videoItem.code,
                        scheme: videoItem.videoType,
                        token: res.data.data.token,
                    },
                }).then((res) => {
                    curVideo.src = res.data.data.url
                    flyTo && top.mapUtil.flyTo(videoItem.flyTo)
                    this.rendermixControlPanel({ curVideo, id: videoItem.code,videoItem })
                })
            })
        },

        rendermixControlPanel(params) {
            let { curVideo, id ,videoItem} = params
            let that = this
            let view = top.view

            var VideoMix = new top.ArcGisUtils.getVideoMixInstance()//虚实融合实例初始化

            top.window[`videoMixWidget${id}`] = new VideoMix({
                view,
                displayBBox: false,
                addGround: false,
                addSketch: true,
                token: '727d5ddcc1ae4d1f9f5aaccab7100a54',
            })

            //创建视频容器
            top.window[`mixVideo${id}`] = top.document.createElement('div') // 创建video标签
            top.document.body.appendChild(top.window[`mixVideo${id}`])
            top.window[`mixVideo${id}`].id = `VIDEOUNICLASS${id}` //加个类型方便后续移除
            top.window[`mixVideo${id}`].style.position = 'absolute' // 设置绝对定位
            top.window[`mixVideo${id}`].style.top = 0 // 设置距离顶部0像素
            top.window[`mixVideo${id}`].style.height = 0 // 设置高度0像素
            top.window[`mixVideo${id}`].style.visibility = 'hidden' // 设置元素隐藏

            //初始化视频
            top.window.mixPlayers[`player${id}`]?.destroy()
            delete top.window.mixPlayers[`player${id}`]
            top.window[`mixPlayer${id}`] = new top.window.HlsJsPlayer({
                el: top.window[`mixVideo${id}`],
                url: curVideo.src,
                playsinline: true,
                whitelist: [''],
                autoplay: true,
                loop: true,
            })
            top.window.mixPlayers[`player${id}`] = top.window[`mixPlayer${id}`]
            top.window[`mixPlayer${id}`].once('complete', () => {
                top.window[`mixVideoTag${id}`] = top.window[`mixVideo${id}`].getElementsByTagName('video')[0] //拿到该元素下的video
                top.window[`videoMixWidget${id}`].setVideo(top.window[`mixVideoTag${id}`])
            })
            //读取服务器上的视角并加载
            axios({
                method: 'get',
                url: baseURL.admApi + '/screen/layer/getUserPerspective/' + top.commonObj.userId,
                headers: {
                    'Content-Type': 'application/json',
                    ptid: top.commonObj.ptid,
                    Authorization: top.commonObj.Authorization,
                },
            }).then((renderRes) => {
                let renderData = null
                if (renderRes && renderRes.data.data != undefined) {
                    renderData = JSON.parse(renderRes.data.data.json)
                }
                if (!renderData) {//如果不存在视角，则读取109超级管理员的视角
                    axios({
                        method: 'get',
                        url: baseURL.admApi + '/screen/layer/getUserPerspective/109',
                        headers: {
                            'Content-Type': 'application/json',
                            ptid: top.commonObj.ptid,
                            Authorization: top.commonObj.Authorization,
                        },
                    }).then((renderRes_user109) => {
                        if (renderRes_user109.data.data != undefined) {
                            renderData = JSON.parse(renderRes_user109.data.data.json)
                        }
                        top.window[`videoMixWidget${id}`].setViewLineByConfig(renderData[that.videoLyrValue][id])
                        this.renderPanelContrtol({ id })
                    })
                } else {
                    top.window[`videoMixWidget${id}`].setViewLineByConfig(renderData[that.videoLyrValue][id])
                    this.renderPanelContrtol({ id })
                }
                // {
                //     line: [
                //         [119.64269341577658, 29.082020486624646, 105.70464224740863],
                //         [119.64337848163085, 29.07915731858245, -59.296487730927765],
                //     ],
                //     near: 47,
                //     far: 355,
                //     rad: 0,
                //     fov: 0.2792526803190927,
                // }
            })
        },
        renderPanelContrtol({ id }) {
            window[`viewLine${id}`] = 0
            window[`rotate${id}`] = 0
            window[`fov${id}`] = 0

            // 监听图层改变、是否在加载模型
            top.window[`videoMixWidget${id}`].on('isUpdated', update)
            top.window[`videoMixWidget${id}`].on('layers', update)
            function update() {
                top.window[`videoMixWidget${id}`].isUpdated
                window[`viewLine${id}`] = top.window[`videoMixWidget${id}`]?.viewLine
                window[`rotate${id}`] = window[`viewLine${id}`] ? (window[`viewLine${id}`].rad / Math.PI) * 180 : 0
                window[`fov${id}`] = window[`viewLine${id}`] ? (window[`viewLine${id}`].fov / Math.PI) * 180 : 0
            }
            update()

            window[`viewLineSketch${id}`] = top.window[`videoMixWidget${id}`].viewSketcher
            document.getElementById(`showViewLine${id}`).onclick = function () {
                if (!window[`viewLineSketch${id}`]) return
                window[`viewLineSketch${id}`].setLineVisible(!window[`viewLine${id}`].visible)
            }
            document.getElementById(`drawViewLine${id}`).onclick = function () {
                if (!window[`viewLineSketch${id}`]) return
                window[`viewLineSketch${id}`].createPolygon()
            }
            document.getElementById(`editViewLine${id}`).onclick = function () {
                if (!window[`viewLineSketch${id}`]) return
                window[`viewLineSketch${id}`].updatePolygon()
            }
            document.getElementById(`removeViewLine${id}`).onclick = function () {
                top.window[`videoMixWidget${id}`].removeViewLine()
            }
            document.getElementById(`logConfig${id}`).onclick = function () {
                if (!window[`viewLine${id}`]) return
                update()
                console.log(window[`viewLine${id}`].printConfig())
                alert('配置信息已打印至控制台！')

                delete renderData['']
                delete renderData['undefined']

                delete renderData[that.videoLyrValue]['far']
                delete renderData[that.videoLyrValue]['fov']
                delete renderData[that.videoLyrValue]['line']
                delete renderData[that.videoLyrValue]['near']
                delete renderData[that.videoLyrValue]['rad']

                if (id) {
                    renderData[that.videoLyrValue][id] = window[`viewLine${id}`].printConfig()
                } else {
                    renderData[that.videoLyrValue] = window[`viewLine${id}`].printConfig()
                }
                axios({
                    method: 'post',
                    url: baseURL.admApi + '/screen/layer/userPerspective',
                    data: {
                        userid: top.commonObj.userId,
                        json: renderData,
                    },
                    headers: {
                        'Content-Type': 'application/json',
                        ptid: top.commonObj.ptid,
                        Authorization: top.commonObj.Authorization,
                    },
                })
            }

            //旋转角度调整滑条
            if (!window[`rotateSlider${id}`])
                window[`rotateSlider${id}`] = new top.Slider({
                    container: document.getElementById(`rotate${id}`),
                    min: 0,
                    max: 359.9,
                    steps: 0.1,
                    values: [window[`rotate${id}`]],
                    visibleElements: {
                        labels: true,
                        rangeLabels: true,
                    },
                })
            window[`rotateSlider${id}`].on(['thumb-change', 'thumb-drag'], function () {
                window[`viewLine${id}`].setRad((window[`rotateSlider${id}`].values[0] / 180) * Math.PI)
            })

            //   近裁剪调整滑条
            if (!window[`nearSlider${id}`])
                window[`nearSlider${id}`] = new top.Slider({
                    container: document.getElementById(`near${id}`),
                    min: 0,
                    max: window[`viewLine${id}`].far - 1,
                    steps: 1,
                    values: [window[`viewLine${id}`]?.near],
                    visibleElements: {
                        labels: true,
                        rangeLabels: true,
                    },
                })
            window[`nearSlider${id}`].on(['thumb-change', 'thumb-drag'], function () {
                window[`viewLine${id}`].near = window[`nearSlider${id}`].values[0]
                window[`farSlider${id}`].min = window[`nearSlider${id}`].values[0] + 1
                window[`viewLine${id}`].updateGra()
            })

            //  远裁剪调整滑条
            if (!window[`farSlider${id}`])
                window[`farSlider${id}`] = new top.Slider({
                    container: document.getElementById(`far${id}`),
                    min: window[`viewLine${id}`].near + 1,
                    max: 1000,
                    steps: 1,
                    values: [window[`viewLine${id}`]?.far],
                    visibleElements: {
                        labels: true,
                        rangeLabels: true,
                    },
                })
            window[`farSlider${id}`].on(['thumb-change', 'thumb-drag'], function () {
                window[`viewLine${id}`].far = window[`farSlider${id}`].values[0]
                window[`nearSlider${id}`].max = window[`farSlider${id}`].values[0] - 1
                window[`viewLine${id}`].updateGra()
            })

            //   视宽调整滑条
            if (!window[`wideSlider${id}`])
                window[`wideSlider${id}`] = new top.Slider({
                    container: document.getElementById(`wide${id}`),
                    min: 1,
                    max: 179,
                    steps: 1,
                    values: [window[`fov${id}`]],
                    visibleElements: {
                        labels: true,
                        rangeLabels: true,
                    },
                })
            window[`wideSlider${id}`].on(['thumb-change', 'thumb-drag'], function () {
                window[`viewLine${id}`].fov = (window[`wideSlider${id}`].values[0] / 180) * Math.PI
                window[`viewLine${id}`].updateGra()
            })

        },
        removeVideoMixWidget(id) {
            if (id) {
                $(`#mixControlPanel${id}`).css({ display: 'none' })
                top.window[`videoMixWidget${id}`]?.removeViewLine()
                delete top.window[`videoMixWidget${id}`]

                top.window.mixPlayers[`player${id}`]?.destroy()
                delete top.window.mixPlayers[`player${id}`]
            } else {
                for (let i = 1; i < 4; i++) {
                    $(`#mixControlPanel${i}`).css({ display: 'none' })
                    top.window[`videoMixWidget${i}`]?.removeViewLine()
                }
                for (let i = 1; i < 12; i++) {
                    $(`#mixControlPanel1${i}`).css({ display: 'none' })
                    top.window[`videoMixWidget1${i}`]?.removeViewLine()
                }
                this.videoToMapRes.forEach(itemGroup=>{//视频融合实例销毁
                    itemGroup.videoGroup.forEach(videoItem=>{
                        $(`#mixControlPanel${videoItem.code}`).css({ display: 'none' })
                        top.window[`videoMixWidget${videoItem.code}`]?.removeViewLine()
                        delete top.window[`videoMixWidget${videoItem.code}`]
                    })
                })
                Object.keys(top.window.mixPlayers).forEach((key) => {//视频插件实例销毁
                    top.window.mixPlayers[key].destroy()
                    delete top.window.mixPlayers[key]
                })
            }
        },

        //多维查询
        selectField() {
            this.queryFields = []
            let thisLayer = top.mapUtil.layers[this.multiQueryLyrValue]

            if (!this.multiQueryLyrValue || !thisLayer) return

            thisLayer.fields.map((item) => {
                this.queryFields.push({ label: item.alias, value: item.name, type: item.type }) //使用alias在前端展示，而数据使用name字段存储
                this.queryFieldDict[item.alias] = item.name
            })
        },
        doMultiQuery() {
            let thisLayer = top.mapUtil.layers[this.multiQueryLyrValue]

            if (!this.multiQueryLyrValue || !this.queryFieldValue || !this.querymultiQueryInput || !thisLayer) return
            this.multiQueryResult = []//将查询结果置空

            let thisQueryLyr = top.mapUtil.layers[this.multiQueryLyrValue]
            const queryParams = thisQueryLyr.createQuery()

            //兼容不同字段类型
            let queryScript = ''
            this.queryFields.map((item) => {
                if (item.label == this.queryFieldValue) {
                    queryScript =
                        item.type == 'string'
                            ? `${item.value} like '%${this.querymultiQueryInput}%'`
                            : `${item.value} = ${this.querymultiQueryInput}`
                }
            })
            queryParams.where = queryScript
            thisQueryLyr.queryFeatures(queryParams).then((res) => {
                res.features.map((item) => {
                    if (!item.geometry) return//如果为空数据，则不在前端展示
                    this.multiQueryResult.push({
                        name: this.textHighLight(
                            item.attributes[this.queryFieldDict[this.queryFieldValue]],
                            this.querymultiQueryInput
                        ),
                        onclick: () => {
                            //将缩放及高亮方法写入查询结果数组的点击方法中
                            let thisLayer = top.mapUtil.layers[this.multiQueryLyrValue]
                            if (!thisLayer) return

                            let zoomToFeature
                            if (item.geometry.type == 'point') {
                                zoomToFeature = {
                                    x: item.geometry.x - 0.01,
                                    y: item.geometry.y - 0.06,
                                    z: 8000,
                                    heading: 8.82768488383057,
                                    tilt: 42.07626999930734,
                                }
                            } else {
                                zoomToFeature = {
                                    x: item.geometry.centroid.x - 0.01,
                                    y: item.geometry.centroid.y - 0.06,
                                    z: 8000,
                                    heading: 8.82768488383057,
                                    tilt: 42.07626999930734,
                                }
                            }
                            top.mapUtil.flyTo(zoomToFeature)

                            top.view.whenLayerView(item.layer).then(function (layerView) {
                                if (top.window.highlightGraphic) top.window.highlightGraphic.remove()
                                top.window.highlightGraphic = layerView.highlight(item)
                            })
                        },
                    })
                })
            })
        },
        textHighLight(source, keywords) {
            const reg = new RegExp(keywords, 'gi')
            return source.replace(reg, `<span style="color: red">${keywords}</span>`)
        },
        resetMultiQuery() {
            this.multiQueryLyrValue = ''
            this.queryFieldValue = ''
            this.queryFields = []
            this.querymultiQueryInput = ''
            this.multiQueryResult = []//将查询结果置空
            if (top.window.highlightGraphic) top.window.highlightGraphic.remove()
        },

        //建筑智能排布
        clickForSelect() {
            top.window.strongExclusion?.clearDrawPolygon()
            top.window.strongExclusion?.destroy()
            top.window.strongExclusion = new top.ArcGisUtils.strongExclusion({
                view: top.view,
                loading(loading) {
                    // if(loading) console.log('计算中');
                },
            })
            top.window.strongExclusion.getPlot()
        },
        autoLayout() {
            let that = this

            this.autoLayoutResult = []
            $('#autoLayout-info').css({ display: 'none' })

            top.window.strongExclusion
                ?.startAnalyze(
                    +this.buildLayout_length,
                    +this.buildLayout_width,
                    +this.buildLayout_foreDistance,
                    +this.buildLayout_backDistance,
                    +this.buildLayout_floorNum,
                    +this.buildLayout_floorHeight
                )
                .then((res) => {
                    $('#autoLayout-info').css({ display: 'block' }) //打开统计面板
                    $('#featureEditorWidget').css({ display: 'block' }) //打开编辑面板
                    res.forEach((item) => {
                        that.autoLayoutResult.push({
                            name: item.indexName,
                            value: Math.round(item.caseIndex * 100) / 100,
                        })
                    })
                })
        },
        selectFeature() {
            top.window.editorWidget?.destroy()
            top.window.editorWidget = null
            this.featureHeight = ''

            top.window.editorWidget = new top.ArcGisUtils.FeatureEditor({
                view: top.view,
                layer: top.window.strongExclusion.polygonLayer,
            })
            top.window.editorWidget.getFeature(({ feature }) => {
                console.log(feature)
            })
        },
        featureHeightChange() {
            if (!this.featureHeight) return
            top.window.editorWidget?.editorViewModel.featureFormViewModel.setValue('height', this.featureHeight)
        },
        async saveFeatureEdit() {
            let that = this
            if (!top.window.editorWidget?.editorViewModel.activeWorkflow.data.edits.feature) return
            await top.window.editorWidget?.save() //保存当前编辑
            top.window.editorWidget?.destroy()
            top.window.editorWidget = null
            this.featureHeight = ''
            this.autoLayoutResult = []
            await top.window.strongExclusion?.refreshResult().then((res) => {
                res.forEach((item) => {
                    let caseIndex = item.caseIndex
                    if (typeof caseIndex == 'string') caseIndex = caseIndex * 1
                    if (caseIndex.length && caseIndex.length > 1) {
                        let sum = 0
                        for (let i = 0; i < caseIndex.length; i++) {
                            caseIndex[i] = caseIndex[i] * 1
                            sum += caseIndex[i]
                        }
                        caseIndex = sum / caseIndex.length
                    }
                    that.autoLayoutResult.push({
                        name: item.indexName,
                        value: Math.round(caseIndex * 100) / 100,
                    })
                })
            })
        },
        removeLayout() {
            top.view.environment.lighting.date = new Date('Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)')
            top.view.environment.lighting.directShadowsEnabled = false
            top.window.strongExclusion?.clearDrawPolygon()
            top.window.strongExclusion?.destroy()
            top.window.strongExclusion = null
            top.window.editorWidget?.destroy()
            top.window.editorWidget = null
            this.featureHeight = ''
            $('#autoLayout-info').css({ display: 'none' })
            $('#featureEditorWidget').css({ display: 'none' })
        },

        //场景特效模拟
        addPointEffect(type) {
            let that = this
            this.effectFlag = type
            this.stopPick()
            this.pickPoint((point) => {
                switch (type) {
                    case 'fire':
                        if (!top.window.fireEffectlocations) top.window.fireEffectlocations = []
                        if (top.window.fireEffectlocations.length > 0) top.ArcGisUtils.removeFireEffect()
                        top.window.fireEffectlocations.push({
                            x: point.lng,
                            y: point.lat,
                            z: point.z,
                            heightLevel: 3,
                            sizeLevel: 2,
                            color: 'yellow',
                        })
                        top.ArcGisUtils.addFireEffect(top.window.fireEffectlocations)
                        break
                    case 'fog':
                        if (!top.window.fogEffectlocations) top.window.fogEffectlocations = []
                        if (top.window.fogEffectlocations.length > 0) top.ArcGisUtils.removeFireSmokeEffect()
                        top.window.fogEffectlocations.push({
                            x: point.lng,
                            y: point.lat,
                            z: point.z,
                            heightLevel: 5,
                            sizeLevel: 2,
                            color: 'yellow',
                        })
                        top.ArcGisUtils.addFireSmokeEffect(top.window.fogEffectlocations)
                        break
                    case 'fountain':
                        if (!top.window.fountainEffectlocations) top.window.fountainEffectlocations = []
                        if (top.window.fountainEffectlocations.length > 0) top.ArcGisUtils.removeFountainEffect()
                        top.window.fountainEffectlocations.push({
                            x: point.lng,
                            y: point.lat,
                            z: point.z,
                            heightLevel: 0.005,
                            sizeLevel: 0.2,
                            particleCount: 30000,
                            color: 'blue',
                        })
                        top.ArcGisUtils.addFountainEffect(top.window.fountainEffectlocations)
                        break

                    default:
                        break
                }
            })
        },
        removePointEffect(type) {
            this.effectFlag = null
            switch (type) {
                case 'fire':
                    top.ArcGisUtils.removeFireEffect()
                    top.window.fireEffectlocations = []
                    break
                case 'fog':
                    top.ArcGisUtils.removeFireSmokeEffect()
                    top.window.fogEffectlocations = []
                    break
                case 'fountain':
                    top.ArcGisUtils.removeFountainEffect()
                    top.window.fountainEffectlocations = []
                    break
                default:
                    top.ArcGisUtils.removeFireEffect()
                    top.window.fireEffectlocations = []
                    top.ArcGisUtils.removeFireSmokeEffect()
                    top.window.fogEffectlocations = []
                    top.ArcGisUtils.removeFountainEffect()
                    top.window.fountainEffectlocations = []
                    break
            }
            this.stopPick()
        },

        //坐标拾取
        pickPoint(callback) {
            if (!window._pickEvent) {
                window._pickEvent = (e) => {
                    if (callback) callback(e)
                }
                top.ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {
                    const { x, y, z } = point
                    if (window._pickEvent) window._pickEvent({ lng: x, lat: y, z: z })
                })
            }
        },
        stopPick() {
            if (window._pickEvent) {
                top.ArcGisUtils.mapClickEventHandle._coordinateClientList = []
                window._pickEvent = null
            }
        },

        //城市小品
        gltfLyrChange(gltfVal) {
            if (window.gltfRef) {
                window.gltfRef.url = 'https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/gltf/' + this.gltfLyrValue
                let symbolSize = {
                    '标牌-减速慢行.glb': { height: 10, width: 7 },
                    '标牌-限速40.glb': { height: 10, width: 7 },
                    '标牌-向左.glb': { height: 10, width: 7 },
                    'AcerPlatanoides.glb': { height: 20, width: 15 },
                    'building.gltf': { height: 50, width: 30 },
                }
                window.gltfRef.create({ symbol: symbolSize[this.gltfLyrValue] })
            }
        },
        startGltfLyr() {
            if (!window.gltfRef) {
                window.gltfRef = new top.ArcGisUtils.gltfModelToMap({
                    view: top.view,
                    url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/libs/gltf/' + this.gltfLyrValue,
                })
            }
            let symbolSize = {
                '标牌-减速慢行.glb': { height: 10, width: 7 },
                '标牌-限速40.glb': { height: 10, width: 7 },
                '标牌-向左.glb': { height: 10, width: 7 },
                'AcerPlatanoides.glb': { height: 20, width: 15 },
                'building.gltf': { height: 50, width: 30 },
            }
            window.gltfRef.create({ symbol: symbolSize[this.gltfLyrValue] })
        },
        stopGltfLyr() {
            if (window.gltfRef) {
                window.gltfRef.stop()
            }
        },
        removeGltfLyr() {
            if (window.gltfRef) {
                window.gltfRef.clear()
            }
        },
        destroyGltfLyr() {
            if (window.gltfRef) {
                window.gltfRef.destroy()
                window.gltfRef = null
            }
        },

        //限高分析
        heightRestrictlyrChange() {
            if (!top.mapUtil.layers[`${this.heightRestrictlyrValue}`]) {
                console.log(top.mapUtil.layers[`${this.heightRestrictlyrValue}`]); 
                return
            }
        },
        drawHeightRestrict() {
            if (!window.heightRestrictWidget) {
                window.heightRestrictWidget = new top.ArcGisUtils.LimitHeight({ view: top.view, })
            }
            window.heightRestrictWidget.drawPolygon();

        },
        heightRestrictliderChange() {
            if (!top.mapUtil.layers[`${this.heightRestrictlyrValue}`]) return
            if (!this.heightRestrictRegionClear && window.heightRestrictWidget) {//当绘制面没有被清除且限高分析实例存在
                window.heightRestrictWidget.startAnalysis(
                    top.mapUtil.layers[`${this.heightRestrictlyrValue}`],
                    this.heightRestrictValue
                );
            }
        },
        startHeightRestrict() {
            this.heightRestrictRegionClear=false
            if (!this.heightRestrictRegionClear && window.heightRestrictWidget) {//当绘制面没有被清除且限高分析实例存在
                window.heightRestrictWidget.startAnalysis(
                    top.mapUtil.layers[`${this.heightRestrictlyrValue}`]
                );
            }
        },
        removeheightRestrict() {
            this.heightRestrictRegionClear = true
            this.heightRestrictValue = 10
            window.heightRestrictWidget?.clear();
        },
        //drawtool统一销毁
        drawToolDestroy() {
            top.window.drawTool?.destroy()
            top.window.drawTool = null
        },
        // 关闭所有工具与弹窗
        async cleanall(params) {
            let view = top.view
            let { toolPanel } = params
            switch (toolPanel) {
                case '测量工具':
                    //关闭距离测量
                    top.window.distanceMeasurementWidget?.destroy()
                    top.window.distanceMeasurementWidget = null
                    //关闭面积测量
                    top.window.areaMeasurementWidget?.destroy()
                    top.window.areaMeasurementWidget = null
                    //关闭角度测量
                    this.stopAngleMeasure()
                    //关闭坐标拾取
                    this.stopcoordPick()

                    $('#angle-box > div > div')[0].innerHTML = '使用'
                    $('#dis-box > div > div')[0].innerHTML = '使用'
                    $('#area-box > div > div')[0].innerHTML = '使用'
                    $('#coordPick-box > div > div')[0].innerHTML = '使用'
                    break
                case '标绘工具':
                    this.currDraw = ''
                    this.drawFlag = ''
                    this.coordPickResult = []
                    this.drawToolDestroy()
                    break
                case '空间分析工具':
                    //剖切工具
                    top.window.sliceWidget?.destroy()
                    top.window.sliceWidget = null
                    //通视分析
                    top.window.lineOfSightWidget?.destroy()
                    top.window.lineOfSightWidget = null
                    //日照分析
                    top.window.lightWidget?.destroy()
                    top.window.lightWidget = null
                    top.mapUtil.mapview.environment.lighting.date = new Date('Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)')
                    top.mapUtil.mapview.environment.lighting.directShadowsEnabled = false

                    //穹顶分析
                    top.mapUtil.mapview.container.style.cursor = 'default'
                    top.window.domAnalysisRef?.removeAll()
                    top.window.domAnalysisRef = null

                    //还原地表透明度
                    const { ground } = top.mapUtil.mapview.map
                    ground.opacity = 1
                    this.sliderValue = 10

                    //天际线
                    top.window.skylineRef?.destroy()
                    top.window.skylineRef = null
                    top.$('#skyLine').css({ display: 'none' })
                    top.$('#skyLineMask').css({ display: 'none' })
                    //缓冲区分析
                    top.window.bufferWidget?.destroy()
                    top.window.bufferWidget = null

                    //日照
                    top.window.mapUtil.mapview.environment.lighting.date = new Date(
                        'Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)'
                    )
                    this.rzDate = new Date()
                    this.rzTime = new Date().getHours() * 60 + new Date().getMinutes() * 1
                    if (this.rzPlay) this.ctRzPlay()

                    //淹没分析
                    top.window.rainFail?.stop()
                    top.window.rainFail = null
                    this.baseSliderValue = 0
                    this.floodTimeSliderValue = 0
                    $('#floodArea').html(``)

                    //BIM分析
                    await top.window.BIMwidget?.destroy()
                    top.window.BIMwidget = null
                    view.map.remove(await window.BIMlayer1)
                    window.BIMlayer1 = null

                    //环点漫游
                    this.RoamStop()
                    //飞行漫游
                    this.flyRoamStop()

                    //白模渲染
                    this.renderLyrsValue = ''
                    this.renderOptionsValue = ''
                    this.colorRampValue = ''
                    $('#singleColor').css({ display: 'none' })
                    $('#colorRamp').css({ display: 'none' })

                    //热力图渲染
                    this.renderHotValue = ''
                    this.removehotRender()

                    //关闭格网渲染
                    this.renderGridValue = ''
                    this.removeGridRender()

                    //仿真视域关闭
                    this.stimuRoamStop()

                    //卷帘关闭
                    this.removeSwipe()

                    //区域查询关闭
                    this.queryValue = ''
                    this.queryLyrs = []
                    top.window.highLight?.remove()
                    top.window.highLight = null
                    $('#queryRes').html(``)

                    //视频融合
                    // this.videoLyrValue = ''
                    this.removeVideoMixWidget('')

                    //多维查询
                    this.resetMultiQuery()

                    //建筑智能排布
                    this.removeLayout()
                    this.buildLayout_length = 20
                    this.buildLayout_width = 20
                    this.buildLayout_foreDistance = 20
                    this.buildLayout_backDistance = 20
                    this.buildLayout_floorNum = 10
                    this.buildLayout_floorHeight = 3
                    this.autoLayoutResult = []

                    //场景特效模拟
                    this.removePointEffect('')

                    //城市小品
                    this.destroyGltfLyr()

                    //限高分析
                    this.removeheightRestrict()
                    this.heightRestrictlyrValue=''
                    top.window.heightRestrictWidget=null
                    break
                default://默认将工具二级面板隐藏，追加到上级页面
                    $('#groundSlider').css({ display: 'none' })
                    $('#toolbar').append($('#groundSlider'))

                    $('#qdAnalysis').css({ display: 'none' })
                    $('#toolbar').append($('#qdAnalysis'))

                    $('#daylight').css({ display: 'none' })
                    $('#toolbar').append($('#daylight'))

                    $('#floodPanel').css({ display: 'none' })
                    $('#toolbar').append($('#floodPanel'))

                    $('#BIMPanel').css({ display: 'none' })
                    $('#toolbar').append($('#BIMPanel'))

                    $('#RoamPanel').css({ display: 'none' })
                    $('#toolbar').append($('#RoamPanel'))

                    $('#flyRoamPanel').css({ display: 'none' })
                    $('#toolbar').append($('#flyRoamPanel'))

                    $('#colorPanel').css({ display: 'none' })
                    $('#toolbar').append($('#colorPanel'))

                    $('#hotPanel').css({ display: 'none' })
                    $('#toolbar').append($('#hotPanel'))

                    $('#gridPanel').css({ display: 'none' })
                    $('#toolbar').append($('#gridPanel'))

                    $('#stimuRoamPanel').css({ display: 'none' })
                    $('#toolbar').append($('#stimuRoamPanel'))

                    $('#swipePanel').css({ display: 'none' })
                    $('#toolbar').append($('#swipePanel'))

                    $('#queryPanel').css({ display: 'none' })
                    $('#toolbar').append($('#queryPanel'))

                    $('#videoPanel').css({ display: 'none' })
                    $('#toolbar').append($('#videoPanel'))

                    $('#multiQueryPanel').css({ display: 'none' })
                    $('#toolbar').append($('#multiQueryPanel'))

                    $('#buildLayoutPanel').css({ display: 'none' })
                    $('#toolbar').append($('#buildLayoutPanel'))

                    $('#sceneEffectPanel').css({ display: 'none' })
                    $('#toolbar').append($('#sceneEffectPanel'))

                    $('#addGltfLyrPanel').css({ display: 'none' })
                    $('#toolbar').append($('#addGltfLyrPanel'))

                    $('#heightRestrictPanel').css({ display: 'none' })
                    $('#toolbar').append($('#heightRestrictPanel'))
                    break
            }
        },

        beforeClose() {
            window.addEventListener('beforeunload', () => {
                this.stopPick()//关闭点特效
                top.window.drawTool?.cancel()//关闭绘图但不清除
            })
        },
    },
})
