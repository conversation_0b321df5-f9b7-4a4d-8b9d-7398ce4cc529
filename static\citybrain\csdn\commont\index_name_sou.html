<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>名称搜索地图位置</title>
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/animate.css" />
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>
  <script src="/static/js/jslib/turf.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/css/index_name_sou.css" />
  <style>
    .img-close-box {
      width: 85%;
      height: 65px;
      position: relative;
      top: 0;
      left: 0;
    }

    .img_close {
      cursor: pointer;
      display: inline-block;
      width: 120px;
      height: 66px;
      background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
      background-repeat: no-repeat;
      background-position: center;
      position: absolute;
      right: 0px;
      top: 50px;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <div id="souBox" class="souBox">
      <!-- 搜索框 -->
      <div class="img-close-box" id="moveDome">
        <div style="width: 1150px; height: 80%"></div>
      </div>
      <div class="img_close" @click="closeFind('index_name_sou')"></div>
      <div class="topSou">
        <i class="search-icon"></i>
        <div class="search-box">
          <el-input v-model="namePoint" @keyup.enter.native="searchBtn" placeholder="请输入关键字" clearable
            @clear="setEmptyData"></el-input>
        </div>
        <!-- showSearchBox -->
        <div :class="[showSearchBox1?'circle-sou-css-click':'','circle-sou-css']" @click="circleVideoFun()"
          title="画圈查询"></div>
        <div :class="[showSearchBox2?'line-sou-css-click':'','line-sou-css']" @click="lineVideoFun()" title="画线查询">
        </div>
      </div>
      <div class="content-box animated fadeIn" v-show="showContent">
        <!-- 导航条 mode="horizontal"横向布局-->
        <div class="navBox">
          <img class="middle-lb-left" src="/static/citybrain/tckz/img/indexNameSou/left.png" @click="next" alt="" />
          <img class="middle-lb-right" src="/static/citybrain/tckz/img/indexNameSou/right.png" @click="pre" alt="" />

          <el-carousel indicator-position="outside" :autoplay="false" :interval="10000" ref="zmd_top" arrow="never"
            style="width: 1100px; margin: 0 auto; height: 75px; overflow: hidden">
            <el-carousel-item>
              <div class="middle">
                <div :class="['middle-item',activeIndex=='视频监控'?'middle-item-active':'']" @click="handleSelect('视频监控')">
                  视频监控
                  <span v-if="spjkData!==0&&!showSearchBox">({{spjkData.length}})</span>
                  <span v-if="spjk2Data!==0&&showSearchBox">({{spjk2Data.length}})</span>
                </div>
                <div :class="['middle-item',activeIndex=='物联感知设备'?'middle-item-active':'']"
                  @click="handleSelect('物联感知设备')">
                  物联感知设备
                  <span class="countAll" v-if="wlgzsbData!==0&&!showSearchBox">({{wlgzsbData.length}})</span>
                  <span class="countAll" v-if="wlgzs2bData!==0&&showSearchBox">({{wlgzs2bData.length}})</span>
                </div>
                <div :class="['middle-item',activeIndex=='地名地址'?'middle-item-active':'']" @click="handleSelect('地名地址')">
                  地名地址
                  <span v-if="dmdzData!==0">({{dmdzData.length}})</span>
                </div>
                <div :class="['middle-item',activeIndex=='POI'?'middle-item-active':'']" @click="handleSelect('POI')">
                  POI
                  <span class="countAll" v-if="poiData!==0">({{poiData.length}})</span>
                </div>
              </div>
            </el-carousel-item>
            <el-carousel-item>
              <div class="middle">
                <div :class="['middle-item',activeIndex=='网格数据'?'middle-item-active':'']" @click="handleSelect('网格数据')">
                  网格数据
                  <span class="countAll" v-if="wgsjData!==0">({{wgsjData.length}})</span>
                </div>
                <div :class="['middle-item',activeIndex=='数据指标'?'middle-item-active':'']" @click="handleSelect('数据指标')">
                  数据指标
                  <span class="countAll" v-if="sjzbData!==0">({{sjzbData.length}})</span>
                </div>
                <div :class="['middle-item',activeIndex=='接入系统'?'middle-item-active':'']" @click="handleSelect('接入系统')">
                  接入系统
                  <span class="countAll" v-if="jrxtData!==0">({{jrxtData.length}})</span>
                </div>
                <div :class="['middle-item',activeIndex=='综治网格'?'middle-item-active':'']" @click="handleSelect('综治网格')">
                  综治网格
                  <span class="countAll" v-if="zzwgData!==0">({{zzwgData.length}})</span>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- 列表展示盒子 v-show="showContent"-->
        <div class="main">
          <!-- 显示总数 -->
          <div style="margin: 20px 20px 0 20px; display: flex; justify-content: space-between">
            <div v-if="activeIndex!='视频监控'" class="countTit s-c-yellow-gradient1 s-font-40">
              <!-- {{activeIndex}}({{tabAllCount}}) -->
            </div>
            <!-- 物联感知设备 v-if="activeIndex=='物联感知设备'&&showSearchBox" -->
            <div v-if="activeIndex=='物联感知设备'&&showSearchBox" style="width: 330px">
              <div class="checkbox-ul" style="cursor: pointer">
                <el-select v-model="tabValue" multiple collapse-tags style="margin-left: 20px" placeholder="请选择">
                  <el-option v-for="(item,index) in tabData" :key="item.type" :label="item.name" :value="item.type"
                    @click.native="changeData(item,index)">
                    <img class="checkbox-box-img"
                      :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${item.type}.png`" alt="" />
                    <span>{{item.name}}</span>
                  </el-option>
                </el-select>
              </div>
            </div>
            <div
              v-else-if="activeIndex!='地名地址'&&activeIndex!='视频监控'&&activeIndex!='接入系统'&&activeIndex!='数据指标'&&activeIndex!='综治网格'"
              class="select" @click="showSelct=!showSelct">
              <div class="flow-icon" :class="showSelct?'flow-icon1':''">
                <img src="/static/citybrain/tckz/img/up.png" alt="" width="20" />
              </div>
              <div class="ul">
                <div style="cursor: pointer">{{startName}}</div>
                <ul :class="[showSelct?'ul-active':'']">
                  <li style="cursor: pointer" v-for="(item,index) in selectLi" @click="selectChange(index,item)">
                    {{item}}
                  </li>
                </ul>
              </div>
            </div>
            <!-- 视频监控的分类 -->
            <div v-else-if="!showSearchBox&&activeIndex=='视频监控'" class="spjkss-css">
              <!-- 区县搜索 -->
              <div class="select" style="position: relative" @click="showSelct=!showSelct">
                <div class="flow-icon" style="top: -12px; right: 15px" :class="showSelct?'flow-icon2':''">
                  <img src="/static/citybrain/tckz/img/up.png" alt="" width="20" />
                </div>
                <div class="ul">
                  <div style="cursor: pointer">{{startName}}</div>
                  <ul :class="[showSelct?'ul-active':'']">
                    <li style="cursor: pointer" v-for="(item,index) in selectLi" @click="spQxselectChange(index,item)">
                      {{item}}
                    </li>
                  </ul>
                </div>
              </div>
              <!-- 行业搜索 -->
              <div class="select" style="position: relative; width: 300px" @click="showSelct1=!showSelct1">
                <div class="flow-icon" style="top: -12px; right: 15px" :class="showSelct1?'flow-icon2':''">
                  <img src="/static/citybrain/tckz/img/up.png" alt="" width="20" />
                </div>
                <div class="ul">
                  <div class="text-overflow"
                    style="cursor: pointer; width: 87%; padding-left: 20px; box-sizing: border-box" :title="spHyValue">
                    {{spHyValue}}
                  </div>
                  <ul :class="[showSelct1?'ul-active':'']">
                    <li class="text-overflow" style="cursor: pointer" v-for="(item,index) in spHyData"
                      @click="spSelectChange(index,item)" :title="item.hymc">
                      {{item.hymc}}
                    </li>
                  </ul>
                </div>
              </div>
              <!-- 分类搜索 -->

              <div style="width: 300px">
                <div class="checkbox-ul" style="cursor: pointer">
                  <el-select v-model="spBqValue" multiple collapse-tags style="margin-left: 20px" placeholder="请选择">
                    <el-option v-for="(item,index) in spBqData" :key="index" :label="item.label_name"
                      :value="item.label_name" @click.native="spSelectFun(item,index)"></el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
          <!-- 搜索周边视频和物联感知设备-->
          <ul class="con-bottom-ul"
            v-if="(showSearchBox&&activeIndex=='视频监控')|| (showSearchBox&&activeIndex=='物联感知设备')">
            <li v-for="(item,index) in allData" @click="flytoPoint(item)"
              style="display: flex; flex-direction: column; flex-wrap: wrap; justify-content: space-between">
              <span class="name" style="padding: 20px 0 20px 8px">
                <span class="index-icon">{{index+1}}</span>
              </span>
              <div style="width: 90%" class="li-top flex-align-center">
                <div class="icon-jydw">
                  <img v-if="activeIndex=='物联感知设备'"
                    :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${item.subtype}.png`" alt="" />
                  <img v-if="activeIndex=='视频监控'"
                    src="/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhddzx_map_video_zbjk.png" alt="" />
                </div>
                <div class="name">{{item.name}}</div>
              </div>
              <div style="width: 90%" class="flex-between">
                <div style="margin-left: 15px" class="color3 flex-align-center">
                  <span v-if="activeIndex=='物联感知设备'">{{item.type}}</span>
                  <span v-if="activeIndex=='视频监控'">{{item.jklx}}</span>
                </div>
                <!-- <div style="margin-left: -12%;" class="flex-align-center">
                    <span class="color3 fs-18">董开军 13312341234</span>
                    <span class="icon_lxdh" style="display: block;"></span>
                  </div> -->
                <div style="margin-right: 15px" class="flex-end">
                  <div class="icon-jl"></div>
                  <span class="color3 fs-28">{{item.jl}} km</span>
                </div>
              </div>
            </li>
          </ul>

          <!-- 视频 -->
          <ul class="spjkUl" v-else-if="!showSearchBox&&activeIndex=='视频监控'">
            <li v-for="(item,index) in allData">
              <!-- :class="(item.jwd&&item.jwd.split(',')[0]=='') || (item.jwd&&item.jwd.split(',')[1]=='')||(!item.jwd)?'click-no':''" -->
              <div style="padding: 20px 0 5px 8px; display: flex; justify-content: space-between; align-items: center"
                @click="clickList(item)">
                <span class="name" style="padding: 20px 0 20px 8px">
                  <span class="index-icon">{{index+1}}</span>
                </span>
                <div class="sptel">
                  <i v-if="item.device_type">{{item.device_type}}</i>
                </div>
                <div style="flex: 1">
                  <span style="display: flex" class="sptel">
                    <span class="name">{{item.name}}</span>
                  </span>
                  <p style="margin: 5px 0 1px">
                    <span class="spjkicon" v-for="(ele,i) in item.iconArr">
                      <span>{{ele}}</span>
                    </span>
                  </p>
                </div>
                <div :class="[item.is_online=='1'||item.is_online==1?'qiuji':'qiangji','pointIcon']"></div>
              </div>
            </li>
          </ul>
          <!-- poi && 物联感知 -->
          <ul class="poiUl" v-else-if="activeIndex=='POI'|| (!showSearchBox&&activeIndex=='物联感知设备')">
            <li v-for="(item,index) in allData"
              :class="(item.jwd&&item.jwd.split(',')[0]=='') || (item.jwd&&item.jwd.split(',')[1]=='')||(!item.jwd)?'click-no':''">
              <div style="padding: 20px 0px 5px 8px; display: flex; justify-content: space-between; align-items: center"
                @click="clickList(item)">
                <span class="name" style="padding: 20px 0 20px 8px">
                  <span class="index-icon">{{index+1}}</span>
                </span>
                <div style="flex: 1">
                  <p style="margin: 0; display: flex">
                    <span class="iconspjk-img" v-if="item.device_type!=''" class="s-c-blue3-gradient"
                      style="font-size: 24px; white-space: nowrap; margin-right: 10px; padding-top: 2px">
                      {{item.device_type}}
                    </span>
                    <span class="name" style="flex: 1">{{item.name}}</span>
                  </p>
                  <p style="
                        margin: 2px 0;
                        display: flex;
                        font-size: 25px;
                        justify-content: space-between;
                        color: #00bbf9;
                      ">
                    <span class="s-flex">
                      <i v-if="item.address!=''&&item.address!='--'" class="address-icon"></i>
                      <span v-if="item.address!=''&&item.address!='--'">地址：</span>
                      {{item.address}}
                    </span>
                    <span class="s-flex"
                      v-if="item.addinfo&&item.addinfo.deptname&&item.addinfo!=''&&item.addinfo.deptname!=undefined">
                      <i class="bm-icon"></i>
                      <span class="">所属部门：</span>
                      {{item.addinfo.deptname}}
                    </span>
                  </p>
                </div>
                <!-- <div class="pointIcon active"></div> -->
              </div>
              <!-- {{index+1}}. {{item.name}} -->
            </li>
          </ul>
          <!-- 地名地址样式 -->
          <ul class="dmdzUl" v-else-if="activeIndex=='地名地址'">
            <li v-for="(item,index) in allData">
              <div style="padding: 20px 0px 5px 8px; display: flex; justify-content: space-between; align-items: center"
                @click="clickList(item)">
                <span class="name" style="padding: 20px 0 20px 8px">
                  <span class="index-icon">{{index+1}}</span>
                </span>
                <div style="flex: 1">
                  <p style="margin: 0; display: flex">
                    <span class="name" style="flex: 1">{{item.name}}</span>
                  </p>
                  <p style="
                        margin: 2px 0;
                        display: flex;
                        font-size: 25px;
                        justify-content: space-between;
                        color: #00bbf9;
                      ">
                    <span class="s-flex">
                      <i v-if="item.address!=''&&item.address!='--'" class="address-icon"></i>
                      <span v-if="item.address!=''&&item.address!='--'">地址：</span>
                      {{item.address}}
                    </span>
                  </p>
                </div>
                <!-- <div class="pointIcon active"></div> -->
              </div>
              <!-- {{index+1}}. {{item.name}} -->
            </li>
          </ul>
          <!-- 综治网格 -->
          <ul class="zzwgUl" v-if="showSearchBox&&activeIndex=='综治网格'">
            <li v-for="(item,index) in allData" @click="addOrRemoveGrid(item)"
              style="display: flex; flex-direction: row; flex-wrap: wrap; justify-content: space-between">
              <span class="name" style="padding: 20px 0 20px 8px">
                <span class="index-icon">{{index+1}}</span>
              </span>
              <div style="width: 90%" class="li-top flex-align-center">
                <div class="name" style="padding-top: 20px">
                  {{item.attributes.SZZ}}{{item.attributes.SZSQ}}{{item.attributes.NAME}}</div>
              </div>
            </li>
          </ul>
          <!-- 其他样式
          <ul class="dmdzUl" v-else>
            <li v-for="(item,index) in allData" @click="clickList(item)" :title="item.name">
              <span class="name" style="padding: 20px 0 5px 8px">
                <span class="index-icon">{{index+1}}</span>
                <span>{{item.name||item.dzqc}}</span>
              </span>
            </li>
          </ul> -->

          <!-- 分页 -->
          <div class="el_page_sjzx" v-if="tabAllCount!=undefined&&tabAllCount!=''&&tabAllCount!=0">
            <el-pagination @current-change="pageChange" layout="prev, pager, next" :pager-count="5" :page-size="15"
              :current-page.sync="currentPage1" :total="tabAllCount"></el-pagination>
          </div>
        </div>
        <!-- <div
            :class="[showContent?'hide-img':'show-img','show-hide-btn']"
            @click="showHideContent"
          ></div> -->
      </div>
    </div>
  </div>
</body>

<script type="module">
  import * as geometryEngine from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/geometryEngine.js";
  import Polyline from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/Polyline.js";
  import Point from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/geometry/Point.js";
  import Graphic from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/Graphic.js";
  import FeatureLayer from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.26/@arcgis/core/layers/FeatureLayer.js";
  var souName = new Vue({
    el: '#app',
    data: {
      namePoint: '', //搜索框model
      showContent: false,
      flagZzwg: false,
      activeIndex: '视频监控', //当前导航下标
      allData: [],
      listData: [],
      listAllData: [],
      currentPage1: 0,
      tabAllCount: 0,
      dmdzData: [],
      spjkData: [],
      poiData: [],
      wlgzsbData: [],
      sjzbData: [],
      jrxtData: [],
      zzwgData: [],
      wgsjData: [],
      showSelct: false,
      zzwgGraphic: null,
      startName: '金华市',
      selectLi: [
        '金华市',
        '婺城区',
        '金义新区',
        '东阳市',
        '义乌市',
        '永康市',
        '兰溪市',
        '浦江县',
        '武义县',
        '磐安县',
        '开发区',
      ],

      // 显示视频监控的行业
      spHyValue: '全部',
      spHyData: [],
      // 显示视频标签的数据
      spBqValue: '',
      spBqData: [],

      showSelct1: false,
      // 显示分类搜索
      showSearchBox: false,
      showSearchBox1: false,
      showSearchBox2: false,
      preClick: null,
      distance: null,
      sgPoint: null,
      wlgzs2bData: [],
      spjk2Data: [],
      tabValue: [
          'wlgz_dzzh',
          'wlgz_skdbjcd',
          'wlgz_syqjcd',
          'wlgz_yysjcd',
          'wlgz_ylqxlk',
          'wlgz_lllk',
          'wlgz_hjsb',
          'wlgz_lcdc',
          'wlgz_nysb',
          'wlgz_qljc',
          'wlgz_qyyd',
          'wlgz_zhld',
          'wlgz_xf'],
      tabData: [
        {
          name: '地质灾害监测点',
          imgName: '地质灾害监测',
          type: 'wlgz_dzzh',
          falg: false,
        },
        {
          name: '水库大坝监测点',
          imgName: '水库大坝监测站',
          type: 'wlgz_skdbjcd',
          falg: false,
        },
        {
          name: '水雨情监测点',
          imgName: 'jyzb',
          type: 'wlgz_syqjcd',
          falg: true,
        },
        {
          name: '饮用水监测点',
          imgName: '饮用水监测点',
          type: 'wlgz_yysjcd',
          falg: false,
        },
        {
          name: '医疗器械冷库',
          imgName: 'jyzb',
          type: 'wlgz_ylqxlk',
          falg: false,
        },
        {
          name: '冷链冷库',
          imgName: 'jyzb',
          type: 'wlgz_lllk',
          falg: false,
        },
        {
          name: '环境设备',
          imgName: 'yy',
          type: 'wlgz_hjsb',
          falg: false,
        },
        {
          name: '路侧地磁',
          imgName: 'yjwz',
          type: 'wlgz_lcdc',
          falg: true,
        },
        {
          name: '农业设备',
          imgName: 'yjzj',
          type: 'wlgz_nysb',
          falg: false,
        },
        {
          name: '桥梁检测',
          imgName: 'bncs',
          type: 'wlgz_qljc',
          falg: false,
        },
        {
          name: '企业用电',
          imgName: 'jyzb',
          type: 'wlgz_qyyd',
          falg: false,
        },
        {
          name: '智慧路灯',
          imgName: 'jyzb',
          type: 'wlgz_zhld',
          falg: false,
        },
        {
          name: '其他',
          imgName: 'wlgzjc',
          type: 'wlgz_xf',
          falg: true,
        },
      ],
      wgDataMapAll: [], //网格总数据
      //摄像头类型
      cameraList: [
        {
          name: '枪机在线',
          code: 'camera-zx-qiangji',
        },
        {
          name: '枪机离线',
          code: 'camera-lx-qiangji',
        },
        {
          name: '球机在线',
          code: 'camera-zx-qiuji',
        },
        {
          name: '球机离线',
          code: 'camera-lx-qiuji',
        },
        {
          name: '半球机在线',
          code: 'camera-zx-banqiu',
        },
        {
          name: '半球机离线',
          code: 'camera-lx-banqiu',
        },
        {
          name: '未知在线',
          code: 'camera-zx-gaodian',
        },
        {
          name: '未知离线',
          code: 'camera-lx-gaodian',
        },
      ],
    },
    created() { },
    mounted() {
      this.spApi()
    },
    methods: {
      // 展开收起
      showHideContent() {
        let souBox = document.getElementById('souBox')
        let parentIframe = parent.document.getElementById('index_name_sou')
        this.showContent = !this.showContent
        if (this.showContent) {
          souBox.style.backgroundImage = "url('/static/citybrain/tckz/img/indexNameSou/bg2.png')"
          souBox.style.height = '1150px'
          parentIframe.style.height = '1150px'
        } else {
          souBox.style.backgroundImage = "url('/static/citybrain/tckz/img/indexNameSou/bg.png')"
          souBox.style.height = '240px'
          parentIframe.style.height = '240px'
        }
      },
      closeFind(names) {
        try {
          this.rmPointIdFun()
          top.mapUtil.plotTool.close()
          top.mapUtil.removeLayer('syr')
          top.mapUtil.removeLayer('syr1')
          if (window.bufferg) top.mapUtil.mapview.graphics.remove(window.bufferg)
          window.parent.ArcGisUtils.removeCodeLayer(window.parent.view)
          top.mapUtil.mapview.graphics.remove(this.zzwgGraphic);
        } catch (error) { }
        this.showSearchBox = false
        if (window.parent == top) {
          top.frames['indexMapIcon'].mainIconVm.searchClick = false
          top.commonObj.funCloseIframe({
            name: names,
          })
        } else {
          window.parent.frames['indexMapIcon'].mainIconVm.searchClick = false
          window.parent.commonObj.funCloseIframe({
            name: names,
          })
        }

        let data = JSON.stringify({
          type: 'backNum',
          info: '关闭了搜索，更新总数量',
        })
        window.parent.postMessage(data, '*')
      },
      // 轮播

      pre() {
        this.$refs.zmd_top.prev()
      },
      next() {
        this.$refs.zmd_top.next()
      },
      // 视频监控的行业和标签
      spApi() {
        $api('csdnsy_gis04').then((res) => {
          let data = {
            hymc: '全部',
            orderid: -1,
          }
          this.spHyData = res
          this.spHyData.unshift(data)
        })
        $api('csdnsy_gis05').then((res) => {
          let data = {
            label_name: '全部',
            lable_code: -1,
          }
          this.spBqData = res.map((item) => {
            let str = {
              label_name: item.label_name,
              lable_code: item.lable_code,
              falg: false,
            }
            return str
          })
          // this.spBqData.unshift(data)
        })
      },
      // 画圈搜索
      circleVideoFun() {
        let that = this
        if (this.preClick == null) this.preClick = "circle"
        if (that.preClick == "line") {
          that.setEmptyData()
          that.showSearchBox = !that.showSearchBox
          if (that.showSearchBox2 == true) { that.showSearchBox2 = !that.showSearchBox2 }
          that.showSearchBox1 = !that.showSearchBox1
        }
        if (that.preClick == "circle") {
          that.showSearchBox = !that.showSearchBox
          that.showSearchBox1 = !that.showSearchBox1
        }
        if (this.showSearchBox) {
          this.spjk2Data = []
          this.wlgzs2bData = []
          this.showContent = true
          this.showHideContent()

          this.activeIndex == '视频监控' || this.activeIndex == '物联感知设备'
            ? ((this.listData = []), (this.allData = []), (this.tabAllCount = 0))
            : ''
          this.rmPointIdFun()
          top.mapUtil.removeLayer('syr')
          top.mapUtil.removeLayer('syr1')
          top.mapUtil.plotTool.active('circle', function (res) {
            that.distance = res.radius
            that.sgPoint = res.center.lng + ',' + res.center.lat
            that.showContent = false
            that.showHideContent()
            that.getWlgzData()
            that.getWlgzData('zbjk')
            // 将选择结果发送消息给父窗口
            setTimeout(() => {
              window.parent.postMessage(JSON.stringify({
                type: '操作搜索',
              }), '*');
            }, 1000);
          })
        } else {
          that.distance = null
          that.sgPoint = null
          this.activeIndex == '视频监控'
            ? (this.listData = this.spjkData)
            : this.activeIndex == '物联感知设备'
              ? (this.listData = this.wlgzsbData)
              : ''
          that.pageChange(1)
          this.spjk2Data = []
          this.wlgzs2bData = []
          top.mapUtil.plotTool.close()
          top.mapUtil.removeLayer('syr')
          top.mapUtil.removeLayer('syr1')
          let pointId = [
            'wlgz_dzzh',
            'wlgz_hjsb',
            'wlgz_skdbjcd',
            'wlgz_lcdc',
            'wlgz_syqjcd',
            'wlgz_nysb',
            'wlgz_yysjcd',
            'wlgz_qljc',
            'wlgz_ylqxlk',
            'wlgz_qyyd',
            'wlgz_lllk',
            'wlgz_zhld',
            'wlgz_xf',
            // "zhddzx_map_video_zbjk",
            'wlgz_dzzh_2',
            'wlgz_hjsb_2',
            'wlgz_skdbjcd_2',
            'wlgz_lcdc_2',
            'wlgz_syqjcd_2',
            'wlgz_nysb_2',
            'wlgz_yysjcd_2',
            'wlgz_qljc_2',
            'wlgz_ylqxlk_2',
            'wlgz_qyyd_2',
            'wlgz_lllk_2',
            'wlgz_zhld_2',
            'wlgz_xf_2',
            // 'zhddzx_map_video_zbjk_2',
            // 'camera-lx-qiangji1',
            // 'camera-lx-qiuji1',
            // 'camera-lx-banqiu1',
            // 'camera-lx-gaodian1',
            // 'camera-zx-qiangji1',
            // 'camera-zx-qiuji1',
            // 'camera-zx-banqiu1',
            // 'camera-zx-gaodian1',
            'video-point1',
            'video-point_line',
            'video-point_circle',
            'video-point_other'
          ]
          this.rmAllLayer(pointId)
        }
        this.preClick = "circle"
      },
      // 划线搜索(一个线路只能最多落三个点)
      lineVideoFun() {
        let that = this
        if (that.preClick == null) that.preClick = "line"
        if (that.preClick == "circle") {
          that.setEmptyData()
          that.showSearchBox = !that.showSearchBox
          if (that.showSearchBox1 == true) { that.showSearchBox1 = !that.showSearchBox1 }
          that.showSearchBox2 = !that.showSearchBox2
        }
        if (that.preClick == "line") {
          that.showSearchBox = !that.showSearchBox
          that.showSearchBox2 = !that.showSearchBox2
        }
        if (this.showSearchBox) {
          this.spjk2Data = []
          this.wlgzs2bData = []
          this.showContent = true
          this.showHideContent()

          this.activeIndex == '视频监控' || this.activeIndex == '物联感知设备'
            ? ((this.listData = []), (this.allData = []), (this.tabAllCount = 0))
            : ''
          this.rmPointIdFun()
          top.mapUtil.removeLayer('syr')
          top.mapUtil.removeLayer('syr1')
          top.mapUtil.plotTool.active('line', function (res) {
            const polyline = new Polyline({
              paths: res.geometry.coordinates,
              spatialReference: { wkid: 4490 }
            })
            var buffer = geometryEngine.buffer(polyline, 0.001);
            window.bufferg = new Graphic({
              geometry: buffer,
              symbol: {
                type: "polygon-3d",  // autocasts as new PolygonSymbol3D()
                symbolLayers: [{
                  type: "fill",  // autocasts as new FillSymbol3DLayer()
                  material: { color: [0, 0, 0, 0.4] },
                  outline: { color: "red", size: "2px" }
                }]
              }
            })
            top.mapUtil.mapview.graphics.add((window.bufferg));
            //由于数据接口不支持范围查询返回 所以这里继续以圆的形式查询 并再做数据筛选
            var cx = (polyline.extent.xmax + polyline.extent.xmin) / 2;
            var cy = (polyline.extent.ymax + polyline.extent.ymin) / 2;
            var dx = polyline.extent.xmax - polyline.extent.xmin;
            var dy = polyline.extent.ymax - polyline.extent.ymin;
            if (dx > dy) var rr = dx * 55.6 + 0.1;
            else var rr = dy * 55.6 + 0.1;
            that.distance = rr
            that.sgPoint = cx + ',' + cy
            console.log(that.distance, that.sgPoint)
            that.showContent = false
            that.showHideContent()
            that.getWlgzData1()
            that.getWlgzData1('zbjk')
            that.pointDataFun1()
            that.pointDataFun1('zbjk')
          })
        } else {
          this.activeIndex == '视频监控'
            ? (this.listData = this.spjkData)
            : this.activeIndex == '物联感知设备'
              ? (this.listData = this.wlgzsbData)
              : ''
          that.pageChange(1)
          this.spjk2Data = []
          this.wlgzs2bData = []
          top.mapUtil.plotTool.close()
          top.mapUtil.removeLayer('syr')
          top.mapUtil.removeLayer('syr1')
          if (window.bufferg) top.mapUtil.mapview.graphics.remove(window.bufferg)
          let pointId = [
            'wlgz_dzzh',
            'wlgz_hjsb',
            'wlgz_skdbjcd',
            'wlgz_lcdc',
            'wlgz_syqjcd',
            'wlgz_nysb',
            'wlgz_yysjcd',
            'wlgz_qljc',
            'wlgz_ylqxlk',
            'wlgz_qyyd',
            'wlgz_lllk',
            'wlgz_zhld',
            'wlgz_xf',
            // "zhddzx_map_video_zbjk",
            'wlgz_dzzh_2',
            'wlgz_hjsb_2',
            'wlgz_skdbjcd_2',
            'wlgz_lcdc_2',
            'wlgz_syqjcd_2',
            'wlgz_nysb_2',
            'wlgz_yysjcd_2',
            'wlgz_qljc_2',
            'wlgz_ylqxlk_2',
            'wlgz_qyyd_2',
            'wlgz_lllk_2',
            'wlgz_zhld_2',
            'wlgz_xf_2',
            // 'zhddzx_map_video_zbjk_2',
            // 'camera-lx-qiangji1',
            // 'camera-lx-qiuji1',
            // 'camera-lx-banqiu1',
            // 'camera-lx-gaodian1',
            // 'camera-zx-qiangji1',
            // 'camera-zx-qiuji1',
            // 'camera-zx-banqiu1',
            // 'camera-zx-gaodian1',
            'video-point1',
            'video-point_line',
            'video-point_circle',
            'video-point_other'
          ]
          this.rmAllLayer(pointId)
        }
        that.preClick = "line"
      },
      flytoPoint(item) {
        console.log('item==>', item)
        let rmPointIdArr = [
          'wlgz_dzzh_2',
          'wlgz_hjsb_2',
          'wlgz_skdbjcd_2',
          'wlgz_lcdc_2',
          'wlgz_syqjcd_2',
          'wlgz_nysb_2',
          'wlgz_yysjcd_2',
          'wlgz_qljc_2',
          'wlgz_ylqxlk_2',
          'wlgz_qyyd_2',
          'wlgz_lllk_2',
          'wlgz_zhld_2',
          'wlgz_xf_2',
          // 'zhddzx_map_video_zbjk_2',
          // 'camera-lx-qiangji1',
          // 'camera-lx-qiuji1',
          // 'camera-lx-banqiu1',
          // 'camera-lx-gaodian1',
          // 'camera-zx-qiangji1',
          // 'camera-zx-qiuji1',
          // 'camera-zx-banqiu1',
          // 'camera-zx-gaodian1',
          'video-point1',
        ]
        this.rmAllLayer(rmPointIdArr)
        let icon = item.subtype + '_2'
        let pointId = item.subtype + '_2'
        if (this.activeIndex == '视频监控') {
          item.pointId = 'video'
          // pointId = 'zhddzx_map_video_zbjk_2'
          // icon = 'zhddzx_map_video_zbjk_2'
          let camersStr = item.cameraType + item.is_online
          this.cameraList.forEach((i) => {
            if (i.name === camersStr) {
              icon = i.code + 1
            }
          })
          pointId = icon
          console.log(icon, pointId)
        }
        let pointData = [
          {
            data: item,
            lng: item.point.split(',')[0],
            lat: item.point.split(',')[1],
          },
        ]
        console.log('pointData==>', pointData)
        // top.mapUtil.loadPointLayer({
        //   data: pointData,
        //   layerid: 'video-point1', //图层id
        //   iconcfg: { image: icon, iconSize: 0.4 }, //图标
        //   onclick: this.openPointMassage,
        //   onblur: this.onblur,
        //   popcfg: {
        //     offset: [50, -100],
        //     show: false,
        //   },
        // })
        let point = item.point.split(',')
        this.flytoAdd(point, 19)
      },
      // 获取物联感知的分类数据和周边视频的列表数据(画圈查询)
      getWlgzData(label) {
        let that = this
        let type = label ? label : this.tabValue.join(',')
        // distance: '12',
        // point:'119.6842722,29.0851881',
        axios({
          method: 'get',
          url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
          params: {
            type: type,
            distance: that.distance,
            point: that.sgPoint,
            // distance: '12',
            // point:'119.6842722,29.0851881',
          },
        }).then(function (data) {
          if (!data.data.data) return
          let dataArr = data.data.data[0]
          label ? (that.spjk2Data = dataArr.result) : (that.wlgzs2bData = dataArr.result)
          that.activeIndex == '视频监控'
            ? ((that.listData = that.spjk2Data), that.pageChange(1))
            : that.activeIndex == '物联感知设备'
              ? ((that.listData = that.wlgzs2bData), that.pageChange(1))
              : ''
        }).then(function () {
          label == 'zbjk'?that.pointDataFun('zbjk'):that.pointDataFun()
        })
      },
      // 整理数据并调用打点方法(画圈查询)
      pointDataFun(label) {
        let that = this
        let typeArr = this.tabValue.join(',')
        let type = label ? label : typeArr
        axios({
          method: 'get',
          url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
          params: {
            type: type,
            distance: that.distance,
            point: that.sgPoint,
            // distance: '12',
            // point:'119.6842722,29.0851881',
          },
        }).then(function (data) {
          let dataArr = []
          if (label == 'zbjk') {
            data.data.data.zbjk.pointData.map((item) => {
              let cameraType = item.isHighAltitude == 1 ? '高点' : item.cameraType
              let obj = {
                cameraType: cameraType,
                data: item.data,
                is_online: item.is_online,
                lat: item.lat,
                lng: item.lng,
                pointType: that.getPointType(item.is_online, cameraType, item.data.warningType),
              }
              dataArr.push(obj)
            })
            // that.addPointFun(dataArr)
            that.getManyPoint(dataArr, "video-point_circle")
            // that.cameraList.forEach((item) => {
            //   that.getManyPoint(that.filterData(dataArr.pointData, item.name, 1), item.code)
            // })
            // 给indexPage_right发送消息 告诉它搜索完毕 更新数量
            let resData = data.data.data.zbjk
            let onlineCount = 0;
            let govCount = 0;
            let total = resData.total;
            resData.pointData.forEach(item => {
              if (item.is_online === "在线") {
                onlineCount++;
              }
              if (item.data.addinfo.labels !== "") {
                govCount++;
              }
            });
            let data1 = JSON.stringify({
              type: 'updataNum',
              total: total,
              onlineCount: onlineCount,
              govCount: govCount
            })
            window.parent.postMessage(data1, '*')
          } else {
            let resultList = []
            let newWlgzs2bData = that.filterWlgzData(that.wlgzs2bData,'type')
            let otherArr = newWlgzs2bData.filter(item => item.type == '其他')
            let filterData = newWlgzs2bData.filter(item => item.type !== '其他')
            let filterData1 = filterData.slice(0,5) //除其他外排名前五位
            let filterDataOther = filterData.slice(5,filterData.length) //其他总和

            filterData1.forEach((item,i) => {
              resultList.push({
                name:item.type,
                value:item.value
              })
            })

            let totalOther = filterDataOther.reduce((sum, item) => sum + item.value, 0)
            let totalValue = totalOther += Number(otherArr[0].value)
            resultList.push({name:"更多",value: totalValue  })
            console.log(newWlgzs2bData,'before');
            console.log(resultList,'after');

            let data1 = JSON.stringify({
              type: 'updataWlgzNum',
              list: resultList
            })
            window.parent.postMessage(data1, '*')


            dataArr = data.data.data
            dataArr.forEach((item) => {
              if (item.pointData.length > 0) {
                that.addPointFun(item)
              }
            })
          }
        })
      },
      //归类并根据length字段大小排序
      filterWlgzData(data, key) {
        const result = data.reduce((acc, item) => {
          if (!acc[item[key]]) {
            acc[item[key]] = [];
          }
          acc[item[key]].push(item);
          return acc;
        }, {});

        // 统计每个分类的数量
        const categorizedData = Object.keys(result).map(tyepName => ({
          type: tyepName,
          value: result[tyepName].length
        }));

        return categorizedData.sort((a, b) => b.length - a.length);
      },

      // 获取物联感知的分类数据和周边视频的列表数据(画线查询)
      getWlgzData1(label) {
        let that = this
        let type = label ? label : this.tabValue.join(',')
        // distance: '12',
        // point:'119.6842722,29.0851881',
        axios({
          method: 'get',
          url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
          params: {
            type: type,
            distance: that.distance,
            point: that.sgPoint,
            // distance: '12',
            // point:'119.6842722,29.0851881',
          },
        }).then(function (data) {
          console.log(data)
          if (!data.data.data) return
          var dataArrE = [];
          data.data.data[0].result.forEach((each) => {
            let test = each.point.split(",").map(Number);
            let point = new Point({
              x: test[0],
              y: test[1],
              spatialReference: { wkid: 4490 }
            })
            const isWithin = geometryEngine.within(point, window.bufferg.geometry);
            if (isWithin) {
              dataArrE.push(each)
            }
          })
          label ? (that.spjk2Data = dataArrE) : (that.wlgzs2bData = dataArrE)
          that.activeIndex == '视频监控'
            ? ((that.listData = that.spjk2Data), that.pageChange(1))
            : that.activeIndex == '物联感知设备'
              ? ((that.listData = that.wlgzs2bData), that.pageChange(1))
              : ''
        })
      },
      // 整理数据并调用打点方法(画线查询)
      pointDataFun1(label) {
        let that = this
        let typeArr = this.tabValue.join(',')
        let type = label ? label : typeArr
        axios({
          method: 'get',
          url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
          params: {
            type: type,
            distance: that.distance,
            point: that.sgPoint,
            // distance: '12',
            // point:'119.6842722,29.0851881',
          },
        }).then(function (data) {
          console.log(data)
          let data1 = [];
          let dataArr = []
          if (label == 'zbjk') {
            data.data.data.zbjk.pointData.forEach((each) => {
              let test = each.point.split(",").map(Number);
              let point = new Point({
                x: test[0],
                y: test[1],
                spatialReference: { wkid: 4490 }
              })
              const isWithin = geometryEngine.within(point, window.bufferg.geometry);
              if (isWithin) {
                data1.push(each)
              }
            })
            data1.map((item) => {
              let cameraType = item.isHighAltitude == 1 ? '高点' : item.cameraType
              let obj = {
                cameraType: cameraType,
                data: item.data,
                is_online: item.is_online,
                lat: item.lat,
                lng: item.lng,
                pointType: that.getPointType(item.is_online, cameraType, item.data.warningType),
              }
              dataArr.push(obj)
            })
            //  that.addPointFun(dataArr)
            that.getManyPoint(dataArr, "video-point_line")
            // that.cameraList.forEach((item) => {
            //   that.getManyPoint(that.filterData(dataArr.pointData, item.name, 1), item.code)
            // })
            // 给indexPage_right发送消息 告诉它搜索完毕 更新数量
            let onlineCount = 0;
            let govCount = 0;
            let total = data1.length;
            data1.forEach(item => {
              if (item.is_online === "在线") {
                onlineCount++;
              }
              if (item.data.addinfo.labels !== "") {
                govCount++;
              }
            });
            let data3 = JSON.stringify({
              type: 'updataNum',
              total: total,
              onlineCount: onlineCount,
              govCount: govCount
            })
            window.parent.postMessage(data3, '*')
          } else {
            var dat = [];
            data.data.data.forEach((each) => {
              var each1 = each
              for (let index = each.pointData.length - 1; index >= 0; index--) {
                let test = each.pointData[index].point.split(",").map(Number);
                let point = new Point({
                  x: test[0],
                  y: test[1],
                  spatialReference: { wkid: 4490 }
                })
                const isWithin = geometryEngine.within(point, window.bufferg.geometry);
                if (!isWithin) {
                  each1.pointData.splice(index, 1)
                }
              }
              that.addPointFun(each1)
            })
          }
        })
      },
      getPointType(is_online, cameraType, warningType) {
        if (warningType && warningType == 1) {
          return '告警视频'
        } else {
          let arr = is_online + '-' + cameraType
          let obj = {
            枪机在线: '在线-枪机',
            枪机离线: '离线-枪机',
            球机在线: '在线-球机',
            球机离线: '离线-球机',
            半球机在线: '在线-半球机',
            半球机离线: '离线-半球机',
            高点在线: '在线-高点',
            高点离线: '离线-高点',
            未知在线: '在线-未知',
            未知离线: '离线-未知',
            铁塔在线: '在线-铁塔',
            铁塔离线: '离线-铁塔'
          }
          for (var key in obj) {
            if (obj[key] == arr) {
              return key
            }
          }
        }
      },
      // filterData(mapObj, name, isOnline) {
      //   let obj = {
      //     枪机在线: ['在线', '枪机', '1'],
      //     枪机离线: ['离线', '枪机', '0'],
      //     球机在线: ['在线', '球机', '1'],
      //     球机离线: ['离线', '球机', '0'],
      //     半球机在线: ['在线', '半球机', '1'],
      //     半球机离线: ['离线', '半球机', '0'],
      //     未知在线: ['在线', '未知', '1'],
      //     未知离线: ['离线', '未知', '0'],
      //   }
      //   if (isOnline == 0) {
      //     return mapObj.filter((item) => {
      //       return item.cameraType == obj[name][1] && item.is_online == obj[name][2]
      //     })
      //   } else {
      //     return mapObj.filter((item) => {
      //       return item.is_online == obj[name][0] && item.cameraType == obj[name][1]
      //     })
      //   }
      // },
      //一次绘制多种不同类型的点
      getManyPoint(pointData, pointId) {
        console.log('r', pointData)
        top.mapUtil.loadPointLayer({
          layerid: pointId,
          data: pointData,
          onclick: this.openPointMassage,
          onblur: this.onblur,
          cluster: true, //是否定义为聚合点位：true/false
          iconcfg: {
            image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
            iconSize: 0.5,
            iconlist: {
              field: 'pointType',
              list: [
                {
                  value: '告警视频',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/warningVideo.png`,
                },
                {
                  value: '枪机在线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                },
                {
                  value: '枪机离线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
                },
                {
                  value: '球机在线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                },
                {
                  value: '球机离线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
                },
                {
                  value: '半球机在线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                },
                {
                  value: '半球机离线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
                },
                {
                  value: '高点在线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                },
                {
                  value: '高点离线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                },
                {
                  value: '未知在线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                },
                {
                  value: '未知离线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                },
                {
                  value: '铁塔在线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-tieta.png`,
                },
                {
                  value: '铁塔离线',
                  size: '50',
                  src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-tieta.png`,
                }
              ],
            },
          },
        })
      },
      // 添加点位方法
      addPointFun(data) {
        top.mapUtil.loadPointLayer({
          data: data.pointData,
          layerid: data.pointId, //图层id
          iconcfg: { image: data.pointType, iconSize: 0.4 }, //图标
          onclick: this.openPointMassage,
          popcfg: {
            offset: [50, -100],
            show: false,
          },
        })
      },

      // 加载图层
      changeData(item, index) {
        if (!this.sgPoint) return
        this.tabData[index].falg = !this.tabData[index].falg
        let falg = this.tabData[index].falg
        let label = item.type
        if (falg) {
          this.getWlgzData()
          this.pointDataFun(label)
        } else {
          this.rmPoint(label)
          this.listData = this.listData.filter((item) => item.subtype != label)
          this.pageChange(1)
        }
      },
      // 视频监控行业搜索
      spSelectChange(index, item) {
        this.spHyValue = item.hymc
        this.spSelectFun()
      },
      // 视频监控区县名称搜索
      spQxselectChange(index, item) {
        this.startName = item
        this.spSelectFun()
      },
      // 视频监控中的区县，行业，标签下拉
      spSelectFun() {
        let that = this
        if (this.startName == '金华市' && this.spHyValue == '全部' && this.spBqValue == '') {
          this.listData = this.listAllData
        } else if (this.startName != '金华市' && this.spHyValue == '全部' && this.spBqValue == '') {
          this.listData = this.listAllData.filter((ele) => ele.xzqx == this.startName)
        } else if (this.startName == '金华市' && this.spHyValue != '全部' && this.spBqValue != '') {
          let arr = []
          for (let i = 0; i < this.spBqValue.length; i++) {
            let arrData = this.listAllData.filter(
              (ele) => ele.hymc == this.spHyValue && ele.addinfo.labels.indexOf(that.spBqValue[i]) != -1
            )
            arr = arr.concat(arrData)
          }
          this.listData = arr
        } else if (this.startName != '金华市' && this.spHyValue != '全部' && this.spBqValue != '') {
          let arr = []
          for (let i = 0; i < this.spBqValue.length; i++) {
            let arrData = this.listAllData.filter(
              (ele) =>
                ele.xzqx == this.startName &&
                ele.hymc == this.spHyValue &&
                ele.addinfo.labels.indexOf(that.spBqValue[i]) != -1
            )
            arr = arr.concat(arrData)
          }
          this.listData = arr
        } else if (this.startName == '金华市' && this.spHyValue != '全部' && this.spBqValue == '') {
          this.listData = this.listAllData.filter((ele) => ele.hymc == this.spHyValue)
        } else if (this.startName != '金华市' && this.spHyValue != '全部' && this.spBqValue == '') {
          this.listData = this.listAllData.filter((ele) => ele.xzqx == this.startName && ele.hymc == this.spHyValue)
        } else if (this.startName == '金华市' && this.spHyValue == '全部' && this.spBqValue != '') {
          let arr = []
          for (let i = 0; i < this.spBqValue.length; i++) {
            let arrData = this.listAllData.filter((ele) => ele.addinfo.labels.indexOf(that.spBqValue[i]) != -1)
            arr = arr.concat(arrData)
          }
          this.listData = arr
        } else if (this.startName != '金华市' && this.spHyValue == '全部' && this.spBqValue != '') {
          let arr = []
          for (let i = 0; i < this.spBqValue.length; i++) {
            let arrData = this.listAllData.filter(
              (ele) => ele.xzqx == this.startName && ele.addinfo.labels.indexOf(that.spBqValue[i]) != -1
            )
            arr = arr.concat(arrData)
          }
          this.listData = arr
        }
        this.pageChange(1)
      },

      // 区县名称搜索
      selectChange(index, item) {
        this.startName = item
        // let name = item == "金义新区" ? "金东区" : item;
        if (item == '金华市') {
          this.listData = this.listAllData
        } else {
          this.listData = this.listAllData.filter((ele) => ele.xzqx == item)
        }
        this.pageChange(1)
      },

      handleSelect(navName) {
        this.startName = '金华市'
        this.spHyValue = '全部'
        this.showSelct1 = false
        this.showSelct = false
        this.activeIndex = navName
        navName == '地名地址'
          ? (this.listData = this.dmdzData)
          : navName == '视频监控'
            ? (this.listData = this.spjkData)
            : navName == 'POI'
              ? (this.listData = this.poiData)
              : navName == '物联感知设备'
                ? (this.listData = this.wlgzsbData)
                : navName == '数据指标'
                  ? (this.listData = this.sjzbData)
                  : navName == '接入系统'
                    ? (this.listData = this.jrxtData)
                    : this.activeIndex == '网格数据'
                      ? (this.listData = this.wgsjData)
                      : this.activeIndex == '综治网格'
                        ? (this.listData = this.zzwgData)
                        : ''
        if (navName == '物联感知设备' && this.showSearchBox) {
          this.listData = this.wlgzs2bData
        } else if (navName == '视频监控' && this.showSearchBox) {
          this.listData = this.spjk2Data
        }
        this.listAllData = this.listData
        this.allData = this.listData && this.listData != [] ? this.listData.slice(0, 15) : []
        this.tabAllCount = this.listData.length
        this.currentPage1 = 1
        top.mapUtil.removeLayer('syr')
        top.mapUtil.removeLayer('syr1')
      },
      searchBtn() {
        let that = this
        this.showSearchBox = false
        this.wlgzs2bData = []
        this.spjk2Data = []
        this.distance = null
        this.sgPoint = null
        if (this.namePoint && this.namePoint !== '') {
          top.mapUtil.plotTool.close()
          this.rmPointIdFun()
          this.searchPlace(this.namePoint)
          this.searchOther(this.namePoint)
          this.showContent = false
          this.showHideContent()
        } else {
          this.setEmptyData()
        }
      },
      setEmptyData() {
        top.mapUtil.plotTool.close()
        window.parent.mapUtil.removeLayer('syr')
        window.parent.mapUtil.removeLayer('syr1')
        window.parent.ArcGisUtils.removeCodeLayer(window.parent.view)
        this.rmPointIdFun()
        this.showSearchBox = false
        this.distance = null
        this.sgPoint = null
        this.namePoint = ''
        this.listAllData = []
        this.listData = []
        this.allData = []
        this.tabAllCount = 0
        this.activeIndex = '地名地址'
        this.dmdzData = []
        this.spjkData = []
        this.poiData = []
        this.wlgzsbData = []
        this.sjzbData = []
        this.jrxtData = []
        this.wgsjData = []
        this.wlgzs2bData = []
        this.spjk2Data = []
        this.showContent = true
        this.showHideContent()
        if (window.bufferg) top.mapUtil.mapview.graphics.remove(window.bufferg)
      },
      searchPlace(name) {
        // $post('/api2.0/solr-provider/api/data-sources/solr-search', {
        //   pageInfo: {
        //     current: 1,
        //     size: 300,
        //     totalSize: 0,
        //   },
        //   tableNames: 'dmdz',
        //   text: name,
        //   searchField: 'dzqc',
        // }).then((res) => {
        //   this.rmPoint('0zhdd_map_hdz')
        //   if (res.data) {
        //     this.dmdzData = res.data.data
        //     if (this.activeIndex == '地名地址') {
        //       this.listData = res.data.data
        //       this.allData = this.listData.slice(0, 15)
        //       this.tabAllCount = this.listData.length
        //       this.listAllData = this.listData
        //     }
        //     let pointArr = [] //上点
        //     res.data.data.map((item) => {
        //       let obj = {
        //         pointId: 'sou',
        //         data: item,
        //         lng: item.x,
        //         lat: item.y,
        //       }
        //       pointArr.push(obj)
        //     })
        //     this.pointTextMapFun('zhdd_map_hdz', pointArr, 'zhdd_map_hdz', 0.4)
        //   } else {
        //     this.rmPoint('0zhdd_map_hdz')
        //   }
        // })
        axios({
          method: 'get',
          url: 'https://csdn.dsjj.jinhua.gov.cn:8101/tdt/v2/search',
          params: {
            type: 'query',
            tk: '36701dd0635f18d6e9afa0b64736b5f0',
            postStr: {
              keyWord: name,
              level: '15',
              mapBound: '119.14,28.32,120.46,29.41',
              queryType: '2',
              count: '100',
              start: '0',
            },
          },
        }).then((response) => {
          this.rmPoint('0zhdd_map_hdz')
          if (response.data.pois) {
            this.dmdzData = response.data.pois
            if (this.activeIndex == '地名地址') {
              this.listData = response.data.pois
              this.allData = this.listData.slice(0, 15)
              this.tabAllCount = this.listData.length
              this.listAllData = this.listData
            }
            let pointArr = [] //进页面上地名点位
            response.data.pois.map((item) => {
              let obj = {
                pointId: 'sou',
                data: item,
                lng: item.lonlat.split(' ')[0],
                lat: item.lonlat.split(' ')[1],
              }
              pointArr.push(obj)
            })
            this.pointTextMapFun('zhdd_map_hdz', pointArr, 'zhdd_map_hdz', 0.4)
          } else {
            this.rmPoint('0zhdd_map_hdz')
          }
        })
      },
      searchOther(name) {
        $api('csdnsy_gis03', { name: name }).then((res) => {
          // this.spjkData = res.filter((item) => item.type == "视频");
          this.spjkData = res.filter((item) => {
            item.iconArr = item.addinfo && item.addinfo.labels ? item.addinfo.labels.split('|') : []
            return item.type == '视频'
          })

          this.poiData = res.filter((item) => item.type == 'POI')
          this.wlgzsbData = res.filter((item) => item.type == '物联感知')
          this.sjzbData = res.filter((item) => item.type == '数据指标')
          this.jrxtData = res.filter((item) => item.type == '接入系统')
          this.wgsjData = res.filter((item) => item.type == '网格数据')
          this.activeIndex == '视频监控'
            ? (this.listData = this.spjkData)
            : this.activeIndex == 'POI'
              ? (this.listData = this.poiData)
              : this.activeIndex == '物联感知设备'
                ? (this.listData = this.wlgzsbData)
                : this.activeIndex == '数据指标'
                  ? (this.listData = this.sjzbData)
                  : this.activeIndex == '接入系统'
                    ? (this.listData = this.jrxtData)
                    : this.activeIndex == '网格数据'
                      ? (this.listData = this.wgsjData)
                      : ''

          if (this.activeIndex != '地名地址') {
            this.allData = this.listData.slice(0, 15)
            this.tabAllCount = this.listData.length
            this.listAllData = this.listData
          }

          this.currentPage1 = 1
          // 所有视频点位上点
          if (this.spjkData !== []) {
            this.rmPoint('video-point_other')
            // this.rmAllLayer([
            //   'camera-zx-qiangji',
            //   'camera-zx-qiuji',
            //   'camera-zx-banqiu',
            //   'camera-zx-gaodian',
            //   'camera-lx-qiangji',
            //   'camera-lx-qiuji',
            //   'camera-lx-banqiu',
            //   'camera-lx-gaodian',
            // ])
            let pointArr = [] //进页面上地名点位
            this.spjkData.map((item) => {
              if (item.jwd && item.jwd.split(',')[0] != '' && item.jwd.split(',')[1] != '') {
                let cameraType = item.addinfo.is_high && item.addinfo.is_high == '1' ? '高点' : item.device_type
                let obj = {
                  pointId: 'video',
                  data: item,
                  lng: item.jwd.split(',')[0],
                  lat: item.jwd.split(',')[1],
                  cameraType: cameraType,
                  is_online: item.is_online == '0' ? '离线' : '在线',
                  pointType: this.getPointType(item.is_online == '0' ? '离线' : '在线', cameraType),
                }
                pointArr.push(obj)
              }
            })
            console.log(pointArr)
            this.getManyPoint(pointArr, "video-point_other")
            // this.cameraList.forEach((item) => {
            //   this.getManyPoint(this.filterData(pointArr, item.name, 0), item.code)
            // })
            // this.pointTextMapFun('camera-load4', pointArr, 'camera-load4', 0.4)
          } else {
            this.rmPoint('video-point_other')
            // this.rmAllLayer([
            //   'camera-zx-qiangji',
            //   'camera-zx-qiuji',
            //   'camera-zx-banqiu',
            //   'camera-zx-gaodian',
            //   'camera-lx-qiangji',
            //   'camera-lx-qiuji',
            //   'camera-lx-banqiu',
            //   'camera-lx-gaodian',
            // ])
          }
        })
      },
      clickList(item) {
        console.log('item==>', item)
        let e = item
        let id = ''
        // item.type == "网格数据" (id = "sjzx-街面秩序")
        this.activeIndex == '地名地址'
          ? (id = 'zhdd_map_hdz')
          : item.type == '视频'
            ? (id = 'camera-load3')
            : item.type == 'POI'
              ? (id = 'rckz-兴趣点通用')
              : item.type == '物联感知'
                ? (id = 'wlgzsb')
                : ''
        let icon = id
        if (this.activeIndex == '地名地址') {
          let pointStr = e.lonlat
          // let pointStr = e.x + ',' + e.y
          let obj = [
            {
              pointId: 'sou',
              data: e,
              lng: pointStr.split(',')[0],
              lat: pointStr.split(',')[1],
            },
          ]
          let flyToPoint = e.lonlat.split(' ')
          // 在这里得等到用户选择了具体地址后请求综治网格   this.searchPlace(经纬度)
          this.queryZzGrid(pointStr)
          // let flyToPoint = [e.x, e.y]
          this.showSearchBox = true
          this.distance = '0.5'
          this.sgPoint = pointStr
          //地名地址时点击查询周边，清除点位，只查当前点及周边，其他分类不清除点位
          this.rmPointIdFun()
          this.getWlgzData()
          this.getWlgzData('zbjk')
          this.pointDataFun()
          this.pointDataFun('zbjk')
          top.mapUtil.loadPointLayer({
            data: obj,
            layerid: '0zhdd_map_hdz_2', //图层id
            iconcfg: { image: 'zhdd_map_hdz_active', iconSize: 0.4 }, //图标
            onclick: this.openPointMassage,
            popcfg: {
              offset: [50, -100],
              show: false,
            },
          })
          this.flytoAdd(flyToPoint, 19)
          // 新增绘制区域边界
          window.parent.ArcGisUtils.removeCodeLayer(window.parent.view)
          window.parent.ArcGisUtils.queryVector({ x: e.lonlat.split(',')[0], y: e.lonlat.split(',')[1] }).then(
            (res) => {
              console.log(res)
            }
          )
        } else if (item.type == '视频' && e != undefined && e != '') {
          this.rmPoint('video-point1')
          // let rmPointIdArr = [
          //   'camera-lx-qiangji1',
          //   'camera-lx-qiuji1',
          //   'camera-lx-banqiu1',
          //   'camera-lx-gaodian1',
          //   'camera-zx-qiangji1',
          //   'camera-zx-qiuji1',
          //   'camera-zx-banqiu1',
          //   'camera-zx-gaodian1',
          // ]
          // this.rmAllLayer(rmPointIdArr)
          if (!e.jwd) {
            this.$message('该分类暂无经纬度信息')
            return
          }
          let pointStr = e.jwd
          let camersStr = item.device_type + '在线'
          let icon1 = ''
          let pointId1 = ''
          this.cameraList.forEach((i) => {
            if (i.name === camersStr) {
              icon1 = i.code + 1
            }
          })
          pointId1 = icon1
          let obj = [
            {
              pointId: 'video',
              data: e,
              lng: pointStr.split(',')[0],
              lat: pointStr.split(',')[1],
            },
          ]
          let flyToPoint = null
          e.jwd ? ((flyToPoint = e.jwd.split(',')), this.flytoAdd(flyToPoint, 19)) : ''

          // top.mapUtil.loadPointLayer({
          //   data: obj,
          //   layerid: 'video-point1', //图层id
          //   iconcfg: { image: icon1, iconSize: 0.4 }, //图标
          //   onclick: this.openPointMassage,
          //   onblur: this.onblur,
          //   popcfg: {
          //     offset: [50, -100],
          //     show: false,
          //   },
          // })
        } else if (item.type == 'POI' || item.type == '物联感知') {
          let pointStr = e.jwd
          let obj = [
            {
              pointId: 'sou',
              data: e,
              lng: pointStr.split(',')[0],
              lat: pointStr.split(',')[1],
            },
          ]
          let flyToPoint = e.jwd.split(',')
          let iconSize = 1
          this.pointTextMapFun(icon, obj, id, iconSize)
          this.flytoAdd(flyToPoint, 19)
        } else if (item.type == '数据指标') {
          console.log('数据指标==>', e.id)
          let leftData1 = {
            type: 'openIframe',
            name: 'cstz3-middle-diong',
            src: baseURL.url + '/static/citybrain/csdn/commont/cstz3-middle-diong.html',
            left: '2480px',
            top: '400px',
            width: '2602px',
            height: '1434px',
            zIndex: '998',
            argument: {
              id: e.id,
              status: 'showEcharts',
            },
          }
          window.parent.postMessage(JSON.stringify(leftData1), '*')
        } else if (item.type == '接入系统') {
          console.log('接入系统==>', e.addinfo.height)
          top.commonObj.openWinHtml(e.addinfo.width, e.addinfo.height, e.addinfo.linkurl)
        } else if (item.type == '网格数据') {
          this.rmShape()

          let data = {
            婺城区: 'wcq',
            金义新区: 'jdq',
            东阳市: 'dys',
            义乌市: 'yws',
            永康市: 'yks',
            兰溪市: 'lxs',
            浦江县: 'pjx',
            武义县: 'wyx',
            磐安县: 'pax',
            开发区: 'kfq',
          }
          let url = `/static/data/grid/${data[item.xzqx]}.json`
          if (!data[item.xzqx]) {
            console.error('字段xzqx数据不正确 : ' + item.xzqx, item)
            this.allFindWgData(item.addinfo.adcode)
            return
          }
          axios.get(url).then((res) => {
            let allGeoJson = [...res.data.features]
            const geoJson = allGeoJson.filter((o) => {
              return o.properties.ADCODE == item.addinfo.adcode
            })

            if (geoJson.length === 0) {
              this.allFindWgData(item.addinfo.adcode)
              return
            }

            this.setWgData(geoJson)
          })
        }
      },
      allFindWgData(adcode) {
        let arr = []
        let data = {
          婺城区: 'wcq',
          金义新区: 'jdq',
          东阳市: 'dys',
          义乌市: 'yws',
          永康市: 'yks',
          兰溪市: 'lxs',
          浦江县: 'pjx',
          武义县: 'wyx',
          磐安县: 'pax',
          开发区: 'kfq',
        }
        for (let key in data) {
          let url = `/static/data/grid/${data[key]}.json`
          axios.get(url).then((res) => {
            let allGeoJson = [...res.data.features]
            const geoJson = allGeoJson.filter((o) => {
              return o.properties.ADCODE == adcode
            })

            if (geoJson.length != 0) {
              this.setWgData(geoJson)
            }
          })
        }
      },
      firstSuccess(promises) {
        return new Promise((resolve, reject) => {
          promises.forEach((item) => {
            item.then((res) => {
              if (res.data.features && res.data.features.length > 0) resolve(res)
            })
          })
        })
      },
      setWgData(geoJson) {
        if (geoJson && geoJson.length != 0) {
          this.flytoAdd([geoJson[0].properties.center_x, geoJson[0].properties.center_y], 19)
          let geojsonData = {
            type: 'FeatureCollection',
            features: [geoJson[0]],
          }
          top.mapUtil.loadPolygonLayer({
            layerid: 'sou_wg',
            data: geojsonData,
            style: {
              strokeColor: [255, 50, 40, 0.9], //多边形轮廓颜色透明度
              fillColor: [193, 210, 240, 0.2], //多边形填充色
            },
            onclick: function (e) {
              console.log('多边形点击事件')
            },
          })
        } else {
          this.$notify.error({
            title: '提示',
            message: '暂无区域信息',
          })
        }
      },
      pageChange(e) {
        let arr = this.listData
        let msg = []
        if (e == 1) {
          this.allData = arr != 0 && arr != [] ? arr.slice(0, 15) : []
        } else {
          this.allData = arr != 0 && arr != [] ? arr.slice(15 * (e - 1), 15 * e) : []
        }
        this.tabAllCount = this.listData.length
      },
      // 请求综治网格
      queryZzGrid(position) {
        let that = this
        return new Promise((resolve, reject) => {
          const layer = new FeatureLayer({
            url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/WG_JH_0513/MapServer/3'
          });
          console.log(typeof position)
          const point = new Point({
            x: position.split(',')[0],
            y: position.split(',')[1],
            spatialReference: { wkid: 4490 }
          });
          const query = {
            geometry: point,
            returnGeometry: true,
            spatialRelationship: 'intersects',
            outFields: ["*"],
          };
          layer.queryFeatures(query)
            .then(function (results) {
              // 成功时解析 Promise
              resolve(results.features);
              that.zzwgData = results.features
            })
            .catch(function (err) {
              // 失败时拒绝 Promise
              reject(err);
            });
        })
      },

      addOrRemoveGrid() {
        this.flagZzwg = !this.flagZzwg
        if (this.flagZzwg) {
          this.zzwgGraphic = new Graphic({
            geometry: this.zzwgData[0].geometry,
            symbol: {
              type: "SimpleFillSymbol",
              color: [255, 255, 255, 0.5],
              outline: {
                color: [0, 0, 0, 1],
                width: 1
              }
            }
          });
          top.mapUtil.mapview.graphics.add(this.zzwgGraphic);
        } else {
          top.mapUtil.mapview.graphics.remove(this.zzwgGraphic);
        }
      },
      // poi和物联感知查询详情
      getInfoData(code, id, point, pointId) {
        $api('csdnsy_gis02', {
          code: code,
          id: id,
        }).then((res) => {
          let arr = Object.keys(res[0]).map((ele) => {
            return {
              name: ele,
              value: res[0][ele],
            }
          })

          let countStr = ''
          for (let index = 0; index < arr.length; index++) {
            if (arr[index].name.indexOf('did') > -1) continue
            else if (arr[index].name.indexOf('value') > -1) continue
            countStr += ` <p style="
                width: 650px;
                font-size: 30px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              ">
              ${arr[index].name}  :<span title="${arr[index].value}">${arr[index].value}</span>
            </p>`
          }
          let str = `<div
        style="
          position: relative;
          background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
          background-size: 100% 100%;
          width: max-content;
          min-height: 250px;
        ">
        <nav
          style="
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding-bottom: 10px;
            margin: 0 20px;
            border-bottom: 1px solid;
            border-bottom: 2px solid;
            border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
            padding-left: 20px;
          ">
          <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">区域详情</h2>
          <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none'">
            <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
          </span>
        </nav>
        <header
          style="
            padding-bottom: 15%;
            margin: 10px 20px 0;
            display: flex;
            justify-content: space-between;
            font-size: 25px;
          ">
          <div style="margin-left: 40px;color:#fff">${countStr}</div>
        </header>
      </div>`

          let objData = {
            layerid: pointId,
            position: point,
            popup: {
              offset: [50, -100],
              closeButton: true,
            },
            content: str,
          }
          top.mapUtil._createPopup(objData)
        })
      },

      // 飞行
      flytoAdd(obj, zoom) {
        top.mapUtil.flyTo({
          destination: obj,
          zoom: zoom,
        })
      },
      // 添加点位方法
      pointTextMapFun(icon, pointData, pointId, iconSize) {
        // this.rmPointIdFun()
        top.mapUtil.plotTool.close()
        this.rmShape()
        this.showSearchBox = false
        this.rmPoint(pointId)
        if (pointData.length > 0) {
          if (pointData[0].lng) {
            top.mapUtil.loadPointLayer({
              data: pointData,
              layerid: pointId, //图层id
              iconcfg: { image: icon, iconSize: iconSize }, //图标
              onclick: this.openPointMassage,
              popcfg: {
                offset: [50, -100],
                show: false,
              },
            })
          } else {
            let iframe1 = {
              type: 'openIframe',
              name: 'video_main',
              src: baseURL.url + '/static/citybrain/csdn/commont/video_main.html',
              width: '100%',
              height: '100%',
              left: '0',
              top: '0',
              zIndex: '1000',
              argument: pointData[0].obj,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          }
        }
      },
      // 查看地图点位点击的详情
      openPointMassage(e, list) {
        console.log("被点击的点", e, list)
        let this_ = this
        // let item = e
        top.mapUtil.removeLayer('syr')
        top.mapUtil.removeLayer('syr1')
        if (e.pointId == 'sou') {
          console.log('sou-window====>', e)
          // if (e.data.dzqc && e.data.tablename == 'dmdz') { //地名地址判断弹窗
          //   this.showPop([e.data.x, e.data.y], e.data.dzqc, e.layerid)
          // }
          if (e.data.lonlat) {
            this.showPop([e.data.lonlat.split(' ')[0], e.data.lonlat.split(' ')[1]], e.data.address, e.layerid)
          } else {
            let point = e.data.jwd.split(',')
            this.getInfoData(e.data.subtype, e.data.id, point, e.layerid)
          }
        } else if (
          (e.subtype && e.subtype.indexOf('wlgz_') > -1) ||
          (e.data.subtype && e.data.subtype.indexOf('wlgz_') > -1)
        ) {
          let coor = e.point ? e.point.split(',') : e.data.jwd.split(',')
          let subtype = e.subtype || e.data.subtype
          this.getInfoData(subtype, e.data.id, coor, e.layerid)
        } else if (e.pointId == 'video') {
          top.mapUtil.flyTo({
            destination: [e.esX, e.esY],
            offset: [0, -999],
          })
          let item = {
            obj: {
              // chn_name: e.data.name,
              chn_name: e.data.video_name,
              pointList: list,
            },
            video_code: e.data.addinfo.chncode,
            csrk: true,
          }

          let iframe1 = {
            type: 'openIframe',
            name: 'video_main_code',
            src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
            width: '100%',
            height: '100%',
            left: '0',
            top: '0',
            zIndex: '1000',
            argument: item,
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
        } else if (e.data != undefined && e.data.pointId == 'video' && e.data.videoSource != 2) {
          // if (e.is_online == '离线') {
          //   this.$message('设备离线')
          //   return
          // }
          let item = {
            obj: {
              // chn_name: e.data.name,
              chn_name: e.data.video_name,
              pointList: list,
            },
            video_code: e.data.addinfo.chncode,
            is_online: e.is_online,
            csrk: true,
          }
          top.mapUtil.flyTo({
            destination: [e.esX, e.esY],
            offset: [0, -999],
          })
          let iframe1 = {
            type: 'openIframe',
            name: 'video_main_code',
            src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
            width: '100%',
            height: '100%',
            left: '0',
            top: '0',
            zIndex: '1000',
            argument: item,
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
        } else if (e.data != undefined && e.data.pointId == 'video' && e.data.videoSource == 2) {
          axios({
            method: 'get',
            url: baseURL.url + '/adm-api/mis/system/videos/webPlay/' + e.data.chnCode,
            headers: {
              Authorization: sessionStorage.getItem('Authorization')
            },
          }).then(function (res) {
            let left1 = (window.screen.availLeft || 0) + (screen.width - 500) / 2
            let top1 = (screen.height - 700) / 2
            window.open(res.data.data, "_blank", 'height=600, width=700, top=' + top1 + ', left=' + left1 + ', crollbars=no, location=no, status=no, alwaysRaised=yes')
          })
        }
      },
      //视屏移入事件
      onblur(e) {
        //onblur
        console.log(e)
        let info = e.data
        let str = ''
        if (e.is_online == 0 || e.is_online == '离线') {
          str = `<div onclick=" this.style.display = 'none'"
                        style="
                          width: 300px;
                          position: absolute;
                          border-radius: 5px;
                          background-color: rgba(6, 26, 48, 1);
                          box-shadow: inset 0 0 20px 0 #00bcfa;
                          z-index: 999999;
                          padding: 24px;">
                           <div
                            style="
                              width: 0px;
                              height: 0px;
                              border-top: 30px solid rgba(4, 91, 129, 0.8);
                              border-right: 15px solid transparent;
                              border-left: 15px solid transparent;
                              position: absolute;
                              bottom: -30px;
                              left: 150px;
                            "
                          ></div>
                    <div class="container1" style="font-size: 30px;color: white;text-align: center;">
                      设备离线中...
                    </div>
                  </div>`
          let objData = {
            layerid: 'syr',
            position: [e.lng, e.lat],
            content: str,
            offset: [50, 100],
          }
          top.mapUtil._createPopup(objData)
        } else {
          $api('xxwh_bqcx_name', { chnCode: info.addinfo.chncode }).then((res) => {
            $api('xxwh_dwzl_video_path', { chnCode: info.addinfo.chncode }).then((el) => {
              let url = ''
              let lable = ''
              let des = ''

              if (el[0] && el[0].path != null) {
                url = baseURL.url + '/imgPath/' + el[0].path.split('fileServer/')[1]
              } else {
                url = '/static/citybrain/tckz/img/video/404.png'
              }
              if (res[0]) {
                let labelName = res[0].lableName.split(',').slice(0, 3)
                let description = res[0].description.split('，')
                labelName.forEach((item) => {
                  lable += `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;">${item}
                              </div>`
                })
                description.forEach((item) => {
                  des += `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;">${item}
                              </div>`
                })
              } else {
                lable = `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;">暂无
                              </div>`
                des = `<div style="color: #dbdee2;
                                  font-size: 28px;
                                  height: 40px;
                                  line-height: 40px;
                                  padding: 0 20px;
                                  box-sizing: border-box;
                                  border-radius: 10px;
                                  margin-right: 10px;
                                  margin-bottom: 10px;">暂无
                          </div>`
              }

              str = `<div onclick=" this.style.display = 'none'"
                          style="
                            width: 800px;
                            position: absolute;
                            border-radius: 5px;
                            background-color: rgba(10, 31, 53, 0.8);
                            z-index: 999999;
                            box-shadow: inset 0 0 40px 0 #5ba3fa;
                            padding: 24px;">

                      <div class="container1">
                        <div style="display:flex;justify-content: space-between;">
                          <p title='${info.video_name}' style='height: 30px;line-height: 30px;color: #fff;font-size: 30px;
                                  white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'>${info.video_name}</p>
                        </div>
                        <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 10px;">
                          <span style="font-size:30px;color:#fff;line-height:40px;">标签：</span>
                          ${lable}
                        </div>
                        <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 0px;">
                          <span style="font-size:30px;color:#fff;line-height:40px;">视频内容：</span>
                          ${des}
                        </div>
                        <img src="${url}" alt="" style='width:100%;height:400px;margin-top: 10px;'>
                      </div>
                    </div>`
              let objData = {
                layerid: 'syr1',
                position: [e.lng, e.lat],
                content: str,
                offset: [50, 100],
              }
              top.mapUtil._createPopup(objData)
            })
          })
        }
      },
      // 清除网格
      rmShape() {
        top.mapUtil.removeLayer('sou_wg')
      },
      // 清除点位
      // 清除点位
      rmPointIdFun() {
        let rmPointIdArr = [
          'wlgz_dzzh',
          'wlgz_hjsb',
          'wlgz_skdbjcd',
          'wlgz_lcdc',
          'wlgz_syqjcd',
          'wlgz_nysb',
          'wlgz_yysjcd',
          'wlgz_qljc',
          'wlgz_ylqxlk',
          'wlgz_qyyd',
          'wlgz_lllk',
          'wlgz_zhld',
          'wlgz_xf',
          // 'zhddzx_map_video_zbjk',
          // 'camera-lx-qiangji',
          // 'camera-lx-qiuji',
          // 'camera-lx-banqiu',
          // 'camera-lx-gaodian',
          // 'camera-zx-qiangji',
          // 'camera-zx-qiuji',
          // 'camera-zx-banqiu',
          // 'camera-zx-gaodian',
          'wlgz_dzzh_2',
          'wlgz_hjsb_2',
          'wlgz_skdbjcd_2',
          'wlgz_lcdc_2',
          'wlgz_syqjcd_2',
          'wlgz_nysb_2',
          'wlgz_yysjcd_2',
          'wlgz_qljc_2',
          'wlgz_ylqxlk_2',
          'wlgz_qyyd_2',
          'wlgz_lllk_2',
          'wlgz_zhld_2',
          'wlgz_xf_2',
          // 'zhddzx_map_video_zbjk_2',
          '0camera-load3',
          '0zhdd_map_hdz',
          'wlgzsb',
          '0zhdd_map_hdz_2',
          'rckz-兴趣点通用',
          'camera-load4',
          // 'camera-load4_2',
          'sou_wg',
          'zhdd_map_hdz',
          'zhdd_map_hdz_2',
          // 'camera-lx-qiangji1',
          // 'camera-lx-qiuji1',
          // 'camera-lx-banqiu1',
          // 'camera-lx-gaodian1',
          // 'camera-zx-qiangji1',
          // 'camera-zx-qiuji1',
          // 'camera-zx-banqiu1',
          // 'camera-zx-gaodian1',
          'video-point1',
          'video-point_line',
          'video-point_circle',
          'video-point_other'
        ]
        this.rmAllLayer(rmPointIdArr)
      },
      rmPoint(id) {
        top.mapUtil.removeLayer(id)
      },
      showPop(coor, address, pointId) {
        let objData = {
          layerid: pointId,
          position: coor,
          popup: {
            offset: [50, -100],
            closeButton: true,
          },
          content: `<div
                style="
                  position: relative;
                  background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                  background-size: 100% 100%;
                  width: max-content;
                  min-height: 250px;
                ">
                <nav
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                    padding-bottom: 10px;
                    margin: 0 20px;
                    border-bottom: 1px solid;
                    border-bottom: 2px solid;
                    border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                    padding-left: 20px;
                  ">
                  <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">区域详情</h2>
                  <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none'">
                    <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
                  </span>
                </nav>
                <header
                  style="
                    padding-bottom: 15%;
                    margin: 10px 20px 0;
                    display: flex;
                    justify-content: space-between;
                    font-size: 25px;
                  ">
                  <div style="margin-left: 40px;color:#fff">
                     <p style="
                        width: 650px;
                        font-size: 30px;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                      "> 地   址 :<span title="${address}">${address}</span>
                    </p>
                  </div>
                </header>
              </div>`,
        }

        top.mapUtil._createPopup(objData)
      },
      // 清除地图
      rmLayer(id) {
        top.mapUtil.removeLayer(id)
      },
      rmAllLayer(id) {
        top.mapUtil.removeAllLayers(id)
      },
      closeAll() {
        top.frames['indexMapIcon'].mainIconVm.searchClick = false
        let rmPointIdArr = [
          'wlgz_dzzh',
          'wlgz_hjsb',
          'wlgz_skdbjcd',
          'wlgz_lcdc',
          'wlgz_syqjcd',
          'wlgz_nysb',
          'wlgz_yysjcd',
          'wlgz_qljc',
          'wlgz_ylqxlk',
          'wlgz_qyyd',
          'wlgz_lllk',
          'wlgz_zhld',
          'wlgz_xf',
          // "zhddzx_map_video_zbjk",
          // 'camera-lx-qiangji',
          // 'camera-lx-qiuji',
          // 'camera-lx-banqiu',
          // 'camera-lx-gaodian',
          // 'camera-zx-qiangji',
          // 'camera-zx-qiuji',
          // 'camera-zx-banqiu',
          // 'camera-zx-gaodian',
          'wlgz_dzzh_2',
          'wlgz_hjsb_2',
          'wlgz_skdbjcd_2',
          'wlgz_lcdc_2',
          'wlgz_syqjcd_2',
          'wlgz_nysb_2',
          'wlgz_yysjcd_2',
          'wlgz_qljc_2',
          'wlgz_ylqxlk_2',
          'wlgz_qyyd_2',
          'wlgz_lllk_2',
          'wlgz_zhld_2',
          'wlgz_xf_2',
          // 'zhddzx_map_video_zbjk_2',
          '0camera-load3',
          '0zhdd_map_hdz',
          'wlgzsb',
          '0zhdd_map_hdz_2',
          'rckz-兴趣点通用',
          'camera-load4',
          // 'camera-load4_2',
          'sou_wg',
          'zhdd_map_hdz',
          'zhdd_map_hdz_2',
          // 'camera-lx-qiangji1',
          // 'camera-lx-qiuji1',
          // 'camera-lx-banqiu1',
          // 'camera-lx-gaodian1',
          // 'camera-zx-qiangji1',
          // 'camera-zx-qiuji1',
          // 'camera-zx-banqiu1',
          // 'camera-zx-gaodian1',
          'video-point1',
          'video-point_line',
          'video-point_circle',
          'video-point_other'
        ]

        try {
          this.rmAllLayer(rmPointIdArr)
          top.mapUtil.plotTool.close()
        } catch (error) { }
        this.showSearchBox = false
      },
    },
    beforeDestroy() {
      console.log('销毁===============》')
      this.rmShape()
    },
  })
  top.emiter.on('beforeCloseIframe', (name) => {
    if (name === 'index_name_sou') {
      souName.closeAll()
    }
  })
  top.emiter &&
    top.emiter.on('longActiveType', () => {
      souName.closeAll()
    })

  // 移动事件
  let oDiv = document.getElementById('moveDome') // 当前元素
  // let self = this // 上下文
  // 禁止选择网页上的文字
  document.onselectstart = function () {
    return false
  }
  oDiv.onmousedown = function (e) {
    // 鼠标按下，计算当前元素距离可视区的距离
    let disX = e.clientX - oDiv.offsetLeft
    let disY = e.clientY - oDiv.offsetTop
    document.onmousemove = function (e) {
      // 通过事件委托，计算移动的距离
      let l =
        e.clientX - disX + Number(window.parent.$("iframe[name='index_name_sou']").css('left').replace('px', ''))
      let t = e.clientY - disY + Number(window.parent.$("iframe[name='index_name_sou']").css('top').replace('px', ''))
      // 移动当前元素
      // oDiv.style.left = l + 'px'
      // oDiv.style.top = t + 'px'
      parent.document.getElementById('index_name_sou').style.left = l + 'px'
      parent.document.getElementById('index_name_sou').style.top = t + 'px'
    }
    document.onmouseup = function (e) {
      document.onmousemove = null
      document.onmouseup = null
    }
    document.onmouseleave = function (e) {
      document.onmousemove = null
      document.onmouseup = null
    }
    // return false不加的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相mouseup失效
    // return false
  }
</script>

</html>