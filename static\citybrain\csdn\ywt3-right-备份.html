<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ywt3-right</title>
    <script src="./jquery/jquery-3.6.1.min.js"></script>
    <script src="./Vue/vue.js"></script>
    <script src="./echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="./js/DHWs_tc.js"></script>
    <script src="./js/iview.js"></script>
    <script src="./js/vue-seamless-scroll.min.js"></script>
    <!-- <link rel="stylesheet" href="./swiper/swiper-bundle.min.css" />
    <script src="./swiper/swiper-bundle.min.js"></script> -->
    <link rel="stylesheet" href="./elementui/css/elementui.css" />
    <script src="./elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <!--<script src="/static/citybrain/csdn/js/video/xgplayer-2.9.6.js"></script>
    <script src="/static/citybrain/csdn/js/video/xgplayer-flv.js"></script>
    <script src="/static/citybrain/csdn/js/video/xgplayer-hls.js"></script>-->
    <link rel="stylesheet" href="/static/citybrain/csdn/css/ywt3-right.css" />
  </head>
  <style>
    /* 鼠标禁用事件 */
    .mouse-pointer {
      cursor: pointer;
    }
    .mouse-not {
      /*cursor: not-allowed;*/
      cursor: default;
    }
    .ul-02 li:nth-child(1) {
      animation: movein 0.3s 0.12s both;
    }

    .ul-02 li:nth-child(2) {
      animation: movein 0.3s 0.24s both;
    }

    .ul-02 li:nth-child(3) {
      animation: movein 0.3s 0.36s both;
    }

    .ul-02 li {
      transform: translateX(0px);
      transition: 0.3s ease;
    }

    @keyframes movein {
      from {
        transform: translateX(360px);
        opacity: 0;
      }
      to {
        transform: translateX(0px);
        opacity: 1;
      }
    }
    .item-imgLeft {
      animation: jumpBoxHandler1 2s infinite;
    }
    .item-imgRight {
      animation: jumpBoxHandler2 2s infinite;
    }

    @keyframes jumpBoxHandler1 {
      0% {
        transform: translate(0px, 0px); /*开始位置*/
      }
      50% {
        transform: translate(10px, 0px); /* 可配置跳动方向 */
      }
      100% {
        transform: translate(0px, 0px); /*结束位置*/
      }
    }
    @keyframes jumpBoxHandler2 {
      0% {
        transform: translate(0px, 0px); /*开始位置*/
      }
      50% {
        transform: translate(-10px, 0px); /* 可配置跳动方向 */
      }
      100% {
        transform: translate(0px, 0px); /*结束位置*/
      }
    }
  </style>

  <body>
    <div id="app" class="app-box" v-cloak>
      <div class="top-text" style="display: none">
        <div class="top-num"><span>3</span>件</div>
        <div class="top-lb">
          <div class="text-lb">
            <div>
              <span class="top-icon warn-red"></span> 2022年7月14日10:42
              <span> [公交车与出租车发生碰撞]</span>
            </div>
            <div>
              <span class="top-icon warn-orange"></span> 2022年7月14日09:42
              <span> [农科教大楼前发现窨井盖丢失]</span>
            </div>
            <div>
              <span class="top-icon warn-yel"></span> 2022年7月14日06:42
              <span> [银都花园东3门口发现窨井盖丢失]</span>
            </div>
          </div>
        </div>
      </div>

      <div style="margin-top: 20px">
        <s-header-title
          title="一网统管"
          htype="1"
          data-time=""
        ></s-header-title>
        <!-- <s-header-title title="城市体征" htype="1" data-time="2022年7月11日"></s-header-title> -->
      </div>
      <!-- <s-header-title2
        title="场景应用"
        htype="1"
        style="margin: 0 auto"
      ></s-header-title2> -->
      <!-- 图片的轮播 -->
      <div
        class="top-item"
        style="margin-bottom: 10px; margin-top: 10px; display: none"
      >
        <div
          v-for="(item,index) in windowList"
          :key="index"
          @click="openWindow(index)"
          class="container-item1"
          style="height: 180px; cursor: pointer"
        >
          <div style="width: 440px; margin: 0 auto">
            <!-- <video width="320" height="160" autoplay>
              <source src="./img/video/1.mp4" type="video/mp4">
            </video> -->
            <img
              :src="item.url"
              alt=""
              height="160"
              style="margin-left: 80px"
            />
            <div
              class="s-c-white"
              style="text-align: center; white-space: nowrap; font-size: 28px"
            >
              {{item.name}}
            </div>
          </div>
        </div>
      </div>

      <!-- 顶部改版02 开始-->
      <div class="top-item-02">
        <div class="item-02" v-for="(item,index) in topTitleData">
          <div class="text-02">
            <!-- <img src="/static/citybrain/csdn/img/ywt/top-img-001.png" alt=""> -->
            <span class="yel-color">{{item.name}}</span>
          </div>
          <ul class="ul-02">
            <li v-for="ele in topListData[index]">
              <img
                src="/static/citybrain/csdn/img/ywt/top-icon-02.png"
                alt=""
              />
              <span>{{ele.name}}</span>
              <div>
                <span class="num-02">{{ele.value}}</span>
                <!-- {{ele.unit}} -->
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 顶部改版02 结束-->

      <s-header-title2
        htype="1"
        title="智能模块区"
        style="margin: 0 auto"
      ></s-header-title2>

      <div class="znmkq-container" style="position: relative">
        <img
          class="middle-lb-left"
          src="./img/ywt/lb-left.png"
          @click="pre"
          alt=""
          style="display: none"
        />
        <img
          class="middle-lb-right"
          src="./img/ywt/lb-right.png"
          @click="next"
          alt=""
          style="display: none"
        />

        <el-carousel
          indicator-position="outside"
          :autoplay="false"
          ref="zmd_top"
          arrow="never"
        >
          <el-carousel-item v-for="item in 4" :key="item">
            <div class="middle">
              <!-- item.isuse可点击 item.isuse外网-->
              <div
                v-for="(item,i) in zhinList"
                v-if="item.isuse==1 && item.sfnb==0"
                class="middle-item"
                @click="openNewPage(item.url)"
              >
                <div>{{item.znmkmc}}</div>
              </div>
              <!-- item.isuse可点击 item.isuse内部跳转
                onclick="top.commonObj.openMenuFun(item.url)"
              -->
              <div
                v-else-if="item.isuse==1 && item.sfnb==1"
                class="middle-item"
                @click="openPage(item.url)"
              >
                <div>{{item.znmkmc}}</div>
              </div>
              <!-- 不可点middle-item-black-->
              <div
                v-else-if="item.isuse==0"
                class="middle-item middle-item-black"
              >
                <div>{{item.znmkmc}}</div>
              </div>

              <img
                class="img01 ig breath-light"
                src="./img/ywt/znmkq2.png"
                alt=""
              />
              <img
                class="img02 ig breath-light"
                src="./img/ywt/znmkq2.png"
                alt=""
              />
              <img
                class="img03 ig breath-light"
                src="./img/ywt/znmkq2.png"
                alt=""
              />
              <img
                class="img04 ig breath-light"
                src="./img/ywt/znmkq2.png"
                alt=""
              />
              <img
                class="img05 ig breath-light"
                src="./img/ywt/znmkq2.png"
                alt=""
              />
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>

        <s-header-title2
        htype="1"
        title="多维集成域"
        :click-flag="true"
        style="cursor: pointer; margin: -20px auto 0"
        @click="openwinUrl"
      ></s-header-title2>
      <div class="dwjcy-container">
        <div
          class="top-box"
          style="display: flex; justify-content: space-evenly"
        >
          <button
            :class="sfShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
            @click="btnClick('sf')"
          >
            <img src="./img/ywt/app-pai.png" alt="" />
            算法{{sfCount}}类
          </button>
          <button
            :class="mxShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
            @click="btnClick('mx')"
          >
            <img src="./img/ywt/bg-model.png" alt="" />
            模型{{mxCount}}类
          </button>
          <button
            :class="zsShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
            @click="btnClick('zs')"
          >
            <img src="./img/ywt/zsk.png" alt="" />
            知识{{zsCount}}个
          </button>
          <button
            :class="zjShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
            @click="btnClick('zj')"
          >
            <img src="./img/ywt/组件.png" alt="" />
            组件{{zjCount}}个
          </button>
        </div>
        <div class="dwjcy-bottom sf" v-if="sfShow">
          <div style="width: 1900px; overflow: hidden; position: relative">
            <img
              style="
                position: absolute;
                top: 35%;
                left: 0px;
                width: 24px;
                height: 47px;
                z-index: 9999;cursor: pointer;
              "
              src="./img/ywt/lb-left.png"
              @click="sfpre"
              alt=""
            />
            <img
              style="
                position: absolute;
                top: 35%;
                right: 0px;
                width: 24px;
                height: 47px;
                z-index: 9999;cursor: pointer;
              "
              src="./img/ywt/lb-right.png"
              @click="sfnext"
              alt=""
            />
            <el-carousel
              indicator-position="outside"
              :autoplay="false"
              ref="zmd_sf"
              arrow="never"
            >
              <el-carousel-item v-for="obj,i in sfNewList" :key="i">
                <div class="dwcj-left">
                  <div
                    class="dwcj-left-item"
                    v-for="(item,index) in sfNewList[i]"
                  >
                    <img src="./img/ywt/dw-sf.png" alt="" width="120" />
                    <p>
                      <img
                        class="item-imgLeft"
                        src="./img/ywt/btn-right.png"
                        alt=""
                        style="width: 30px"
                      />
                      <span
                        class="s-c-yellow-gradient"
                        style="position: relative; top: -5px"
                        >访问{{numList[index]}}次</span
                      >
                      <img
                        class="item-imgRight"
                        src="./img/ywt/btn-left.png"
                        alt=""
                        style="width: 30px"
                      /><br />
                      <span style="margin-left: 20px; color: #dbd1d1"
                        >{{item.ywly}}</span
                      >
                      <span class="s-c-blue-gradient">{{item.value}}个</span>
                    </p>
                  </div>
                  <!-- <div class="dwcj-left-item">
                  <img src="./img/ywt/pjmx.png" alt="" />

                  <p>评价模型<span>1个</span></p>
                </div>
                <div class="dwcj-left-item">
                  <img src="./img/ywt/jcmx.png" alt="" />

                  <p>决策模型<span>2个</span></p>
                </div> -->
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
          <!-- <div class="dwcj-right" style="display: flex; align-items: center">
            <div class="dwcj-right-item">
              <p>上线</p>
              <div>
                0
                <span>个</span>
              </div>
            </div> -->
          <!-- <div class="dwcj-right-item">
              <p>支撑应用</p>
              <div>
                463
                <span>个</span>
              </div>
            </div> -->
          <!-- </div> -->
        </div>
        <div class="dwjcy-bottom mx" v-if="mxShow">
          <div style="width: 1900px; position: relative">
            <img
              style="
                position: absolute;
                top: 35%;
                left: 0px;
                width: 24px;
                height: 47px;
                z-index: 9999;cursor: pointer;
              "
              src="./img/ywt/lb-left.png"
              @click="mxpre"
              alt=""
            />
            <img
              style="
                position: absolute;
                top: 35%;
                right: -20px;
                width: 24px;
                height: 47px;
                z-index: 9999;cursor: pointer;
              "
              src="./img/ywt/lb-right.png"
              @click="mxnext"
              alt=""
            />
            <el-carousel
              indicator-position="outside"
              :autoplay="false"
              ref="zmd_mx"
              arrow="never"
            >
              <el-carousel-item v-for="obj,i in mxNewList" :key="i">
                <div class="dwcj-left">
                  <div
                    class="dwcj-left-item"
                    v-for="(item,index) in mxNewList[i]"
                  >
                    <img src="./img/ywt/dw-mx.png" alt="" width="120" />
                    <p>
                      <img
                        src="./img/ywt/btn-right.png"
                        alt="" class="item-imgLeft"
                        style="width: 30px"
                      />
                      <span class="s-c-yellow-gradient"
                        >访问{{numList1[index]}}次</span
                      >
                      <img
                        src="./img/ywt/btn-left.png"
                        alt="" class="item-imgRight"
                        style="width: 30px"
                      /><br />
                      <span style="margin-left: 20px; color: #dbd1d1"
                        >{{item.ywly}}</span
                      >
                      <span class="s-c-blue-gradient">{{item.value}}个</span>
                    </p>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
        <div class="dwjcy-bottom zs" v-if="zsShow">
          <div class="dwcj-left" style="width: 1900px">
            <div class="dwcj-left-item" v-for="(item,i) in zsList" :key="i">
              <img src="./img/ywt/dw-zs.png" alt="" width="120" />
              <p>{{item.ywly}}<span>{{item.value}}个</span></p>
            </div>
          </div>
        </div>
        <div class="dwjcy-bottom zj" v-if="zjShow">
          <!-- <div class="dwcj-left" style="width: 1900px">
            <div class="dwcj-left-item" v-for="(item,i) in zjList" :key="i">
              <img src="./img/ywt/dw-zj.png" alt="" width="120" />
              <p>{{item.ywly}}<span>{{item.value}}个</span></p>
            </div>
          </div> -->
          <div style="width: 1900px; position: relative">
            <img
              style="
                position: absolute;
                top: 35%;
                left: 0px;
                width: 24px;
                height: 47px;
                z-index: 9999;cursor: pointer;
              "
              src="./img/ywt/lb-left.png"
              @click="zjpre"
              alt=""
            />
            <img
              style="
                position: absolute;
                top: 35%;
                right: -20px;
                width: 24px;
                height: 47px;
                z-index: 9999;cursor: pointer;
              "
              src="./img/ywt/lb-right.png"
              @click="zjnext"
              alt=""
            />
            <el-carousel
              indicator-position="outside"
              :autoplay="false"
              ref="zmd_zj"
              arrow="never"
            >
              <el-carousel-item v-for="obj,i in zjNewList" :key="i">
                <div class="dwcj-left">
                  <div
                    class="dwcj-left-item"
                    v-for="(item,index) in zjNewList[i]"
                  >
                    <img src="./img/ywt/dw-zj.png" alt="" width="120" />
                    <p>
                      <img
                        src="./img/ywt/btn-right.png"
                        alt="" class="item-imgLeft"
                        style="width: 30px"
                      />
                      <span class="s-c-yellow-gradient"
                        >访问{{numList3[index]}}次</span
                      >
                      <img
                        src="./img/ywt/btn-left.png"
                        alt="" class="item-imgRight"
                        style="width: 30px"
                      /><br />
                      <span style="margin-left: 20px; color: #dbd1d1"
                        >{{item.ywly}}</span
                      >
                      <span class="s-c-blue-gradient">{{item.value}}个</span>
                    </p>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
      <!-- <s-header-title2
        title="城市生命体征"
        htype="1"
        style="cursor: pointer; margin: 0 auto"
        onclick="top.vm.cstzState = !top.vm.cstzState;top.commonObj.openMenuFun('ywtg-sy')"
      ></s-header-title2> -->
      <s-header-title2
        title="城市生命体征"
        htype="1"
        style="cursor: pointer; margin: 0 auto"
        :click-flag="true"
        onclick="top.vm.cstzState = !top.vm.cstzState;top.commonObj.openMenuFun('cstz-sy2')"
      ></s-header-title2>
      <div class="top-item" style="margin-bottom: 10px; margin-top: 10px">
        <div
          v-for="(item,index) in echartsList"
          :key="index"
          class="container-item"
        >
          <div class="container-item-title">
            <img
              src="./img/ywt/djtl.png"
              v-if="item.name==='党建统领'"
              style="vertical-align: middle"
            />
            <img
              src="./img/ywt/jjst.png"
              v-if="item.name==='经济生态'"
              style="vertical-align: middle"
            />
            <img
              src="./img/ywt/aqyn.png"
              v-if="item.name==='安全有序'"
              style="vertical-align: middle"
              alt=""
            />
            <img
              src="./img/ywt/ggfw.png"
              v-if="item.name==='公共服务'"
              style="vertical-align: middle"
              alt=""
            />

            {{item.name}}
          </div>
          <div class="container-item-subTitle">
            {{item.lei}}大类{{item.xiang}}项指标
          </div>
          <div style="display: flex; justify-content: center">
            <div style="text-align: center; line-height: 40px">
              <!--v-if="index==0"-->
              <p
                style="margin: 0"
                class="s-c-yellow-gradient s-font-45 s-w7 s-m-t-15"
              >
                {{item.info.split(',')[0]}}
              </p>
              <p style="margin-top: 10px" class="s-font-30 s-c-grey-light">
                {{item.info.split(',')[1]}}
              </p>
            </div>

            <!--<div
                :id="'echarts'+(index+1)"
                v-else
                style="width: 500px; height: 180px"
              ></div>-->
          </div>
        </div>
      </div>
      <s-header-title2
        htype="1"
        title="感知网"
        :click-flag="true"
        onclick="top.commonObj.openMenuFun('gzw-sy')"
        style="margin: 0 auto; cursor: pointer"
      ></s-header-title2>

      <!-- 视频 -->
      <!-- <iframe src="ywt-1.html" frameborder="0" style="width: 2070px; height: 500px;" frameborder="no" border="0"
        marginwidth="0" marginheight="0" scrolling="no" allowtransparency="yes"></iframe> -->

      <!-- 显示九宫格视频图片 -->
      <!-- <div class="item-list" style="position:static;flex-wrap: wrap;">
        <div class="item" v-for="(item, index) in imgvideoListShow" @click="openVideoFun(item)">
          <div class="item-head" style="position:static">{{ item.title }}</div>
          <div class="item-body">
            <div :id="'dom' + index" style="width:450px;height: 300px;">
              <img v-if="!togDayToNight" :src="`./img/cstz/day/${item.channelId}.jpg`" style="width: 100%;height: 100%;"
                alt="">
              <img v-else :src="`./img/cstz/night/${item.channelId}.jpg`" style="width: 100%;height: 100%;" alt="">
            </div>
          </div>

        </div>
      </div> -->
      <!-- 感知网 -->
      <div class="gzw">
        <div class="gzw-top">
          <div class="gzw-top-right">
            <span class="tt">前端感知设备</span>
            <span class="tt">(20类)</span>
            <div class="item-num yel-color">
              <div v-for="(item,index) in qdgzsbNum" :key="index">
                <!-- <span class="yel-color">{{item}}</span> -->
                <count-to
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="count-toNum s-c-yellow-gradient"
                >
                </count-to>
              </div>
            </div>
            <span class="tt">个</span>
          </div>
          <div class="gzw-top-left">
            <span class="tt" @click="openVideoFun1" style="cursor: pointer"
              >视频监控</span
            >
            <div class="item-num yel-color">
              <div v-for="(item,index) in spjkNum" :key="index">
                <!-- <span class="yel-color">{{item}}</span> -->
                <count-to
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="count-toNum s-c-yellow-gradient"
                >
                </count-to>
              </div>
              <span>路</span>
              <span class="videoBtn" @click="changeVideos"
                ><i
                  :class="videoflush ? 'el-icon-video-pause': 'el-icon-video-play'"
                ></i
              ></span>
            </div>
          </div>
        </div>
        <div class="gzw-bottom">
          <div class="gzw-right">
            <div class="gzw-item">
              <div
                class="class-item"
                style="width: 100px; height: 120px"
                v-for="(item,index) in wlgzsb"
                @click="addWlPoinFun(item)"
              >
                <div>
                  <img
                    :class="[wlgzId[item]?'mouse-pointer':'opacity-5 mouse-not']"
                    :src="`./img/ywt/${item}.png`"
                    alt=""
                    width="70"
                    height="70"
                  />
                </div>
                <span class="tooltiptext" :class="[wlgzId[item]?'':'opacity-5']"
                  >{{item}}</span
                >
              </div>
            </div>
          </div>
          <!-- <div class="gzw-right">
            <img
              src="./img/ywt/lb-left.png"
              style="width: 34px; height: 60px"
              class="left-img"
              alt=""
              @click="bottomPre"
            />
            <img
              src="./img/ywt/lb-right.png"
              style="width: 34px; height: 60px"
              class="right-img"
              alt=""
              @click="bottomNext"
            />
            <el-carousel
              indicator-position="outside"
              :autoplay="false"
              ref="zmd_bottom"
              arrow="never"
            >
              <el-carousel-item v-for="obj,i in newArr" :key="obj">
                <div class="gzw-item">
                  <div
                    class="class-item"
                    v-for="(item,index) in newArr[i]"
                    @click="addWlPoinFun(item)"
                  >
                    <div>
                      <img :src="`./img/ywt/${item}.png`" alt="" />
                    </div>

                    <div class="class-item-name">{{item}}</div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div> -->
          <div class="gzw-sp">
            <iframe src="ywt-3.html" id="ywt-3" frameborder="0" style="width: 850px; height: 330px"
                    frameborder="no" border="0" marginwidth="0" marginheight="0" scrolling="no" allowtransparency="yes" >
            </iframe>
            <!--<div class="gzw-sp-item">
              <div class="gzw-img" @click="openVideoFun(0)">
                &lt;!&ndash; <img src="./img/ywt/xlsp.png" alt="" /> &ndash;&gt;
              </div>
              <div class="gzw-sp-name">
                雪亮视频
                <span class="yel-color">91121路</span>
              </div>
            </div>
            <div class="gzw-sp-item">
              <div class="gzw-img" @click="openVideoFun(1)">
                &lt;!&ndash; <img src="./img/ywt/gdsp.png" alt="" /> &ndash;&gt;
              </div>
              <div class="gzw-sp-name">
                铁塔及其他视频
                <span class="yel-color">212路</span>
              </div>
            </div>-->
            <!-- 视频 -->
            <!--<div id="vidRight" style="width: 850px; height: 330px;display: flex;overflow: hidden;"></div>-->
          </div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>

  <script>

    // const DHWsInstance = DHWs.getInstance({
    //   reConnectCount: 2,
    //   connectionTimeout: 30 * 1000,
    //   messageEvents: {
    //     loginState() {
    //       // console.log('aaaa')
    //     },
    //   },
    // })

    // (function openIframe() {
    //   let iframe1 = {
    //     type: "openIframe",
    //     name: "main_mapIcon",
    //     src: baseURL.url + "/static/citybrain/csdn/commont/main_mapIcon.html",
    //     width: "415px",
    //     height: "480px",
    //     left: "5193px",
    //     top: "220px",
    //     zIndex: "555",
    //   };
    //   window.parent.postMessage(JSON.stringify(iframe1), "*");
    // })();

    var vm = new Vue({
      el: "#app",
      data() {
        return {
          qdgzsbNum: "",
          spjkNum: "",
          base_url: "https://csdn.dsjj.jinhua.gov.cn:8101/jhyjzh-server/screen_api/",
          videoToken: "",

          show_diong: false,
          show_diong_title: "",
          sfShow: true,
          mxShow: false,
          zsShow: false,
          zjShow: false,
          leftNum: 1,
          wlgzsbValue: "16",
          wlgzsb: [
            "车辆GPS",
            "水质监测",
            "水量监测",
            "水位监测",
            "土壤墒情",
            "大气质量",
            "雨量监测",
            "桥梁监测",
            "重量计量",
            "位移监测",
            "烟雾监测",
            "温度监测",
            "用电量",
            "电压监测",
            "水压监测",
            "路侧地磁",
            "倾角监测",
            "裂缝监测",
            "路灯监测",
            "垃圾桶",
          ],
          leftData: [
            {
              name: "区级中心",
              value: "10",
              unit: "个",
            },
            {
              name: "街道中心",
              value: "39",
              unit: "个",
            },
            {
              name: "村中心",
              value: "458",
              unit: "个",
            },
          ],
          rightData: [
            {
              name: "今日上报数",
              value: "463",
              unit: "个",
            },
            {
              name: "今日处置数",
              value: "75",
              unit: "",
            },
            {
              name: "今日超时未处理",
              value: "77",
              unit: "",
            },
          ],
          rightData0: [
            {
              name: "今日事件总数",
              value: "463",
              unit: "个",
            },
            {
              name: "今日事件完成率",
              value: "75%",
              unit: "",
            },
            {
              name: "今日派单及时率",
              value: "77%",
              unit: "",
            },
          ],
          rightData1: [
            {
              name: "今日上报数",
              value: "463",
              unit: "个",
            },
            {
              name: "今日处置数",
              value: "75",
              unit: "",
            },
            {
              name: "今日超时未处理",
              value: "77",
              unit: "",
            },
          ],
          rightData2: [
            {
              name: "今日上报数",
              value: "463",
              unit: "个",
            },
            {
              name: "今日处置数",
              value: "75",
              unit: "",
            },
            {
              name: "今日超时未处理",
              value: "77",
              unit: "",
            },
          ],

          togDayToNight: false,
          ydNum: "",
          videoTabIndex: 1,
          activeName: "0",
          gasList: [
            {
              content: "",
            },
          ],
          cityeventEnd: null,
          cityevent: [
            { name: "110接警数", value: 326, unit: "次", falg: false },
            { name: "119接警数", value: 85, unit: "次", falg: false },
            { name: "120接电数", value: 132, unit: "次", falg: false },
            { name: "8890接单数", value: 127, unit: "次", falg: false },
            { name: "交通事故数", value: 2, unit: "起", falg: false },
            { name: "综治执法案件", value: 10, unit: "起", falg: false },
            { name: "综治案件量", value: 105, unit: "起", falg: false },
            { name: "消防安全数", value: 5, unit: "起", falg: false },
            { name: "水位预警", value: 0, unit: "个", falg: false },
            { name: "地质灾害预警", value: 0, unit: "个", falg: false },
            { name: "饮用水预警", value: 0, unit: "个", falg: true },
            { name: "血库库存预警", value: 0, unit: "个", falg: false },
            { name: "水源地预警", value: 0, unit: "个", falg: false },
            { name: "旱情预警", value: 0, unit: "个", falg: false },
            { name: "气象预警", value: 0, unit: "个", falg: false },
            { name: "水库预警", value: 0, unit: "个", falg: false },
            // { name: '110接警数', value: 0, unit: '次', falg: false },
            // { name: '119接警数', value: 0, unit: '次', falg: false },
            // { name: '120接电数', value: 0, unit: '次', falg: false },
            // { name: '8890接单数', value: 0, unit: '次', falg: false },
            // { name: '交通安全', value: 0, unit: '起', falg: false },
            // { name: '交通拥堵指数', value: 0, unit: '', point: 2, falg: false },
            // { name: '空气质量', value: '', unit: '良好', falg: false },
            // { name: '气象预警', value: 0, unit: '个', falg: false },
            // { name: '水位预警', value: 0, unit: '个', falg: false },
            // { name: '地质灾害预警', value: 0, unit: '个', falg: false },
            // { name: '饮用水预警', value: 0, unit: '个', falg: false },
            // { name: '血库库存预警', value: 0, unit: '个', falg: false },
            // { name: '水源地预警', value: 0, unit: '个', falg: false },
            // { name: '旱情预警', value: 0, unit: '个', falg: false },
            // { name: '综治案件量', value: 0, unit: '个', falg: false },
            // { name: '水库预警', value: 0, unit: '个', falg: false }
          ],
          tableData: [
            {
              id: 1,
              name: "多湖街道栖凤街849号,步阳华府人行井盖破损",
              time: "2022-4-28",
              addr: "金东区多湖街道东盛村",
              state: 0,
            },
            {
              name: "华龙小区东南门口道路积水，影响车辆通行",
              time: "2022-3-22",
              addr: "华龙小区",
              state: 0,
            },
            {
              name: "联建小区西南门口,发现窨井盖丢失",
              time: "2022-3-18",
              addr: "联建小区西南门口",
              state: 0,
            },
            {
              name: "云都公寓发现过道堆货，存在安全隐患",
              time: "2022-2-17",
              addr: "云都公寓",
              state: 0,
            },
          ],

          //视频控件

          // ws: DHWsInstance,
          showModal: true,
          activePanel: "key1",
          isLogin: false,
          loginType: "1",
          loginIp: '*************',
          userName: "csdn",
          userPwd: "Jhcsdn2024$",
          loginPort: '7902',
          token: "",
          ctrlType: "playerWin",
          ctrl: "ctrl1",

          // 图片视频
          imgvideoListShow: [
            {
              channelId: "33079952001321083431",
              title: "市政府顶楼西北角",
              dom: "dom1",
            },
            {
              channelId: "33079952001321087131",
              title: "市政府屋顶",
              dom: "dom2",
            },
            {
              channelId: "33070352001320080216",
              title: "金华南站候车室",
              dom: "dom3",
            },
            {
              channelId: "33070255001320080388",
              title: "万固广场",
              dom: "dom4",
            },
          ],
          allImgvideoListShow: [],

          videoList: [],
          videoInterval: null,
          // 感知网
          spjkValue: [9, 1, 3, 3, 3],
          wxygValue: [8, 6],
          wlgzzbTypeValue: [1, 6],
          wlgzzbValue: [2, 1, 3, 4, 5],
          dlxxValue: [2, 0, 4, 5],
          echartsList: [],
          zhinList: [],
          sfCount: "",
          mxCount: "",
          zsCount: "",
          zjCount: "",
          sfList: [],
          mxList: [],
          zsList: [],
          zjList: [],
          cstzEcgartsTitle: [],
          cstzEcgartsTitle1: [],
          wlgzId: {
            车辆GPS: {
              url: "8aada4a47dbbe931017dc7550a2f22b0",
              name: "公交车",
            },
            水量监测: {
              url: "8aada4a47cc5876a017cc59215cd0005",
              name: "水雨情监测点",
            },
            水质监测: {
              url: "8aada4a47be36b72017be37a466b0006",
              name: "饮用水质传感器",
            },
            水位监测: {
              url: "8aada4a47d123213017d4b114e6c00a0",
              name: "水库大坝监测站",
            },
            土壤墒情: {
              url: "8aada4a47f4d661c017fb5a31eec0017",
              name: "地质灾害监测",
            },
          },
          lastWlName: "",
          windowList: [
            // {
            //   url:"/static/citybrain/csdn/img/video/防疫在线小.gif",
            //   name: "防疫在线平台",
            // },
            // {
            //   url: "/static/citybrain/csdn/img/video/外国人小.gif",
            //   name: "外国人驾驶舱",
            // },
            // {
            //   url: "/static/citybrain/csdn/img/video/工业固废小.gif",
            //   name: "工业固废一件事",
            // },
            // {
            //   url: "/static/citybrain/csdn/img/video/信义贷小.gif",
            //   name: "信义贷大数据",
            // },
          ],
          numList: [83, 26, 35, 44, 25],
          numList1: [126, 38, 56, 32, 16],
          numList3: [43, 56, 26, 30, 51],
          videoID: 0,
          videoflush: false,
          codeVidel: null,
          topTitleData: [],
          topListData: [
            [
              {
                name: "事件数",
                value: "235",
                nuit: "件",
              },
              {
                name: "处置数",
                value: "235",
                nuit: "件",
              },
              {
                name: "及时率",
                value: "95.8%",
                nuit: "",
              },
            ],
            [
              {
                name: "事件数",
                value: "235",
                nuit: "件",
              },
              {
                name: "处置数",
                value: "235",
                nuit: "件",
              },
              {
                name: "及时率",
                value: "95.8%",
                nuit: "",
              },
            ],
            [
              {
                name: "事件数",
                value: "235",
                nuit: "件",
              },
              {
                name: "处置数",
                value: "235",
                nuit: "件",
              },
              {
                name: "及时率",
                value: "95.8%",
                nuit: "",
              },
            ],
            [
              {
                name: "事件数",
                value: "235",
                nuit: "件",
              },
              {
                name: "处置数",
                value: "235",
                nuit: "件",
              },
              {
                name: "及时率",
                value: "95.8%",
                nuit: "",
              },
            ],
            [
              {
                name: "事件数",
                value: "235",
                nuit: "件",
              },
              {
                name: "处置数",
                value: "235",
                nuit: "件",
              },
              {
                name: "及时率",
                value: "95.8%",
                nuit: "",
              },
            ],
          ],
          setInterval: null,
        };
      },
      created() {
        this.getVideoNum();
        this.gettopListData();
      },
      // created() {
      //   console.log(this.cstzEcgartsTitle)
      //   var that = this
      //   $api('/ywtgLeft001').then((res) => {
      //     console.log('dddd1', res)
      //     that.cstzEcgartsTitle1 = res
      //   })

      //   $api('/ywtgLeft002').then((res) => {
      //     console.log('dddd2', res)
      //     that.cstzEcgartsTitle = res

      //     // this.cstzEcgartsTitle = res.data
      //     // res.forEach((element) => {
      //     //   for (let i = 0; i < that.cstzEcgartsTitle.length; i++) {
      //     //     if (
      //     //       element.ancestors_name ==
      //     //       that.cstzEcgartsTitle[i].ancestors_name
      //     //     ) {
      //     //       that.cstzEcgartsTitle[i].total1 = element.total
      //     //     }
      //     //   }
      //     // })
      //     // console.log(that.cstzEcgartsTitle);
      //   })
      // },
      mounted: function () {
        // top.emiter.on(top.EventType.szfzEmit, (name) => {
        //   DHWsInstance.openVideo([name]);
        // });
        var that = this;

        //    top.emiter.on(top.EventType.changeMap,()=>{
        //     that.showMapFun()
        //     })

        // that.login();
        that.getImgVideoFun(this.videoTabIndex);
        // that.openVideoWindow();
        that.initApi();
        that.queryWindow();
        that.setInterval = setInterval(() => {
          this.qdgzsbNum = "0";
          this.spjkNum = "0";
          that.gettopListData();
          that.getVideoNum();
        }, 30000);

        //视频每60秒切换
        // that.init(0);
        // let i=2
        // setInterval(function(){
        //   that.init(i)
        //   i+=2
        //   if(i===6){i=0}
        // },60000)
        window.addEventListener("message", (e) => {
          console.log("e", e);
          const item = JSON.parse(e.data.data.data);
          console.log(item);
          if (e.data && item.pointId == 4) {
            // console.log(classiFication())

            let coor = e.data.data.point.split(",");

            let name = item.item.device_name;
            let type = item.item.type_name;
            let mc =
              item.item.type_name.indexOf("车") === -1 ? "名称" : "车牌号";

            let objData = {
              funcName: "customPop",
              coordinates: coor,

              // coordinates: ['119.607129', '29.068155'],
              closeButton: true,
              html: `<div
            class="contain"
            onclick=" this.style.display = 'none'"
            style="
              height: 200px;
              position: absolute;
              width: max-content;
              display:inline-block;
              background-color: rgba(0, 0, 0, 0.8);
              border: 2px solid #00aae2;
              box-sizing: border-box;
              border-style: solid;
              border-width: 4px;
              border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
              border-image-slice: 1;
            "
          >
            <div
              class="title"
              style="
                background: linear-gradient(360deg, #096c8d, #073446);
                width: 100%;
                height: 60px;
                font-size: 32px;
                color: #fff;
                padding:0 30px;
                box-sizing: border-box;
                line-height: 60px;
              "
            >
             ${type}
            </div>
            <div

              class="content"
              style="
                display: flex;
                align-items: center;
                font-size: 28px;
                color: #fff;
                padding: 20px;
              "
            >


              <span style="display:inline-block;line-hight:30px;align-self: baseline;
        line-height: 55px;
    flex-shrink: 0"> ${mc}：${name}</span>
            </div>
          </div>`,
            };
            console.log(top.document.getElementById("map"));
            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          }
        });
      },
      // watch: {
      //   echartsList: {
      //     handler() {
      //       this.$nextTick(() => {
      //         let arr = this.echartsList;
      //         this.getEcharts(
      //           "echarts2",
      //           arr[1].info.split(",")[0],
      //           arr[1].info.split(",")[1]
      //         );
      //         this.getEcharts(
      //           "echarts3",
      //           arr[2].info.split(",")[0],
      //           arr[2].info.split(",")[1]
      //         );
      //         this.getEcharts(
      //           "echarts4",
      //           arr[3].info.split(",")[0],
      //           arr[3].info.split(",")[1]
      //         );
      //       });
      //     },
      //     immediate: true,
      //   },
      // },
      computed: {
        newArr() {
          let newArr = [];
          for (let i = 0; i < this.wlgzsb.length; i += 5) {
            newArr.push(this.wlgzsb.slice(i, i + 5));
          }

          return newArr;
        },
        mxNewList() {
          let newArr = [];
          for (let i = 0; i < this.mxList.length; i += 5) {
            newArr.push(this.mxList.slice(i, i + 5));
          }

          return newArr;
        },
        sfNewList() {
          let newArr = [];
          for (let i = 0; i < this.sfList.length; i += 5) {
            newArr.push(this.sfList.slice(i, i + 5));
          }

          return newArr;
        },
        zjNewList() {
          let newArr = [];
          for (let i = 0; i < this.zjList.length; i += 5) {
            newArr.push(this.zjList.slice(i, i + 5));
          }

          return newArr;
        },
        //   echartsList() {
        //     this.cstzEcgartsTitle.forEach((item) => {
        //       this.cstzEcgartsTitle1.forEach((obj) => {
        //         if (item.ancestors_name === obj.ancestors_name) {
        //           item.total1 = obj.total
        //         }
        //       })

        //       console.log(item)
        //     })

        //     return this.cstzEcgartsTitle
        //   },
      },
      methods: {
        openwinUrl(){
          // top.commonObj.openWinHtml("3840","2160", "http://10.24.160.89:7070/runner?project=63098ad84cad1432366b7601")
          top.commonObj.openWinHtml("3840","2160", "/static/citybrain/csdn/dwjcy.html")
        },
        gettopListData() {
          let that = this;
          $api("/csdnsy_right23").then((res) => {
            // console.log(res)
            that.topListData = res.map((item, index) => {
              return [
                { name: "事件数", value: item.sjs, unit: "件" },
                { name: "处置数", value: item.czs, unit: "件" },
                { name: "及时率", value: item.czl + "%", unit: "" },
              ];
            });
            // console.log(that.topListData)
            that.topTitleData = res.map((item) => {
              return { name: item.zb };
            });

            // console.log(that.topTitleData)
          });
        },
        // 切换视频
        changeVideos() {
          // if(this.videoID===12){
          //   this.videoID = 0
          // }else{
          //   this.videoID = this.videoID+2
          // }
          this.videoflush = !this.videoflush;
          document
            .getElementById("ywt-3")
            .contentWindow.postMessage(this.videoflush, "*");
        },
        getVideoNum() {
          $api("/wlgzLeft002").then((res) => {
            this.qdgzsbNum = res[0].device_num.toString();
          });

          $api("/vslLeft001").then((res1) => {
            // console.log(res1)
            // console.log(res1[0].total)
            this.spjkNum = res1[0].total.toString();
          });
        },

        // openVideoWindow(){
        //   let Data = {
        //     type: 'openIframe',
        //     name: 'ywt-3',
        //     src: baseURL.url + '/static/citybrain/csdn/ywt-3.html',
        //     left: "6741px",
        //     top: "1746px",
        //     width: "850px",
        //     height: "330px",
        //     zIndex:"200",
        //   }
        //   window.parent.postMessage(
        //     JSON.stringify(Data), '*'
        //   )
        // },

        init(i) {
          $.get(this.base_url + "zhdd/video/login").then((res) => {
            // console.log(res.data.token);
            this.videoToken = res.data.token;
            //保持token活跃
            $get(this.base_url + "zhdd/video/KeepLogin", {
              token: this.videoToken,
            });
            //获取视频数据
            // $get(this.base_url+'zhdd/yyh/yyh007',{ly:'sy'}).then((res)=>{
            //   this.funVideoList(res,i)
            // })
            $api("/csdn/cstz/cstzRight009").then((res) => {
              this.funVideoList(res, i);
            });
          });
        },
        //HLS视频
        funHLSVideo(params) {
          let { id, url, width = 420, height = 286 } = params;
          let player = new HlsJsPlayer({
            id: id,
            autoplay: true,
            isLive: true,
            volume: 0,
            url: url,
            poster: "",
            playsinline: true,
            controls: false,
            height: height,
            width: width,
          });
        },
        //FLV视频
        funFLVVideo(params) {
          let { id, url, width = 420, height = 286 } = params;
          let player = new FlvJsPlayer({
            id: id,
            autoplay: true,
            isLive: true,
            volume: 0,
            url: url,
            poster: "",
            playsinline: true,
            controls: false,
            height: height,
            width: width,
          });
        },
        funVideoList(data, i) {
          console.log(i);
          let sjs = "";
          let $html = [];
          $("#vidRight").empty();
          $get(this.base_url + "zhdd/video/getRealMonitor", {
            channelId: data[i].channelId,
            token: this.videoToken,
          }).then((res) => {
            sjs = Math.ceil(Math.random() * 100000);
            $html = [];
            $html.push(
              `<div class="bg-video"><div class="video-con" id="video${sjs}" data-url="${res.url}" data-id="video${sjs}" data-type="${data[i].urlType}"></div><div class="video-text ellipsis" title="${data[i].title}">${data[i].title}</div></div>`
            );
            $("#vidRight").append($html.join(""));
            this.funHLSVideo({ id: `video${sjs}`, url: res.url });
          });
          $get(this.base_url + "zhdd/video/getRealMonitor", {
            channelId: data[i + 1].channelId,
            token: this.videoToken,
          }).then((res) => {
            sjs = Math.ceil(Math.random() * 100000);
            $html = [];
            $html.push(
              `<div class="bg-video"><div class="video-con" id="video${sjs}" data-url="${
                res.url
              }" data-id="video${sjs}" data-type="${
                data[i + 1].urlType
              }"></div><div class="video-text ellipsis" title="${
                data[i + 1].title
              }">${data[i + 1].title}</div></div>`
            );
            $("#vidRight").append($html.join(""));
            this.funHLSVideo({ id: `video${sjs}`, url: res.url });
          });
        },

        queryWindow() {
          $api("/bmjr032").then((res) => {
            this.windowList = res;
          });
        },
        openWindow(index) {
          let code = "";
          let url = "";
          switch (index) {
            case 0:
              code = "bmjr053";
              break;
            case 1:
              code = "bmjr048";
              break;
            case 2:
              code = "bmjr047";
              break;
            case 3:
              code = "bmjr041";
              break;
          }
          $api("/bmjr031", { code: code }).then((res) => {
            console.log(res);
            url = res[0].LINKURL;
            top.commonObj.openWinHtml(res[0].WIDTH, res[0].HEIGHT, url);
          });
        },
        mxpre() {
          this.$refs.zmd_mx.prev();
        },
        mxnext() {
          this.$refs.zmd_mx.next();
        },
        sfpre() {
          this.$refs.zmd_sf.prev();
        },
        sfnext() {
          this.$refs.zmd_sf.next();
        },
        zjpre() {
          this.$refs.zmd_zj.prev();
        },
        zjnext() {
          this.$refs.zmd_zj.next();
        },
        async initApi() {
          // 党建统领 1,经济生态 2,安全有序 3,公共服务 4
          let a1 = await $api("/csdnsy_right11", { code: "党建统领" });
          let a2 = await $api("/csdnsy_right11", { code: "经济生态" });
          let a3 = await $api("/csdnsy_right11", { code: "安全有序" });
          let a4 = await $api("/csdnsy_right11", { code: "公共服务" });
          Promise.all([a1, a2, a3, a4]).then((res) => {
            res.map((els, i) => {
              this.echartsList.push(els[0]);
            });
          });
          $api("/csdnsy_right21").then((res) => {
            this.zhinList = res;
          });
          $api("/csdnsy_right22", { code: "算法" }).then((res) => {
            this.sfCount = res.length;
            this.sfList = res;
          });
          $api("/csdnsy_right22", { code: "模型" }).then((res) => {
            this.mxCount = res.length;
            this.mxList = res;
          });
          $api("/csdnsy_right22", { code: "知识" }).then((res) => {
            this.zsCount = res.length;
            this.zsList = res;
          });
          $api("/csdnsy_right22", { code: "组件" }).then((res) => {
            this.zjCount = res.length;
            this.zjList = res;
          });
        },
        btnClick(a) {
          // console.log(a)
          switch (a) {
            case "sf":
              this.sfShow = true;
              this.zsShow = false;
              this.mxShow = false;
              this.zjShow = false;
              break;
            case "mx":
              this.sfShow = false;
              this.zsShow = false;
              this.mxShow = true;
              this.zjShow = false;
              break;
            case "zs":
              this.sfShow = false;
              this.zsShow = true;
              this.mxShow = false;
              this.zjShow = false;
              break;
            case "zj":
              this.sfShow = false;
              this.zsShow = false;
              this.mxShow = false;
              this.zjShow = true;
              break;
            default:
              break;
          }
        },
        openNewPage(url) {
          window.open(url);
        },
        openPage(url) {
          top.commonObj.openMenuFun(url);
        },
        pre() {
          this.$refs.zmd_top.prev();
        },
        next() {
          this.$refs.zmd_top.next();
        },
        bottomPre() {
          this.$refs.zmd_bottom.prev();
        },
        bottomNext() {
          this.$refs.zmd_bottom.next();
        },
        getEcharts(dom, num, tel1, tel) {
          let myChart = echarts.init(document.getElementById(dom));

          let col =
            num < 20
              ? "#760443"
              : num < 60
              ? new echarts.graphic.LinearGradient(1, 0, 0, 1, [
                  {
                    offset: 1,
                    color: "#760443",
                  },
                  {
                    offset: 0,
                    color: "#12DE83",
                  },
                ])
              : new echarts.graphic.LinearGradient(1, 0, 0, 1, [
                  {
                    offset: 1,
                    color: "#760443",
                  },
                  {
                    offset: 0.5,
                    color: "#FAD276",
                  },
                  {
                    offset: 0,
                    color: "#12DE83",
                  },
                ]);

          var datas = {
            value: num,
            title: tel1,
          };
          let startAngle = 180,
            endAngle = 0;
          var fontColor = "#00f6f7";
          var seriesName = "";
          let noramlSize = 16;
          let state = "";
          let center = ["50%", "90%"];
          let wqradius = 0,
            nqradius = 0,
            kdradius;
          wqradius = "150%";
          nqradius = "130%";
          kdradius = "150%";
          let min = 0,
            max = 100;
          let nqColor = [[datas.value / 100, col]];

          let wqColor = "rgba(22, 138, 255, 0.9)";
          let circleLineW = 2;
          myChart.setOption({
            // title: {
            //   //标题
            //   show: true,
            //   x: 'center',
            //   top: '5%',
            //   text: tel,
            //   textStyle: {
            //     fontWeight: '500',
            //     fontSize: 24,
            //     color: '#fff',
            //   },
            // },

            series: [
              {
                type: "gauge",
                radius: "155%",
                startAngle,
                endAngle,
                center,
                pointer: {
                  show: false,
                },
                // data: dataArr,
                title: {
                  show: false,
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    //   color: "rgb(4, 145, 139)",
                    color: "#fff",
                    width: 2,
                    shadowOffsetX: 0,
                    shadowOffsetY: 0,
                    opacity: 1,
                  },
                },
                axisTick: {
                  show: true,
                  splitNumber: 4,
                  length: 8,
                  lineStyle: {
                    width: 1,
                    color: "#20c998",
                  },
                },
                splitLine: {
                  length: 15, //刻度节点线长度
                  lineStyle: {
                    width: 2,
                    color: "#20c998",
                  }, //刻度节点线
                },
                axisLabel: {
                  show: false,
                },
                detail: {
                  show: 0,
                },
                animationDuration: 4000,
              },
              {
                name: "白色圈刻度",
                type: "gauge",
                radius: kdradius,
                center,
                startAngle, //刻度起始
                endAngle, //刻度结束
                z: 7,
                splitNumber: 10,
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                  color: fontColor,
                  fontSize: noramlSize,
                  formatter: "{value}%",
                }, //刻度节点文字颜色
                pointer: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                detail: {
                  show: false,
                },
              },
              {
                name: "外层圈", //刻度背景
                type: "gauge",
                z: 2,
                radius: wqradius,
                startAngle,
                endAngle,
                center, //整体的位置设置
                axisLine: {
                  // 坐标轴线
                  lineStyle: {
                    // 属性lineStyle控制线条样式
                    color: [[1, wqColor]],
                    width: circleLineW,
                    opacity: 1, //刻度背景宽度
                  },
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                detail: {
                  show: 0,
                },
              },
              {
                name: "指针",
                type: "gauge",
                z: 9,
                radius: "160%",
                startAngle,
                endAngle,
                center, //整体的位置设置
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                min,
                max,
                //指针样式位置
                pointer: {
                  show: true,
                  width: 4,
                  length: "20%",
                  offsetCenter: [0, -105],
                },
                itemStyle: {
                  normal: {
                    color: wqColor,
                  },
                },
                detail: {
                  show: true,
                  offsetCenter: [0, -25],
                  formatter: [
                    "{value|" +
                      datas.value +
                      "}\n" +
                      "{tel|" +
                      datas.title +
                      "}",
                  ].join("\n"),
                  rich: {
                    value: {
                      fontSize: 30,
                      lineHeight: 50,
                      color: fontColor,
                      fontWeight: "700",
                    },
                    tel: {
                      fontSize: 26,
                      lineHeight: 20,
                      color: "#3EE579",
                      fontWeight: "500",
                    },
                  },
                },
                data: [datas.value], //指针位置
              },
              {
                name: "内层盘",
                type: "gauge",
                z: 6,
                radius: nqradius,
                startAngle,
                endAngle,
                center, //整体的位置设置
                axisLine: {
                  lineStyle: {
                    // 属性lineStyle控制线条样式//控制外圈位置
                    color: nqColor,
                    width: 15,
                    opacity: 0.9, //控制外圈位置，颜色，宽度，透明度
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
                detail: {
                  show: 0,
                },
              },
              {
                name: "内层小环",
                type: "gauge",
                z: 6,
                radius: "110%",
                startAngle,
                endAngle,
                center: center, //整体的位置设置
                axisLine: {
                  lineStyle: {
                    // 属性lineStyle控制线条样式//控制外圈位置
                    color: [[1, wqColor]],
                    width: circleLineW,
                    // opacity: 0.9 //控制外圈位置，颜色，宽度，透明度
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
                detail: {
                  show: 0,
                },
              },
            ],
          });
        },
        // showHtml(){
        //   window.parent.postMessage({
        //     value: 'cstz2',
        //     id: 'cstz2'
        //   },'*')
        // },
        showHtml() {
          top.vm.cstzState = !top.vm.cstzState;
          top.commonObj.openMenuFun("ywtg-sy");

          //  top.commonObj.openMenu('ywtg-sy')

          // if (top.vm.cstzState) {
          //   top.commonObj && top.commonObj.hideMap()
          // } else {
          //   top.commonObj && top.commonObj.showMap()
          // }
        },
        getImgVideoFun(code) {
          let time = new Date();
          let h = time.getHours();
          h >= 8 && h < 20
            ? (this.togDayToNight = false)
            : h >= 20 || h < 8
            ? (this.togDayToNight = true)
            : "";
          let that = this;
          $api("/csdn/cstz/cstzRight009", { code: code }).then((res) => {
            that.allImgvideoListShow = res;
            // that.imgvideoListShow=res
            // that.imgvideoListShow = res.slice(0, 4)
          });
        },

        // openVideoFun(ele) { // 调用创建控件接口
        //   DHWsInstance.openVideo([ele.channelId])

        // },
        openVideoFun(ele) {
          let num = ele * 4;
          this.imgvideoListShow = this.allImgvideoListShow.slice(num, num + 4);
          let arr = [];
          for (let item of this.imgvideoListShow) {
            arr.push(item.channelId);
          }
          var params = [

            '33079953001320081919',
            '33079953001320081919',
            '33079953001320081919',
            '33079953001320081919',
          ]
          top.DHWsInstance.openVideo(arr)

        },
        openVideoFun1() {
          let params = [];
          for (let i = 0; i < this.allImgvideoListShow.length; i++) {
            params.push(this.allImgvideoListShow[i].channelId);
          }
          // for (let item of this.allImgvideoListShow) {
          //   arr.push(item.channelId)
          // }

          top.DHWsInstance.openVideo(params)

        },

        setnum() {
          setInterval(() => {
            for (var i = 0; i < this.cityevent.length; i++) {
              i == 6
                ? (this.cityevent[i].value = "")
                : (this.cityevent[i].value = 0);
              if (i == 5) {
                continue;
              }
            }
            setTimeout(() => {
              this.cityevent = this.copy(this.cityeventEnd);
            }, 500);
          }, 60000);
        },
        copy(obj) {
          let newObj = null;
          if (typeof obj === "object" && obj !== null) {
            newObj = obj instanceof Array ? [] : {};
            for (let i in obj) {
              newObj[i] =
                typeof obj[i] === "object" ? this.copy(obj[i]) : obj[i];
            }
          } else {
            newObj = obj;
          }
          return newObj;
        },
        setOnesNum() {
          this.cityevent = [
            { name: "110接警数", value: 326, unit: "次", falg: false },
            { name: "119接警数", value: 85, unit: "次", falg: false },
            { name: "120接电数", value: 132, unit: "次", falg: false },
            { name: "8890接单数", value: 127, unit: "次", falg: false },
            { name: "交通事故数", value: 2, unit: "起", falg: false },
            { name: "综治执法案件", value: 10, unit: "起", falg: false },
            { name: "综治案件量", value: 105, unit: "起", falg: false },
            { name: "消防安全数", value: 5, unit: "起", falg: false },
            { name: "水位预警", value: 0, unit: "个", falg: false },
            { name: "地质灾害预警", value: 0, unit: "个", falg: false },
            { name: "饮用水预警", value: 0, unit: "个", falg: true },
            { name: "血库库存预警", value: 0, unit: "个", falg: false },
            { name: "水源地预警", value: 0, unit: "个", falg: false },
            { name: "旱情预警", value: 0, unit: "个", falg: false },
            { name: "气象预警", value: 0, unit: "个", falg: false },
            { name: "水库预警", value: 0, unit: "个", falg: false },
          ];
        },
        changeVideo(e, i) {
          let index = e.name * 4;
          this.imgvideoListShow = this.allImgvideoListShow.slice(
            index,
            index + 4
          );

          // this.getVideoList(e.name);
          // this.$nextTick(() => {
          //   this.create()
          // })
        },

        // destroy() {
        //   // 调用销毁控件接口
        //   if (!this.isLogin) {
        //     this.$Message.info('正在登陆客户端，请稍等......')
        //     return false
        //   }
        //   this.ws.destroyCtrl(['ctrl0', 'ctrl1', 'ctrl2', 'ctrl3'])
        // },
        // login() {
        //   // 调用登录接口



        //   this.ws.detectConnectQt().then((res) => {
        //     if (res) {
        //       // 连接客户端成功
        //       this.ws.login({
        //         loginIp: this.loginIp,
        //         loginPort: this.loginPort,
        //         userName: this.userName,
        //         userPwd: this.loginType == '1' ? this.userPwd : '',
        //         token: this.loginType == '2' ? this.token : '',
        //         https: 1,
        //       })
        //       // this.$Message.info('登录中...')
        //       console.log('登录中...')
        //       this.ws.on('loginState', (res) => {
        //         this.isLogin = res
        //         if (res) {
        //           // this.$Message.success('登录成功')
        //           console.log('登录成功')
        //           this.activePanel = 'key2'
        //         } else {
        //           // this.$Message.info('登录失败')
        //           console.log('登录失败')
        //         }
        //       })
        //     } else {
        //       // 连接客户端失败
        //       this.$Message.info('请重新安装客户端')
        //     }
        //   })
        // },


        leftClickFun(index) {
          this.leftNum = index;
          let name = "rightData" + index;
          this.rightData = this[name];
        },

        showMapFun() {
          let conePoint = [
            {
              name: "交通事故",
              icon: "red",
              time: "2022-07-14",
              lng: "119.636711",
              lat: "29.080511",
              thing: "公交车与出租车发生碰撞",
            },
            {
              name: "井盖丢失",
              icon: "red",
              time: "2022-07-14",
              lng: "119.65329",
              lat: "29.079287",
              thing: "农科教大楼前发现窨井盖丢失",
            },
            {
              name: "井盖丢失",
              icon: "red",
              time: "2022-07-14",
              lng: "119.643174",
              lat: "29.076174",
              thing: "银都花园东3门口发现窨井盖丢失",
            },
            {
              name: "道路积水",
              icon: "yellow",
              time: "2022-07-14",
              lng: "119.647585",
              lat: "29.083763",
              thing: "可爱幼儿园门口道路积水，影响车辆通行",
            },
            {
              name: "过道堆货",
              icon: "yellow",
              time: "2022-07-14",
              lng: "119.651948",
              lat: "29.080824 ",
              thing: "金德纺织发现过道堆货，存在安全隐患",
            },
            {
              name: "过道堆货",
              icon: "yellow",
              time: "2022-07-14",
              lng: "119.636398",
              lat: "29.083968",
              thing: "欧景名城发现过道堆货，存在安全隐患",
            },
          ];
          // 暂时注释加载墙体和锥体

          for (let item of conePoint) {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "loadConePoint",
                data: {
                  coord: [item.lng, item.lat, 30],
                  name: item.name,
                  time: item.time,
                  type: item.icon, // red,orange,yellow,green
                  key: ["事件名称", "告警事件", "告警详情"],
                  value: [item.name, item.time, item.thing],
                  content: [item.thing],
                },
              })
            );
          }
        },

        // 物联感知的点位数据
        addWlPoinFun(name) {
          let that = this;
          this.rmPointFun();
          if (
            this.wlgzId[name] == undefined ||
            this.wlgzId[name] == null ||
            this.lastWlName == name
          ) {
            this.lastWlName = "";
            return;
          }
          this.lastWlName = name;
          let id = this.wlgzId[name].url;

          axios({
            method: "post",
            url: baseURL.url + "/dtdd/iot/aep/v1/api/device/list",
            data: {
              type_id: id,
              page_size: 40000,
              page_num: 1,
            },
          }).then(function (allRes) {
            let pointData = [];
            let textData = [];
            let twoText = [];
            allRes.data.list.forEach((item, index) => {
              let til = item.type_name === "公交车GPS" ? ["车牌号"] : ["名称"];
              let str = {
                data: {
                  // title: item.type_name,
                  // key: til,
                  // value: [item.device_name],
                  item,
                  pointId: 4,
                },
                point: item.longitude + "," + item.latitude,
              };
              let textStr = {
                pos: [item.longitude, item.latitude, 0],
                text: item.device_name,
              };
              if (
                item.longitude != "" &&
                item.longitude != 0.0 &&
                item.longitude != null &&
                item.longitude != undefined
              ) {
                pointData.push(str);
                textData.push(textStr);
              }
            });
            that.pointTextMapFun(name, pointData, name + "1", textData, name);
          });
        },
        // 加载3D文字和地图点位的方法
        pointTextMapFun(icon, pointData, pointId, textData, textId) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad", //功能名称
              pointType: icon, //点位类型图标
              pointId: pointId,
              pointData: pointData,
              setClick: true,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          );
          // top.document.getElementById('map').contentWindow.Work.funChange(
          //   JSON.stringify({
          //     funcName: '3Dtext',
          //     id: textId,
          //     textData: textData,
          //     zoomShow: true,
          //     textSize: 30,
          //     color: [255, 255, 255, 1],
          //   })
          // )
        },
        // 清除点位
        rmPointFun() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
            })
          );
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rm3Dtext",
            })
          );
        },
      },
      beforeDestroy() {
        clearInterval(this.setInterval);
        this.videoInterval = null;
      },
    });
  </script>
</html>
