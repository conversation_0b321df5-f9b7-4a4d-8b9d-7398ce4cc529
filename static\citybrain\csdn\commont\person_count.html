<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>查询人数-兴趣点-弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <!-- <script src="/static/js/jslib/turf.js"></script> -->
    <script src="/static/js/jslib/vue-count-to.min.js"></script>

    <style>
      * {
        margin: 0;
        padding: 0;
      }

      .topBox001 {
        width: 750px;
        height: 580px;
        padding: 25px 15px;
        overflow: hidden;
        background: url('/static/citybrain/csrk_3840/img/commont/top-bg.png') no-repeat;
        background-size: cover;
        overflow: hidden;
        box-sizing: border-box;
        background-color: rgb(12 52 97 / 90%);
        border: 1px solid #00c0ff;
      }

      .hearder_h2 {
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 36px;
        font-weight: 500;
        text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
        background: linear-gradient(180deg, #caffff 0%, #caffff 0%, #ffffff 0%, #00c0ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .hearder_h2 > span {
        background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 20px;
        white-space: nowrap;
      }

      .w50 {
        width: 50%;
      }

      /* 表格样式修改 */
      .el-table {
        max-height: 420px !important;
        overflow: hidden;
        overflow-y: auto;
        color: rgb(197, 192, 192);
        padding-right: 5px !important;
        background: transparent !important;
      }

      .el-table th,
      .el-table tr {
        font-size: 30px !important;
      }

      .el-table tr {
        background: url('/static/citybrain/csdn/img/table_tr_bg.png') no-repeat;
        background-size: 99% 97%;
      }

      .el-table td,
      .el-table th.is-leaf {
        border: 0 !important;
      }

      .el-table tbody tr:hover > td {
        background: #1d4a7acb !important;
      }

      .el-table::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 2px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .el-table::-webkit-scrollbar-thumb {
        border-radius: 2px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 10px;
      }

      .el-table--border::after,
      .el-table--group::after,
      .el-table::before {
        background-color: transparent;
      }

      .el-checkbox,
      .el-checkbox__input {
        zoom: 120%;
      }

      .el-table .cell {
        line-height: normal;
      }

      .el-table .el-table__cell {
        padding: 8px 0 15px !important;
      }

      .el-checkbox__inner {
        width: 18px;
        height: 18px;
      }

      .number {
        display: inline-block;
        font-size: 40px;
      }

      .number .numbg {
        display: inline-block;
        width: 34px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        background: url('/static/citybrain/hjbh/img/rkzt/numBg.png') no-repeat;
        background-size: contain;
        margin: 0 4px;
        border-radius: 8px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <!-- static\EGS(v1.0.0)\lib\EGS(v1.0.0)\image\spritesImage -->
      <div class="topBox001">
        <p class="hearder_h2">
          <img src="/static/citybrain/csrk_3840/img/commont/tit-l.png" width="180px" alt="" />
          <span>区域情况</span>
          <img src="/static/citybrain/csrk_3840/img/commont/tit-r.png" width="180px" alt="" />
        </p>
        <div class="s-flex s-row-center s-font-32 s-c-white s-m-t-10 s-text-center">
          <p class="s-m-10">区域人数</p>
          <div class="s-flex">
            <div class="number s-c-yellow-gradient" v-for="(item, i) in count" :key="i">
              <span class="numbg" v-if="item!=','&&item!='.'">
                <count-to
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="s-c-yellow-gradient"
                ></count-to>
              </span>
              <span v-else>{{item}}</span>
            </div>
            人
          </div>
        </div>
        <template>
          <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%; margin-top: 20px"
            :show-header="false"
            @selection-change="handleSelectionChange"
          >
            <el-table-column prop="name" label="名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="s-flex">
                  <img
                    :src="'/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/'+scope.row.icon+'.png'"
                    alt=""
                    width="45px"
                  />
                  <span class="s-c-blue-gradient1">{{scope.row.name}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="num,unit" label="总值" show-overflow-tooltip>
              <template slot-scope="scope">{{scope.row.num}}{{scope.row.unit}}</template>
            </el-table-column>
            <el-table-column prop="avg,unit" label="均值" show-overflow-tooltip>
              <template slot-scope="scope">{{scope.row.avg}}{{scope.row.unit}}/每千人</template>
            </el-table-column>
            <el-table-column type="selection" width="80"></el-table-column>
          </el-table>
        </template>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      window.addEventListener('message', async (e) => {
        if (e.data && e.data.person_count_data) {
          console.log('person_count页面监控信息', e.data)
          if (e.data && e.data.person_count_data) {
            vm.serchFun(e.data.person_count_data)
          }
        }
        if (e.data && e.data.clear_point) {
          vm.clearPointList()
        }
      })
      var vm = new Vue({
        el: '#app',
        data: {
          count: '00000',
          tableData: [
            {
              name: '医疗机构',
              code: '医院',
              codenum: '170101,170102,170103,170104,170105',
              icon: '医院c',
              num: '',
              unit: '家',
              avg: '',
              arr: [],
            },
            {
              name: '学校',
              icon: '学校c',
              num: '',
              code: '学校',
              codenum: '160101, 160102,  160103, 160104,  160105, 160106,  160107',
              unit: '所',
              avg: '',
              arr: [],
            },
            {
              name: '公交车站',
              code: '公交站',
              codenum: '230105',
              icon: '公交车站c',
              num: '',
              unit: '个',
              avg: '',
              arr: [],
            },
            {
              name: '公共厕所',
              code: '公厕',
              codenum: '210102',
              icon: '公共厕所c',
              num: '',
              unit: '个',
              avg: '',
              arr: [],
            },
            {
              name: '运动场馆',
              code: '游泳场池,高尔夫球场,场馆',
              codenum: '180101,180105,180106',
              icon: '运动场馆c',
              num: '',
              unit: '个',
              avg: '',
              arr: [],
            },
            {
              name: '公园广场',
              code: '植物园,公园,广场,动物园',
              codenum: '180303,180307,180402,180404,180405',
              icon: '公园广场c',
              num: '',
              unit: '个',
              avg: '',
              arr: [],
            },
          ],
          clickName: [],
        },
        mounted() {},
        methods: {
          clearPointList() {
            this.tableData.map((res) => {
              top.mapUtil.removeLayer('personCount' + res.name)
            })
          },
          isHas(small, big) {
            let main = []
            if (small.length == 0) return big[0]
            for (let i = 0; i < small.length; i++) {
              big.filter((ele) => {
                if (ele != small[i]) {
                  main = ele
                }
              })
            }
            return main
          },

          handleSelectionChange(e) {
            if (this.clickName.length > e.length) {
              //减
              let item = this.isHas(e, this.clickName) //赛选减的目标
              top.mapUtil.removeLayer('personCount' + item.name)
            } else {
              //加
              let mainObj = this.isHas(this.clickName, e) //赛选减的目标
              let pointData = []
              mainObj.arr.map((item) => {
                let str = {
                  lng: item.x,
                  lat: item.y,
                  name: item.name,
                  address: item.address || item.city + item.county + item.town,
                }
                if (item.x != '' && item.x != null && item.x != undefined) {
                  pointData.push(str)
                }
              })
              let pointId = 'personCount' + mainObj.name
              this.pointTextMapFun(mainObj.icon, pointData, pointId, 1)
            }
            this.clickName = e
          },

          pointTextMapFun(icon, pointData, pointId, iconSize) {
            let popcfg = {
              offset: [50, -100],
              show: true,
              dict: { name: '名称', address: '地址' },
            }
            top.mapUtil.loadPointLayer({
              data: pointData,
              layerid: pointId, //图层id
              iconcfg: { image: icon, iconSize: iconSize }, //图标
              popcfg: popcfg,
            })
          },
          async serchFun(data) {
            let _this = this
            _this.count = data.countAll.toString()
            // data.mapGeoJson.geometry.type = 'MultiPolygon'
            let polygon = data.mapGeoJson.properties.searchgeometry
              ? {
                  type: 'Feature',
                  geometry: data.mapGeoJson.properties.searchgeometry,
                  properties: {},
                }
              : data.mapGeoJson
            let obj = { type: 'FeatureCollection', features: [polygon] }
            // const keys = window.parent.mapUtil.layers.drawMine ? 'searchgeometry' : 'geometry'
            // let obj = { type: 'FeatureCollection', features: [data.mapGeoJson] } //暂时注释、21日接口问题
            for (let index = 0; index < _this.tableData.length; index++) {
              !(function (index) {
                let axiosData = {
                  pageInfo: {
                    current: 1,
                    size: 10000,
                    totalSize: 0,
                  },
                  text: _this.tableData[index].codenum,
                  // text: _this.tableData[index].code,
                  tableNames: 'poi',
                  returnGeo: true,
                  sortField: '',
                  order: '',
                  geometry: JSON.stringify(obj),
                }
                // axiosData[keys] = JSON.stringify(obj)
                axios({
                  method: 'post',
                  url: baseURL.url + '/api2.0/solr-provider/api/data-sources/solr-search',
                  data: axiosData,
                  headers: {
                    'Content-Type': 'application/json',
                  },
                }).then((res) => {
                  top.mapUtil.removeLayer('personCount' + _this.tableData[index].name)
                  if (res.data.data == null) {
                    _this.tableData[index].arr = []
                    _this.tableData[index].num = '-'
                    _this.tableData[index].avg = _this.tableData[index].num == 0 ? 0 : '-'
                    return
                  }
                  console.log('res==>', res.data.data.dataList)
                  _this.tableData[index].arr = res.data.data
                  _this.tableData[index].num =
                    res.data.pageInfo.totalSize > res.data.data.length
                      ? res.data.pageInfo.totalSize
                      : res.data.data.length
                  _this.tableData[index].avg =
                    _this.tableData[index].num == 0
                      ? 0
                      : data.countAll == 0
                      ? _this.tableData[index].num
                      : ((_this.tableData[index].num / data.countAll) * 1000).toFixed(1)
                })
                // axios({
                //   method: 'post',
                //   // /gismap/spacesearch/esData/geojson/list-----http://10.24.161.200:9000
                //   url: '/gismap/spacesearch/esData/geojson/list',
                //   data: {
                //     esType1: _this.tableData[index].code,
                //     pageIndex: 1,
                //     pageSize: 3000,
                //     geojson: JSON.stringify(obj),
                //   },
                //   transformRequest: [
                //     function (data) {
                //       let ret = ''
                //       for (let it in data) {
                //         ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
                //       }
                //       ret = ret.substring(0, ret.lastIndexOf('&'))
                //       return ret
                //     },
                //   ],
                //   headers: {
                //     'Content-Type': 'application/x-www-form-urlencoded',
                //   },
                // }).then((res) => {
                //   _this.tableData[index].arr = res.data.data.dataList
                //   _this.tableData[index].num = res.data.data.totalNum
                //   // _this.tableData[index].avg =
                //   //   allCount == 0 ? 0 : ((res.data.data.totalNum / allCount) * 1000).toFixed(1)
                //   _this.tableData[index].avg = ((res.data.data.totalNum / data.countAll) * 1000).toFixed(1)
                // })
              })(index)
            }
          },
        },
      })
      top.emiter.on('beforeCloseIframe', (name) => {
        if (name === 'person_count') {
          top.mapUtil.plotTool.close()
          vm.clearPointList()
        }
      })
      top.emiter &&
        top.emiter.on('longActiveType', () => {
          top.mapUtil.plotTool.close()
          vm.clearPointList()
        })
    </script>
  </body>
</html>
