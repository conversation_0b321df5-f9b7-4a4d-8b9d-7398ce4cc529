<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>centerBot</title>
    <!-- <script src="./jquery/jquery-3.6.1.min.js"></script> -->
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>

    <script src="../echarts/echarts.min.js"></script>

    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="../elementui/css/elementui.css" />
    <script src="../elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <style>
      * {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
      }
      body {
        overflow: hidden;
      }
      html,
      body,
      #app,
      .center_container {
        width: 3379px;
        height: 813px;
        background: url('/static/citybrain/csdn/img/ywt/center-bc.png')
          no-repeat 100% 100%;
      }
      .mouse-pointer {
        cursor: pointer;
      }
      .mouse-not {
        /*cursor: not-allowed;*/
        cursor: default;
      }
      .title_container {
        display: flex;
        height: 126px;
        align-items: center;
      }
      .title_container div {
        width: 1427px;
        height: 9px;
      }
      .title_container h3 {
        font-size: 40px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #d6e7f9;
        margin: 0 22px;
        background: linear-gradient(
          180deg,
          #aed6ff 0%,
          #74b8ff 47.4853515625%,
          #9ccfff 50%,
          #ddeeff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .title_container .left-img {
        background: url('/static/citybrain/csdn/img/ywt/center-title-left.png.png')
          no-repeat 100% 100%;
      }
      .title_container .right-img {
        background: url('/static/citybrain/csdn/img/ywt/center-title-rght.png')
          no-repeat 100% 100%;
      }
      .content {
        padding: 0 30px;
      }
      .content-top {
        display: flex;
        height: 306px;
        width: 100%;
      }
      .sanicon {
        display: inline-block;
        width: 40px;
        height: 27px;
        background: url('/static/citybrain/csdn/img/ywt/三级标题图标.png')
          no-repeat;
      }
      .content_top_left,
      .content_top_right {
        flex: 1;
      }
      .dwjcy-bottom {
        display: flex;
        height: 166px;
      }
      .btnActive {
        width: 254px;
        height: 67px;
        /* background-image: url('/static/citybrain/csdn/img/ywt/doubleclick-active.png'); */
        /* background-image: url("./img/ywt/doubleclick-active.png"); */
        font-size: 32px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 67px;
        text-align: center;
        background-color: transparent;
        border: unset;
        border-bottom: 2px solid #fff;
      }
      .top-box-item:focus {
        background-image: url('./img/ywt/doubleclick-active.png');
      }
      .top-box {
        display: flex;
        justify-content: space-evenly;
        width: 70%;
        margin-left: 300px;
        margin-top: 10px;
        border-bottom: 2px solid #123c60;
      }
      .top-box-item {
        width: 254px;
        height: 67px;
        /* background-image: url('/static/citybrain/csdn/img/ywt/doubleclick.png'); */
        /* background-image: url("./img/ywt/doubleclick.png"); */
        font-size: 32px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 67px;
        text-align: center;
        background-color: transparent;
        border: unset;
        opacity: 0.6;
      }
      .el-carousel__item.is-animating {
        -webkit-transition: -webkit-transform 3s ease-in-out;
        transition: -webkit-transform 3s ease-in-out;
        transition: transform 3s ease-in-out;
        transition: transform 3s ease-in-out, -webkit-transform 3s ease-in-out;
      }
      .el-carousel__indicators {
        display: none;
      }
      .gzw-right {
        /* position: relative; */
        display: flex;
        /* overflow: hidden; */
      }
      .el-carousel__container {
        height: 300px;
      }
      .dwcj-left {
        width: 1900px;
        display: flex;
        justify-content: space-evenly;
        margin-top: 50px;
      }
      .dwcj-left p span {
        /* display: block; */
        /* margin-left: 20px; */
        font-size: 28px;
      }
      .dwcj-left p {
        white-space: nowrap;
        display: inline-block;
        font-size: 28px;
        font-family: FZZhengHeiS-DB-GB;
        font-weight: 700;
        /* color: #d6e7f9;
        background: linear-gradient(
          to bottom,
          #baeeee,
          #ffffff,
          #00f6f7,
          #ffffff
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent; */
      }
      .dwcj-left-item {
        display: flex;
        align-items: center;
      }
      .dwcj-right {
        display: flex;
        justify-content: space-around;
        width: 450px;
      }
      .dwjcy-bottom {
        display: flex;
        height: 166px;
      }
      .dwcj-right-item {
        font-size: 36px;
        width: 256px;
        height: 143px;
        background-image: url('./img/ywt/dwjc-right-bc.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        text-align: center;
      }
      .dwcj-right-item p {
        font-size: 40px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #ffffff;
        margin: 7px 0;
      }
      .dwcj-right-item div {
        font-size: 50px;
        font-family: Bebas Neue;
        font-weight: 400;
        color: #9aa9bf;
        font-weight: 700;

        background: linear-gradient(
          180deg,
          #ffffff 0%,
          #00c0ff 50.244140625%,
          #ffffff 53.0029296875%,
          #cbf2ff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .dwcj-right-item div span {
        font-size: 40px;
      }
      .item-imgLeft {
        animation: jumpBoxHandler1 2s infinite;
      }
      .item-imgRight {
        animation: jumpBoxHandler2 2s infinite;
      }

      @keyframes jumpBoxHandler1 {
        0% {
          transform: translate(0px, 0px); /*开始位置*/
        }
        50% {
          transform: translate(10px, 0px); /* 可配置跳动方向 */
        }
        100% {
          transform: translate(0px, 0px); /*结束位置*/
        }
      }
      @keyframes jumpBoxHandler2 {
        0% {
          transform: translate(0px, 0px); /*开始位置*/
        }
        50% {
          transform: translate(-10px, 0px); /* 可配置跳动方向 */
        }
        100% {
          transform: translate(0px, 0px); /*结束位置*/
        }
      }
      .ig {
        position: absolute;
        top: 100px;
      }
      .img01 {
        /* left: 387px;*/
        /* left: 488px; */
        left: 208px;
      }
      .img02 {
        /* left: 790px; */
        /* left: 992px; */
        left: 448px;
      }
      /* .img03 {
        left: 1090px;
      } */
      .img03 {
        /* left: 1200px; */
        left: 681px;
      }
      .img04 {
        left: 916px;
      }
      .img05 {
        left: 1154px;
      }
      .middle-lb-left,
      .middle-lb-right {
        position: absolute;
        width: 88px;
        /* height: 67px; */
        z-index: 999;
      }
      .middle-lb-left {
        left: -30px;
        top: 77px;
      }
      .middle-lb-right {
        right: -37px;
        top: 77px;
      }
      .middle-item {
        cursor: pointer;
        display: flex;
        width: 145px;
        font-size: 34px;
        font-family: Source Han Sans SC;
        font-weight: 500;
        color: #ffffff;
        background-size: cover;
        justify-content: center;
        background-image: url('/static/citybrain/csdn/img/ywt/znmkq.png');
      }
      .middle-item-black {
        /* cursor: not-allowed; */
        cursor: default;
        color: #b1b1b1;
        background-image: url('/static/citybrain/csdn/img/ywt/znmkq-1.png');
      }
      .middle-item div {
        margin-top: 57px;
        width: 79%;
        font-size: 28px;
        text-align: center;
      }

      /* .right-img {
        position: absolute;
        right: -5px;
        top: 25%;
        z-index: 999;
      }
      .left-img {
        position: absolute;
        left: 0px;
        top: 25%;
        z-index: 999;
      } */
      .middle {
        position: relative;
        display: flex;
        margin-bottom: 2px;
        justify-content: space-around;
        height: 100%;
        padding-top: 38px;
      }
      .el-carousel__container {
        overflow: hidden;
      }
      .content-bottom {
        display: flex;
      }
      .content-bottom > div:nth-child(1) {
        width: 30%;
      }
      .content-bottom > div:nth-child(4) {
        /* flex: 0.36; */
        width: 40%;
      }
      .content-bottom > div:nth-child(2),
      .content-bottom > div:nth-child(3) {
        width: 15%;
      }
      .zwy_item {
        width: 458px;
        height: 111px;
        background-size: 100% 100%;
        line-height: 120px;
        padding-left: 120px;
        box-sizing: border-box;
        margin-top: 30px;
      }
      .wlaq_item {
        width: 216px;
        height: 251px;
        background-image: url('/static/citybrain/csdn/img/ywt/wlaq.png');
        background-size: 100% 100%;
        text-align: center;
      }
      .wlaq_item > div:nth-child(1) {
        margin-top: 24px;
      }
      .wlaq_item > div:nth-child(2) {
        margin-top: 58px;
      }
      .gzw_left {
        width: 299px;
        margin-right: 41px;
      }
      .gzw_left > div {
        height: 300px;
      }
      .gzw_left > div > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 186px;
        height: 142px;
        font-size: 34px;
        font-family: FZZhengHeiS-DB-GB;
        font-weight: 400;
        color: #ffffff;
      }
      .gzw_left_active_top {
      }

      .gzw_left > div > div:nth-child(1) {
        background-image: url('/static/citybrain/csdn/img/ywt/btn-center-active.png');
      }
      .gzw_left > div > div:nth-child(2) {
        background-image: url('/static/citybrain/csdn/img/ywt/btn-center-bc.png');
      }
      .gzw_left > div > div:nth-child(1) div {
        width: 120px;
      }
      .gzw_right {
        height: 300px;
      }
      .gzw-right {
        display: flex;
        /* margin-top: -50px; */
        height: 300px;
      }
      .gzw-item {
        /* flex: 0.98; */

        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        position: relative;
      }
      .class-item {
        /* display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 192px;
        height: 234px; */
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background-image: url('/static/citybrain/csdn//img/ywt/item.png');
        background-size: 100% 100%;
      }
      .gzw-item {
        /* flex: 0.98; */
        width: 987px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        position: relative;
      }
      .gzw-item-0 {
        display: flex;
        align-content: center;
        font-size: 32px;
        margin-bottom: 20px;
      }
      .gzw-item-img {
        width: 120px;
        height: 115px;
        margin-right: 10px;
      }
      .gzw-item-img > img {
        width: 100%;
        height: 100%;
      }
      .gzw-tiem-name {
        font-size: 32px;
        color: #fff;
      }

      .gzw-sp {
        display: flex;
        margin-left: 58px;
      }
      .gzw-sp-item {
        width: 400px;
        height: 100%;
        font-size: 28px;
        text-align: center;
        color: #fff;
        background-image: url(./img/ywt/gdsp.png);
        background-size: 100% 100%;
      }
      .gzw-sp-item:first-child {
        margin-right: 50px;
        background-image: url(./img/ywt/xlsp.png);
      }
      .gzw-img {
        width: 320px;
        height: 230px;
        margin-bottom: 20px;
      }
      .gzw-img > img {
        width: 100%;
        height: 100%;
      }
      .class-item .tooltiptext {
        visibility: visible;
        white-space: nowrap;
        font-size: 24px;
        color: #fff;
        text-align: center;
        position: absolute;
        bottom: 10px;
        z-index: 1;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <div class="center_container">
        <div class="title_container">
          <!-- <img
            src="/static/citybrain/csdn/img/ywt/center-title-left.png"
            alt=""
          /> -->
          <div class="left-img"></div>
          <h3>一体化智能化公共数据平台</h3>
          <div class="right-img"></div>
          <!-- <img
            src="/static/citybrain/csdn/img/ywt/center-title-right.png"
            alt=""
          /> -->
        </div>

        <div class="content">
          <div class="content-top">
            <div class="content_top_left">
              <p
                style="
                  margin: -10px 0 5px;
                  color: #fff;
                  font-size: 38px;
                  cursor: pointer;
                "
                @click="openwinUrl"
              >
                <i class="sanicon"></i>多维集成域
              </p>
              <div class="dwjcy-container">
                <div
                  class="top-box"
                  style="display: flex; justify-content: space-evenly"
                >
                  <div
                    :class="sfShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
                    @click="btnClick('sf')"
                  >
                    <img
                      src="/static/citybrain/csdn/img/ywt/app-pai.png"
                      alt=""
                    />
                    算法{{sfCount}}类
                  </div>
                  <button
                    :class="mxShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
                    @click="btnClick('mx')"
                  >
                    <img
                      src="/static/citybrain/csdn/img/ywt/bg-model.png"
                      alt=""
                    />
                    模型{{mxCount}}类
                  </button>
                  <button
                    :class="zsShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
                    @click="btnClick('zs')"
                  >
                    <img src="/static/citybrain/csdn/img/ywt/zsk.png" alt="" />
                    知识{{zsCount}}个
                  </button>
                  <button
                    :class="zjShow? 'btnActive mouse-pointer':'top-box-item mouse-pointer'"
                    @click="btnClick('zj')"
                  >
                    <img src="/static/citybrain/csdn/img/ywt/组件.png" alt="" />
                    组件{{zjCount}}个
                  </button>
                </div>
                <div class="dwjcy-bottom sf" v-if="sfShow">
                  <div
                    style="width: 1900px; overflow: hidden; position: relative"
                  >
                    <img
                      style="
                        position: absolute;
                        top: 29%;
                        left: -28px;
                        width: 88px;

                        z-index: 9999;
                        cursor: pointer;
                      "
                      src="/static/citybrain/csdn/img/ywt/swiper-left.png"
                      @click="sfpre"
                      alt=""
                    />
                    <img
                      style="
                        position: absolute;
                        top: 29%;
                        right: -18px;
                        width: 88px;
                        z-index: 9999;
                        cursor: pointer;
                      "
                      src="/static/citybrain/csdn/img/ywt/swiper-right.png"
                      @click="sfnext"
                      alt=""
                    />
                    <el-carousel
                      indicator-position="outside"
                      :autoplay="false"
                      ref="zmd_sf"
                      arrow="never"
                    >
                      <el-carousel-item v-for="obj,i in sfNewList" :key="i">
                        <div class="dwcj-left">
                          <div
                            class="dwcj-left-item"
                            v-for="(item,index) in sfNewList[i]"
                          >
                            <img
                              src="/static/citybrain/csdn/img/ywt/dw-sf.png"
                              alt=""
                              width="120"
                            />
                            <p>
                              <img
                                class="item-imgLeft"
                                src="/static/citybrain/csdn/img/ywt/btn-right.png"
                                alt=""
                                style="width: 30px"
                              />
                              <span
                                class="s-c-yellow-gradient"
                                style="position: relative; top: -5px"
                                >访问{{numList[index]}}次</span
                              >
                              <img
                                class="item-imgRight"
                                src="/static/citybrain/csdn/img/ywt/btn-left.png"
                                alt=""
                                style="width: 30px"
                              /><br />
                              <span style="margin-left: 20px; color: #dbd1d1"
                                >{{item.ywly}}</span
                              >
                              <span class="s-c-blue-gradient"
                                >{{item.value}}个</span
                              >
                            </p>
                          </div>
                          <!-- <div class="dwcj-left-item">
                          <img src="./img/ywt/pjmx.png" alt="" />
        
                          <p>评价模型<span>1个</span></p>
                        </div>
                        <div class="dwcj-left-item">
                          <img src="./img/ywt/jcmx.png" alt="" />
        
                          <p>决策模型<span>2个</span></p>
                        </div> -->
                        </div>
                      </el-carousel-item>
                    </el-carousel>
                  </div>
                  <!-- <div class="dwcj-right" style="display: flex; align-items: center">
                    <div class="dwcj-right-item">
                      <p>上线</p>
                      <div>
                        0
                        <span>个</span>
                      </div>
                    </div> -->
                  <!-- <div class="dwcj-right-item">
                      <p>支撑应用</p>
                      <div>
                        463
                        <span>个</span>
                      </div>
                    </div> -->
                  <!-- </div> -->
                </div>
                <div class="dwjcy-bottom mx" v-if="mxShow">
                  <div style="width: 1900px; position: relative">
                    <img
                      style="
                        position: absolute;
                        top: 29%;
                        left: -28px;
                        width: 88px;

                        z-index: 9999;
                        cursor: pointer;
                      "
                      src="/static/citybrain/csdn/img/ywt/swiper-left.png"
                      @click="mxpre"
                      alt=""
                    />
                    <img
                      style="
                        position: absolute;
                        top: 29%;
                        right: -18px;
                        width: 88px;
                        z-index: 9999;
                        cursor: pointer;
                      "
                      src="/static/citybrain/csdn/img/ywt/swiper-right.png"
                      @click="mxnext"
                      alt=""
                    />
                    <el-carousel
                      indicator-position="outside"
                      :autoplay="false"
                      ref="zmd_mx"
                      arrow="never"
                    >
                      <el-carousel-item v-for="obj,i in mxNewList" :key="i">
                        <div class="dwcj-left">
                          <div
                            class="dwcj-left-item"
                            v-for="(item,index) in mxNewList[i]"
                          >
                            <img
                              src="/static/citybrain/csdn/img/ywt/dw-mx.png"
                              alt=""
                              width="120"
                            />
                            <p>
                              <img
                                src="/static/citybrain/csdn/img/ywt/btn-right.png"
                                alt=""
                                class="item-imgLeft"
                                style="width: 30px"
                              />
                              <span class="s-c-yellow-gradient"
                                >访问{{numList1[index]}}次</span
                              >
                              <img
                                src="/static/citybrain/csdn/img/ywt/btn-left.png"
                                alt=""
                                class="item-imgRight"
                                style="width: 30px"
                              /><br />
                              <span style="margin-left: 20px; color: #dbd1d1"
                                >{{item.ywly}}</span
                              >
                              <span class="s-c-blue-gradient"
                                >{{item.value}}个</span
                              >
                            </p>
                          </div>
                        </div>
                      </el-carousel-item>
                    </el-carousel>
                  </div>
                </div>
                <div class="dwjcy-bottom zs" v-if="zsShow">
                  <div class="dwcj-left" style="width: 1900px">
                    <div
                      class="dwcj-left-item"
                      v-for="(item,i) in zsList"
                      :key="i"
                    >
                      <img
                        src="/static/citybrain/csdn/img/ywt/dw-zs.png"
                        alt=""
                        width="120"
                      />
                      <p>{{item.ywly}}<span>{{item.value}}个</span></p>
                    </div>
                  </div>
                </div>
                <div class="dwjcy-bottom zj" v-if="zjShow">
                  <!-- <div class="dwcj-left" style="width: 1900px">
                    <div class="dwcj-left-item" v-for="(item,i) in zjList" :key="i">
                      <img src="./img/ywt/dw-zj.png" alt="" width="120" />
                      <p>{{item.ywly}}<span>{{item.value}}个</span></p>
                    </div>
                  </div> -->
                  <div style="width: 1900px; position: relative">
                    <img
                      style="
                        position: absolute;
                        top: 29%;
                        left: -28px;
                        width: 88px;

                        z-index: 9999;
                        cursor: pointer;
                      "
                      src="/static/citybrain/csdn/img/ywt/swiper-left.png"
                      @click="zjpre"
                      alt=""
                    />
                    <img
                      style="
                        position: absolute;
                        top: 29%;
                        right: -18px;
                        width: 88px;
                        z-index: 9999;
                        cursor: pointer;
                      "
                      src="/static/citybrain/csdn/img/ywt/swiper-right.png"
                      @click="zjnext"
                      alt=""
                    />
                    <el-carousel
                      indicator-position="outside"
                      :autoplay="false"
                      ref="zmd_zj"
                      arrow="never"
                    >
                      <el-carousel-item v-for="obj,i in zjNewList" :key="i">
                        <div class="dwcj-left">
                          <div
                            class="dwcj-left-item"
                            v-for="(item,index) in zjNewList[i]"
                          >
                            <img
                              src="/static/citybrain/csdn/img/ywt/dw-zj.png"
                              alt=""
                              width="120"
                            />
                            <p>
                              <img
                                src="/static/citybrain/csdn/img/ywt/btn-right.png"
                                alt=""
                                class="item-imgLeft"
                                style="width: 30px"
                              />
                              <span class="s-c-yellow-gradient"
                                >访问{{numList3[index]}}次</span
                              >
                              <img
                                src="/static/citybrain/csdn/img/ywt/btn-left.png"
                                alt=""
                                class="item-imgRight"
                                style="width: 30px"
                              /><br />
                              <span style="margin-left: 20px; color: #dbd1d1"
                                >{{item.ywly}}</span
                              >
                              <span class="s-c-blue-gradient"
                                >{{item.value}}个</span
                              >
                            </p>
                          </div>
                        </div>
                      </el-carousel-item>
                    </el-carousel>
                  </div>
                </div>
              </div>
            </div>
            <div class="content_top_right">
              <p style="margin: -10px 0 5px; color: #fff; font-size: 38px">
                <i class="sanicon"></i>智能模块区
              </p>
              <div class="znmkq-container" style="position: relative">
                <img
                  class="middle-lb-left"
                  src="/static/citybrain/csdn/img/ywt/swiper-left.png"
                  @click="pre"
                  alt=""
                />
                <img
                  class="middle-lb-right"
                  src="/static/citybrain/csdn/img/ywt/swiper-right.png"
                  @click="next"
                  alt=""
                />

                <el-carousel
                  indicator-position="outside"
                  :autoplay="false"
                  ref="zmd_top"
                  arrow="never"
                >
                  <el-carousel-item v-for="item in 4" :key="item">
                    <div class="middle">
                      <!-- item.isuse可点击 item.isuse外网-->
                      <div
                        v-for="(item,i) in zhinList"
                        v-if="item.isuse==1 && item.sfnb==0"
                        class="middle-item"
                        @click="openNewPage(item.url)"
                      >
                        <div>{{item.znmkmc}}</div>
                      </div>
                      <!-- item.isuse可点击 item.isuse内部跳转
                        onclick="top.commonObj.openMenuFun(item.url)"
                      -->
                      <div
                        v-else-if="item.isuse==1 && item.sfnb==1"
                        class="middle-item"
                        @click="openPage(item.url)"
                      >
                        <div>{{item.znmkmc}}</div>
                      </div>
                      <!-- 不可点middle-item-black-->
                      <div
                        v-else-if="item.isuse==0"
                        class="middle-item middle-item-black"
                      >
                        <div>{{item.znmkmc}}</div>
                      </div>

                      <img
                        class="img01 ig breath-light"
                        src="/static/citybrain/csdn/img/ywt/znmkq2.png"
                        alt=""
                      />
                      <img
                        class="img02 ig breath-light"
                        src="/static/citybrain/csdn/img/ywt/znmkq2.png"
                        alt=""
                      />
                      <img
                        class="img03 ig breath-light"
                        src="/static/citybrain/csdn/img/ywt/znmkq2.png"
                        alt=""
                      />
                      <img
                        class="img04 ig breath-light"
                        src="/static/citybrain/csdn/img/ywt/znmkq2.png"
                        alt=""
                      />
                      <img
                        class="img05 ig breath-light"
                        src="/static/citybrain/csdn/img/ywt/znmkq2.png"
                        alt=""
                      />
                    </div>
                  </el-carousel-item>
                </el-carousel>
              </div>
            </div>
          </div>

          <div class="content-bottom">
            <div class="conten_bottom_1">
              <p style="margin: -10px 0 5px; color: #fff; font-size: 38px">
                <i class="sanicon"></i>数据资源
              </p>

              <div
                style="
                  display: flex;
                  flex-wrap: wrap;
                  padding-top: 30px;
                  justify-content: space-between;
                "
              >
                <div
                  class="ggsjpt_item"
                  style="
                    display: flex;
                    justify-content: center;
                    height: 100px;
                    width: 27%;
                    margin-right: 30px;
                    margin-bottom: 40px;
                  "
                  v-for="(item,index) in ggsjptList"
                  :key="index"
                >
                  <img
                    :src="`/static/citybrain/csdn/img/ywt/${item.name}.png`"
                    style="width: 101px; margin-right: 10px"
                    alt=""
                  />

                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      white-space: nowrap;
                      justify-content: space-between;
                    "
                  >
                    <div
                      style="
                        font-size: 30px;
                        font-family: Source Han Sans SC;
                        font-weight: 500;
                        color: #ffffff;
                        line-height: 24px;
                      "
                    >
                      {{item.name}}
                    </div>
                    <div style="font-size: 20px" class="s-c-blue-gradient">
                      {{item.subTitle}}
                    </div>
                    <div
                      class="s-c-yellow-gradient"
                      style="font-size: 37px; line-height: 30px"
                    >
                      {{item.value}}
                      <span class="unit" style="font-size: 28px"
                        >{{item.unit}}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="conten_bottom_2">
              <p style="margin: -10px 0 5px; color: #fff; font-size: 38px">
                <i class="sanicon"></i>政务云
              </p>
              <div
                class="zwy_item"
                v-for="(item,index) in zwyList"
                :style="`background-image:url('/static/citybrain/csdn/img/ywt/${item.name}.png') ;`"
              >
                <div style="font-size: 32px; color: #fff">
                  {{item.name}}
                  <span class="s-c-yellow-gradient" style="font-size: 38px"
                    >{{item.value}}</span
                  >
                  <span s-c-yellow-gradient style="font-size: 28px"
                    >{{item.unit}}</span
                  >
                </div>
              </div>
              <div></div>
            </div>
            <div class="conten_bottom_3">
              <p style="margin: -10px 0 5px; color: #fff; font-size: 38px">
                <i class="sanicon"></i>网络安全
              </p>
              <div
                style="
                  display: flex;

                  justify-content: space-evenly;
                  margin-top: 30px;
                "
              >
                <div class="wlaq_item" v-for="(item,index) in wlaqList">
                  <div class="s-font-50 s-c-blue-gradient1">{{item.value}}</div>
                  <div class="s-font-30" style="color: #fff">{{item.name}}</div>
                  <div class="s-font-30" style="color: #fff">
                    ({{item.unit}})
                  </div>
                </div>
              </div>
            </div>
            <div class="conten_bottom_4">
              <p
                style="
                  margin: -10px 0 5px;
                  color: #fff;
                  font-size: 38px;
                  cursor: pointer;
                "
                onclick="top.commonObj.openMenuFun('gzw-sy')"
              >
                <i class="sanicon"></i>感知网
              </p>
              <div style="display: flex">
                <div class="gzw_left">
                  <div
                    style="
                      background-image: url('/static/citybrain/csdn/img/ywt/btn-center.png');
                      background-size: 100% 100%;
                      margin-top: -38px;
                      height: 389px;
                      padding-top: 51px;
                    "
                  >
                    <div style="margin-left: 43px" @click="tabBtn(0)">
                      <div>前端感知设备</div>
                    </div>
                    <div style="margin-left: 43px" @click="tabBtn(1)">
                      <div>视频监控</div>
                    </div>
                  </div>
                </div>
                <div class="gzw_right" style="flex: 1; position: relative">
                  <img
                    style="
                      position: absolute;
                      top: 29%;
                      left: -79px;
                      width: 88px;

                      z-index: 9999;
                      cursor: pointer;
                    "
                    src="/static/citybrain/csdn/img/ywt/swiper-left.png"
                    @click="wlgzpre"
                    alt=""
                    v-if="(tab==0&&newArr.length!=1) || (tab===1&& newArr1.length!=1)"
                  />
                  <img
                    v-if="(tab==0&&newArr.length!=1) || (tab===1&& newArr1.length!=1)"
                    style="
                      position: absolute;
                      top: 29%;
                      right: -18px;
                      width: 88px;
                      z-index: 9999;
                      cursor: pointer;
                    "
                    src="/static/citybrain/csdn/img/ywt/swiper-right.png"
                    @click="wlgznext"
                    alt=""
                  />
                  <el-carousel
                    v-if="tab==0"
                    indicator-position="outside"
                    :autoplay="false"
                    ref="wlgz_swper"
                    arrow="never"
                  >
                    <el-carousel-item v-for="obj,i in newArr" :key="i">
                      <div class="gzw-right">
                        <div class="gzw-item">
                          <div
                            class="class-item"
                            style="
                              width: 135px;
                              height: 148px;
                              margin-right: 60px;
                            "
                            v-for="(item,index) in obj"
                            @click="addWlPoinFun(item)"
                          >
                            <div>
                              <img
                                :class="[wlgzId[item]?'mouse-pointer':'opacity-5 mouse-not']"
                                :src="`/static/citybrain/csdn/img/ywt/${item}.png`"
                                alt=""
                                width="50"
                                height="50"
                              />
                            </div>
                            <span
                              class="tooltiptext"
                              :class="[wlgzId[item]?'':'opacity-5']"
                              >{{item}}</span
                            >
                          </div>
                        </div>
                      </div>
                    </el-carousel-item>
                  </el-carousel>
                  <el-carousel
                    v-if="tab==1"
                    indicator-position="outside"
                    :autoplay="false"
                    ref="wlgz_swper"
                    arrow="never"
                  >
                    <el-carousel-item v-for="obj,i in newArr1" :key="i">
                      <div class="gzw-right">
                        <div class="gzw-item">
                          <div
                            class="class-item"
                            style="
                              width: 135px;
                              height: 148px;
                              margin-right: 60px;
                            "
                            v-for="(item,index) in obj"
                            @click="addWlPoinFun(item)"
                          >
                            <div>
                              <img
                                :class="[wlgzId[item]?'mouse-pointer':'opacity-5 mouse-not']"
                                :src="`/static/citybrain/csdn/img/ywt/${item}.png`"
                                alt=""
                                width="50"
                                height="50"
                              />
                            </div>
                            <span
                              class="tooltiptext"
                              :class="[wlgzId[item]?'':'opacity-5']"
                              >{{item}}</span
                            >
                          </div>
                        </div>
                      </div>
                    </el-carousel-item>
                  </el-carousel>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>

  <script>
    var vm = new Vue({
      el: '#app',
      data() {
        return {
          tab: 0,
          sfShow: true,
          mxShow: false,
          zsShow: false,
          zjShow: false,
          sfCount: '',
          mxCount: '',
          zsCount: '',
          zjCount: '',
          sfList: [],
          mxList: [],
          zsList: [],
          zjList: [],
          numList: [83, 26, 35, 44, 25],
          numList1: [126, 38, 56, 32, 16],
          numList3: [43, 56, 26, 30, 51],
          zhinList: [],
          zwyList: [
            {
              name: '综合算力',
              value: 23264,
              unit: '核',
            },
            {
              name: '总储存',
              value: 2265176,
              unit: 'GB',
            },
          ],
          ggsjptList: [
            {
              name: '编目',
              subTitle: '今年新增目录数',
              value: 462,
              unit: '个',
            },
            {
              name: '归集',
              subTitle: '今年归集数据量',
              value: 462.5,
              unit: '亿条',
            },
            {
              name: '治理',
              subTitle: '今年数据治理总量',
              value: 462.5,
              unit: '万条',
            },
            {
              name: '共享',
              subTitle: '今年接口调用总量',
              value: 462.5,
              unit: '万次',
            },
            {
              name: '开放',
              subTitle: '今年新增发放数据集',
              value: 462,
              unit: '个',
            },
            {
              name: '安全',
              subTitle: '今年新增安全数据集',
              value: 462,
              unit: '个',
            },
          ],
          wlaqList: [
            {
              name: '防护应用数量',
              unit: '个',
              value: 264,
            },
            {
              name: '拦截次数',
              unit: '次',
              value: 264,
            },
          ],
          wlgzsb: [
            '车辆GPS',
            '水质监测',
            '水量监测',
            '水位监测',
            '土壤墒情',
            '大气质量',
            '雨量监测',
            '桥梁监测',
            '重量计量',
            '位移监测',
            '烟雾监测',
            '温度监测',
            '用电量',
            '电压监测',
            '水压监测',
            '路侧地磁',
            '倾角监测',
            '裂缝监测',
            '路灯监测',
            '垃圾桶',
          ],
          jkspList: [
            '位移监测',
            '电压监测',
            '水压监测',
            '水位监测',
            '土壤墒情',
            '路侧地磁',
            '倾角监测',
            '裂缝监测',
            '路灯监测',
            '垃圾桶',
          ],
          wlgzId: {
            车辆GPS: {
              url: '8aada4a47dbbe931017dc7550a2f22b0',
              name: '公交车',
            },
            水量监测: {
              url: '8aada4a47cc5876a017cc59215cd0005',
              name: '水雨情监测点',
            },
            水质监测: {
              url: '8aada4a47be36b72017be37a466b0006',
              name: '饮用水质传感器',
            },
            水位监测: {
              url: '8aada4a47d123213017d4b114e6c00a0',
              name: '水库大坝监测站',
            },
            土壤墒情: {
              url: '8aada4a47f4d661c017fb5a31eec0017',
              name: '地质灾害监测',
            },
          },
        }
      },
      created() {
        this.getApi()
      },
      mounted() {
        window.addEventListener('message', (e) => {
          console.log('e', e)
          if (!e.data.data) return
          const item = JSON.parse(e.data.data.data)
          console.log(item)
          if (e.data && item.pointId == 4) {
            // console.log(classiFication())

            let coor = e.data.data.point.split(',')

            let name = item.item.device_name
            let type = item.item.type_name
            let mc =
              item.item.type_name.indexOf('车') === -1 ? '名称' : '车牌号'

            let objData = {
              funcName: 'customPop',
              coordinates: coor,

              // coordinates: ['119.607129', '29.068155'],
              closeButton: true,
              html: `<div
            class="contain"
            onclick=" this.style.display = 'none'"
            style="
              height: 200px;
              position: absolute;
              width: max-content;
              display:inline-block;
              background-color: rgba(0, 0, 0, 0.8);
              border: 2px solid #00aae2;
              box-sizing: border-box;
              border-style: solid;
              border-width: 4px;
              border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
              border-image-slice: 1;
            "
          >
            <div
              class="title"
              style="
                background: linear-gradient(360deg, #096c8d, #073446);
                width: 100%;
                height: 60px;
                font-size: 32px;
                color: #fff;
                padding:0 30px;
                box-sizing: border-box;
                line-height: 60px;
              "
            >
             ${type}
            </div>
            <div

              class="content"
              style="
                display: flex;
                align-items: center;
                font-size: 28px;
                color: #fff;
                padding: 20px;
              "
            >


              <span style="display:inline-block;line-hight:30px;align-self: baseline;
        line-height: 55px;
    flex-shrink: 0"> ${mc}：${name}</span>
            </div>
          </div>`,
            }
            console.log(top.document.getElementById('map'))
            top.document
              .getElementById('map')
              .contentWindow.Work.funChange(JSON.stringify(objData))
          }
        })
      },
      computed: {
        newArr() {
          let newArr = []
          for (let i = 0; i < this.wlgzsb.length; i += 10) {
            newArr.push(this.wlgzsb.slice(i, i + 10))
          }

          return newArr
        },
        newArr1() {
          let newArr = []
          for (let i = 0; i < this.jkspList.length; i += 10) {
            newArr.push(this.jkspList.slice(i, i + 10))
          }

          return newArr
        },
        mxNewList() {
          let newArr = []
          for (let i = 0; i < this.mxList.length; i += 5) {
            newArr.push(this.mxList.slice(i, i + 5))
          }

          return newArr
        },
        sfNewList() {
          let newArr = []
          for (let i = 0; i < this.sfList.length; i += 5) {
            newArr.push(this.sfList.slice(i, i + 5))
          }

          return newArr
        },
        zjNewList() {
          let newArr = []
          for (let i = 0; i < this.zjList.length; i += 5) {
            newArr.push(this.zjList.slice(i, i + 5))
          }

          return newArr
        },
      },
      methods: {
        tabBtn(val) {
          this.tab = val
        },
        openwinUrl() {
          // top.commonObj.openWinHtml("3840","2160", "http://10.24.160.89:7070/runner?project=63098ad84cad1432366b7601")
          top.commonObj.openWinHtml(
            '3840',
            '2160',
            '/static/citybrain/csdn/dwjcy.html'
          )
        },
        // 物联感知的点位数据
        addWlPoinFun(name) {
          let that = this
          this.rmPointFun()
          if (
            this.wlgzId[name] == undefined ||
            this.wlgzId[name] == null ||
            this.lastWlName == name
          ) {
            this.lastWlName = ''
            return
          }
          this.lastWlName = name
          let id = this.wlgzId[name].url

          axios({
            method: 'post',
            url: baseURL.url + '/dtdd/iot/aep/v1/api/device/list',
            data: {
              type_id: id,
              page_size: 40000,
              page_num: 1,
            },
          }).then(function (allRes) {
            let pointData = []
            let textData = []
            let twoText = []
            allRes.data.list.forEach((item, index) => {
              let til = item.type_name === '公交车GPS' ? ['车牌号'] : ['名称']
              let str = {
                data: {
                  // title: item.type_name,
                  // key: til,
                  // value: [item.device_name],
                  item,
                  pointId: 4,
                },
                point: item.longitude + ',' + item.latitude,
              }
              let textStr = {
                pos: [item.longitude, item.latitude, 0],
                text: item.device_name,
              }
              if (
                item.longitude != '' &&
                item.longitude != 0.0 &&
                item.longitude != null &&
                item.longitude != undefined
              ) {
                pointData.push(str)
                textData.push(textStr)
              }
            })
            that.pointTextMapFun(name, pointData, name + '1', textData, name)
          })
        },
        // 加载3D文字和地图点位的方法
        pointTextMapFun(icon, pointData, pointId, textData, textId) {
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'pointLoad', //功能名称
              pointType: icon, //点位类型图标
              pointId: pointId,
              pointData: pointData,
              setClick: true,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          )
          // top.document.getElementById('map').contentWindow.Work.funChange(
          //   JSON.stringify({
          //     funcName: '3Dtext',
          //     id: textId,
          //     textData: textData,
          //     zoomShow: true,
          //     textSize: 30,
          //     color: [255, 255, 255, 1],
          //   })
          // )
        },
        // 清除点位
        rmPointFun() {
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'rmPoint',
            })
          )
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'rm3Dtext',
            })
          )
        },
        openNewPage(url) {
          window.open(url)
        },
        openPage(url) {
          top.commonObj.openMenuFun(url)
        },
        getApi() {
          $api('/csdnsy_right21').then((res) => {
            this.zhinList = res
          })
          $api('/csdnsy_right22', { code: '算法' }).then((res) => {
            this.sfCount = res.length
            this.sfList = res
          })
          $api('/csdnsy_right22', { code: '模型' }).then((res) => {
            this.mxCount = res.length
            this.mxList = res
          })
          $api('/csdnsy_right22', { code: '知识' }).then((res) => {
            this.zsCount = res.length
            this.zsList = res
          })
          $api('/csdnsy_right22', { code: '组件' }).then((res) => {
            this.zjCount = res.length
            this.zjList = res
          })
        },
        mxpre() {
          this.$refs.zmd_mx.prev()
        },
        mxnext() {
          this.$refs.zmd_mx.next()
        },
        wlgzpre() {
          this.$refs.wlgz_swper.prev()
        },
        wlgznext() {
          this.$refs.wlgz_swper.next()
        },
        mxpre() {
          this.$refs.zmd_mx.prev()
        },
        mxnext() {
          this.$refs.zmd_mx.next()
        },
        sfpre() {
          this.$refs.zmd_sf.prev()
        },
        sfnext() {
          this.$refs.zmd_sf.next()
        },
        zjpre() {
          this.$refs.zmd_zj.prev()
        },
        zjnext() {
          this.$refs.zmd_zj.next()
        },
        btnClick(a) {
          // console.log(a)
          switch (a) {
            case 'sf':
              this.sfShow = true
              this.zsShow = false
              this.mxShow = false
              this.zjShow = false
              break
            case 'mx':
              this.sfShow = false
              this.zsShow = false
              this.mxShow = true
              this.zjShow = false
              break
            case 'zs':
              this.sfShow = false
              this.zsShow = true
              this.mxShow = false
              this.zjShow = false
              break
            case 'zj':
              this.sfShow = false
              this.zsShow = false
              this.mxShow = false
              this.zjShow = true
              break
            default:
              break
          }
        },
        pre() {
          this.$refs.zmd_top.prev()
        },
        next() {
          this.$refs.zmd_top.next()
        },
      },
    })
  </script>
</html>
9
