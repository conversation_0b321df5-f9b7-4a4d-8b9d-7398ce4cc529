// import { setAct } from "../../../js/jslib/uti/l.js"
var requestType = 1 // 0: 本地 1: 真实地址
var msgFun = function (event) {
  if (event.data && event.data.type == 'videoCode') {
    console.log('道路拥堵===》', event.data.data)
    let item = {
      obj: {
        chn_name: '道路拥堵视频播放',
      },
      video_code: event.data.data,
    }

    let iframe1 = {
      type: 'openIframe',
      name: 'video_main_code',
      src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
      width: '100%',
      height: '100%',
      left: '0',
      top: '0',
      zIndex: '1000',
      argument: item,
    }
    window.parent.postMessage(JSON.stringify(iframe1), '*')
  }
}

window.addEventListener('message', msgFun, false)

top.emiter &&
  top.emiter.on('beforeDestroedIframe', () => {
    top.window.removeEventListener('popstate', msgFun)
  })

var vm = new Vue({
  el: '#app',
  data: {
    showMap: false,
    showBottom: true,
    control3Done: false,
    control3Dtwo: false,
    control3Dthree: false,
    showCity: false,
    cityLi: true,
    timer0: '',
    loadTime: '',
    sydTime: '',
    yysTime: null,
    cityList: [
      { name: '婺城区', falg: true },
      { name: '金东区', falg: true },
      { name: '武义县', falg: false },
      { name: '浦江县', falg: false },
      { name: '磐安县', falg: false },
      { name: '兰溪市', falg: false },
      { name: '义乌市', falg: false },
      { name: '东阳市', falg: false },
      { name: '永康市', falg: false },
    ],
    top5Map: 0,
    showRealpersonMap: false,
    riskDialogVisible: false,
    riskIndex: 3, // 风险信息列表下钻 index
    prpoDetail: {},
    areaValue: '',
    showCard: false,
    showQyrk: false,

    // 左边的标题
    firstTitle: [
      {
        name: '城市生命线',
        number: 1,
        parentNumber: 0,
        idName: 'title1',
        flag: false,
        children: [
          {
            typeName: '百货商场购物中心',
            checked: false,
          },
          {
            typeName: '移动',
            checked: false,
          },
          {
            typeName: '联通',
            checked: false,
          },
          {
            typeName: '长途汽车站',
            checked: false,
          },
          {
            typeName: '风景名胜',
            checked: false,
          },
        ],
      },
      {
        name: '城市公共安全',
        number: 2,
        parentNumber: 0,
        idName: 'title2',
        flag: false,
        children: [
          {
            typeName: '百货商场购物中心',
            checked: false,
          },
          {
            typeName: '移动',
            checked: false,
          },
          {
            typeName: '联通',
            checked: false,
          },
          {
            typeName: '长途汽车站',
            checked: false,
          },
          {
            typeName: '风景名胜',
            checked: false,
          },
        ],
      },
      {
        name: '城市运行管理',
        number: 3,
        parentNumber: 0,
        idName: 'title3',
        flag: false,
        children: [
          {
            typeName: '百货商场购物中心',
            checked: false,
          },
          {
            typeName: '移动',
            checked: false,
          },
          {
            typeName: '联通',
            checked: false,
          },
          {
            typeName: '长途汽车站',
            checked: false,
          },
          {
            typeName: '风景名胜',
            checked: false,
          },
        ],
      },
      {
        name: '城市公共服务',
        number: 4,
        parentNumber: 0,
        idName: 'title4',
        flag: false,
        children: [
          {
            typeName: '百货商场购物中心',
            checked: false,
          },
          {
            typeName: '移动',
            checked: false,
          },
          {
            typeName: '联通',
            checked: false,
          },
          {
            typeName: '长途汽车站',
            checked: false,
          },
          {
            typeName: '风景名胜',
            checked: false,
          },
        ],
      },
    ],

    qxName: '详情',

    leftTitle: '城市交通',
    altTitle: '疫情概况',
    altName: '',
    showAlt: false,
    showCity: false,
    left1Num: -1,
    showLeft1: false,
    showThreeAlt: false,
    showCursor: false,
    caseDialogVisible: false,
    medicalDialogVisible: false,
    orderDialogVisible: false,
    showHospital: false,
    showIsNormal: false,
    showInfo: true,

    data0Num: -1,
    data1Num: -1,
    data2Num: -1,
    data3Num: -1,
    altData0: {
      thTitle: [],
      trData: [],
      textTitle: '',
      textData: [],
    },
    altData1: {
      title: '',
      data: [],
      thTitle: [],
      trData: [],
    },

    smxNum: 0,
    ggaqNum: 0,
    yxglNum: 0,
    ggfwNum: 0,
    medicalDialogTabsTitle: ['近期就诊趋势', '每日就诊趋势'],
    medicalDialogTabsContent: ['recentChart', 'dailyChart'],
    medicalDialogTabsIndex: 0,
    caseTableHeader: ['就诊科室', '人次'],
    caseList: [],

    data2: [],

    data3: [],

    itemData0: [],
    itemData1: [],
    itemData2: [],
    itemData3: [],
    left1Data: [
      {
        name: '交通拥堵数',
        value: '18',
      },
      {
        name: '交通违章',
        value: '258',
      },
      {
        name: '交通事故',
        value: '258',
      },
    ],
    // 城市生命线
    cssmxData: {
      data0: [],
      data1: [],
      data2: [],
      data3: [],
      data4: [],
    },
    // 城市公共安全
    csggaqData: {
      data0: [],
      data1: [],
      data2: [],
      data3: [],
      data4: [],
    },
    // 城市运行管理
    csyxglData: {
      data0: [],
      data1: [],
      data2: [],
      data3: [],
      data4: [],
    },
    // 城市公共服务
    csggfwData: {
      data0: [],
      data1: [],
      data2: [],
      data3: [],
      data4: [],
    },
    // 城市生命线-城市燃气-停电小区个数
    tdxqData: {
      data0: [],
      data1: [],
      data2: [],
      data3: [],
      data4: [],
    },

    myChart: {},
    myChart1: {},
    // 地图打点正常的数据和异常的数据
    greenData: [],
    redData: [],
    greenYysData: [],
    redYysData: [],
    allVal: true,
    greenVal: true,
    redVal: true,
    yysData: {
      wcq: [],
      jyxq: [],
      lxs: [],
      pjx: [],
      yws: [],
      dys: [],
      pax: [],
      yks: [],
      wyx: [],
    },

    // 核酸检测点
    hsjcData: [],
    // 不需要被关闭的窗口
    openWind: [],
    // 重点区域检测覆盖率的数据
    zdqyfglData: [
      { value1: '1天1检', value2: '61926', value3: '99.10%', value4: '61386' },
      { value1: '2天1检', value2: '44919', value3: '92.00%', value4: '41321' },
      {
        value1: '3天1检',
        value2: '343396',
        value3: '93.60%',
        value4: '321314',
      },
      {
        value1: '7天1检',
        value2: '211699',
        value3: '96.30%',
        value4: '203759',
      },
      { value1: '校园检', value2: '38619', value3: '96.30%', value4: '37204' },
      // { "value1": "即时检", "value2": "", "value3": "", "value4": "" },
      // { "value1": "落地检", "value2": "", "value3": "", "value4": "" }
    ],
    zdqyfglData2: [
      {
        value1: '三小从业人员',
        value2: '92255',
        value3: '94.10%',
        value4: '86822',
      },
      {
        value1: '住院患者及陪护',
        value2: '1926',
        value3: '98.30%',
        value4: '1894',
      },
      {
        value1: '其他快递从业人员',
        value2: '19818',
        value3: '98.10%',
        value4: '19438',
      },
      {
        value1: '加油站工作人员',
        value2: '1569',
        value3: '99.00%',
        value4: '1554',
      },
      {
        value1: '商场超市工作人员',
        value2: '27191',
        value3: '94.40%',
        value4: '25673',
      },
      {
        value1: '商贸企业一线从业人员',
        value2: '5218',
        value3: '92.00%',
        value4: '4802',
      },
      {
        value1: '商贸企业其他从业人员',
        value2: '256',
        value3: '74.20%',
        value4: '190',
      },
      {
        value1: '国省道沿线修理店从业人员',
        value2: '256',
        value3: '100.00%',
        value4: '256',
      },
      { value1: '外卖骑手', value2: '4962', value3: '99.40%', value4: '4931' },
      {
        value1: '客运出租驾驶人员及车站工作人员',
        value2: '12475',
        value3: '97.90%',
        value4: '12207',
      },
      {
        value1: '宾馆酒店工作人员',
        value2: '5969',
        value3: '97.10%',
        value4: '5796',
      },
      {
        value1: '宾馆酒店工作人员',
        value2: '8115',
        value3: '93.00%',
        value4: '7546',
      },
      {
        value1: '工业企业重点岗位人员',
        value2: '46039',
        value3: '85.70%',
        value4: '39448',
      },
      {
        value1: '市场工作人员',
        value2: '56965',
        value3: '95.40%',
        value4: '54350',
      },
      {
        value1: '影院工作人员',
        value2: '762',
        value3: '95.40%',
        value4: '727',
      },
      {
        value1: '快递一线从业人员',
        value2: '12109',
        value3: '94.80%',
        value4: '11478',
      },
      {
        value1: '文博剧院工作人员',
        value2: '1078',
        value3: '99.60%',
        value4: '1074',
      },
      {
        value1: '洗浴足浴从业人员',
        value2: '3094',
        value3: '95.50%',
        value4: '2955',
      },
      {
        value1: '洗浴足浴从业人员',
        value2: '7697',
        value3: '93.20%',
        value4: '7176',
      },
      {
        value1: '网娱场所工作人员',
        value2: '4217',
        value3: '96.40%',
        value4: '4064',
      },
      {
        value1: '网约货运驾驶人员',
        value2: '14473',
        value3: '85.90%',
        value4: '12436',
      },
      {
        value1: '轨道交通工作人员',
        value2: '1022',
        value3: '99.50%',
        value4: '1017',
      },
      {
        value1: '酒吧、棋牌等场所从业人员',
        value2: '5817',
        value3: '95.30%',
        value4: '5546',
      },
      {
        value1: '零售药店从业人员',
        value2: '6011',
        value3: '99.80%',
        value4: '6000',
      },
      {
        value1: '餐饮业工作人员',
        value2: '2368',
        value3: '97.30%',
        value4: '2305',
      },
      {
        value1: '高速服务收费人员',
        value2: '1734',
        value3: '93.90%',
        value4: '1629',
      },
    ],
    zdhyjcData: [
      { value1: '行政执法', value2: '5817', value3: '95.30%', value4: '273 ' },
      { value1: '商务', value2: '45964', value3: '95.30%', value4: '2272 ' },
      { value1: '市场监管', value2: '5819', value3: '92.00%', value4: '466 ' },
      {
        value1: '属地政府',
        value2: '19818',
        value3: '94.40%',
        value4: '1110 ',
      },
      { value1: '交通运输', value2: '1569', value3: '97.00%', value4: '47 ' },
      { value1: '经信', value2: '27191', value3: '97.30%', value4: '734 ' },
      { value1: '指挥部', value2: '5218', value3: '98.20%', value4: '94 ' },
      { value1: '生态环境', value2: '256', value3: '99.00%', value4: '3 ' },
      { value1: '教育', value2: '256', value3: '99.30%', value4: '2 ' },
      { value1: '卫生健康', value2: '4962', value3: '99.80%', value4: '10 ' },
      { value1: '经信局', value2: '12475', value3: '85.90%', value4: '1759 ' },
      { value1: '民政', value2: '3094', value3: '99.50%', value4: '15 ' },
      { value1: '邮政管理', value2: '7697', value3: '95.30%', value4: '362 ' },
      { value1: '文化旅游', value2: '4217', value3: '99.80%', value4: '8 ' },
      { value1: '住建', value2: '14473', value3: '97.30%', value4: '391 ' },
      { value1: '宣传', value2: '1022', value3: '93.90%', value4: '62 ' },
      { value1: '合计', value2: '102248', value3: '92.56%', value4: '7608' },
    ],
    zdhyjcData2: [
      {
        value1: '商贸企业其他从业人员',
        value2: '256',
        value3: '74.20%',
        value4: '66',
      },
      {
        value1: '商贸企业一线从业人员',
        value2: '5218',
        value3: '92.00%',
        value4: '416',
      },
      {
        value1: '商场超市工作人员',
        value2: '27191',
        value3: '94.40%',
        value4: '1518',
      },
      {
        value1: '宾馆酒店工作人员',
        value2: '5605',
        value3: '97.00%',
        value4: '167',
      },
      {
        value1: '餐饮业工作人员',
        value2: '2368',
        value3: '97.30%',
        value4: '63',
      },
      {
        value1: '商超接触进口物品其他人员',
        value2: '109',
        value3: '98.20%',
        value4: '2',
      },
      {
        value1: '加油站工作人员',
        value2: '1569',
        value3: '99.00%',
        value4: '15',
      },
      {
        value1: '美发从业人员',
        value2: '3200',
        value3: '99.30%',
        value4: '24',
      },
      {
        value1: '商超进口商品接触人员',
        value2: '446',
        value3: '99.80%',
        value4: '1',
      },
      {
        value1: '国省道沿线旅馆从业人员',
        value2: '2',
        value3: '100.00%',
        value4: '0',
      },
    ],
    //疫情防控3D地图数据
    content3Done: [
      //核酸检测情况
      { name: '浦江县', num: 379231, unit: '人' },
      { name: '兰溪市', num: 385356, unit: '人' },
      { name: '婺城区', num: 727501, unit: '人' },
      { name: '金义新区', num: 601827, unit: '人' },
      { name: '义乌市', num: 2478645, unit: '人' },
      { name: '武义县', num: 384657, unit: '人' },
      { name: '永康市', num: 905299, unit: '人' },
      { name: '东阳市', num: 1066278, unit: '人' },
      { name: '磐安县', num: 110169, unit: '人' },
    ],
    content3Dtwo: [
      //集中隔离情况
      { name: '浦江县', num: 3386, unit: '人' },
      { name: '兰溪市', num: 3386, unit: '人' },
      { name: '婺城区', num: 33886, unit: '人' },
      { name: '金义新区', num: 33886, unit: '人' },
      { name: '义乌市', num: 35886, unit: '人' },
      { name: '武义县', num: 55886, unit: '人' },
      { name: '永康市', num: 45886, unit: '人' },
      { name: '东阳市', num: 25886, unit: '人' },
      { name: '磐安县', num: 65886, unit: '人' },
    ],
    content3Dthree: [
      //重点区域覆盖率
      { name: '浦江县', num: 99.0, unit: '%' },
      { name: '兰溪市', num: 92.0, unit: '%' },
      { name: '婺城区', num: 94.1, unit: '%' },
      { name: '金义新区', num: 98.3, unit: '%' },
      { name: '义乌市', num: 74.2, unit: '%' },
      { name: '武义县', num: 98.1, unit: '%' },
      { name: '永康市', num: 99.4, unit: '%' },
      { name: '东阳市', num: 100.0, unit: '%' },
      { name: '磐安县', num: 94.4, unit: '%' },
    ],
    // 河道预警
    hdyjData: [],
    // 拥堵路段数据
    hdldData: [],
    // 水库预警的视频播放
    videoData: [],
    skyjVideo: null,
    skyVideoHtml: null,
    showVideo: false,
    isVideoSrc: false,
    videoTitle: '查看视频',

    // 底部更新
    itemIndex: null,
    itemList: [],
    titleName: '安全有序',
    showMiddle: true,
    // 新的名字
    newFirstTitle: [
      {
        name: '党建统领',
        id: '1',
        num: '0',
      },
      {
        name: '经济生态',
        id: '2',
        num: '0',
      },
      {
        name: '安全有序',
        id: '3',
        num: '1',
      },
      {
        name: '公共服务',
        id: '4',
        num: '0',
      },
    ],
    togFirstTitle: 3,
    twoTitle1: [
      {
        name: '城市电力',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市燃气',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市用水',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市通信',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市设施',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '应急资源',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市消防',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '安全生产',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '自然灾害',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
    ],
    twoTitle2: [
      {
        name: '城市电力',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市燃气',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市用水',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市通信',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市设施',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
    ],
    twoTitle3: [
      {
        name: '城市电力',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市交通',
        num: 1,
        isClick: true,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市燃气',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市用水',
        num: 0,
        isClick: true,
        parentId: 3,
        type: 'data3',
      },
      {
        name: '城市通信',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市设施',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '应急资源',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市消防',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '安全生产',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '自然灾害',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
    ],
    twoTitle4: [
      {
        name: '城市电力',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市燃气',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市用水',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市通信',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市设施',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '应急资源',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '城市消防',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '安全生产',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
      {
        name: '自然灾害',
        num: 0,
        isClick: false,
        parentId: 1,
        type: 'data0',
      },
    ],
    // 轮播
    eindex: 0,
    Lfet: 0,
    isTr: true,
    lWid: null,
    // 顶部的内容
    topAltTitle: [
      {
        name: '文明城市',
        isUrl: false,
      },
      {
        name: '绿色水电',
        isUrl: true,
      },
      {
        name: '惠农补贴',
        isUrl: false,
      },
      {
        name: '自贸区',
        isUrl: false,
      },
      {
        name: '"梅"好兰溪',
        isUrl: true,
      },
      {
        name: '文旅大数据',
        isUrl: false,
      },
      {
        name: '数字田园',
        isUrl: false,
      },
      {
        name: '兰江流域数字孪生',
        isUrl: false,
      },
      {
        name: '共富家园',
        isUrl: false,
      },
      {
        name: '数字社会',
        isUrl: false,
      },
      {
        name: '影视产业',
        isUrl: true,
      },
      {
        name: '国际商贸',
        isUrl: true,
      },
      {
        name: '世界五金',
        isUrl: false,
      },
      {
        name: '温泉茶叶',
        isUrl: false,
      },
      {
        name: '中药材',
        isUrl: true,
      },
    ],
    topAltTitNum: -1,
    //各区县体征指标下拉
    showSelct: false,
    optionsData: ['婺城区', '金义新区', '东阳市', '义乌市', '永康市', '兰溪市', '浦江县', '武义县', '磐安县', '开发区'],
    optionValue: '县(市、区)城市体征',
  },

  updated() {},
  created() {},
  mounted() {
    let that = this
    // this.clearPoint()
    // this.clearHotMapAll()
    // this.drawQgMap()

    //   top.emiter.on(top.EventType.cstz3Emit, (data) => {
    //     console.log(data);
    //     this.titleName=data
    //   })

    // 保留的
    // this.$nextTick(() => {
    //   that.drawRecentChart()
    //   that.drawDailyChart()
    // })
    // this.getMsg()
    this.initFun()
    // this.getFirstTitleFun()
    // top.window.emiter.on(top.EventType.szfzEmit, (info) => {
    //   this.showMiddle=info
    // })
  },
  methods: {
    // data里面的变量调用接口
    async initFun() {
      let that = this

      document.getElementById('main01').style.display = 'block'
      this.itemList = this.twoTitle3
      this.lWid = (this.itemList.length - 1) * 450
      $api('/csdn/cstz2-middle/cstzMiddle002').then((res) => {
        this.itemData0 = this.fileTerDataFun(res, 'data0')
        this.itemData1 = this.fileTerDataFun(res, 'data1')
        this.itemData2 = this.fileTerDataFun(res, 'data2')
        this.itemData3 = this.fileTerDataFun(res, 'data3')
      })
      let cssms = await $api('/csdn/cstz2-middle/cstzMiddle003', { code: 1 })
      this.cssmxData.data0 = this.fileTerDataFun(cssms, 'data0')
      this.cssmxData.data1 = this.fileTerDataFun(cssms, 'data1')
      this.cssmxData.data2 = this.fileTerDataFun(cssms, 'data2')
      this.cssmxData.data3 = this.fileTerDataFun(cssms, 'data3')
      this.cssmxData.data4 = this.fileTerDataFun(cssms, 'data4')
      let ssgg = await $api('/csdn/cstz2-middle/cstzMiddle003', { code: 2 })
      this.csggaqData.data0 = this.fileTerDataFun(ssgg, 'data0')
      this.csggaqData.data1 = this.fileTerDataFun(ssgg, 'data1')
      this.csggaqData.data2 = this.fileTerDataFun(ssgg, 'data2')
      this.csggaqData.data3 = this.fileTerDataFun(ssgg, 'data3')
      this.csggaqData.data4 = this.fileTerDataFun(ssgg, 'data4')

      let csyx = await $api('/csdn/cstz2-middle/cstzMiddle003', { code: 3 })
      this.csyxglData.data0 = this.fileTerDataFun(csyx, 'data0')
      this.csyxglData.data1 = this.fileTerDataFun(csyx, 'data1')
      this.csyxglData.data2 = this.fileTerDataFun(csyx, 'data2')
      this.csyxglData.data3 = this.fileTerDataFun(csyx, 'data3')
      this.csyxglData.data4 = this.fileTerDataFun(csyx, 'data4')

      let csgg = await $api('/csdn/cstz2-middle/cstzMiddle003', { code: 4 })
      this.csggfwData.data0 = this.fileTerDataFun(csgg, 'data0')
      this.csggfwData.data1 = this.fileTerDataFun(csgg, 'data1')
      this.csggfwData.data2 = this.fileTerDataFun(csgg, 'data2')
      this.csggfwData.data3 = this.fileTerDataFun(csgg, 'data3')
      this.csggfwData.data4 = this.fileTerDataFun(csgg, 'data4')

      $api('/csdn/cstz2-middle/cstzMiddle007').then((res) => {
        this.tdxqData = res[0]
      })
      $api('/csdn/cstz2-middle/cstzMiddle009').then((res) => {
        this.caseList = res
      })
      $api('/csdn/cstz2-middle/cstzMiddle010', { code: 1 }).then((res) => {
        this.data2 = res
      })
      $api('/csdn/cstz2-middle/cstzMiddle010', { code: 2 }).then((res) => {
        this.data3 = res
      })
      // $api('/csdn/cstz2-middle/cstzMiddle012').then((res) => {
      //   this.hsjcData = res
      //   this.csggfwData.data2[4].value = this.hsjcData.length
      // })
      let aa = "'" + '婺城区' + "'" + ',' + "'" + '金东区' + "'"
      $api('/cstz_baiduydd', { addressName: aa }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          let time = new Date(res[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res[i].idx = Number(res[i].idx)
        }
        this.hdldData = res
      })
      this.getYysDataFun()
      document.getElementById('main01').style.display = 'block'
      // this.csggaqData.data4[1].value = 0
      // this.content3Done.forEach((item, index) => {
      //   this.csggaqData.data4[1].value += item.num
      // })
      // this.csggaqData.data4[2].value = 0
      // this.content3Dtwo.forEach((item) => {
      //   this.csggaqData.data4[2].value += item.num
      // })
      // this.csggaqData.data4[3].value = 0
      // let num1 = 0
      // this.content3Dthree.forEach((item) => {
      //   num1 += item.num
      // })
      // this.csggaqData.data4[3].value =
      //   (num1 / this.content3Dthree.length).toFixed(2) + '%'

      // this.csggaqData.data4[4].value = 0
      // let num2 = null
      // this.zdhyjcData.forEach((item) => {
      //   let num = +item.value3.replace('%', '')
      //   num2 += num
      // })
      // this.csggaqData.data4[4].value =
      //   (num2 / this.zdhyjcData.length).toFixed(2) + '%'

      // this.csggaqData.data4[5].value = 0
      // let num3 = null
      // this.zdqyfglData.forEach((item) => {
      //   let num0 = +item.value3.replace('%', '')
      //   num3 += num0
      // })
      // this.csggaqData.data4[5].value =
      //   (num3 / this.zdqyfglData.length).toFixed(2) + '%'
    },

    // 区县的点击事件
    getOptionData(item, index) {
      let that = this
      this.optionValue = item
      this.showSelct = false
      if (item == '东阳市') {
        // top.commonObj.openWinHtml("7680","2160", "http://10.45.207.188:8080/?jh=1#/csdnyxzx/stc")

        bmjr.toOpen('bmjr157')
      } else if (item == '婺城区') {
        // top.commonObj.openWinHtml("7680","2160", "http://10.24.160.25/shareScreen/eyJzY3JlZW5JZCI6NzYyfQ==?timeStamp=183177f57c2")

        window.open(
          'http://10.24.160.25/shareScreen/eyJzY3JlZW5JZCI6NzYyfQ==?timeStamp=183177f57c2',
          '婺城区',
          'directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=' +
            2160 +
            ', width=' +
            7680 +
            ', top=0, left=0' +
            ''
        )
      } else if (item == '义乌市') {
        bmjr.toOpen('bmjr095')
      } else if (item == '永康市') {
        bmjr.toOpen('bmjr111')
      } else if (item == '金义新区') {
        bmjr.toOpen('bmjr032')
      }
    },

    // 金华特殊指标点击事件
    topAltTitNumFun(item, index) {
      index == this.topAltTitNum ? (this.topAltTitNum = -1) : (this.topAltTitNum = index)
      // let code=item.name=="中药材" ? 'bmjr097':item.name=="国际商贸" ?'bmjr116':''
      let codeObj = {
        中药材: {
          code: 'bmjr097',
        },
        国际商贸: {
          code: 'bmjr116',
        },
        '"梅"好兰溪': {
          code: 'bmjr127',
        },
        影视产业: {
          code: 'bmjr129',
        },
      }
      let code = ''
      if (item.name == '绿色水电') {
        // top.commonObj.openWinHtml(
        //   '3840',
        //   '2160',
        //   'http://10.24.160.25/shareScreen/eyJzY3JlZW5JZCI6NzA0fQ==?timeStamp=182f1958071'
        // )

        window.open(
          'http://10.24.160.25/shareScreen/eyJzY3JlZW5JZCI6NzA0fQ==?timeStamp=182f1958071',
          '婺城区',
          'directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=' +
            2160 +
            ', width=' +
            3840 +
            ', top=0, left=1920' +
            ''
        )
      } else if (codeObj[item.name]) {
        code = codeObj[item.name].code
        bmjr.toOpen(code)
      }
    },
    Bannerrigth() {
      if (this.eindex == 0) {
        this.isTr = false
        this.eindex = this.itemList.length - 10
        this.Lfet = -this.eindex * 450
        setTimeout((res) => {
          this.isTr = true
          this.eindex--
          this.Lfet = -this.eindex * 450
        }, 10)
      } else {
        this.eindex--
        this.Lfet = -this.eindex * 450
      }
    },
    BannerLfet() {
      this.isTr = true
      this.eindex++
      this.Lfet = -this.eindex * 450
      if (this.eindex == this.itemList.length - 10) {
        setTimeout((res) => {
          this.eindex = 0
          this.isTr = false
          this.Lfet = -this.eindex * 450
        }, 500)
      }
    },
    togFirstTitleFun(id) {
      let that = this
      this.togFirstTitleId = id
      let name = 'twoTitle' + id
      this.itemList = this[name]
      ;(this.eindex = 0), (this.Lfet = 0), (this.isTr = true), (this.lWid = (this.itemList.length - 1) * 450)
    },

    // 新的方法

    // 过滤接口中的数据
    fileTerDataFun(data, name) {
      let arr = data.filter((item) => {
        return item.type == name
      })
      return arr
    },
    setYysValue() {
      // 河道预警
      let that = this
      return new Promise((resolve) => {
        let strName = "'" + '兰江' + "'" + ',' + "'" + '金华江' + "'"
        $api('/xxwh_ztbq_point', { remark: strName }).then((res) => {
          let arr = []
          res.forEach((item) => {
            let pointLen = item.gps_x + ',' + item.gps_y
            let str = {
              data: [],
              point: pointLen,
              code: item.chn_code,
            }
            arr.push(str)
          })
          that.hdyjData = arr
          that.csggaqData.data1[3].value = res.length
          resolve({})
        })
      })
    },
    // 获取饮用水数据
    getYysDataFun() {
      let that = this
      axios({
        method: 'post',
        url: baseURL.url + '/dtdd/iot/aep/v1/api/device/list',
        data: {
          type_id: '8aada4a47be36b72017be37a466b0006',
          page_size: 40000,
          page_num: 1,
        },
      }).then(function (allRes) {
        let pointData = allRes.data.list.map((item) => {
          return new Promise((resolve, reject) => {
            axios({
              method: 'post',
              url: baseURL.url + '/dtdd/iot/aep/v1/api/warning/list',
              data: {
                type_id: '8aada4a47be36b72017be37a466b0006',
                did: item.did,
              },
            }).then((res) => {
              let name = (res.data && res.data.list[0] && res.data.list[0].warning_name) || ''
              let arr = [name, item.current_warning_count, item.device_state]
              resolve(arr)
            })
          })
        })
        Promise.all(pointData).then((ele) => {
          that.csyxglData.data3[1].value = []
          let value = 0
          ele.forEach((item) => {
            if (item[0] != '离线报警' && item[1] > 0 && item[2] == 'ONLINE') {
              value++
            }
          })
          that.csyxglData.data3[1].value = value
          that.csyxglData.data3[1].value > 0
            ? (that.csyxglData.data3[1].actFalg = 2)
            : (that.csyxglData.data3[1].actFalg = 0)

          Promise.all([that.getData(), that.setYysValue()]).then(() => {
            that.showIconFun()
          })
        })
      })
    },
    getData() {
      let that = this
      return new Promise((resolve) => {
        $api('/cstz_ywtb').then((res) => {
          if (res && res[0].rate) {
            that.csggfwData.data0[3].value = res[0].rate + '%'
          }
        })
        // axios({
        //   method: 'get',
        //   url: baseURL.url + '/traapi/gsTrafficJam',
        // }).then((res) => {
        //   let num = +res.data[0].idx
        //   that.cssmxData.data0[0].value = num || 0 //实时道路拥堵指数【2=>非常拥堵,1=>平缓拥堵,0=>正常流畅】
        //   num.toFixed(2) >= 1.8 ? that.cssmxData.data0[0].actFalg = 2 : (num.toFixed(2) >= 1.5 ? that.cssmxData.data0[0].actFalg = 1 : that.cssmxData.data0[0].actFalg = 0)
        //   that.cssmxData.data0[1].value = (res.data[0].description / 1000) || 0 //实时道路严重拥堵里程
        //   that.cssmxData.data0[2].value = (res.data[0].realSpeed)|| 0 //实时道路平均速度r1405
        //   resolve()
        // })
        $api('/gsTrafficJam').then((res) => {
          let num = (+res[0].idx).toFixed(2)
          that.cssmxData.data0[0].value = num || 0 //实时道路拥堵指数【2=>非常拥堵,1=>平缓拥堵,0=>正常流畅】
          num >= 1.8
            ? (that.cssmxData.data0[0].actFalg = 2)
            : num >= 1.5
            ? (that.cssmxData.data0[0].actFalg = 1)
            : (that.cssmxData.data0[0].actFalg = 0)
          that.cssmxData.data0[1].value = res[0].description / 1000 || 0 //实时道路严重拥堵里程
          that.cssmxData.data0[2].value = (+res[0].realSpeed).toFixed(2) || 0 //实时道路平均速度
          resolve({})
        })
        // 危化品经营企业数
        $api('/cstz_whpsyqy').then((res) => {
          that.csggaqData.data3[2].value = res.length
        })
        // 危化品生产企业数
        $api('/cstz_whpscqy').then((res) => {
          that.csggaqData.data3[3].value = res.length
        })
        // 水库预警
        $api('/cstz_skyj').then((res) => {
          that.csggaqData.data1[4].value = res.filter((item) => {
            let sksw = item.z == '-' || item.z ? 0 : item.z
            let jjsw = +item.jjsw
            return sksw >= jjsw
          }).length
          that.csggaqData.data1[4].value > 0
            ? (that.csggaqData.data1[4].actFalg = 2)
            : (that.csggaqData.data1[4].actFalg = 0)
        })
        // 水源地预警指数
        $api('/cstz_sydyjzs').then((res) => {
          that.csyxglData.data2[0].value = res.filter((item) => {
            return item.szzk != '优' && item.szzk != '良'
          }).length
          that.csyxglData.data2[0].value > 0
            ? (that.csyxglData.data2[4].actFalg = 2)
            : (that.csyxglData.data2[0].actFalg = 0)
        })

        // 城市运行管理中的空气
        $api('cstz_pm25').then((res) => {
          that.csyxglData.data1[0].value = res[0].aqi
          that.csyxglData.data1[1].value = res[0].pm25
          that.csyxglData.data1[2].value = res[0].no2
          that.csyxglData.data1[3].value = res[0].so2
          that.csyxglData.data1[4].value = res[0].sywrw
          // 单位
          that.csyxglData.data1[0].unit = ''
          that.csyxglData.data1[1].unit = ''
          that.csyxglData.data1[2].unit = ''
          that.csyxglData.data1[3].unit = ''
          that.csyxglData.data1[4].unit = ''
        })

        // 城市饮用水
        // axios({
        //   headers: {
        //     'Content-Type': 'application/json;charset=UTF-8',
        //   },
        //   method: 'post',
        //   url: baseURL.url230 + '/dtdd/iot/aep/v1/api/device/list',
        //   data: {
        //     type_id: '8aada4a47be36b72017be37a466b0006',

        //     page_size: 40000,
        //     page_num: 1,
        //   },
        // }).then(function (res) {
        //   that.csyxglData.data3[1].value = resData.data.list.filter(item => {
        //     return item.current_warning_count > 0 && item.device_state == "ONLINE"
        //   }).length
        // })
        // $api('/gsTrafficJam').then((res) => {
        //   that.cssmxData.data0[0].value = res[0].idx //实时道路拥堵指数
        //   that.cssmxData.data0[1].value = res[0].description / 1000 //实时道路严重拥堵里程
        //   that.cssmxData.data0[2].value = res[0].realSpeed //实时道路平均速度
        // })
        // 出租车 "8aada4a47d123213017d92e6cfa24f0d"
        axios({
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
          method: 'post',
          url: baseURL.url230 + '/iot/aep/v1/device/listNoPage',
          data: {
            type_id: '8aada4a47d123213017d92e6cfa24f0d',
            device_state: 'ONLINE',
          },
        }).then(function (res) {
          that.csggfwData.data1.map((o, i) => {
            if (o.name == '在线出租车') {
              that.csggfwData.data1[i].value = res.data.list.length
            }
          })
        })
        // 120急救车 "8aada4a47cc5876a017cdf1fda5360d4"
        axios({
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
          },
          method: 'post',
          url: baseURL.url230 + '/iot/aep/v1/device/listNoPage',
          data: {
            type_id: '8aada4a47cc5876a017cdf1fda5360d4',
          },
        }).then(function (res) {
          that.csggfwData.data2.map((o, i) => {
            if (o.name == '120救护车') {
              that.csggfwData.data2[i].value = res.data.list.length
            }
          })
        })
      })
    },
    closeRiskDialog() {
      this.riskDialogVisible = false
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmPoint',
          pointId: 'gjsj', //点位类型Id
        })
      )
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmVideo',
          videoType: 'spdw',
        })
      )
    },
    showCoverage() {
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'RoadVideo',
          videoType: 'spdw',
          distance: 50,
          lngLats: '119.6563,29.1048;119.6563,29.1048',
        })
      )
    },
    watchCity() {
      var str = ''
      let that = this
      for (let i = 0; i < this.cityList.length; i++) {
        if (this.cityList[i].falg == true) {
          str += "'" + this.cityList[i].name + "'" + ','
        }
      }
      if (str.length > 0) {
        str = str.substr(0, str.length - 1)
      }

      $api('/cstz_baiduydd', { addressName: str }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          let time = new Date(res[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res[i].idx = Number(res[i].idx)
        }
        that.altData0.trData = res
        console.log('res===>', res)
        var roadPointData = []
        res.map((ele) => {
          let roadName = `${ele.description}`
          let address = ele.congestSourceDesc ? ele.congestSourceDesc : '--'
          let arr = ele.location.split(',')
          let pointArr = that.transTo4490(arr)
          let point = pointArr[0] + ',' + pointArr[1]
          let str = {
            data: {
              title: roadName,
              key: ['地址'],
              value: [address],
            },
            point: point,
          }
          roadPointData.push(str)
        })
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: '拥堵', //点位类型图标
            pointId: '拥堵',
            pointData: roadPointData,
            size: [0.08, 0.08, 0.08, 0.08],
            popup: {
              offset: [50, -100],
            },
          })
        )
      })
    },
    // 选择县区弹窗
    checkSpan2(index, item) {
      var a = 0
      for (let i = 0; i < this.cityList.length; i++) {
        if (this.cityList[i].falg == true) {
          a++
        }
      }
      a > 1 ? (this.cityList[index].falg = !this.cityList[index].falg) : (this.cityList[index].falg = true)
      this.watchCity()
    },

    // 获取一级标题的名字
    getFirstTitleFun() {
      this.firstTitle = []
      let arr = []
      let that = this
      $api('gsPhysical').then((res) => {
        console.log('res==>', res)
        res.map((item) => {
          let str = {
            name: item.name,
            number: item.number,
            parentNumber: item.parentNumber,
            idName: '',
            flag: false,
            children: [],
          }
          str.idName = 'title' + item.number
          arr.push(str)
        })
        that.firstTitle = arr
        that.firstTitle.map((item) => {
          that.getTypeName(item)
        })
      })
    },

    // 根据一级标题调取三级标题的名字
    async getTypeName(item) {
      let that = this
      let arr = []
      let res = await axios({
        method: 'get',
        url: baseURL.url + '/api/?indexid=gsPhysicalLeapfrog',
        headers: {
          ptid: 'PT0001',
          portToken: sessionStorage.getItem('token'),
        },
        params: {
          number: item.number,
        },
      })
      res.data.data.map((item) => {
        let str = {
          id: item.id,
          parent_number: item.parent_number,
          type_level: item.type_level,
          type_number: item.type_number,
          typeName: item.type_name,
          url: `/static/2Dmap2.28/images/spritesImage/${item.type_name}.png` || '',
          checked: false,
        }
        arr.push(str)
      })
      item.idName == 'title1'
        ? (this.firstTitle[0].children = arr)
        : item.idName == 'title2'
        ? (this.firstTitle[1].children = arr)
        : item.idName == 'title3'
        ? (this.firstTitle[2].children = arr)
        : item.idName == 'title4'
        ? (this.firstTitle[3].children = arr)
        : ''
    },
    // 三级标题打点
    checkSpan1(index, i, item) {
      this.firstTitle[index].children[i].checked = !this.firstTitle[index].children[i].checked
      let pointData = []
      let isTrue = this.firstTitle[index].children[i].checked
      if (isTrue) {
        axios({
          method: 'get',
          url: baseURL.url + '/api/?indexid=gsPhysicalInfo',
          headers: {
            ptid: 'PT0001',
            portToken: sessionStorage.getItem('token'),
          },
          params: {
            number: item.type_number,
          },
        }).then(function (res) {
          res.data.data.map((ele) => {
            let address = ele.ADDRESS ? ele.ADDRESS : '--'
            let phone = ele.PHONE ? ele.PHONE : '--'
            pointData.push({
              data: {
                title: ele.NAME,
                key: ['地址', '联系电话'],
                value: [address, phone],
              },
              point: ele.longitude + ',' + ele.latitude,
            })
          })

          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'loadJuhePoint', //功能名称
              pointType: item.typeName, //点位类型图标
              pointId: item.typeName,
              pointData: pointData,
              size: [0.08, 0.08, 0.08, 0.08],
            })
          )
        })
      } else {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmJuhePoint',
            pointId: item.typeName, //点位类型Id
          })
        )
      }
    },
    // 二级标题调取三级标题的名字
    async showThreeTitleFun(fName, item) {
      let that = this
      let arr = []
      let idName = ''
      let id = ''
      let res = await axios({
        method: 'get',
        url: baseURL.url + '/api/?indexid=gsPhysicalSub',
        headers: {
          ptid: 'PT0001',
          portToken: sessionStorage.getItem('token'),
        },
        params: {
          number: item.number,
        },
      })
      arr = res.data.data
      for (let ele = 0; ele < this.firstTitle.length; ele++) {
        idName = this.firstTitle[ele].idName
        if (fName == this.firstTitle[ele].name) {
          this.firstTitle[ele].flag = true
          id = '#' + idName + ' .table-content'
          $(id).slideDown(500)
        } else {
          id = '#' + idName + ' .table-content'
          $(id).slideUp(500)
        }
        for (let i = 0; i < this.firstTitle[ele].children.length; i++) {
          this.firstTitle[ele].children[i].checked = false
          for (let index in arr) {
            if (this.firstTitle[ele].children[i].type_number == arr[index].number) {
              this.checkSpan1(ele, i, this.firstTitle[ele].children[i])
            }
          }
        }
      }
    },
    // 一级标题点击事件
    toggleProductPanel(item) {
      item.idName == 'title1'
        ? (this.firstTitle[0].flag = !this.firstTitle[0].flag)
        : item.idName == 'title2'
        ? (this.firstTitle[1].flag = !this.firstTitle[1].flag)
        : item.idName == 'title3'
        ? (this.firstTitle[2].flag = !this.firstTitle[2].flag)
        : item.idName == 'title4'
        ? (this.firstTitle[3].flag = !this.firstTitle[3].flag)
        : ''
      let id = '#' + item.idName + ' .table-content'
      $(id).slideToggle(500)
    },
    showIconNum() {
      this.smxNum = this.itemData0.filter((item) => {
        return item.flag == 0
      }).length
      this.ggaqNum = this.itemData1.filter((item) => {
        return item.flag == 0
      }).length
      this.yxglNum = this.itemData2.filter((item) => {
        return item.flag == 0
      }).length
      this.ggfwNum = this.itemData3.filter((item) => {
        return item.flag == 0
      }).length
    },

    // 显示城市生命线和城市公共安全这些大一级标题的右上角警告
    flagFun(data) {
      return data.some((item) => {
        return item.flag == 0
      })
    },
    // 显示城市生命线和城市公共安全下发的列表
    showIconFun() {
      for (let index in this.itemData0) {
        this.itemData0[index].actFalg = this.showIconNumFun(this.cssmxData, index)
        this.itemData0[index].flag = this.showIconItemFun(this.cssmxData, index)
      }
      for (let index in this.itemData1) {
        this.itemData1[index].actFalg = this.showIconNumFun(this.csggaqData, index)
        this.itemData1[index].flag = this.showIconItemFun(this.csggaqData, index)
      }

      for (let index in this.itemData2) {
        this.itemData2[index].actFalg = this.showIconNumFun(this.csyxglData, index)
        this.itemData2[index].flag = this.showIconItemFun(this.csyxglData, index)
      }
      for (let index in this.itemData3) {
        this.itemData3[index].actFalg = this.showIconNumFun(this.csggfwData, index)
        this.itemData3[index].flag = this.showIconItemFun(this.csggfwData, index)
      }

      this.showIconNum()
    },
    // 计算显示警告的图标数据量
    showIconNumFun(data, index) {
      index = parseInt(index)
      let arrData =
        index == 0
          ? data.data0
          : index == 1
          ? data.data1
          : index == 2
          ? data.data2
          : index == 3
          ? data.data3
          : index == 4
          ? data.data4
          : ''
      let arr = []
      for (let ele in arrData) {
        arr.push(arrData[ele])
      }
      return arr.filter((item) => {
        return item.actFalg == 2
      }).length
    },
    // 是否显示警告样式
    showIconItemFun(data, index) {
      index = parseInt(index)
      let arrData =
        index == 0
          ? data.data0
          : index == 1
          ? data.data1
          : index == 2
          ? data.data2
          : index == 3
          ? data.data3
          : index == 4
          ? data.data4
          : ''
      let arr = []
      for (let ele in arrData) {
        arr.push(arrData[ele])
      }
      let falg = arr.some((item) => {
        return item.actFalg == 2
      })
      return falg ? 0 : 1
    },
    // 关闭道路拥堵弹窗
    closeRoadFun() {
      this.showAlt = this.showCity = false
      this.left1Num = -1
      this.clearPoint()
      this.rmVideoFun()
    },

    // 关闭医院就诊弹窗
    closeHospital() {
      this.caseDialogVisible = false
      this.medicalDialogVisible = false
      this.orderDialogVisible = false
      this.showHospital = false
      this.left1Num = -1
    },

    /*
     * 显示就诊人次走势弹窗
     */
    showHospitalDialog() {
      this.medicalDialogTabsIndex = 0
      this.caseDialogVisible = true
      this.medicalDialogVisible = false
      this.orderDialogVisible = false
    },

    showMedicalDialog() {
      this.medicalDialogTabsIndex = 0
      this.caseDialogVisible = false
      this.medicalDialogVisible = true
      let that = this
      this.$nextTick(() => {
        that.drawRecentChart()
        that.drawDailyChart()
      })
    },

    // 鼠标悬浮出现小手的效果
    changeCursor(name) {
      let arr = [
        '今日线路检修',
        '出租车在途率',
        '公交车在途数',
        '停气小区数量',
        '实时道路拥堵指数',
        '医院的就诊人次',
        '地质灾害报警数量',
        '城市饮用水预警',
        '核酸检测点',
        '危化品运输风险指数',
        '120救护车',
        '在线出租车',
        '重点人群检测覆盖率',
        '重点行业监测覆盖率',
        '疫情概况',
        '核酸检测情况',
        '集中隔离情况',
        '重点区域检测覆盖率',
        '河道预警',
        '危化品经营企业数',
        '危化品生产企业数',
        '规上企业用电量',
        '全社会用电量',
        '水库预警',
        '水源地预警指数',
      ]

      let falg = arr.some((item) => {
        return item == name
      })
      if (falg) {
        this.showCursor = true
      } else {
        this.showCursor = false
      }
    },
    // 关闭所有弹窗和效果
    closeLeft() {
      this.clearPoint()
      this.clear3Dtext()
      this.rmAddMapFun()
      this.clearHotMapAll()
      // this.rmJuhePointFun()
      this.showLeft1 = false
      this.left1Num = -1
      this.data0Num = -1
      this.data1Num = -1
      this.data2Num = -1
      this.data3Num = -1
      this.showAlt = this.showCity = false
      this.showThreeAlt = false
      this.caseDialogVisible = false
      this.medicalDialogVisible = false
      this.orderDialogVisible = false
      this.showHospital = false
      this.hidThreeTitle()
    },
    // 左边弹窗点击方法
    showAltFun(name, index) {
      this.altName = name
      this.altTitle = name
      this.altData0.textData = []
      this.altData0.textTitle = ''
      this.altData1.title = ''
      this.altData1.data = []
      this.altData0.thTitle = ''
      this.showThreeAlt = false
      this.showHospital = false
      this.control3Dthree = this.control3Done = this.control3Dtwo = false
      this.medicalDialogVisible = this.orderDialogVisible = false
      this.caseDialogVisible = false
      this.showVideo = false
      this.isVideoSrc = false
      this.skyVideoHtml = null
      this.left1Num != index ? (this.left1Num = index) : (this.left1Num = -1)

      // 拥有点击事件的相同的操作
      let itemArr = [
        '今日线路检修',
        '出租车在途率',
        '公交车在途数',
        '停气小区数量',
        '实时道路拥堵指数',
        '医院的就诊人次',
        '地质灾害报警数量',
        '城市饮用水预警',
        ,
        '核酸检测点',
        '危化品运输风险指数',
        '120救护车',
        '在线出租车',
        '重点人群检测覆盖率',
        '重点行业监测覆盖率',
        '疫情概况',
        '核酸检测情况',
        '集中隔离情况',
        '重点区域检测覆盖率',
        '河道预警',
        '危化品经营企业数',
        '危化品生产企业数',
        '水库预警',
        '水源地预警指数',
      ]
      // itemArr.forEach((item) => {
      //   if (item == name) {
      //     this.rmVideoFun()
      //     this.rmPopFun()
      //     this.clearHistogram()
      //     this.clear3Dtext()
      //     this.clearPoint()
      //     this.rmAddMapFun()
      //     this.clearLine()
      //     this.clearPointById('拥堵')
      //     clearTimeout(this.loadTime)
      //     clearTimeout(this.sydTime)
      //   }
      // })

      if (name == '停气小区数量') {
        this.altData0.textData.textTitle = this.tdxqData.textTitle
        let value = {
          value: this.tdxqData.text,
        }
        this.altData0.textData.push(value)
        this.altData1.title = this.tdxqData.dataTitle
        this.altData1.data = this.tdxqData.data
        this.altData1.thTitle = []
        this.altData1.trData = []
        index == this.left1Num ? (this.showAlt = true) : (this.showAlt = false)
      } else if (name == '实时道路拥堵指数') {
        this.showInfo = false
        this.watchCity() //调用表格渲染
        this.altData1.title = '实时道路拥堵情况'
        this.altData0.trData = this.hdldData
        index == this.left1Num ? (this.showAlt = true) : (this.showAlt = false)
        this.showAlt == true ? (this.showInfo = false) : (this.showInfo = true)
        this.showCity = !this.showCity //区县下拉框
        this.showCity == true
          ? top.document.getElementById('map').contentWindow.Work.funChange(
              JSON.stringify({
                funcName: 'addMap',
                data: {
                  url: 'https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/services/other_jh_road_green/achievement/achievement_grid_tile_14/hexadecimal/{z}/{x}/{y}.png?token=997c588f4df1483f8e21c2c354b9e3f0',
                  id: 'dlyd_road',
                },
              })
            )
          : (this.clearPoint(), this.rmAddMapFun())
      } else if (name == '地质灾害报警数量') {
        this.showAlt = this.showCity = false
        // this.showLeft1 = false
        this.greenData = this.greenYysData
        this.redData = this.redYysData
        // 地质灾害报警数量的点位
        // this.choosePoint("8aada4a47f4d661c017fb5a31eec0017", "yys-green", "yys-red")
        // 暂时使用城市饮用水

        // this.choosePoint(
        //   '8aada4a47be36b72017be37a466b0006',
        //   'yys-green',
        //   'yys-red'
        // )
      } else if (name == '城市饮用水预警') {
        this.showAlt = this.showCity = false
        this.showLeft1 = false
        this.greenData = this.greenYysData
        this.redData = this.redYysData
        this.choosePoint('8aada4a47be36b72017be37a466b0006', 'yys-green', 'yys-red')
      } else if (name == '医院的就诊人次') {
        index == this.left1Num ? (this.showHospital = true) : (this.showHospital = false)
        // this.showHospital == true? this.left1Num = index: this.left1Num = -1
      } else if (name == '核酸检测点') {
        // 核酸监测点打点
        this.left1Num == index ? this.showPointFun(this.hsjcData) : this.clearPoint()
      } else if (name == '危化品运输风险指数') {
        index == this.left1Num
          ? mapPoint.chooseMapPoint('危货车GPS', '8aada4a47d123213017d79ad01220156')
          : this.clearPoint()
      } else if (name == '120救护车') {
        index == this.left1Num
          ? mapPoint.chooseMapPoint120('急救车120', '8aada4a47cc5876a017cdf1fda5360d4', '')
          : this.clearPoint()
      } else if (name == '在线出租车') {
        index == this.left1Num
          ? mapPoint.chooseMapPoint('出租车GPS', '8aada4a47d123213017d92e6cfa24f0d')
          : this.clearPoint()
      } else if (name == '重点人群检测覆盖率') {
        this.altData1.title = '重点人群检测覆盖率'
        this.altData0.thTitle = ['分类', '总人数', '覆盖率', '到期未检人数']
        this.altData0.trData = this.zdqyfglData

        this.altData1.thTitle = ['检测对象', '总人数', '覆盖率', '已检人数', '牵头部门']
        this.altData1.trData = this.zdqyfglData2
        index == this.left1Num ? (this.showAlt = true) : (this.showAlt = false)
      } else if (name == '重点行业监测覆盖率') {
        this.altData1.title = '详情'
        this.altData0.thTitle = ['事件名称', '总人数', '覆盖率', '到期未检人数']
        this.altData0.trData = this.zdhyjcData
        this.altData1.thTitle = ['行业', '总人数', '覆盖率', '到期未检人数']
        this.altData1.trData = this.zdhyjcData2
        index == this.left1Num ? (this.showAlt = true) : (this.showAlt = false)
      } else if (name == '疫情概况') {
        index == this.left1Num ? (this.showAlt = true) : (this.showAlt = false)
      } else if (name == '核酸检测情况') {
        this.showAlt = this.showCity = false
        if (index == this.left1Num) {
          this.clearHotMapAll()
          top.document.getElementById('map').contentWindow.Work.change3D(9)
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'Histogram',
              HistogramData: this.content3Done,
            })
          )
          this.control3Done = true
        } else {
          this.clearHistogram()
          this.control3Done = false
        }
      } else if (name == '集中隔离情况') {
        this.showAlt = this.showCity = false
        if (index == this.left1Num) {
          this.clearHotMapAll()
          top.document.getElementById('map').contentWindow.Work.change3D(9)
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'Histogram',
              HistogramData: this.content3Dtwo,
            })
          )
          this.control3Dtwo = true
        } else {
          this.clearHistogram()
          this.control3Dtwo = false
        }
      } else if (name == '重点区域检测覆盖率') {
        this.showAlt = this.showCity = false
        if (index == this.left1Num) {
          this.control3Dthree = true
          this.clearHotMapAll()
          top.document.getElementById('map').contentWindow.Work.change3D(9)
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'Histogram',
              HistogramData: this.content3Dthree,
            })
          )
        } else {
          this.control3Dthree = false
          this.clearHistogram()
        }
      } else if (name == '河道预警') {
        this.showAlt = this.showCity = false
        let len = this.hdyjData[0].point.split(',')[0]
        let lat = this.hdyjData[0].point.split(',')[1]
        const flyData = {
          funcName: 'flyto',
          flyData: {
            postion: {
              x: 119.46525573730469,
              y: 29.211069107055664,
              z: 4813.18603515625,
            },
            rotation: {
              x: 88.64361572265625,
              y: 176.910888671875,
              z: 180,
            },
          },
        }
        // const flyData = {
        //     funcName: 'flyto',
        //     flyData: {
        //         postion: {
        //             x: this.hdyjData[0].geometry.coordinates[0],
        //             y: this.hdyjData[0].geometry.coordinates[1],
        //             z: 16071.5556640625,
        //         },
        //         rotation: {
        //             x: 88.6431884765625,
        //             y: 176.910888671875,
        //             z: 180,
        //         },
        //     },
        // }

        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(flyData))
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     "funcName": "videoJuhe",
        //     "videoJuheData": {
        //       "type": "FeatureCollection",
        //       "features": this.hdyjData
        //     },
        //     "buffer": 1
        //   })
        // )

        index == this.left1Num
          ? top.document.getElementById('map').contentWindow.Work.funChange(
              JSON.stringify({
                funcName: 'pointLoad',
                pointType: 'camera-load1', // 点位类型（图标名称）
                pointId: 'camera-load1', // 点位唯一id
                height: '0',
                pointData: this.hdyjData,
                popup: {
                  offset: [50, -100],
                },
              })
            )
          : this.clearPoint()
      } else if (name == '危化品生产企业数') {
        this.left1Num == index ? this.getWhpScDataFun() : this.clearPoint()
      } else if (name == '危化品经营企业数') {
        this.left1Num == index ? this.getWhpjyDataFun() : this.clearPoint()
      } else if (name == '水库预警') {
        this.left1Num == index ? this.getSkyjDataFun() : (this.clearPoint(), clearTimeout(this.loadTime))
      } else if (name == '水源地预警指数') {
        this.left1Num == index ? this.getSydyjDataFun() : (this.clearPoint(), clearTimeout(this.sydTime))
      } else {
        this.showAlt = this.showCity = false
        this.showInfo = true
        this.caseDialogVisible = false
        this.showIsNormal = false
        this.medicalDialogVisible = this.orderDialogVisible = this.showHospital = false
      }
      if (name == '规上企业用电量' || name == '全社会用电量') {
        let leftData = {
          type: 'openIframe',
          name: 'cstz2-dian-diong',
          src: baseURL.url + '/static/citybrain/csdn/cstz2-dian-diong.html',
          width: '1716px',
          height: '1000px',
          left: '2667px',
          top: '264px',
          argument: name,
        }
        window.parent.postMessage(JSON.stringify(leftData), '*')
      }
    },

    // 危化品生产企业数
    getWhpScDataFun() {
      $api('/cstz_whpscqy').then((res) => {
        let pointData = res.map((item) => {
          let str = {
            data: {
              title: item.qyname,
              key: ['生产地址', '主要产品及生产规模', '储罐(容器)总容积(立方米)'],
              value: [item.scaddress, item.cpgm, item.volume],
            },
            point: item.longitude + ',' + item.latitude,
          }
          return str
        })
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: 'whpscfx', //点位类型图标
            pointId: 'whpscfx',
            pointData: pointData,
            size: [0.08, 0.08, 0.08, 0.08],
            popup: {
              offset: [50, -100],
            },
          })
        )
      })
    },
    // 危化品经营企业数
    getWhpjyDataFun() {
      $api('/cstz_whpsyqy').then((res) => {
        let pointData = res.map((item) => {
          let str = {
            data: {
              title: item.enterprisename,
              key: [
                '具体地址',
                '安全经营许可证编号',
                '核准经营范围',
                '安全使用许可证编号',
                '安全使用许可证许可范围',
                '化工行业分类',
                '罐区总数量',
                '罐区罐总容积',
                '仓库总数量',
                '仓库总容积',
                '周边1000米范围内的单位或设施情况',
              ],
              value: [
                item.stoaddress,
                item.safetybizcertifno,
                item.businessscope,
                item.safetyusecertifno,
                item.satetyusescope,
                item.chemicalsclassification,
                item.tankareanum,
                item.tankareacubage,
                item.warehousenum,
                item.warehousecubage,
                item.facilitiescondition,
              ],
            },
            point: item.longitude + ',' + item.latitude,
          }
          return str
        })
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: 'whpjyfx', //点位类型图标
            pointId: 'whpjyfx',
            pointData: pointData,
            size: [0.08, 0.08, 0.08, 0.08],
            popup: {
              offset: [50, -100],
            },
          })
        )
      })
    },
    //水库预警
    getSkyjDataFun() {
      let that = this
      $api('/cstz_skyj').then((res) => {
        let pointDataRed = []
        let pointData = []
        res.forEach((item) => {
          let str = {
            // data: {
            //   title: item.carrier_name,
            //   key: ['设备状态', '水库水位', '警戒水位',"采集时间"],
            //   value: [item.syq_device_state, item.z, item.jjsw,item.insert_time],

            // },
            point: item.syq_longitude + ',' + item.syq_latitude,
            serial: item.serial,
            codes: item.code,
            title: item.carrier_name,
            jd: item.syq_longitude,
            wd: item.syq_latitude,
            state: item.syq_device_state,
            z: item.syq_device_state,
            jjsw: item.jjsw,
            time: item.insert_time,
            eventType: 'skyj',
          }
          let sksw = item.z == '-' || item.z ? 0 : item.z
          let jjsw = +item.jjsw
          if (sksw >= jjsw) {
            pointDataRed.push(str)
          } else {
            pointData.push(str)
          }
        })
        const flyData = {
          funcName: 'flyto',
          flyData: {
            postion: {
              x: 119.77444458007812,
              y: 29.145221710205078,
              z: 100001.1171875,
            },
            rotation: {
              x: 88.64361572265625,
              y: 176.910888671875,
              z: 180,
            },
          },
        }
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(flyData))
        that.loadTime = setTimeout(() => {
          console.log('加载水库预警点位')
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'pointLoad', //功能名称
              pointType: 'skyj', //点位类型图标
              pointId: 'skyj',
              pointData: pointData,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          )
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'pointLoad', //功能名称
              pointType: 'skyj-red', //点位类型图标
              pointId: 'skyjred',
              pointData: pointDataRed,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          )
        }, 3000)
      })
    },

    // 水库预警点位点击方法
    getSkyjPointDataFun(e) {
      let that = this
      let evn = JSON.parse(e.data)
      this.videoTitle = '查看视频'
      if (evn == undefined || evn.data == undefined || evn.data.eventType != 'skyj') return
      console.log('================================================================')
      this.videoData = evn.data
      this.showVideo = true
      this.isVideoSrc = false
      this.skyVideoHtml =
        '<video id="my-video" class="video-js vjs-default-skin" width="1600px" heigth="1200px" preload="auto"> </video>'
      // this.$nextTick(()=>{
      this.skyjVideo = videojs('my-video', {
        bigPlayButton: false,
        textTrackDisplay: false,
        errorDisplay: false,
        volumePanel: false,
        fullscreenToggle: true,
        playbackRateMenuButton: false,
        pictureInPictureToggle: false, //隐藏画中画按钮，默认为true
      })

      // })

      axios({
        method: 'post',
        url: 'http://10.24.162.230:8000/iot/aep/v1/api/cityManagement/videoStreaming',
        data: {
          serial: evn.data.serial,
          code: evn.data.codes,
        },
      }).then(function (res) {
        if (res.data.return_code == 'FAIL' || res.data.data[0].HLS == undefined) {
          that.isVideoSrc = true
        } else {
          let FLV = res.data.data[0].FLV
          let HLS = res.data.data[0].HLS
          that.videoTitle = res.data.data[0].ChannelName

          that.skyjVideo.src([
            {
              type: 'application/x-mpegURL',
              src: HLS,
            },
          ])
          that.skyjVideo.play()
        }
      })
    },
    // 关闭视频
    closeVideo() {
      this.showVideo = false
      this.isVideoSrc = false
      if (this.skyVideoHtml != null) this.skyjVideo.dispose()
      this.skyVideoHtml = null
    },
    //水源地预警指数
    getSydyjDataFun() {
      let that = this
      $api('/cstz_sydyjzs').then((res) => {
        let pointDataRed = []
        let pointDataCs = []
        let pointData = []
        res.forEach((item) => {
          let sfcb = item.sfcb == 0 ? '否' : '是'
          let cbwrwjcbbs = item.cbwrwjcbbs == '\\N' ? '-' : item.cbwrwjcbbs
          let str = {
            data: {
              title: item.cdmc,
              key: ['水质类别', '是否超标', '超标污染物', '水质状况'],
              value: [item.szlb, sfcb, cbwrwjcbbs, item.szzk],
            },
            point: item.jd + ',' + item.wd,
          }
          if (item.szzk == '重度污染') {
            pointDataRed.push(str)
          } else if (item.szzk == '轻度污染') {
            pointDataCs.push(str)
          } else {
            pointData.push(str)
          }
        })

        const flyData = {
          funcName: 'flyto',
          flyData: {
            postion: {
              x: 119.77444458007812,
              y: 29.145221710205078,
              z: 100001.1171875,
            },
            rotation: {
              x: 88.64361572265625,
              y: 176.910888671875,
              z: 180,
            },
          },
        }
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(flyData))
        that.sydTime = setTimeout(() => {
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'pointLoad', //功能名称
              pointType: 'sydyjzsLs', //点位类型图标
              pointId: 'skyj',
              pointData: pointData,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          )
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'pointLoad', //功能名称
              pointType: 'sydyjzsHs', //点位类型图标
              pointId: 'sydyjzsRed',
              pointData: pointDataRed,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          )
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'pointLoad', //功能名称
              pointType: 'sydyjzsCs', //点位类型图标
              pointId: 'sydyjzsCs',
              pointData: pointDataCs,
              size: [0.08, 0.08, 0.08, 0.08],
              popup: {
                offset: [50, -100],
              },
            })
          )
        }, 3000)
      })
    },

    // 核酸检测打点
    showPointFun(data) {
      let point = []
      let pointData = []
      data.map((item) => {
        point = item.jwd.split(',')
        let str = {
          data: {
            title: '核酸检测地点',
            key: ['核酸检测机构', '地址', '工作时间'],
            value: [item.name, item.address, item.time],
          },
          point: point[0] + ',' + point[1],
        }
        pointData.push(str)
      })
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'pointLoad', //功能名称
          pointType: 'hsjc', //点位类型图标
          pointId: '核酸检测',
          pointData: pointData,
          size: [0.08, 0.08, 0.08, 0.08],
          popup: {
            offset: [50, -100],
          },
        })
      )
    },

    // 根据勾选状态加载地图点位
    togMapPoint(greenData, redData, num) {
      this.clearPoint()

      if (num == 0) {
        this.allVal = !this.allVal
        if (this.allVal) {
          this.redVal = this.greenVal = true
        } else {
          this.redVal = this.greenVal = false
          return
        }

        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: 'yys-green', //点位类型图标
            pointId: '全选饮用水绿色',
            pointData: greenData,
            popup: {
              offset: [50, -100],
            },
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: 'yys-red', //点位类型图标
            pointId: '全选饮用水红色',
            pointData: redData,
            popup: {
              offset: [50, -100],
            },
          })
        )
      } else if (num == 1) {
        this.allVal = this.redVal = false
        if (this.greenVal) return
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: 'yys-green', //点位类型图标
            pointId: '饮用水绿色',
            pointData: greenData,
            popup: {
              offset: [50, -100],
            },
          })
        )
      } else if (num == 2) {
        this.allVal = this.greenVal = false
        if (this.redVal) return
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'pointLoad', //功能名称
            pointType: 'yys-red', //点位类型图标
            pointId: '饮用水红色',
            pointData: redData,
            popup: {
              offset: [50, -100],
            },
          })
        )
      }
    },
    /*
     * 地图打点
     */
    choosePoint(type, icon, iconRed) {
      let that = this
      const params = {
        type_id: type,
        page_size: 40000,
        page_num: 1,
      }
      axios({
        method: 'post',
        url: baseURL.url + '/dtdd/iot/aep/v1/api/device/list',
        data: {
          type_id: type,
          page_size: 40000,
          page_num: 1,
        },
      }).then(function (allRes) {
        let pointData = allRes.data.list.map((item) => {
          return new Promise((resolve, reject) => {
            axios({
              method: 'post',
              url: baseURL.url + '/dtdd/iot/aep/v1/api/warning/list',
              data: {
                type_id: type,
                did: item.did,
              },
            }).then((res) => {
              let qxName = item.area_name == '金华市' ? item.area_name : item.area_name.slice(2)
              let str = [
                {
                  cuiot_push_time: '20220425143001',
                  device_state: 'ONLINE',
                  device_maintain_status: 1,
                  cuiot_carrier_area_id: '8aada4a47be74d61017bf2d62ce6022c',
                  ph: '8.1',
                  zd: '0.289',
                  device_data_type: 'COVER_SZJC',
                  did: 'YK0001',
                },
              ]
              let name = (res.data && res.data.list[0] && res.data.list[0].warning_name) || ''

              resolve({
                longitude: item.longitude,
                latitude: item.latitude,
                deviceName: item.device_name,
                carrier_name: item.carrier_name,
                device_state: item.device_state,
                qxName: qxName,
                device_warning_state:
                  name != '离线报警' &&
                  item.device_state == 'ONLINE' &&
                  item.current_warning_count > 0 &&
                  item.current_warning_count != null
                    ? '异常'
                    : '正常',
                valueList: item.gather_value_list[0] || str,
                warning_name: (res.data && res.data.list[0] && res.data.list[0].warning_name) || '--',
                notice_info: (res.data && res.data.list[0] && res.data.list[0].notice_info) || '--',
                warning_status: (res.data && res.data.list[0] && res.data.list[0].warning_status) || '--',
              })
            })
          })
        })
        Promise.all(pointData).then((ele) => {
          let eleArr = ele.filter((edata) => {
            return edata.warning_name != '离线报警'
          })
          Object.assign(that.yysData.wcq, that.QxfilterDataFun(eleArr, '婺城区'))
          Object.assign(that.yysData.jyxq, that.QxfilterDataFun(eleArr, '金义新区'))
          Object.assign(that.yysData.lxs, that.QxfilterDataFun(eleArr, '兰溪市'))
          Object.assign(that.yysData.pjx, that.QxfilterDataFun(eleArr, '浦江县'))
          Object.assign(that.yysData.yws, that.QxfilterDataFun(eleArr, '义乌市'))
          Object.assign(that.yysData.dys, that.QxfilterDataFun(eleArr, '东阳市'))
          Object.assign(that.yysData.pax, that.QxfilterDataFun(eleArr, '磐安县'))
          Object.assign(that.yysData.yks, that.QxfilterDataFun(eleArr, '永康市'))
          Object.assign(that.yysData.wyx, that.QxfilterDataFun(eleArr, '武义县'))

          let qxArr = ['婺城区', '金义新区', '兰溪市', '浦江县', '义乌市', '东阳市', '磐安县', '永康市', '武义县']
          let str = {
            name: '',
            color: [],
          }

          //三色图
          let renderBankuaiData = []
          for (let i = 0; i < qxArr.length; i++) {
            str = {}
            str.name = qxArr[i]
            let falg = false
            switch (i) {
              case 0:
                falg = that.someWarnFun(that.yysData.wcq)
                break
              case 1:
                falg = that.someWarnFun(that.yysData.jyxq)
                break
              case 2:
                falg = that.someWarnFun(that.yysData.lxs)
                break
              case 3:
                falg = that.someWarnFun(that.yysData.pjx)
                break
              case 4:
                falg = that.someWarnFun(that.yysData.yws)
                break
              case 5:
                falg = that.someWarnFun(that.yysData.dys)
                break
              case 6:
                falg = that.someWarnFun(that.yysData.pax)
                break
              case 7:
                falg = that.someWarnFun(that.yysData.yks)
                break
              case 8:
                falg = that.someWarnFun(that.yysData.wyx)
                break
              default:
                break
            }
            if (falg) {
              str.color = [255, 0, 0, 0.45]
            } else {
              str.color = [0, 255, 0, 0.45]
            }
            renderBankuaiData.push(str)
          }
          var textData = [
            {
              pos: [119.**************, 29.*************, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //浦江县
            {
              pos: [119.*************, 29.***************, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, // 金义新区
            {
              pos: [119.**************, 29.**************, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //兰溪市
            {
              pos: [119.*************, 29.**************, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //婺城区
            {
              pos: [120.**************, 29.***************, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //义乌市
            {
              pos: [119.7269204711914, 28.79677101135254, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //武义县
            {
              pos: [120.1469204711914, 28.97677101135254, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //永康市
            {
              pos: [120.4169204711914, 29.24677101135254, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //东阳市
            {
              pos: [120.6299204711914, 29.06677101135254, 15000],
              color: [255, 255, 255, 1],
              text: '提示文字',
            }, //磐安县
          ]

          textData[0].text = that.QxfilterDataTextFun(that.yysData.pjx)
          textData[1].text = that.QxfilterDataTextFun(that.yysData.jyxq)
          textData[2].text = that.QxfilterDataTextFun(that.yysData.lxs)
          textData[3].text = that.QxfilterDataTextFun(that.yysData.wcq)
          textData[4].text = that.QxfilterDataTextFun(that.yysData.yws)
          textData[5].text = that.QxfilterDataTextFun(that.yysData.wyx)
          textData[6].text = that.QxfilterDataTextFun(that.yysData.yks)
          textData[7].text = that.QxfilterDataTextFun(that.yysData.dys)
          textData[8].text = that.QxfilterDataTextFun(that.yysData.pax)

          top.document.getElementById('map').contentWindow.Work.change3D(9)

          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'renderBankuai',
              renderBankuaiData,
            })
          )

          top.document.getElementById('map').contentWindow.Work.flyShow = true

          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: '3Dtext',
              textData: textData,
            })
          )

          that.showMapDialog(eleArr)
          // that.yysTime = setTimeout(
          //   function () {
          //     top.document.getElementById('map').contentWindow.Work.funChange(
          //       JSON.stringify({
          //         funcName: '3Dtext',
          //         textData: textData,
          //       })
          //     )

          //     that.showMapDialog(eleArr)
          //   }, 5000)
        })
      })
    },
    // 监听饮用水右击事件
    getMsg() {
      window.addEventListener('message', this.getSkyjPointDataFun, false)
      // 监听右击事件
      // window.addEventListener('message', this.showMapDialog, false)
    },
    // 饮用水事件方法
    showMapDialog(data) {
      // let data = []
      // console.log('城市饮用水预警', e)
      // if (
      //   e.data.data &&
      //   e.data.data !== undefined &&
      //   (this.altName == '城市饮用水预警' || this.altName == '地质灾害报警数量')
      // ) {
      //   let name = e.data.data[0].targets[0].name
      //   name == '婺城区'
      //     ? (data = this.yysData.wcq)
      //     : name == '金义新区'
      //     ? (data = this.yysData.jyxq)
      //     : name == '兰溪市'
      //     ? (data = this.yysData.lxs)
      //     : name == '浦江县'
      //     ? (data = this.yysData.pjx)
      //     : name == '义乌市'
      //     ? (data = this.yysData.yws)
      //     : name == '东阳市'
      //     ? (data = this.yysData.dys)
      //     : name == '磐安县'
      //     ? (data = this.yysData.pax)
      //     : name == '永康市'
      //     ? (data = this.yysData.yks)
      //     : name == '武义县'
      //     ? (data = this.yysData.wyx)
      //     : ''

      // 显示全选这些按钮
      this.showIsNormal = true
      this.redVal = this.greenVal = this.allVal = true
      this.setMapPointFun(data)
      // }
    },
    // 区县中是否有报警的信息
    someWarnFun(data) {
      let falg = data.some((item) => {
        return item.device_warning_state == '异常'
      })
      return falg
    },
    //过滤区县的数据的名字
    QxfilterDataTextFun(data) {
      let arr = []
      for (let item of data) {
        if (item.device_warning_state == '异常') {
          arr.push(item)
        }
      }
      return arr.length
    },
    //过滤区县的数据
    QxfilterDataFun(data, name) {
      let arr = []
      for (let item of data) {
        if (item.qxName == name) {
          arr.push(item)
        }
      }
      return arr
    },
    // 直接在地图上面打点
    setMapPointFun(data) {
      this.rmVideoFun()
      this.rmPopFun()

      const pointData = []
      const pointDataRed = []
      let that = this
      data.map((item) => {
        let zd = item.valueList.zd != undefined ? item.valueList.zd : '0'
        let ph = item.valueList.ph != undefined ? item.valueList.ph : '0'
        if (item.device_warning_state == '正常') {
          pointData.push({
            data: {
              title: item.carrier_name,
              key: [
                '告警名称',
                '告警状态',
                '告警描述',
                '余氯',
                '二氧化氯',
                '浊度',
                '总有机碳',
                'PH',
                'CODmn',
                '总氯',
                '总锰',
              ],
              value: [
                item.warning_name,
                item.warning_status,
                item.notice_info,
                '0',
                '0 mg/L',
                zd,
                '0 mg/L',
                ph,
                '0 mg/L',
                '0 mg/L',
                '0 mg/L',
              ],
            },
            point: item.longitude + ',' + item.latitude,
          })
        } else if (item.device_warning_state == '异常') {
          pointDataRed.push({
            data: {
              title: item.carrier_name,
              key: [
                '告警名称',
                '告警状态',
                '告警描述',
                '余氯',
                '二氧化氯',
                '浊度',
                '总有机碳',
                'PH',
                'CODmn',
                '总氯',
                '总锰',
              ],
              value: [
                item.warning_name,
                item.warning_status,
                item.notice_info,
                '0',
                '0 mg/L',
                zd,
                '0 mg/L',
                ph,
                '0 mg/L',
                '0 mg/L',
                '0 mg/L',
              ],
            },
            point: item.longitude + ',' + item.latitude,
          })
        }
      })

      Object.assign(that.greenYysData, pointData)
      Object.assign(that.redYysData, pointDataRed)

      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'pointLoad', //功能名称
          pointType: 'yys-green', //点位类型图标
          pointId: '测量值1',
          pointData: pointData,
          popup: {
            offset: [50, -100],
          },
        })
      )
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'pointLoad', //功能名称
          pointType: 'yys-red', //点位类型图标
          pointId: '测量值2',
          pointData: pointDataRed,
          popup: {
            offset: [50, -100],
          },
        })
      )
    },
    /*
     * 绘制拥挤地段
     */

    roadMapFun(obj) {
      let that = this
      this.clearLine()
      let arrLngLats = obj.linkStates
      // 畅通 缓行 拥堵 严重拥堵
      // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
      let hxData = []
      let ydData = []
      let yzydData = []
      for (let key of Object.keys(arrLngLats)) {
        let line = arrLngLats[key]
        if (obj.idx > 1.5) {
          let arrData = line.split(';')
          for (let i = 0; i < arrData.length; i++) {
            // 排序
            // let str=that.roadSort(arrData[i])
            // 不排序
            let str = arrData[i]
            let arr = str.split(/[,;]/)
            let coords = that.transTo4490(arr)
            obj.idx >= 1.5 && obj.idx < 2
              ? hxData.push(coords)
              : obj.idx >= 2 && obj.idx < 4
              ? ydData.push(coords)
              : obj.idx >= 4
              ? yzydData.push(coords)
              : ''
          }
        }
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            flyTo: false,
            id: 'hxRoad',
            funcName: 'createLine',
            coordsArr: hxData, //坐标
            width: 10, //线宽
            color: [252, 194, 95, 1], //rgba
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            flyTo: false,
            id: 'ydRoad',
            funcName: 'createLine',
            coordsArr: ydData, //坐标
            width: 10, //线宽
            color: [250, 108, 103, 1], //rgba
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            flyTo: false,
            id: 'yzydRoad',
            funcName: 'createLine',
            coordsArr: yzydData, //坐标
            width: 10, //线宽
            color: [180, 18, 14, 1], //rgba
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmVideo',
            videoType: '拥挤地段' + key,
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'RoadVideo',
            videoType: '拥挤地段' + key,
            distance: 100,
            lngLats: arrLngLats[key],
          })
        )
      }
    },
    // 处理路况的经纬度
    transTo4490(arr) {
      const length = arr.length / 2
      const pointArr = []
      // let mapName = top.document.getElementById('map').contentWindow.map
      let mapName = top.document.getElementById('map').contentWindow.egs1.contentWindow.map
      for (let i = 1; i <= length; i++) {
        const index = i * 2
        pointArr.push(
          mapName.gcoord
            .transform([arr[index - 2], arr[index - 1]], mapName.gcoord.BD09, mapName.gcoord.WGS84)
            .concat(0)
        )
      }
      return pointArr.flat()
    },
    // 底部的二级标题点击事件
    showRedLeftFun(item, index) {
      let that = this
      if (!item.isClick) return
      this.left1Num = -1
      this.showQyrk = false
      this.showAlt = this.showThreeAlt = this.caseDialogVisible = this.showCity = false
      this.medicalDialogVisible = this.orderDialogVisible = this.showHospital = false
      this.showIsNormal = false
      this.showInfo = true
      this.riskDialogVisible = false
      this.showVideo = false
      this.isVideoSrc = false
      this.skyVideoHtml = null
      // this.goToMap()
      // this.clear3Dtext()
      // this.clearHotMapAll()
      // this.clearPoint()
      // this.rmPopFun()
      // this.rmVideoFun()
      // this.clearHistogram()
      // this.clearLine()
      // clearTimeout(this.loadTime)
      // clearTimeout(this.sydTime)
      // let iframes = document.getElementById('mainPage').childNodes
      // for (let i = 3; i < iframes.length; i++) {
      //   iframes[i].remove()
      // }

      // $api('/csdn/cstz2-middle/cstzMiddle003', { code: item.parentId }).then(res=>{
      //   let dataArr=that.fileTerDataFun(res,item.type)
      //   that.left1Data=dataArr
      //   that.showLeft1=true
      // })

      let data = {}
      let lastIndex = -1
      item.parentId == 1
        ? (lastIndex = this.data0Num)
        : item.parentId == 2
        ? (lastIndex = this.data1Num)
        : item.parentId == 3
        ? (lastIndex = this.data2Num)
        : item.parentId == 4
        ? (lastIndex = this.data3Num)
        : -1

      if (index == lastIndex) {
        this.showLeft1 = !this.showLeft1
      } else {
        this.showLeft1 = true
      }

      if (item.parentId == 1) {
        data = this.cssmxData
        this.showLeft1 ? (this.data0Num = index) : (this.data0Num = -1)
        this.data1Num = this.data2Num = this.data3Num = -1
      } else if (item.parentId == 2) {
        data = this.csggaqData
        this.data0Num = this.data2Num = this.data3Num = -1
        this.showLeft1 ? (this.data1Num = index) : (this.data1Num = -1)
      } else if (item.parentId == 3) {
        data = this.csyxglData
        this.data0Num = this.data1Num = this.data3Num = -1
        this.showLeft1 ? (this.data2Num = index) : (this.data2Num = -1)
      } else if (item.parentId == 4) {
        data = this.csggfwData
        this.data0Num = this.data1Num = this.data2Num = -1
        this.showLeft1 ? (this.data3Num = index) : (this.data3Num = -1)
      }

      that.left1Data = data[item.type]
    },

    /*
     * 渲染近期就诊趋势图
     */
    drawRecentChart() {
      this.myChart = echarts.init(document.getElementById('recentChart'))

      let option = {
        xAxis: {
          type: 'category',
          data: ['3.1', '3.2', '3.3', '3.4', '3.5', '3.6', '3.7', '3.8'],
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgb(66,106,149)',
              width: '1',
            },
          },
          axisLabel: {
            textStyle: {
              color: '#fff',
            },
            fontSize: 22,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#fff',
            },
            fontSize: 24,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgb(66,106,149)',
            },
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(88, 138, 139,.6)',
              width: '0',
            },
          },
        },
        series: [
          {
            data: [1000, 1200, 1833, 2000, 2346, 2869, 3166, 3591],
            type: 'line',
          },
        ],
      }

      this.myChart.setOption(option)
    },

    /*
     * 渲染每日就诊 chart
     */
    drawDailyChart() {
      this.myChart1 = echarts.init(document.getElementById('dailyChart'))

      let option = {
        xAxis: {
          type: 'category',
          data: ['12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00'],
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgb(66,106,149)',
              width: '1',
            },
          },
          axisLabel: {
            textStyle: {
              color: '#fff',
            },
            fontSize: 22,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#fff',
            },
            fontSize: 24,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgb(66,106,149)',
            },
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(88, 138, 139,.6)',
              width: '0',
            },
          },
        },
        series: [
          {
            data: [10, 12, 33, 20, 23, 69, 31],
            type: 'line',
          },
        ],
      }

      this.myChart1.setOption(option)
    },

    // 清除柱状图
    clearHistogram() {
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmHistogram',
        })
      )
    },
    //清除柱状图+弹窗
    clearHistogram() {
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmHistogram',
        })
      )
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmPop',
        })
      )
    },
    // 清除线段
    clearLine() {
      top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify({ funcName: 'rmLine' }))
    },
    // 清除墙体和锥体
    clearWall() {
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmActiveWall',
        })
      )
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmConePoint',
        })
      )
    },
    // 关闭多余的弹窗
    colseOtherWin() {
      let that = this
      let win = [
        'showLeft1',
        'showThreeAlt',
        'showCursor',
        'caseDialogVisible',
        'medicalDialogVisible',
        'orderDialogVisible',
        'showHospital',
        'showAlt',
        'showCity',
        'showCard',
        'riskDialogVisible',
        'showIsNormal',
        'showQyrk',
        'showRealpersonMap',
        'showVideo',
        'isVideoSrc',
      ]
      if (this.skyjVideo != null) this.skyjVideo.dispose()
      this.skyVideoHtml = null
      this.left1Num = -1
      this.data0Num = -1
      this.data1Num = -1
      this.data2Num = -1
      this.data3Num = -1
      win.forEach(function (item) {
        if (that.openWind.indexOf(item) < 0) {
          that[item] = false
        }
      })
      // this.hidThreeTitle()

      // 关闭iframe弹窗
      var iframes = document.getElementById('mainPage').childNodes
      for (let i = 3; i < iframes.length; i++) {
        iframes[i].remove()
      }

      this.clearPoint()
      this.clearLine()
    },

    //  清除动态聚合

    rmJuhePointFun() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmJuhePoint',
            pointId: '', //点位类型Id
          })
        )
      } catch (error) {}
    },
    // 隐藏三级指标
    hidThreeTitle() {
      for (let ele = 0; ele < this.firstTitle.length; ele++) {
        let idName = this.firstTitle[ele].idName
        let id = ''
        this.firstTitle[ele].flag = false
        id = '#' + idName + ' .table-content'
        $(id).slideUp(500)
        for (let i = 0; i < this.firstTitle[ele].children.length; i++) {
          this.firstTitle[ele].children[i].checked = true
          this.checkSpan1(ele, i, this.firstTitle[ele].children[i])
        }
      }
    },

    // 清除飞线
    rmflyLineFun() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmflyLine',
          })
        )
      } catch (error) {}
    },
    /*
     * 清除热力图
     */
    clearHotMapAll() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmhotPowerMap',
          })
        )
      } catch (error) {}
    },
    // 清除地图撒点
    clearPoint() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmPoint',
            pointId: '',
          })
        )
      } catch (error) {}
    },
    clearPointById(id) {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmPoint',
            pointId: id,
          })
        )
      } catch (error) {}
    },

    // 清除3D文字
    clear3Dtext() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rm3Dtext',
          })
        )
      } catch (error) {}
    },
    // 清除清除叠加图层
    rmAddMapFun() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmAddMap',
            id: 'dlyd_road',
          })
        )
      } catch (error) {}
    },
    // 清除弹窗
    rmPopFun() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmPop',
          })
        )
      } catch (error) {}
    },

    // 清除视频
    rmVideoFun() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmVideo',
            videoType: '拥挤地段',
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmVideo',
            videoType: '',
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmVideo',
            videoType: 'spdw',
          })
        )
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmVideo',
            videoType: '视频点位',
          })
        )
      } catch (error) {}
    },
    // 回归原点
    goToZer() {
      const flyData = {
        funcName: 'flyto',
        flyData: {
          postion: {
            x: 119.64541625976562,
            y: 29.070894241333008,
            z: 707.720703125,
          },
          rotation: {
            x: 29.88661003112793,
            y: 346.91796875,
            z: 0.0003855216200463474,
          },
        },
      }
      top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(flyData))
    },
    // 回到深色地图
    goToMap() {
      top.document.getElementById('map').contentWindow.Work.change3D(1)
      this.clearWall()
      // 切换视角
      const flyData = {
        funcName: 'flyto',
        flyData: {
          postion: {
            x: 119.6402359008789,
            y: 29.078903198242188,
            z: 16071.5556640625,
          },
          rotation: {
            x: 88.6431884765625,
            y: 176.910888671875,
            z: 180,
          },
        },
      }
      top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(flyData))
    },
  },
  beforeDestroy() {
    window.removeEventListener('message', this.showMapDialog, false)
    window.removeEventListener('message', this.getSkyjPointDataFun, false)
    this.clear3Dtext()
    this.clearPoint()
    this.clearHotMapAll()
    this.rmVideoFun()
    this.rmAddMapFun()
    clearTimeout(this.timer0)
    clearTimeout(this.yysTime)
    clearTimeout(this.loadTime)
    clearTimeout(this.sydTime)
    if (this.skyjVideo != null) this.skyjVideo.dispose()
  },
})
