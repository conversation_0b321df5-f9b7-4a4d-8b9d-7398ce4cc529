body,
p {
  padding: 0;
  margin: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.lg-orange {
  background-image: linear-gradient(#ffe2cd 10%, #ffffff 60%, #fd852e 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.flex-1 {
  flex: 1;
}

.pointer {
  cursor: pointer;
}

.main-box {
  width: 7680px;
  height: 2005px;
  background: url('/static/citybrain/csdn/img/cstz4-page/bg.png') no-repeat 100% 100%;
  display: flex;
  justify-content: space-between;
  padding: 100px 140px 0 140px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.left-box,
.right-box {
  width: 1930px;
  height: 100%;
}

.center-box {
  flex: 1;
  background: url('/static/citybrain/csdn/img/cstz4-page/center-bg.png') no-repeat;
  background-size: 110%;
  position: relative;
}

.iframe-box {
  width: 1926px;
  height: 954px;
  overflow: hidden;
}

/* 中间样式 */
.top-box {
  width: 100%;
  height: 310px;
  padding: 0 500px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.bottom-box {
  width: 100%;
  height: calc(100% - 310px);
  background-image: url('/static/citybrain/csdn/img/cstz4-page/bottom-bg.png');
  background-position: center -470px;
  background-repeat: no-repeat;
  position: relative;
}

.num-box {
  width: 100%;
  height: 100%;
  font-size: 138px;
  text-align: center;
  font-weight: bold;
  background: url('/static/citybrain/csdn/img/cstz4-page/num-bottom.png') no-repeat;
  background-position: center 100px;
  letter-spacing: -10px;
  position: absolute;
  top: 0;
  z-index: 1;
}

.pie-css {
  overflow: hidden;
}

.pie-css > div {
  transform: rotateX(65deg) scale(2) !important;
}

/* 图片位置 */
.bg1 {
  width: 100%;
  height: 1064px;
  position: absolute;
  z-index: 0;
  bottom: -420px;
  background: url('/static/citybrain/csdn/img/cstz4-page/bottom-bg1.png') no-repeat;
  background-position: center;
}

.bg0 {
  width: 1195px;
  height: 946px;
  position: absolute;
  top: 100px;
  left: 1200px;
  z-index: 0;
  background: url('/static/citybrain/csdn/img/cstz4-page/city.png') no-repeat;
  background-position: center;
  cursor: pointer;
}

.center-text {
  width: 100%;
  text-align: center;
  text-align: center;
  font-size: 48px;
  font-weight: bold;
  font-style: italic;
  margin-top: 200px;
}

.list {
  padding-top: 180px;
  box-sizing: border-box;
  position: relative;
}

.ul-list {
  width: 2894px;
  height: 1100px;
  margin: 0 auto;
  position: relative;
}

.li-list {
  width: 300px;
  height: 300px;
  font-size: 48px;
  line-height: 60px;
  margin: auto;
  position: absolute;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.li-list:nth-of-type(1) {
  left: 400px;
  top: -100px;
}

.li-list:nth-of-type(2) {
  right: 520px;
  top: 100px;
}

.li-list:nth-of-type(3) {
  right: 600px;
  bottom: -100px;
}

.li-list:nth-of-type(4) {
  left: 500px;
  bottom: 0;
}

.pointer-css {
  width: 269px;
  height: 153px;
  background: url('/static/citybrain/csdn/img/cstz4-page/pointer.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 280px;
  left: 1790px;
  z-index: 10;
  animation: jump1 3.5s infinite;
}

/* 连接线 */
.line1 {
  width: 800px;
  height: 100px;
  background: url('/static/citybrain/csdn/img/cstz4-page/left1-line.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 334px;
  left: 0;
  z-index: 2;
}

.line2 {
  width: 870px;
  height: 100px;
  background: url('/static/citybrain/csdn/img/cstz4-page/left2-line.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  bottom: 400px;
  left: 0;
  z-index: 2;
}

.line3 {
  width: 900px;
  height: 100px;
  background: url('/static/citybrain/csdn/img/cstz4-page/right1-line.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 542px;
  left: 2633px;
  z-index: 2;
}

.line4 {
  width: 953px;
  height: 100px;
  background: url(/static/citybrain/csdn/img/cstz4-page/right2-line.png) no-repeat;
  background-size: 100% 100%;
  position: absolute;
  bottom: 288px;
  left: 2570px;
  z-index: 2;
}

.line2 > .dot {
  margin-bottom: -95px;
}

.line3 > .dot {
  margin-left: 888px;
}

.line4 > .dot {
  margin-left: 955px;
  margin-top: 80px;
}

.dot {
  display: inline-block;
  width: 36px;
  height: 36px;
  background-image: url('/static/citybrain/csdn/img/cstz4-page/dot.png');
  background-size: 100% 100%;
  margin-left: -20px;
  margin-top: -20px;
}

@keyframes jump1 {
  0% {
    transform: translate(-10px, 20px);
    /*开始位置*/
  }

  50% {
    /* transform: translate(0px, 50px); */
    transform: translate(90px, -130px);
    /* 可配置跳动方向 */
  }

  100% {
    transform: translate(-10px, 20px);
    /*开始位置*/
  }
}

@keyframes jump2 {
  0% {
    transform: scale(1);
    /*开始位置*/
  }

  50% {
    transform: scale(0.92);
    /* 可配置跳动方向 */
  }

  100% {
    transform: scale(1);
  }
}

.revolve-box {
  width: 2600px;
  height: 1100px;
  position: relative;
  top: -1125px;
  left: 580px;
  border-radius: 50%;
  transform-style: preserve-3d;
  transform: rotateZ(10deg) rotateY(0deg) rotateX(40deg);
}

.revolve-box img {
  width: 86px;
  height: 86px;
  position: absolute;
}

.revolve-box img:nth-child(1) {
  animation: animX 6s cubic-bezier(0.36, 0, 0.64, 1) -3s infinite alternate,
    animY 6s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate,
    scale 12s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate;
}

.revolve-box img:nth-child(2) {
  animation: animX 6s cubic-bezier(0.36, 0, 0.64, 1) -5.4s infinite alternate,
    animY 6s cubic-bezier(0.36, 0, 0.64, 1) -2.4s infinite alternate,
    scale 12s cubic-bezier(0.36, 0, 0.64, 1) -2.4s infinite alternate;
}

.revolve-box img:nth-child(3) {
  animation: animX 6s cubic-bezier(0.36, 0, 0.64, 1) -7.8s infinite alternate,
    animY 6s cubic-bezier(0.36, 0, 0.64, 1) -4.8s infinite alternate,
    scale 12s cubic-bezier(0.36, 0, 0.64, 1) -4.8s infinite alternate;
}

.revolve-box img:nth-child(4) {
  animation: animX 6s cubic-bezier(0.36, 0, 0.64, 1) -10.2s infinite alternate,
    animY 6s cubic-bezier(0.36, 0, 0.64, 1) -7.2s infinite alternate,
    scale 12s cubic-bezier(0.36, 0, 0.64, 1) -7.2s infinite alternate;
}

.revolve-box img:nth-child(5) {
  animation: animX 6s cubic-bezier(0.36, 0, 0.64, 1) -12.6s infinite alternate,
    animY 6s cubic-bezier(0.36, 0, 0.64, 1) -9.6s infinite alternate,
    scale 12s cubic-bezier(0.36, 0, 0.64, 1) -9.6s infinite alternate;
}

@keyframes animX {
  0% {
    left: -5%;
  }

  100% {
    left: 95%;
  }
}

@keyframes animY {
  0% {
    top: -5%;
  }

  100% {
    top: 95%;
  }
}

@keyframes scale {
  0% {
    transform: scale(0.5);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
}
