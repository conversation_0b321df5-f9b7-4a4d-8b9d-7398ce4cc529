<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>名称搜索地图位置</title>
    <script src="/static/citybrain/hjbh/js/vue.js"></script>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      .seachBox {
        display: flex;
        width: 600px;
        height: 60px;
        overflow: hidden;
      }
      .el-select {
        width: 40%;
      }
      .el-select > .el-input {
        height: 100%;
      }
      .el-input--suffix .el-input__inner {
        height: 100%;
        text-align: center;
        font-size: 22px !important;
        border-radius: 0;
        border: 1px solid #2565a3 !important;
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
      }
      .el-input {
        height: 100%;
      }
      .el-autocomplete {
        width: 70%;
      }
      .el-autocomplete .el-input__inner {
        font-size: x-large;
        border-radius: 0;
        border: 0 !important;
        border-top: 1px solid #2565a3 !important;
        border-bottom: 1px solid #2565a3 !important;
      }
      .el-input__inner {
        color: #fff !important;
        background: rgba(19, 42, 75, 0.9);
        height: 100% !important;
      }
      .el-button {
        background: #009ace;
        color: #fff;
        font-size: 22px !important;
        border: 1px solid #2565a3 !important;
        border-left: 0 !important;
        border-radius: 0 !important;
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
      }
      .el-select-dropdown {
        background: rgba(19, 42, 75, 1);
        border: 0.2px solid #2565a3 !important;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background: rgba(36, 68, 119, 1) !important;
      }
      .el-popper[x-placement^="bottom"] .popper__arrow {
        display: none;
      }
      .el-scrollbar {
        background: rgba(19, 42, 75, 0.9) !important;
        border: 0 !important;
        border-radius: 4px;
      }
      .el-autocomplete-suggestion {
        border: 0.2px solid #2565a3 !important;
        background: rgba(19, 42, 75, 1);
      }
      .el-autocomplete-suggestion li {
        color: #ccc !important;
      }
      .el-autocomplete-suggestion li:hover {
        background: rgba(36, 68, 119, 1) !important;
      }
      .el-scrollbar__wrap::-webkit-scrollbar {
        width: 6px;
        height: 1px;
      }
      .el-scrollbar__wrap::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: #20aeff;
        height: 10px;
      }
      .el-select-dropdown__item {
        color: #ccc;
        font-size: 20px;
      }
      .el-select-dropdown__item.is-disabled {
        color: #6c6868;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="seachBox">
        <el-select v-model="select" @change="changeSelect">
          <el-option label="地名地址" value="地址"></el-option>
          <el-option label="视频监控" value="视频"></el-option>
          <el-option label="POI点位" value="POI"></el-option>
          <el-option label="物联感知设备" value="物联感知"></el-option>
          <el-option label="网格查询" value="网格" :disabled="true"></el-option>
        </el-select>
        <el-autocomplete
          v-model="namePoint"
          :fetch-suggestions="querySearchAsync"
          :placeholder="altName"
          @select="handleSelect"
        ></el-autocomplete>
        <el-tooltip content="搜索" placement="right" effect="light">
          <el-button icon="el-icon-search" @click="searchBtn()"></el-button>
        </el-tooltip>
      </div>
    </div>
  </body>

  <script>
    window.addEventListener("message", function (event) {
      if (
        Object.prototype.toString.call(event.data) === "[object Object]" &&
        event.data
      ) {
        if (event.data && event.data.type == "pointClick" && event.data.data) {
          const item = JSON.parse(event.data.data.data);
          if (item.pointId == "sou") {
            // console.log(item);
            // debugger;
            souName.getInfoData(item);
          }
        }
      }
    });
    let souName = new Vue({
      el: "#app",
      data: {
        select: "地址",
        altName: "地名地址",
        namePoint: "",
        results: [],
        wgData: [
          {
            name: "婺城区",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/wcqqkwl/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "金义新区",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/jdqqkwg/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "东阳市",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/dysqkwg/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "义乌市",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/ywsqkwl/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "永康市",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/yksqkwg/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "兰溪市",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/lxsqkwg/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "浦江县",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/pjxqkwl/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "武义县",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/wyxqkwl/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "磐安县",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/paxqkwg/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
          {
            name: "开发区",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/gismap/egis-map-engine/map/services/jhkfqqkwg/wmts100/1.0.0/default/{TileMatrixSet}/{z}/{y}/{x}",
          },
        ],
      },
      watch: {
        namePoint(newValue, oldValue) {
          newValue == "" ? this.resetInput() : "";
        },
      },
      created() {},
      mounted() {},
      methods: {
        changeSelect() {
          this.namePoint = "";
          this.altName =
            this.select == "地址"
              ? "地名地址"
              : this.select == "视频"
              ? "视频监控"
              : this.select == "物联感知"
              ? "物联感知设备"
              : "POI点位";
        },
        searchVideo(type, name) {
          return $api("csdnsy_gis01", { code: type, name: name });
        },
        searchBtn() {
          let sel = this.select;
          console.log("this.namePoint==>", this.namePoint);
          if (sel == "地址") {
            this.searchPlace().then((response) => {
              if (response.data.pois) {
                let point = response.data.pois[0];
                this.handleSelect(point);
              }
            });
          } else {
            this.searchVideo(this.select, this.namePoint).then((res) => {
              if (res) {
                this.handleSelect(res[0]);
              }
            });
          }
        },
        searchPlace() {
          return axios({
            method: "get",
            url: "https://csdn.dsjj.jinhua.gov.cn:8101/tdt/search",
            params: {
              type: "query",
              tk: "86103f598bc5f9e7b1d3ea9479f48f7f",
              postStr: {
                keyWord: this.namePoint,
                level: "11",
                mapBound: "119.14,28.32,120.46,29.41",
                queryType: "1",
                count: "20",
                start: "0",
              },
            },
          });
        },
        querySearchAsync(queryString, cb) {
          let sel = this.select;
          if (sel == "地址") {
            this.searchPlace().then((response) => {
              if (response.data.pois) {
                this.results = JSON.parse(
                  JSON.stringify(response.data.pois).replace(/name/g, "value")
                );
                cb(this.results);
              }
            });
          } else {
            this.searchVideo(this.select, queryString).then((res) => {
              if (res) {
                this.results = JSON.parse(
                  JSON.stringify(res).replace(/name/g, "value")
                );
                cb(this.results);
              }
            });
          }
        },
        handleSelect(e) {
          // let id = "zhdd_map_hdz";
          let id =
            this.select == "地址"
              ? "zhdd_map_hdz"
              : this.select == "视频"
              ? "camera-load3"
              : this.select == "物联感知"
              ? "wlgzsb"
              : "rckz-兴趣点通用";
          let icon =
            this.select == "地址"
              ? "zhdd_map_hdz"
              : this.select == "视频"
              ? "camera-load3"
              : this.select == "物联感知"
              ? "wlgzsb"
              : "rckz-兴趣点通用";
          if (this.select == "地址") {
            let pointStr =
              e.lonlat.split(" ")[0] + "," + e.lonlat.split(" ")[1];
            let obj = [{ data: { pointId: "sou", obj: e }, point: pointStr }];
            let flyToPoint = e.lonlat.split(" ");
            this.pointTextMapFun(icon, obj, id, 0.4);
            this.flytoAdd(flyToPoint);
          } else if (this.select == "视频" && e != undefined && e != "") {
            let pointStr = e.jwd;
            let obj = [
              {
                data: { pointId: "sou", obj: e },
                point: pointStr,
                code: e.addinfo,
              },
            ];
            let flyToPoint = e.jwd.split(",");
            this.pointTextMapFun(icon, obj, id, 0.4);
            this.flytoAdd(flyToPoint);
          } else {
            console.log("e==>", e);
            let pointStr = e.jwd;
            let obj = [{ data: { pointId: "sou", obj: e }, point: pointStr }];
            let flyToPoint = e.jwd.split(",");
            let iconSize = 1;
            this.pointTextMapFun(icon, obj, id, iconSize);
            this.flytoAdd(flyToPoint);
          }
        },
        resetInput() {
          this.rmPoint("zhdd_map_hdz");
          this.rmPoint("camera-load3");
          this.rmPoint("wlgzsb");
          this.rmPoint("rckz-兴趣点通用");
        },
        // poi和物联感知查询详情
        getInfoData(item) {
          $api("csdnsy_gis02", {
            code: item.obj.subtype,
            id: item.obj.id,
          }).then((res) => {
            let arr = Object.keys(res[0]).map((ele) => {
              return {
                name: ele,
                value: res[0][ele],
              };
            });

            let countStr = "";
            for (let index = 0; index < arr.length; index++) {
              if (arr[index].name.indexOf("did") > -1) continue;
              else if (arr[index].name.indexOf("value") > -1) continue;
              countStr += `<div
          class="item"
          style="display: flex; font-size: 32px; color: #2299e2; line-height: 70px"
        >
          <span style="margin-left:30px;white-space: nowrap; ">${arr[index].name}  :</span>
          <span style="color: #fff; margin-left:30px;

          "
            >${arr[index].value}</span
          >
        </div>`;
            }
            let str = `
        <div
      onclick=" this.style.display = 'none'"
      style="
        width: 800px;
        position: absolute;

        border-radius: 5px;
        background-color: rgba(10, 31, 53, 0.8);
        z-index: 999999;
        -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
        box-shadow: inset 0 0 40px 0 #5ba3fa;
        padding: 24px;
      "
    >
      <div
        style="
          width: 0px;
          height: 0px;
          border-top: 70px solid rgba(10, 31, 53, 0.8);
          border-right: 60px solid transparent;
          position: absolute;
          bottom: -70px;
          left: 102px;
        "
      ></div>
      <div class="container">${countStr}</div>
    </div>
        `;

            let objData = {
              funcName: "customPop",
              coordinates: item.obj.jwd.split(","),
              closeButton: true,
              html: str,
            };

            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          });
        },
        // 飞行
        flytoAdd(obj) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "flyto",
              flyData: {
                center: obj,
                zoom: 12.5, //大
                pitch: 2, //倾斜角
              },
            })
          );
        },
        // 添加点位方法
        pointTextMapFun(icon, pointData, pointId, iconSize) {
          this.rmPoint("zhdd_map_hdz");
          this.rmPoint("camera-load3");
          this.rmPoint("wlgzsb");
          this.rmPoint("rckz-兴趣点通用");
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad", //功能名称
              pointType: icon, //点位类型图标
              pointId: "0" + pointId,
              setClick: true,
              pointData: pointData,
              imageConfig: { iconSize: iconSize },
              size: [0.01, 0.01, 0.01, 0.01],
              popup: {
                offset: [50, -100],
              },
            })
          );
          // 加载3D文字方法
          let str = {
            pos: [
              pointData[0].point.split(",")[0],
              pointData[0].point.split(",")[1],
              20,
            ],
            color: [0, 229, 238, 1],
            text: pointData[0].data.obj.name,
          };
          // top.document.getElementById("map").contentWindow.Work.funChange(
          //   JSON.stringify({
          //     funcName: "3Dtext",
          //     id: "00" + pointId,
          //     textData: [str],
          //     textSize: 30,
          //     zoomShow: true,
          //     color: [171, 196, 104, 1],
          //   })
          // );
        },
        // 清除点位
        rmPoint(id) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "0" + id, //传id清除单类，不传清除所有
            })
          );
          try {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "rmPop",
              })
            );
          } catch (error) {}
          // top.document.getElementById("map").contentWindow.Work.funChange(
          //   JSON.stringify({
          //     funcName: "rm3DtextById",
          //     id: "00" + id,
          //   })
          // );
        },
      },
    });
  </script>
</html>
