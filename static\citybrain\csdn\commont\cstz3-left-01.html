<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />

    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="../jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="../echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>

    <style>
      ul,
      li {
        list-style: none;
      }
      body {
        margin: 0;
      }
      ul {
        padding: 0;
        margin: 0;
      }
      #cstz3_left1 {
        width: 900px;
        height: 1660px;
        overflow: hidden;
        background-color: #092c4e;
        /* padding: 20px 10px; */
        /* padding-top: 20px; */
      }
      .text {
        color: #fff !important;
        font-size: 55px;
      }

      .djtl_container {
        width: 100%;
        height: 1660px;
        /* overflow-y: scroll; */
        box-sizing: border-box;
      }
      .djtl_item {
        width: 97%;
        height: 400px;
        /* background-color: #132d57; */
        margin: 0 15px;
      }

      .djtl_container::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .djtl_container::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }
      .djtl_item_title {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 32px;
        text-align: center;
        line-height: 46px;
        margin: 30px 0;
      }
      .djtl_item_title .name {
        line-height: 48px;
      }

      .item_small_title {
        color: #fff;
        font-size: 30px;
        text-align: center;
        margin-bottom: 10px;
      }
      .djtl_item_content {
        display: flex;
      }
      .right_content,
      .left_content {
        width: 50%;
        height: 330px;
      }
      .echarts_content {
        display: flex;
      }
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
    </style>
  </head>

  <body>
    <div id="cstz3_left1">
      <!-- <nav style="padding: 0px 0 0 10px">
        <s-header-title-2 title="党建统领" />
      </nav> -->
      <ul class="djtl_container">
        <li class="djtl_item">
          <div class="djtl_item_title" style="margin: 25px 0">
            <span class="name">党员总数</span>

            <s-num :value="dySum" color="lg-yellow" :unit="dyUnit" unit-color="white" />
          </div>
          <div class="djtl_item_content">
            <div class="left_content">
              <div class="item_small_title">性别结构</div>
              <div class="echarts_content">
                <div id="echarts01" style="width: 330px; height: 285px"></div>
                <div id="echarts02" style="width: 330px; height: 285px"></div>
              </div>
            </div>
            <div class="right_content">
              <div class="item_small_title">学历结构</div>
              <!-- <div id="echarts03" style="height: 285px"></div> -->
              <div id="echarts03" style="width: 250px; height: 285px; margin: 0 auto"></div>
            </div>
          </div>
        </li>
        <li class="djtl_item">
          <div class="djtl_item_title">
            <span class="name">团员总数</span>
            <s-num :value="tySum" color="lg-yellow" :unit="tyUnit" unit-color="white" />
          </div>
          <div id="pieCharts1" style="height: 330px"></div>
        </li>
        <li class="djtl_item">
          <div class="djtl_item_title">
            <span class="name">公务员总数</span>
            <s-num :value="gwySum" color="lg-yellow" :unit="gwyUnit" unit-color="white" />
          </div>
          <div id="pieCharts3" style="height: 313px"></div>
        </li>
        <li class="djtl_item">
          <div class="djtl_item_title">
            <span class="name">民主党派总人数</span>
            <s-num :value="mzdSum" color="lg-yellow" :unit="mzdUnit" unit-color="white" />
          </div>
          <div id="pieCharts2" style="height: 250px"></div>
        </li>
      </ul>
    </div>
  </body>
</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
  var vm = new Vue({
    el: '#cstz3_left1',
    data: {
      // 党员总数
      dySum: '',
      dyUnit: '',
      // 团员总数
      tySum: '',
      tyUnit: '',
      // 民主党派总人数
      mzdSum: '',
      mzdUnit: '',
      // 公务员总数
      gwySum: '',
      gwyUnit: '',

      xlData: [
        {
          name: '高中',
          value: 58,
        },
        {
          name: '大专',
          value: 108,
        },
        {
          name: '本科',
          value: 158,
        },
        {
          name: '大专以上',
          value: 11.25,
        },
        {
          name: '其他',
          value: 58,
        },
      ],
      tyzsData: [
        {
          name: '学生团员',
          value: '21.9',
        },
        {
          name: '企业团员',
          value: '2.82',
        },
        {
          name: '乡镇（村）团员',
          value: '6.63',
        },
        {
          name: '机关事业单位',
          value: '2.30',
        },
        {
          name: '城市街道（社区）团员',
          value: '2.28',
        },
        {
          name: '社会组织和其他领域',
          value: '0.92',
        },
      ],
      mzdpData: [
        {
          name: '民革',
          value: '15858',
        },
        {
          name: '民盟',
          value: '4821',
        },
        {
          name: '民建',
          value: '4145',
        },
        {
          name: '民进',
          value: '10000',
        },
        {
          name: '农民工',
          value: '10584',
        },
        {
          name: '九三学社',
          value: '2858',
        },
      ],
      gwyData: [
        {
          name: '国企、高效市管干部',
          value: '21.9',
        },
        {
          name: '县（市、区）市管干部',
          value: '6.63',
        },
        {
          name: '市管干部',
          value: '2.82',
        },
        {
          name: '市直单位管干部',
          value: '2.30',
        },
      ],
    },
    created() {},
    watch: {},
    mounted() {
      this.initFun()
    },
    methods: {
      initFun() {
        let that = this
        // this.getEcharts003('echarts03', this.xlData)

        // this.pieChart('pieCharts2', this.mzdpData, 70, '2%', '10%', '人')

        $api('djtlLeft005', { belong_to: '各民族党派人数' }).then((res) => {
          // console.log("res==>",res);
          let unit = ''
          let data = res.map((item) => {
            unit = item.total.substr(item.total.length - 1, 1)
            let str = {
              name: item.name,
              value: item.total.replace(unit, ''),
            }
            return str
          })
          this.pieChart('pieCharts2', data, 40, '5%', '10%', unit, '-2%')
        })

        // 党员总数

        $api('djtlLeft001').then((res) => {
          let data = res.filter((item) => {
            return item.mkmc === '党建发展'
          })
          console.log(data)
          let manData = ''
          let womanData = ''

          let xlData = ''
          data.forEach((item) => {
            if (item.ymbq == '党员总数') {
              that.dyUnit = item.unit
              that.dySum = item.value
            } else if (item.ymbq == '男党员总数') {
              manData = item.ymbq + '\n' + item.value + item.unit
            } else if (item.ymbq == '女党员总数') {
              womanData = item.ymbq + '\n' + item.value + item.unit
            } else if (item.ymbq == '大专以上党员总数') {
              xlData = item.ymbq + '\n' + item.value + item.unit
            }
          })

          that.getEcharts001('echarts01', '/static/citybrain/djtl/img/djtl-middle/cenleft002.png', 70.4, manData)
          that.getEcharts001('echarts02', '/static/citybrain/djtl/img/djtl-middle/cenleft003.png', 29.6, womanData)
          that.getEcharts001('echarts03', '/static/citybrain/djtl/img/djtl-middle/cenleft005.png', 29.29, xlData)
        })
        $api('djtlLeft001').then((res) => {
          let tzgzData = res.filter((item) => {
            return item.mkmc === '统战工作'
          })
          that.mzdSum = tzgzData[0].value
          that.mzdUnit = tzgzData[0].unit
        })

        $api('djtlLeft001').then((res) => {
          let tjfzData = res.filter((item) => {
            return item.mkmc === '团建发展'
          })
          that.tyUnit = tjfzData[1].unit
          that.tySum = tjfzData[1].value
        })

        $api('djtlLeft006', { belong_to: '各领域团员分布' }).then((res) => {
          let unit = ''
          let data = res.map((item) => {
            unit = item.total.substr(item.total.length - 2, 2)
            let str = {
              name: item.name,
              value: item.total.replace(unit, ''),
            }
            return str
          })
          that.pieChart('pieCharts1', data, 20, '10%', '0%', unit, '10%')
        })

        $api('djtlLeft001').then((res) => {
          let gzgwData = res.filter((item) => {
            return item.mkmc === '公职公务'
          })
          that.gwyUnit = gzgwData[0].unit
          that.gwySum = (Number(gzgwData[0].value) + Number(gzgwData[1].value)).toString()
          let dataArr = []
          dataArr.push(gzgwData[0])
          dataArr.push(gzgwData[1])
          let data = dataArr.map((item) => {
            let str = {
              name: item.ymbq,
              value: item.value,
              unit: item.unit,
            }
            return str
          })
          console.log(data)
          let manData = gzgwData[0].value
          let womanData = gzgwData[1].value
          this.pieChartWomanChart('pieCharts3', data, womanData, manData)
        })
      },
      getEcharts001(dom, img, value, tle) {
        let myEc = echarts.init(document.getElementById(dom))
        let angle = 0 //角度，用来做简单的动画效果的
        let val = value
        let option = {
          graphic: {
            elements: [
              {
                type: 'image',
                style: {
                  image: img,
                  width: 70,
                  height: 70,
                },
                left: 'center',
                top: '52px',
              },
            ],
          },

          title: [
            {
              text: tle,
              x: 'center',
              y: '62%',
              textStyle: {
                fontSize: 28,
                color: '#ffffff',
              },
            },
          ],
          series: [
            {
              name: 'ring5',
              type: 'custom',
              coordinateSystem: 'none',
              //   center: ['50%', '10%'],
              renderItem: function (params, api) {
                return {
                  type: 'arc',
                  shape: {
                    cx: api.getWidth() / 2,
                    cy: api.getHeight() / 3,
                    r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                    startAngle: ((0 + angle) * Math.PI) / 180,
                    endAngle: ((90 + angle) * Math.PI) / 180,
                  },
                  style: {
                    stroke: '#16EAFF',
                    fill: 'transparent',
                    lineWidth: 3,
                  },
                  silent: true,
                }
              },
              data: [0],
            },
            {
              name: 'ring5',
              type: 'custom',
              //   center: ['50%', '10%'],
              coordinateSystem: 'none',
              renderItem: function (params, api) {
                return {
                  type: 'arc',
                  shape: {
                    cx: api.getWidth() / 2,
                    cy: api.getHeight() / 3,
                    r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                    startAngle: ((180 + angle) * Math.PI) / 180,
                    endAngle: ((270 + angle) * Math.PI) / 180,
                  },
                  style: {
                    stroke: '#16EAFF',
                    fill: 'transparent',
                    lineWidth: 3,
                  },
                  silent: true,
                }
              },
              data: [0],
            },
            {
              name: 'ring5',
              type: 'custom',
              //   center: ['50%', '10%'],

              coordinateSystem: 'none',
              renderItem: function (params, api) {
                return {
                  type: 'arc',
                  shape: {
                    cx: api.getWidth() / 2,
                    cy: api.getHeight() / 3,
                    r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
                    startAngle: ((270 + -angle) * Math.PI) / 180,
                    endAngle: ((40 + -angle) * Math.PI) / 180,
                  },
                  style: {
                    stroke: '#16EAFF',
                    fill: 'transparent',
                    lineWidth: 3,
                  },
                  silent: true,
                }
              },
              data: [0],
            },
            {
              name: 'ring5',
              type: 'custom',
              coordinateSystem: 'none',
              //   center: ['50%', '10%'],

              renderItem: function (params, api) {
                return {
                  type: 'arc',
                  shape: {
                    cx: api.getWidth() / 2,
                    cy: api.getHeight() / 3,
                    r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
                    startAngle: ((90 + -angle) * Math.PI) / 180,
                    endAngle: ((220 + -angle) * Math.PI) / 180,
                  },
                  style: {
                    stroke: '#16EAFF',
                    fill: 'transparent',
                    lineWidth: 3,
                  },
                  silent: true,
                }
              },
              data: [0],
            },
            {
              name: 'ring5',
              type: 'custom',
              coordinateSystem: 'none',
              //   center: ['50%', '10%'],

              renderItem: function (params, api) {
                let x0 = api.getWidth() / 2
                let y0 = api.getHeight() / 3
                let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65
                let point = getCirlPoint(x0, y0, r, 90 + -angle)
                return {
                  type: 'circle',
                  shape: {
                    cx: point.x,
                    cy: point.y,
                    r: 4,
                  },
                  style: {
                    stroke: '#16EAFF', //粉
                    fill: '#0CD3DB',
                  },
                  silent: true,
                }
              },
              data: [0],
            },
            {
              name: 'ring5', //绿点
              type: 'custom',

              coordinateSystem: 'none',
              renderItem: function (params, api) {
                let x0 = api.getWidth() / 2
                let y0 = api.getHeight() / 3
                let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65
                let point = getCirlPoint(x0, y0, r, 270 + -angle)
                return {
                  type: 'circle',
                  shape: {
                    cx: point.x,
                    cy: point.y,
                    r: 4,
                  },
                  style: {
                    stroke: '#16EAFF', //绿
                    fill: '#0CD3DB',
                  },
                  silent: true,
                }
              },
              data: [0],
            },
            {
              type: 'pie',
              radius: ['53%', '42%'],
              center: ['50%', '33%'],
              silent: true,
              clockwise: true,
              startAngle: 90,
              z: 0,
              zlevel: 0,
              label: {
                normal: {
                  position: 'center',
                },
              },
              data: [
                {
                  value: value,
                  name: '',
                  itemStyle: {
                    normal: {
                      color: {
                        // 完成的圆环的颜色
                        colorStops: [
                          {
                            offset: 0,
                            color: '#5180F8', // 0% 处的颜色
                          },
                          {
                            offset: 1,
                            color: '#16EAFF', // 100% 处的颜色
                          },
                        ],
                      },
                    },
                  },
                },
                {
                  value: 100 - value,
                  name: '',
                  label: {
                    normal: {
                      show: false,
                    },
                  },
                  itemStyle: {
                    normal: {
                      color: '#173164',
                    },
                  },
                },
              ],
            },
          ],
        }

        //获取圆上面某点的坐标(x0,y0表示坐标，r半径，angle角度)
        function getCirlPoint(x0, y0, r, angle) {
          let x1 = x0 + r * Math.cos((angle * Math.PI) / 180)
          let y1 = y0 + r * Math.sin((angle * Math.PI) / 180)
          return {
            x: x1,
            y: y1,
          }
        }

        function draw() {
          angle = angle + 3
          myEc.setOption(option, true)
          //window.requestAnimationFrame(draw);
        }

        setInterval(function () {
          //用setInterval做动画感觉有问题
          draw()
        }, 100)

        myEc.setOption(option)
        myEc.getZr().on('mousemove', (param) => {
          myEc.getZr().setCursorStyle('default')
        })
      },
      getEle(dom, numArr) {
        let charts01 = echarts.init(document.getElementById(dom))
        //数据
        var XName = ['推送', '处置中', '完成']
        var data1 = [[100, 100, 100], numArr]
        var Line = ['线1', '线2']
        var color = ['#256589', '#2952D7']

        //数据处理
        var datas = []
        Line.map((item, index) => {
          if (index == 0) {
            datas.push({
              symbolSize: 20,
              symbol: 'circle',
              hoverAnimation: false,
              name: item,
              type: 'line',
              data: data1[index],
              itemStyle: {
                normal: {
                  borderWidth: 10,
                  color: color[index],
                },
              },
            })
          } else {
            datas.push({
              symbolSize: 25,
              symbol: 'circle',
              hoverAnimation: false,
              name: item,
              type: 'line',
              data: data1[index],
              itemStyle: {
                normal: {
                  borderWidth: 22,
                  color: color[index],
                },
              },
            })
          }
        })

        option = {
          grid: {
            left: '0%',
            top: '40%',
            bottom: '60%',
            right: '5%',
          },
          yAxis: [
            {
              type: 'value',
              position: 'right',
              max: 100,
              splitLine: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
            },
          ],
          xAxis: [
            {
              type: 'category',
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#6A989E',
                },
              },
              axisLabel: {
                inside: true,
                show: true,
                textStyle: {
                  color: '#90deff', // x轴颜色
                  fontWeight: 'normal',
                  fontSize: '20',
                  lineHeight: -70,
                },
              },
              data: XName,
            },
          ],
          series: datas,
        }

        charts01.setOption(option)
        charts01.getZr().on('mousemove', (param) => {
          charts01.getZr().setCursorStyle('default')
        })
      },
      getEcharts003(dom, dataNew) {
        let myEc = echarts.init(document.getElementById(dom))
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: <br/> {d}%',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          //   legend: {
          //     orient: 'vertical',
          //     left: '50%',
          //     bottom: '15%',
          //     icon: 'circle',
          //     itemGap: 14,
          //     textStyle: {
          //       color: '#D6E7F9',
          //       fontSize: 26,
          //       rich: {
          //         percent: {
          //           color: '#ffd79b',
          //           fontSize: 28,
          //         },
          //       },
          //     },
          //     formatter: function (name) {
          //       var data = option.series[0].data //获取series中的data
          //       var total = 0
          //       var tarValue
          //       for (var i = 0, l = data.length; i < l; i++) {
          //         total += data[i].value
          //         if (data[i].name == name) {
          //           tarValue = data[i].value
          //         }
          //       }
          //       var p = (tarValue / total) * 100
          //       return name + ': ' + tarValue + '万'
          //     },
          //   },
          legend: {
            orient: 'vertical',
            left: '50%',
            bottom: '15%',
            icon: 'circle',
            itemGap: 14,
            textStyle: {
              color: '#D6E7F9',
              fontSize: 22,
              rich: {
                percent: {
                  color: '#ffd79b',
                  fontSize: 25,
                },
              },
            },
            // formatter: function (name) {
            //   for(let i=0;i<dataNew.length;i++){
            //     if(dataNew[i].name==name){
            //       return name + ' ' + '    ' +'{percent|'+dataNew[i].text+'}'+' '
            //     }
            //   }
            // },
            formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += Number(data[i].value)
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              var p = (tarValue / total) * 100
              if (dom == 'pieCharts0') {
                return name + ' ' + '{percent|' + p.toFixed(2) + '%}'
              }
              return name + ' ' + '{percent|' + tarValue + '万' + '}' + ' '
            },
          },
          series: [
            {
              name: dom === 'charts_lxfl' ? '类型分类' : '事项分类',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['30%', '50%'],
              roseType: 'area',
              itemStyle: {
                borderRadius: 5,
              },
              label: {
                show: false,
              },
              data: dataNew,
            },
          ],
        }
        myEc.setOption(option)
        myEc.getZr().on('mousemove', (param) => {
          myEc.getZr().setCursorStyle('default')
        })
      },
      pieChart(dom, dataNew, itemGap, legendRight, legendTop, dataNuit, graphicTop) {
        const myCharts2 = echarts.init(document.getElementById(dom))
        let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}:{d}%',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          legend: {
            orient: 'vertical',
            right: legendRight,
            top: legendTop,
            icon: 'circle',
            itemGap: itemGap,
            textStyle: {
              color: '#D6E7F9',
              fontSize: 30,
              rich: {
                percent: {
                  color: '#ffd79b',
                  fontSize: 32,
                },
              },
            },
            // formatter: function (name) {
            //   for(let i=0;i<dataNew.length;i++){
            //     if(dataNew[i].name==name){
            //       return name + ' ' + '    ' +'{percent|'+dataNew[i].text+'}'+' '
            //     }
            //   }
            // },
            formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += Number(data[i].value)
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              var p = (tarValue / total) * 100
              if (dom == 'pieCharts2') {
                return name + ' ' + '{percent|' + p.toFixed(2) + '%}'
              }
              return name + ' ' + '{percent|' + tarValue + dataNuit + '}' + ' '
            },
          },
          graphic: [
            {
              z: 4,
              type: 'image',
              id: 'logo',
              left: '8.5%',
              // top: '2%',
              top: graphicTop,
              z: -10,
              bounding: 'raw',
              rotation: 0, //旋转
              origin: [50, 40], //中心点
              scale: [0.7, 0.7], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            },
          ],
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['23%', '48%'],
              itemStyle: {
                normal: {
                  borderColor: '#132d57',
                  borderWidth: 10,
                },
              },
              // tooltip: {
              //   trigger: "item",
              //   formatter: function (params) {
              //     return (
              //       params.marker+
              //       params.name +
              //       ":" +params.value
              //     );
              //   },
              // },
              label: {
                show: false,
              },
              data: dataNew,
            },
          ],
        }
        myCharts2.setOption(option)
        myCharts2.getZr().on('mousemove', (param) => {
          myCharts2.getZr().setCursorStyle('default')
        })
      },
      pieChartWomanChart(dom, data, womanData, manData) {
        const myCharts2 = echarts.init(document.getElementById(dom))
        // let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
        var symbols = [
          'path://M18.2629891,11.7131596 L6.8091608,11.7131596 C1.6685112,11.7131596 0,13.032145 0,18.6237673 L0,34.9928467 C0,38.1719847 4.28388932,38.1719847 4.28388932,34.9928467 L4.65591984,20.0216948 L5.74941883,20.0216948 L5.74941883,61.000787 C5.74941883,65.2508314 11.5891201,65.1268798 11.5891201,61.000787 L11.9611506,37.2137775 L13.1110872,37.2137775 L13.4831177,61.000787 C13.4831177,65.1268798 19.3114787,65.2508314 19.3114787,61.000787 L19.3114787,20.0216948 L20.4162301,20.0216948 L20.7882606,34.9928467 C20.7882606,38.1719847 25.0721499,38.1719847 25.0721499,34.9928467 L25.0721499,18.6237673 C25.0721499,13.032145 23.4038145,11.7131596 18.2629891,11.7131596 M12.5361629,1.11022302e-13 C15.4784742,1.11022302e-13 17.8684539,2.38997966 17.8684539,5.33237894 C17.8684539,8.27469031 15.4784742,10.66467 12.5361629,10.66467 C9.59376358,10.66467 7.20378392,8.27469031 7.20378392,5.33237894 C7.20378392,2.38997966 9.59376358,1.11022302e-13 12.5361629,1.11022302e-13',
          'path://M28.9624207,31.5315864 L24.4142575,16.4793596 C23.5227152,13.8063773 20.8817445,11.7111088 17.0107398,11.7111088 L12.112691,11.7111088 C8.24168636,11.7111088 5.60080331,13.8064652 4.70917331,16.4793596 L0.149791395,31.5315864 C-0.786976655,34.7595013 2.9373074,35.9147532 3.9192135,32.890727 L8.72689855,19.1296485 L9.2799493,19.1296485 C9.2799493,19.1296485 2.95992025,43.7750224 2.70031069,44.6924335 C2.56498417,45.1567684 2.74553639,45.4852068 3.24205501,45.4852068 L8.704461,45.4852068 L8.704461,61.6700801 C8.704461,64.9659872 13.625035,64.9659872 13.625035,61.6700801 L13.625035,45.360657 L15.5097899,45.360657 L15.4984835,61.6700801 C15.4984835,64.9659872 20.4191451,64.9659872 20.4191451,61.6700801 L20.4191451,45.4852068 L25.8814635,45.4852068 C26.3667633,45.4852068 26.5586219,45.1567684 26.4345142,44.6924335 C26.1636859,43.7750224 19.8436568,19.1296485 19.8436568,19.1296485 L20.3966199,19.1296485 L25.2043926,32.890727 C26.1862111,35.9147532 29.9105828,34.7595013 28.9625083,31.5315864 L28.9624207,31.5315864 Z M14.5617154,0 C17.4960397,0 19.8773132,2.3898427 19.8773132,5.33453001 C19.8773132,8.27930527 17.4960397,10.66906 14.5617154,10.66906 C11.6274788,10.66906 9.24611767,8.27930527 9.24611767,5.33453001 C9.24611767,2.3898427 11.6274788,0 14.5617154,0 L14.5617154,0 Z',
          'path://M512 292.205897c80.855572 0 146.358821-65.503248 146.358821-146.358821C658.358821 65.503248 592.855572 0 512 0 431.144428 0 365.641179 65.503248 365.641179 146.358821 365.641179 227.214393 431.144428 292.205897 512 292.205897zM512 731.282359c-80.855572 0-146.358821 65.503248-146.358821 146.358821 0 80.855572 65.503248 146.358821 146.358821 146.358821 80.855572 0 146.358821-65.503248 146.358821-146.358821C658.358821 796.273863 592.855572 731.282359 512 731.282359z',
        ]
        var bodyMax = 29069 //指定图形界限的值
        var labelSetting = {
          normal: {
            show: true,
            position: 'bottom',
            offset: [0, 30],
            formatter: function (param) {
              return ((param.value / bodyMax) * 100).toFixed(0) + '%'
            },
            textStyle: {
              fontSize: 20,
              color: '#fff',
              fontFamily: 'Arial',
            },
          },
        }
        const option = {
          tooltip: {
            trigger: 'item',
            // formatter: '{b}:{d}%',
            formatter: function(params) {
              if(params.seriesType==="pie"){
                return params.name+':'+params.percent+"%"
              }
            },
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          color: ['#69cce6', '#ff8282', '#fed55f', '#269846'],
          grid: {
            left: '40%',
            right: '40%',
            top: '30%',
            bottom: '45%',
            containLabel: true,
          },
          xAxis: {
            position: 'top',
            data: ['', ''],
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              textStyle: {
                color: '#666',
                fontSize: 30,
              },
            },
          },
          yAxis: {
            max: bodyMax,
            splitLine: {
              show: false,
            },
            axisTick: {
              // 刻度线
              show: false,
            },
            axisLine: {
              // 轴线
              show: false,
            },
            axisLabel: {
              // 轴坐标文字
              show: false,
            },
          },
          series: [
            {
              name: '男性',
              type: 'pictorialBar',
              //   symbolClip: true,
              symbolBoundingData: bodyMax,
              label: labelSetting,

              data: [
                {
                  value: manData,
                  symbol: symbols[0],
                  itemStyle: {
                    normal: {
                      color: 'rgba(105,204,230)', //单独控制颜色
                    },
                  },
                },
                {},
              ],
              // markLine: markLineSetting,
              z: 10,
            },
            {
              name: '女性',
              type: 'pictorialBar',
              //   symbolClip: true,
              symbolBoundingData: bodyMax,
              label: labelSetting,
              data: [
                {
                  textStyle: {
                    fontSize: '20',
                  },
                },
                {
                  value: womanData,
                  symbol: symbols[1],
                  itemStyle: {
                    normal: {
                      color: '#fff', //单独控制颜色
                      fontSize: '20',
                    },
                  },
                },
              ],
              // markLine: markLineSetting,
              z: 10,
            },
            {
              // 设置背景底色，不同的情况用这个
              name: 'full',
              type: 'pictorialBar', //异型柱状图 图片、SVG PathData
              symbolBoundingData: bodyMax,
              animationDuration: 0,
              itemStyle: {
                normal: {
                  color: '#ccc', //设置全部颜色，统一设置
                },
              },
              z: 0,
              data: [
                {
                  itemStyle: {
                    normal: {
                      color: 'rgba(105,204,230,0.40)', //单独控制颜色
                    },
                  },
                  value: 100,
                  symbol: symbols[0],
                },
                {
                  itemStyle: {
                    normal: {
                      color: 'rgba(255,130,130,0.40)', //单独控制颜色
                    },
                  },
                  value: 100,
                  symbol: symbols[1],
                },
              ],
            },
            {
              name: '男女比例',
              type: 'pie',
              radius: ['65%', '85%'],
              avoidLabelOverlap: false,
              //   label: {
              //     normal: {
              //       show: false,
              //       position: 'center',
              //     },
              //     emphasis: {
              //       show: false,
              //       textStyle: {
              //         fontSize: '30',
              //         fontWeight: 'bold',
              //       },
              //     },
              //   },
              label: {
                formatter: '{b}\n{c}人', // 显示百分比，
                textStyle: {
                  fontSize: '26',
                  fontWeight: 'bold',
                  color: '#fff',
                },
              },
              labelLine: {
                normal: {
                  show: true,
                  legend: 60, //第一条折现
                  legend2: 55, //第二条折现
                  lineStyle: {
                    color: '#69cce6', //折现颜色
                  },
                },
              },
              data: data,
            },
          ],
        }
        myCharts2.setOption(option)
        myCharts2.getZr().on('mousemove', (param) => {
          myCharts2.getZr().setCursorStyle('default')
        })
      },
    },
  })
</script>
