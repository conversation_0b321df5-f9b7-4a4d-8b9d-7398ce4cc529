var vm = new Vue({
  el: '#app',
  data: {
    jhstszb: [],
    // 区县
    showInfo: true,
    showSelct: false,
    optionsData: ['婺城区', '金义新区', '兰溪市', '东阳市', '义乌市', '永康市', '浦江县', '武义县', '磐安县', '开发区'],
    optionValue: '县(市、区)城市体征',
    // 一级标题
    showHead: true,
    headId: -1, //用来判断是否是同一个点击事件
    headData: [],
    //二级标题弹窗
    showTwoLevel: false,
    twoLevelNum: -1, //判断是否是同一个对象
    // allTwoLevelData代表所有的二级菜单
    // twoLevelData代表需要显示的二级的菜单
    allTwoLevelData: [],
    twoLevelData: [],
    // 弹窗数据
    altData0: {
      thTitle: [],
      trData: [],
      textTitle: '',
      textData: [],
    },
    altData1: {
      thTitle: [],
      trData: [],
      textTitle: '',
      textData: [],
    },
    altData2: {
      thTitle: [],
      trData: [],
      textTitle: '',
      textData: [],
    },
    // 拥堵的区县
    showCity: false,
    cityLi: true,
    cityList: [
      { name: '婺城区', falg: true },
      { name: '金东区', falg: true },
      { name: '武义县', falg: false },
      { name: '浦江县', falg: false },
      { name: '磐安县', falg: false },
      { name: '兰溪市', falg: false },
      { name: '义乌市', falg: false },
      { name: '东阳市', falg: false },
      { name: '永康市', falg: false },
    ],
    // 拥堵的数据
    altTitle: '实时道路拥堵指数',
    showAlt: false,
    showAlt1: false,
    showAlt2: false,
    videoMapId: [],
    // 拥堵路段数据
    hdldData: [
      {
        abnormal: '异常',
        congestAffectArea: '金东区',
        congestSourceArea: '金东区',
        congestSourceDesc: '金华东枢纽附近',
        congestSourceLoc: '119.723830,29.163955',
        congestTailDesc: '金华东枢纽附近',
        description: '金华东枢纽附近',
        direction: '北向南',
        distance: '0.5025 km',
        durationMin: '1 分钟',
        endTime: '1666788120',
        eventId: '333-金华东枢纽-202210262042-***********',
        eventSource: '异常拥堵',
        extraEventStatus: '拥堵',
        idx: '12.23',
        insert_time: '2022-10-26 20:45:00',
        location: '119.723830,29.163955',
        roadName: '金华东枢纽',
        roadType: '主干',
        semantic: '金华东枢纽附近',
        speed: '3.37 km/h',
        startTime: '1666788120',
        linkStates: {
          1: '119.659316,29.138878,119.659376,29.138561,119.659395,29.138410;119.659395,29.138410,119.659405,29.137908;119.659106,29.138630,119.659395,29.138410;119.658791,29.138826,119.658866,29.138778,119.659022,29.138691,119.659106,29.138630',
          3: '119.658791,29.138826,119.659316,29.138878;119.657572,29.138696,119.658791,29.138826;119.652203,29.137327,119.653503,29.137811,119.654122,29.138031,119.654696,29.138206,119.655263,29.138352,119.655850,29.138471,119.656402,29.138559,119.657226,29.138660,119.657572,29.138696',
        },
      },
    ],
    // 其他判断用的字段
    sksyIndex: null,
    // 拥堵
    ydIndex: 0,
    // 拥堵里程
    ydlcIndex: 0,
    // 拥堵速度
    ydsdIndex: 0,
    // 物联感知的预警存储数据和对应的对象
    // 根据id获取对应物联感知的id和data中的变量
    warningStr: [
      {
        name: '饮用水预警数量',
        id: '15',
        yjId: '8aada4a47be36b72017be37a466b0006',
        variable: 'yysArr',
        key: ['PH值', '浊度', '余氯'],
        value: ['ph', 'zd', 'yl'],
      },
      {
        name: '厨房预警',
        id: '14',
        yjId: '8aada4a482c962bf018316a63b126834',
        variable: 'cfyjArr',
        key: ['厨房预警'],
        value: ['eventcontent'],
      },
      {
        name: '扬尘预警',
        id: '17',
        yjId: '8aada4a482c962bf018315ed7a4a52ff',
        variable: 'ycyjArr',
        key: ['扬尘预警'],
        value: ['eventcontent'],
      },
      {
        name: '山洪预警',
        id: '18',
        yjId: '8aada4a482c962bf0183405f1fdb0d45',
        variable: 'shyjArr',
        key: ['山洪预警', '事件等级'],
        value: ['content', 'level'],
      },
      {
        name: '地质灾害预警',
        id: '16',
        yjId: '8aada4a47f4d661c017fb5a31eec0017',
        variable: 'dzyjArr',
        key: ['地质灾害预警'],
        value: [],
      },
      // {
      //   name: '水库水位预警',
      //   id: '19',
      //   yjId: '8aada4a47d123213017d4b114e6c00a0',
      //   variable: 'skyjArr',
      //   key: ['水库水位预警'],
      //   value: [],
      // },
      {
        name: '水库雨量预警',
        id: '20',
        yjId: '8aada4a47cc5876a017cc59215cd0005',
        variable: 'ylyjArr',
        key: ['水库雨量预警'],
        value: [],
      },
      {
        name: '河道预警',
        id: '21',
        yjId: '8aada4a48234856201828fbdd6041c30',
        variable: 'hdyjArr',
        key: ['ph值', '余氯'],
        value: ['ph', 'chlorine'],
      },
      {
        name: '烟感预警',
        id: '22',
        yjId: '8aada4a482c962bf0183159646834a27',
        variable: 'ygyjArr',
        key: ['烟感值', '湿度'],
        value: ['smokevalue', 'temperature'],
      },
    ],
    // 注意，水库雨量不确定是那个id
    // 预警的变量
    yysArr: [],
    cfyjArr: [],
    ycyjArr: [],
    shyjArr: [],
    dzyjArr: [],
    skyjArr: [],
    ylyjArr: [],
    hdyjArr: [],
    ygyjArr: [],
  },
  computed: {
    headDataList() {
      let newArr = []
      for (let i = 0; i < this.headData.length; i += 6) {
        newArr.push(this.headData.slice(i, i + 6))
      }
      return newArr
    },
  },
  updated() {},
  created() {},
  mounted() {
    this.altData0.trData = this.hdldData
    this.initFun()
  },
  methods: {
    async initFun() {
      let that = this
      let aa = "'" + '婺城区' + "'" + ',' + "'" + '金东区' + "'"
      // 获取金华特色的数据
      $get('/csdn/cstz2-middle/cstzMiddleTe.json').then((res) => {
        that.jhstszb = res
      })
      // 获取一级标题
      // "isClick": 是否可以点击
      // "redNum": 预警的数量
      // "id": 自身的id，用于查找对应的二级标题
      // "pid": 0 代表自身的等级为一级标题
      that.headData = await $get('/csdn/cstz2-middle/cstzMiddleHead.json')
      // 获取所有二级标题
      // actFalg代表应该显示的颜色0是正常，1是黄色，2是红色
      // flag代表箭头图标向上还是向下，1代表上，-1代表不显示箭头
      // point代表可以点击
      // isUrl代表是有接口调用
      // isCode代表有传参
      // url是接口
      // code是传参
      that.allTwoLevelData = await $get('/csdn/cstz2-middle/cstzMiddleTwoLevel.json')
      $api('/cstz_baiduydd', { addressName: aa }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          let time = new Date(res[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res[i].idx = Number(res[i].idx)
        }
        this.hdldData = res
      })
      // 真实数据
      // that.getOtherData(),
      // that.getYjDataFun()
      // Promise.all([that.getYdData(), that.getSkswData()]).then(() => {
      //   that.showIconFun()
      // })
      // 模拟数据
      Promise.all([that.getYdData()]).then(() => {
        that.showIconFun()
      })
    },
    getYjDataFun() {
      let that = this
      that.warningStr.forEach((ele, i) => {
        axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/api/warning/list',
          data: {
            type_id: ele.yjId,
            page_size: 1000,
          },
        }).then(function (allRes) {
          let allDataArr = allRes.data.list.filter((item) => {
            if (item.warning_name == '离线报警' || item.warning_status == 'CLOSED' || item.warning_status == 'FINISH') {
              return
            } else {
              return item
            }
          })
          let pointData = allDataArr.map((item) => {
            return new Promise((resolve, reject) => {
              axios({
                method: 'post',
                url: baseURL.url + '/dtdd/iot/aep/v1/api/device/list',
                data: {
                  did_array: [item.did],
                },
              }).then((res) => {
                let data = res.data.list[0]
                // if (data.device_state != 'ONLINE') return
                let arr = []
                if (data.device_state != 'ONLINE') {
                  arr.push({})
                } else {
                  let dataStr = data && data.gather_value_list && data.gather_value_list[0]
                  let str = {
                    name: item.warning_name,
                    time: item.update_time,
                    did: item.did,
                    key: [],
                    value: [],
                    latitude: data.latitude,
                    longitude: data.longitude,
                    device_state: data.device_state,
                  }
                  for (let i = 0; i < ele.key.length; i++) {
                    str.key[i] = ele.key[i]
                    let value = ele.value[i]
                    str.value[i] = dataStr[value] || '--'
                  }
                  arr.push(str)
                }
                resolve(arr)
              })
            })
          })
          Promise.all(pointData).then((o) => {
            let arr = o.filter((item) => {
              return item[0].device_state == 'ONLINE'
            })
            that[ele.variable] = arr
            let index = that.allTwoLevelData.findIndex((el) => {
              return el.id == ele.id
            })
            that.allTwoLevelData[index].value = arr.length
            if (that.allTwoLevelData[index].value > 0) {
              that.allTwoLevelData[index].actFalg = 2
            } else {
              that.allTwoLevelData[index].actFalg = 0
            }
            if (i == that.warningStr.length - 1) {
              that.showIconFun()
            }
          })
        })
      })
    },
    handleImg() {
      let iframe4 = {
        type: 'openIframe',
        name: 'cstzTree',
        src: 'static/citybrain/csdn/cstz-tree.html',
        width: '7680px',
        height: '2160px',
        left: '0',
        top: '0',
        zIndex: '999',
      }
      // top.commonObj.createModalOverlay()
      window.parent.postMessage(JSON.stringify(iframe4), '*')
      top.emiter.emit('rightIframeHide')
    },
    getOtherData() {
      let that = this
      return new Promise((resolve) => {
        let indexArr = []
        that.allTwoLevelData.forEach((item, index) => {
          if (item.name == '实时道路拥堵指数') {
            that.ydIndex = index
          } else if (item.name == '实时道路严重拥堵里程') {
            that.ydlcIndex = index
          } else if (item.name == '实时道路平均速度') {
            that.ydsdIndex = index
          } else if (item.name == '水库水位预警') {
            that.sksyIndex = index
          } else if (item.isUrl && item.code) {
            indexArr.push(index)
          }
        })
        for (let i = 0; i < indexArr.length; i++) {
          let index = indexArr[i]
          let code = that.allTwoLevelData[index].code
          let url = '/csdn/cstz2-middle/' + that.allTwoLevelData[index].url
          $get(url, { code: code }).then((res) => {
            that.allTwoLevelData[index].value = res.length
            if (that.allTwoLevelData[index].value > 0) {
              that.allTwoLevelData[index].actFalg = 2
            } else {
              that.allTwoLevelData[index].actFalg = 0
            }
          })
        }
        resolve({})
      })
    },
    // 获取其他数据
    getYdData() {
      let that = this
      return new Promise((resolve) => {
        that.allTwoLevelData.forEach((item, index) => {
          if (item.name == '实时道路拥堵指数') {
            that.ydIndex = index
          } else if (item.name == '实时道路严重拥堵里程') {
            that.ydlcIndex = index
          } else if (item.name == '实时道路平均速度') {
            that.ydsdIndex = index
          } else if (item.name == '水库水位预警') {
            that.sksyIndex = index
          }
        })
        // 拥堵路段
        $api('/baidu_ydzs').then((res) => {
          let num = Number(res[0].yongduIndex)
          that.allTwoLevelData[that.ydIndex].value = num || 0 //实时道路拥堵指数【2=>非常拥堵,1=>平缓拥堵,0=>正常流畅】
          if (num >= 1.8) {
            that.allTwoLevelData[that.ydIndex].actFalg = 2
          } else if (num >= 1.5) {
            that.allTwoLevelData[that.ydIndex].actFalg = 1
          } else {
            that.allTwoLevelData[that.ydIndex].actFalg = 0
          }
          that.allTwoLevelData[that.ydlcIndex].value = res[0].yongduLength4 || '-' //实时道路严重拥堵里程
          that.allTwoLevelData[that.ydsdIndex].value = (+res[0].roadNetSpeed).toFixed(2) || '-' //实时道路平均速度
          resolve({})
        })
      })
    },
    // 获取其他数据
    getSkswData() {
      let that = this
      return new Promise((resolve) => {
        // 水库水位预警
        let skIndex = that.sksyIndex
        $api('/cjjswsj001').then((res) => {
          if (res != '') {
            that.allTwoLevelData[skIndex].value = res.length
            if (that.allTwoLevelData[skIndex].value > 0) {
              that.allTwoLevelData[skIndex].actFalg = 2
            } else {
              that.allTwoLevelData[skIndex].actFalg = 0
            }
          }
          resolve({})
        })
      })
    },

    // 计算一级标题的预警的数量
    showIconFun() {
      for (let i = 0; i < this.headData.length; i++) {
        this.headData[i].redNum = this.showIconNum(this.allTwoLevelData, this.headData[i].id)
      }
    },
    // 显示一级标题预警数量
    showIconNum(data, id) {
      let num = 0
      let arr = data.filter((item) => item.pid == id)
      arr.forEach((ele) => {
        if (ele.actFalg == 2) {
          ++num
        }
      })
      return num
    },
    // 一级标题点击事件
    headClick(item, index) {
      let that = this
      if (!item.isClick) return
      this.rmVideoFun()
      this.closeIframe()
      this.rmAllLayer()
      this.showInfo = true
      this.showAlt = this.showCity = this.showAlt1 = this.showAlt2 = false
      this.twoLevelData = this.allTwoLevelData.filter((ele) => ele.pid == item.id)
      this.headId == item.id ? (this.showTwoLevel = !this.showTwoLevel) : (this.showTwoLevel = true)
      this.headId = item.id
      // 清除地图和弹窗
    },
    // 二级标题点击事件
    twoLevelFun(item, index) {
      let isClick = item.point == true ? true : item.point == 'true' ? true : false
      let variable = this.warningStr.some((ele) => ele.id == item.id)
      if (!isClick) return
      if (item.name == '实时道路拥堵指数') {
        this.showAlt1 = this.showAlt2 = false
        this.showInfo = false
        this.altData0.trData = this.hdldData
        index == this.twoLevelNum
          ? ((this.showAlt = !this.showAlt), (this.showCity = !this.showCity))
          : ((this.showAlt = true), (this.showCity = true))
        if (this.showAlt) {
          this.showInfo = false
          this.watchCity() //调用表格渲染
          // top.mapUtil.loadTileLayer({
          //   layerid: 'dlyd_road',
          //   url: 'https://csdn.dsjj.jinhua.gov.cn:8101/egis-map-engine/services/other_jh_road_green/achievement/achievement_grid_tile_14/hexadecimal/{z}/{x}/{y}.png?token=997c588f4df1483f8e21c2c354b9e3f0',
          // })

          window.parent.mapUtil.loadTrafficLayer({
            layerid: 'dlyd_road',
          })
        } else {
          this.showInfo = true
          this.rmAllLayer()
          this.rmVideoFun()
        }
      } else if (item.name == '水库水位预警') {
        this.altData1.textTitle = item.name
        this.showAlt = this.showAlt2 = false
        index == this.twoLevelNum ? (this.showAlt1 = !this.showAlt1) : (this.showAlt1 = true)
        // 演示要用暂时注释，这是真实数据
        // this.showAlt1 ? this.swskFun() : this.rmLayer('水位水库')
      } else if (variable) {
        // 杨尘预警的在data目录中的cstzMiddleTwoLevel中的value和actFalg都改为0,warningStr里面有的在cstzMiddleTwoLevel中point值为true
        this.showAlt = this.showAlt1 = false
        this.altData2.textTitle = item.name
        index == this.twoLevelNum ? (this.showAlt2 = !this.showAlt2) : (this.showAlt2 = true)
        this.showAlt2 ? this.getOtherTwoLevelFun(item) : ''
      }
      this.twoLevelNum = index
    },
    // 显示其他二级标题数据
    getOtherTwoLevelFun(item) {
      let that = this
      // 真实数据
      // let arr = this.warningStr.filter((ele) => ele.id == item.id)
      // let variable = arr[0].variable
      // that.altData2.trData = this[variable]
      // 模拟数据
      let url = `/csdn/cstz2-middle/${item.url}`
      $get(url, { code: item.code }).then((res) => {
        let arr = res.map((ele) => {
          let str = []
          str[0] = ele
          return str
        })
        that.altData2.trData = arr
      })
    },

    // 拥堵路段的相关事件

    // 关闭道路拥堵弹窗
    closeRoadFun() {
      this.showAlt = this.showCity = this.showAlt1 = this.showAlt2 = false
      this.rmAllLayer()
      this.rmVideoFun()
    },
    /*
     * 绘制拥挤地段
     */
    // 拥堵路段点击事件
    roadMapFun(obj) {
      let that = this
      let rmIdArr = ['hxRoad', 'ydRoad', 'yzydRoad']
      for (let i = 0; i < rmIdArr.length; i++) {
        this.rmLayer(rmIdArr[i])
      }
      let arrLngLats = obj.linkStates
      // 畅通 缓行 拥堵 严重拥堵
      // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
      let hxData = []
      let ydData = []
      let yzydData = []
      let point = obj.location.split(',')
      let pointArr = that.transTo4490(point)
      this.flytoAdd(pointArr)
      this.videoMapId = []
      for (let key of Object.keys(arrLngLats)) {
        let line = arrLngLats[key]
        let arrData = line.split(';')
        let lineArr = []
        let lineStr = ''
        for (let i = 0; i < arrData.length; i++) {
          // 排序
          // let str=that.roadSort(arrData[i])
          // 不排序
          let str = arrData[i]
          let arr = str.split(/[,;]/)
          let coords = that.transTo4490(arr)
          let coordsStr = coords.join(',')
          lineArr.push(coordsStr)
          obj.idx >= 1.5 && obj.idx < 2
            ? hxData.push(coords)
            : obj.idx >= 2 && obj.idx < 4
            ? ydData.push(coords)
            : obj.idx >= 4
            ? yzydData.push(coords)
            : ''
        }
        lineStr = lineArr.join(';')
        top.mapUtil.loadPolylineLayer({
          layerid: 'hxRoad',
          lines: hxData,
          style: {
            width: 10, //线宽
            color: [252, 194, 95, 1], //rgba
          },
        })
        top.mapUtil.loadPolylineLayer({
          layerid: 'ydRoad',
          lines: ydData,
          style: {
            width: 10, //线宽
            color: [250, 108, 103, 1], //rgba
          },
        })
        top.mapUtil.loadPolylineLayer({
          layerid: 'yzydRoad',
          lines: yzydData,
          style: {
            width: 10, //线宽
            color: [180, 18, 14, 1], //rgba
          },
        })
        this.rmLayer('拥挤地段' + key)
        that.videoMapId.push('拥挤地段' + key)
        top.mapUtil.loadRoadVideo({
          layerid: '拥挤地段' + key,
          videoType: '拥挤地段' + key,
          distance: 100,
          lineStr: lineStr,
          onclick: this.onclick,
        })
      }
    },
    // 获取拥堵路段
    watchCity() {
      var str = ''
      let that = this
      for (let i = 0; i < this.cityList.length; i++) {
        if (this.cityList[i].falg == true) {
          str += "'" + this.cityList[i].name + "'" + ','
        }
      }
      if (str.length > 0) {
        str = str.substr(0, str.length - 1)
      }
      this.rmLayer('拥堵1')

      $api('/cstz_baiduydd', { addressName: str }).then((res) => {
        for (let i = 0; i < res.length; i++) {
          let time = new Date(res[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res[i].idx = Number(res[i].idx)
        }
        that.altData0.trData = res
        var roadPointData = []
        res.map((ele) => {
          let roadName = `${ele.description}`
          let address = ele.congestSourceDesc ? ele.congestSourceDesc : '--'
          let arr = ele.location.split(',')
          let pointArr = that.transTo4490(arr)
          let point = pointArr[0] + ',' + pointArr[1]
          let str = {
            data: {
              title: roadName,
              key: ['地址'],
              value: [address],
            },
            point: point,
            lng: pointArr[0],
            lat: pointArr[1],
          }
          roadPointData.push(str)
        })
        let popcfg = {
          offset: [50, -100],
        }
        top.mapUtil.loadPointLayer({
          data: roadPointData,
          layerid: '拥堵1',
          iconcfg: { image: '拥堵', iconSize: 0.5 },
          onclick: this.onclick,
          popcfg: popcfg,
        })
      })
    },
    // 处理路况的经纬度
    transTo4490(arr) {
      const length = arr.length / 2
      const pointArr = []
      // let mapName = top.mapUtil.map
      for (let i = 1; i <= length; i++) {
        const index = i * 2
        let jwd1 = coordtransform.bd09togcj02(arr[index - 2], arr[index - 1])
        let jwd2 = coordtransform.gcj02towgs84(jwd1[0], jwd1[1])
        let str = [jwd2[0], jwd2[1]].concat(0)
        pointArr.push(str)
      }
      return pointArr.flat()
    },
    // 选择县区弹窗
    checkSpan2(index, item) {
      var a = 0
      for (let i = 0; i < this.cityList.length; i++) {
        if (this.cityList[i].falg == true) {
          a++
        }
      }
      a > 1 ? (this.cityList[index].falg = !this.cityList[index].falg) : (this.cityList[index].falg = true)

      this.watchCity()
    },
    // 水库水位预警
    swskFun() {
      let that = this
      $api('cjjswsj001').then((res) => {
        that.altData1.trData = res.map((item) => {
          // 只有这个告警等级 1:特别重大,2:重大,3:较大,4:一般
          let time = new Date(item.sj)
          let h = time.getHours(),
            m = time.getMinutes()
          item.time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          let num = +item.yjdj
          item.yjdj = num == 1 ? '特别重大' : num == 2 ? '重大预警' : num == 3 ? '较大预警' : num == 4 ? '一般预警' : ''
          return item
        })
      })
    },
    // 条装页面
    gotoWindow() {
      //csdnsj.dsjj.jinhua.gov.cn/dplus/view/1575442146887671809
      // 测试环境 http://vivid.saas.ictsoft.cn/dplus/view/1575442146887671809?note=rhre09qmub&hasPassword=0&fromOther=1
      // 正式环境 http://csdnsj.dsjj.jinhua.gov.cn/dplus/view/1575442146887671809?fromOther=1
      top.commonObj.openMenuFun('ywtg_cs_url')
    },
    // 水位水库打点
    skswPointFun(obj) {
      this.rmLayer('水位水库')
      let pointArr = []
      let point = obj.point.split(',')
      this.flytoAdd(point)
      let str = {
        // data: {
        //   title: obj.mc,
        //   key: ['预警时间', '预警水位', '预警等级'],
        //   value: [obj.sj, obj.sssw, obj.yjdj],
        // },
        data: obj,
        pointType: 'ctsz_yj',
        lng: point[0],
        lat: point[1],
        point: point[0] + ',' + point[1],
      }
      pointArr.push(str)
      let popcfg = {
        offset: [500, -1000],
      }
      top.mapUtil.loadPointLayer({
        data: pointArr,
        layerid: '水位水库',
        iconcfg: { image: '水位监测', iconSize: 0.5 },
        onclick: this.onclick,
        popcfg: popcfg,
      })
    },

    onclick(e) {
      if (e.pointType === 'ctsz_yj') {
        let obj = e.data
        let iframe1 = {
          type: 'openIframe',
          name: 'main_shape',
          src: baseURL.url + '/static/citybrain/csdn/commont/main_shape.html',
          width: '100%',
          height: '100%',
          left: '0',
          top: '0',
          zIndex: '997',
          argument: {
            data: obj,
            status: 'ctsz_yj',
          },
        }
        window.parent.postMessage(JSON.stringify(iframe1), '*')
      } else if (e.chn_code) {
        let item = {
          obj: {
            chn_name: e.chn_name,
            is_collection: true,
          },
          video_code: e.chn_code,
        }
        let iframe1 = {
          type: 'openIframe',
          name: 'video_main_code',
          src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
          width: '100%',
          height: '100%',
          left: '0',
          top: '0',
          zIndex: '1000',
          argument: item,
        }
        window.parent.postMessage(JSON.stringify(iframe1), '*')
      }
    },
    // 清除所有的地图操作
    rmAllLayer() {
      let idArr = ['hxRoad', 'ydRoad', 'yzydRoad', 'dlyd_road', '拥堵1', '水位水库']
      top.mapUtil.removeAllLayers(idArr)
    },
    // 一级标题轮播
    to_prev() {
      this.$refs.bottom.prev()
    },
    to_next() {
      this.$refs.bottom.next()
    },

    // 金华特殊指标点击事件
    topAltTitNumFun(item) {
      let codeObj = {
        中药材: {
          code: 'bmjr097',
        },
        国际商贸: {
          code: 'bmjr116',
        },
        影视产业: {
          code: 'bmjr129',
        },
        温泉茶叶: {
          code: 'bmjr170',
        },
      }
      let code = ''
      if (item.name == '绿色水电') {
        window.open(
          'http://10.24.160.25/shareScreen/eyJzY3JlZW5JZCI6NzA0fQ==?timeStamp=182f1958071',
          '婺城区',
          'directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=' +
            2160 +
            ', width=' +
            3840 +
            ', top=0, left=1920' +
            ''
        )
      } else if (item.name.indexOf('好兰溪') > -1) {
        bmjr.toOpen('bmjr127')
      } else if (codeObj[item.name]) {
        code = codeObj[item.name].code
        bmjr.toOpen(code)
      }
    },
    // 区县的点击事件
    getOptionData(item, index) {
      let that = this
      this.optionValue = item
      this.showSelct = false
      if (item == '东阳市') {
        bmjr.toOpen('bmjr169')
      } else if (item == '婺城区') {
        bmjr.toOpen('bmjr076')
      } else if (item == '义乌市') {
        bmjr.toOpen('bmjr095')
      } else if (item == '永康市') {
        bmjr.toOpen('bmjr111')
      } else if (item == '金义新区') {
        bmjr.toOpen('bmjr032')
      } else if (item == '武义县') {
        bmjr.toOpen('bmjr170')
      } else if (item == '浦江县') {
        bmjr.toOpen('bmjr218')
      } else if (item == '兰溪市') {
        bmjr.toOpen('bmjr217')
      }
    },
    // 关闭其他的ifram
    closeIframe() {
      // 关闭iframe弹窗
      var iframes = document.getElementById('mainPage').childNodes
      for (let i = 3; i < iframes.length; i++) {
        iframes[i].remove()
      }
    },
    // 清除地图的方法，点位
    // 清除3D文字
    // 飞行
    flytoAdd(obj) {
      top.mapUtil.flyTo({
        destination: obj,
        zoom: 18,
        pitch: 2, //倾斜角
      })
    },
    // 清除视频
    rmVideoFun() {
      //  清除所有视频
      for (let i = 0; i < this.videoMapId.length; i++) {
        let id = this.videoMapId[i]
        this.rmLayer(id)
      }
    },
    rmLayer(id) {
      try {
        top.mapUtil.removeLayer(id)
      } catch (error) {}
    },
  },
  beforeDestroy() {},
})
