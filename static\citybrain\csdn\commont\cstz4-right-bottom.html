<!--
 * @Author: <PERSON><PERSON><PERSON>0016 <EMAIL>
 * @Date: 2023-03-08 15:31:03
 * @LastEditors: Zhangyifan0016 <EMAIL>
 * @LastEditTime: 2023-08-05 17:57:57
 * @FilePath: \szjh\static\citybrain\csdn\commont\cstz4-right-bottom.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>城市体征</title>
    <link rel="shortcut icon" href="#" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <link rel="stylesheet" href="/static/css/xiaoguo.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/css/cstz4-right.css" />
  </head>

  <body>
    <div id="app">
      <div class="main">
        <div class="top top-bottom">
          <p class="s-c-blue-gradient pointer" @click="openIframe('公共服务')">公共服务</p>
        </div>
        <div class="bottom">
          <div style="width: 100%; height: 100%">
            <ul class="ul-list">
              <li v-for="(item,index) in list" :key="index" class="li-box">
                <p class="text-box" v-if="index<3">
                  <span class="name-css">{{item.name}}</span>
                  <span class="name-css yel-color">{{item.value}}{{item.unit}}</span>
                </p>
                <p class="text-bg" v-if="index<3">{{item.level1}}</p>
                <!-- 换一下顺序 -->
                <p class="text-bg" v-if="index>2">{{item.level1}}</p>
                <p class="text-box text-left" v-if="index>2">
                  <span class="name-css">{{item.name}}</span>
                  <span class="name-css yel-color">{{item.value}}{{item.unit}}</span>
                </p>
              </li>
            </ul>
          </div>
          <div class="center-img breath-light">
            <img src="/static/citybrain/csdn/img/cstz4-right/center-bg2.png" alt="" />
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script>
  var vm = new Vue({
    el: '#app',
    data() {
      return {
        list: [],
      }
    },
    mounted: function () {
      $api('/cstz_gy_ggfw').then((res) => {
        this.list = res
      })
    },
    methods: {
      openIframe(name) {
        let leftData = {
          type: 'openIframe',
          name: 'djtl-page',
          src: '/static/citybrain/djtl/pages/djtl-page.html',
          width: '7680px',
          height: '2005px',
          left: '0',
          top: '115px',
          zIndex: 100,
          argument: {
            name: name,
            type: '城市体征下钻',
          },
        }
        top.postMessage(JSON.stringify(leftData), '*')
      },
    },
  })
</script>
