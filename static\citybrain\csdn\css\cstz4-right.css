body,
p {
  padding: 0;
  margin: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.pointer {
  cursor: pointer;
}

.main {
  width: 1921px;
  height: 928px;
  background: url('/static/citybrain/csdn/img/cstz4/bg.png') no-repeat;
  overflow: hidden;
}

.main-1 {
  background: url('/static/citybrain/csdn/img/cstz4-right/bg.png') no-repeat 100% 100% !important;
  position: relative;
}

.main-1::before {
  content: '';
  width: 100%;
  height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('/static/citybrain/csdn/img/cstz4-page/light.png');
  background-repeat: no-repeat;
  background-position: center -40px;
}

/* 右边-上面的弹窗 */
.top {
  width: 100%;
  height: 100px;
  font-size: 54px;
  line-height: 115px;
  font-weight: bold;
  text-align: center;
  letter-spacing: 5px;
}

.bottom {
  width: 100%;
  height: 830px;
  padding: 50px;
  box-sizing: border-box;
  background: url('/static/citybrain/csdn/img/cstz4-right/num-bg.png') no-repeat;
  background-position: center 200px;
}

.box {
  width: 100%;
  height: 100%;
  background: url('/static/citybrain/csdn/img/cstz4-right/border-bg.png') no-repeat;
  background-position: center;
  padding: 150px 300px 100px 250px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.left,
.center,
.right {
  flex: 0.32;
  height: 100%;
}

.left {
  flex: 0.28;
  display: flex;
  flex-wrap: wrap;
}

.right {
  display: flex;
  flex-wrap: wrap;
}

.center {
  font-size: 120px;
  font-weight: 500;
  text-align: center;
  line-height: 420px;
  color: #a3fdfd;
  padding-right: 80px;
  box-sizing: border-box;
}

.left-box {
  width: 100%;
  height: 50%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.name-css {
  display: block;
  font-size: 32px;
  color: #fff;
}

.value-css {
  font-size: 36px;
  font-weight: bold;
}

.unit-css {
  font-size: 30px;
}

.center {
  background: url('/static/citybrain/csdn/img/cstz4-right/center-bg.png') no-repeat;
  background-position: center;
  position: relative;
  transform-style: preserve-3d;
}

.left-content>p {
  margin-bottom: 10px;
}

.left-content>p:last-child {
  margin-bottom: 0;
}

.icon-img {
  width: 97px;
  height: 97px;
}

.icon-img>img {
  width: 100%;
  height: 100%;
}

/* 右边-下面的弹窗 */
.ul-list {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}

.center-img {
  position: absolute;
  top: 250px;
  left: 680px;
  z-index: 0;
}

.text-bg {
  width: 270px;
  height: 167px;
  font-size: 40px;
  color: #fff;
  text-align: center;
  background: url('/static/citybrain/csdn/img/cstz4-right/text-bg.png') no-repeat 100% 100%;
  animation: jump1 2s infinite;
}

.text-bg:nth-of-type(2n-1) {
  animation: jump1 2s infinite;
}

.text-bg:nth-of-type(2n) {
  animation: jump2 2s infinite;
}

.li-box {
  flex: 0.5;
  height: 300px;
  margin-bottom: 20px;
  display: flex;
}

.text-box {
  height: 130px;
  text-align: right;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 10px;
  background-image: linear-gradient(to left, #0b325c81, #0b325c72, #0b325c1d, #0b325c08);
  margin-right: 30px;
}

.text-left {
  text-align: left;
  background-image: linear-gradient(to right, #0b325c81, #0b325c72, #0b325c1d, #0b325c08) !important;
  margin-right: 0;
  margin-left: 30px;
}

/* 定位 */
.li-box:first-child {
  align-self: flex-end;
  padding-right: 100px;
  box-sizing: border-box;
}

.li-box:nth-of-type(2) {
  position: relative;
  left: -50px;
  top: -20px;
}

.li-box:nth-of-type(4) {
  padding-left: 150px;
  box-sizing: border-box;
}

.li-box:nth-of-type(5) {
  padding-left: 220px;
  padding-top: 50px;
  box-sizing: border-box;
}

.li-box:nth-of-type(6) {
  padding-left: 100px;
  box-sizing: border-box;
}

@keyframes jump1 {
  0% {
    transform: translate(0px, 0px);
    /*开始位置*/
  }

  50% {
    transform: translate(0px, 10px);
    /* 可配置跳动方向 */
  }

  100% {
    transform: translate(0px, 0px);
    /*结束位置*/
  }
}

@keyframes jump2 {
  0% {
    transform: translate(0px, 0px);
    /*开始位置*/
  }

  50% {
    transform: translate(0px, -10px);
    /* 可配置跳动方向 */
  }

  100% {
    transform: translate(0px, 0px);
    /*结束位置*/
  }
}

.top-bottom {
  line-height: 95px !important;
}

.lineOut {
  background: url('/static/citybrain/csdn/img/cstz4-right/lineOut.png') no-repeat;
  position: absolute;
  width: 345px;
  height: 175px;
  top: 70px;
  left: 80px;
  will-change: transform;
  transform-origin: 50% 100%;
  animation: rotation 2.5s linear infinite;
}

.lineIn {
  background: url('/static/citybrain/csdn/img/cstz4-right/lineIn.png') no-repeat;
  position: absolute;
  width: 205px;
  height: 125px;
  top: 245px;
  left: 135px;
  will-change: transform;
  transform-origin: 50% 100%;
  animation: anim 0.8s ease;
}

@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes anim {
  0% {
    top: 225px;
    left: 195px;
    -webkit-transform: rotate(-35deg);
  }

  100% {
    -webkit-transform: rotate(0deg);
  }
}