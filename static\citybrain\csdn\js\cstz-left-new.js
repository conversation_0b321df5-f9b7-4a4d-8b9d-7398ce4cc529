import { setAct } from '../../../js/jslib/util.js'
;(function () {
  var requestType = 1 // 0: 本地 1: 真实地址

  var vm_left = new Vue({
    el: '#cstz_app',
    data: {
      time: '2022年6月27日-7月13日',
      inToMapArr: [],
      outToMapArr: [],
      leftIndex: -1,
      titleDate: '',
      populationTotal: '0000000',
      flowTotal: '0000000',
      mapFlag: false, // 是否是第三张地图
      inProvince: 0,
      outProvince: 0,
      jjzbTotal: ['99', '5'],
      top10Data: [
        {
          top: '1',
          a: '江苏.南京',
          b: 'linear-gradient(360deg, #df8f30, #faff78)',
          c: '60851',
          d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
        },
        {
          top: '2',
          a: '四川.成都',
          b: 'linear-gradient(360deg, #df8f30, #faff78)',
          c: '52238',
          d: 'linear-gradient(360deg, #df8f30, #faff78)',
        },
        {
          top: '3',
          a: '北京',
          b: 'linear-gradient(360deg, #df8f30, #faff78)',
          c: '36048',
          d: 'linear-gradient(360deg, #304ddf, #7882ff)',
        },
        {
          top: '4',
          a: '上海',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '28256',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
        {
          top: '5',
          a: '浙江.杭州',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '25688',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
        {
          top: '1',
          a: '浙江.温州',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '35688',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
        {
          top: '2',
          a: '重庆',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '25688',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
        {
          top: '3',
          a: '安徽',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '25688',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
        {
          top: '4',
          a: '毕节',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '25688',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
        {
          top: '5',
          a: '安顺',
          b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
          c: '25688',
          d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
        },
      ],
      flowObj: {
        business: {
          name1: '江北一百银泰商圈',
          value1: 2,
          name2: '万达广场',
          value2: 3,
          img1: '/static/citybrain/csdn/img/cstz2-middle/商圈小.png',
          img2: '/static/citybrain/csdn/img/cstz2-middle/商圈小.png',
          id: ['3307001A002', '3307001A003'],
        },
        scenic: {
          name1: '双龙洞景区',
          name2: '横店影视城',
          name3: '湖海塘公园',
          img1: '/static/citybrain/csdn/img/cstz2-middle/景区.png',
          img2: '/static/citybrain/csdn/img/cstz2-middle/景区.png',
          img3: '/static/citybrain/csdn/img/cstz2-middle/景区.png',
          value1: 4,
          value2: 1,
          value3: 16,
          id: ['3307001A004', '3307001A001', '3307001A019'],
        },
        industry: {
          name1: '新能源汽车小镇',
          value1: 6,
          name2: '朗朗文化信息产业园',
          value2: 17,
          name2: '健康生物产业园',
          value2: 17,
          img1: '/static/citybrain/csdn/img/cstz2-middle/小区.png',
          img2: '/static/citybrain/csdn/img/cstz2-middle/智慧工业.png',
          img3: '/static/citybrain/csdn/img/cstz2-middle/智慧工业.png',
          id: ['3307001A006', '3307001A034', '3307001A035'],
        },
        asian: {
          name1: '金华体育中心体育馆',
          name2: '浙江师范大学',
          value1: 8,
          value2: 9,
          img1: '/static/citybrain/csdn/img/cstz2-middle/体育馆.png',
          img2: '/static/citybrain/csdn/img/cstz2-middle/学校.png',
          id: ['3307001A008', '3307001A009'],
        },
        station: {
          name1: '金华站',
          name2: '金华南站',
          value1: 10,
          value2: 11,
          img1: '/static/citybrain/csdn/img/cstz2-middle/火车站1.png',
          img2: '/static/citybrain/csdn/img/cstz2-middle/火车站1.png',
          id: ['3307001A010', '3307001A011'],
        },
        epidemic: {
          // name1: '紫金玉澜小区19幢',
          // name2: '华鼎公馆小区1幢',
          // value1: 12,
          // value2: 13,
          // img1: '/static/citybrain/csdn/img/cstz2-middle/小区.png',
          // img2: '/static/citybrain/csdn/img/cstz2-middle/小区.png',
          // id: ['', '']
        },
        huddle: {
          // name1: '市政府南门',
          // name2: '市政府北门',
          // value1: 19,
          // value2: 20,
          // img1: '/static/citybrain/csdn/img/cstz2-middle/小区.png',
          // img2: '/static/citybrain/csdn/img/cstz2-middle/小区.png',
          // id: ['3307001A024', '3307001A025']
        },
      },
      firstFlowObj: [
        {
          code: 1,
          data: 0,
          name: '商圈人群',
        },
        {
          code: 2,
          data: 0,
          name: '景区人群',
        },
        {
          code: 3,
          data: 0,
          name: '工业园区人群',
        },
        {
          code: 4,
          data: 0,
          name: '亚运会场所',
        },
        {
          code: 5,
          data: 0,
          name: '车站人群',
        },
        // {
        //   code: 7,
        //   data: 0,
        //   name: "超长聚集人群"
        // },
      ],
      scenicNum: 0, // 景区人群
      asianNum: 0, // 亚运会场所
      stationNum: 0, // 车站人群
      businessAreaNum: 0, // 商圈人群
      industryNum: 0, // 工业园区人群,
      fyNum: 0,
      jjrqNum: 0,
      jjzbList: [
        {
          type: 'product',
          text: '生产指数',
          value: 105.6,
          text2: '全社会用电量',
          value2: 122.8,
          up: true,
        },
        {
          type: 'trade',
          text: '贸易指数',
          value: 100.7,
          text2: '银联消费指数',
          value2: 105.4,
          up: false,
        },
        {
          type: 'circulate',
          text: '流通指数',
          value: 105.1,
          text2: '快递业务量',
          value2: 105.4,
          up: true,
        },
        {
          type: 'project',
          text: '项目推进指数',
          value: 109.4,
          text2: '快递业务量',
          value2: 105.4,
          up: true,
        },
        {
          type: 'market',
          text: '市场活力指数',
          value: 73.3,
          text2: '新增市场主体',
          value2: 105.4,
          up: false,
        },
      ],
      panelList: [
        {
          text: '第三产业用电量',
          value: 1055556,
          unit: '万千瓦时',
        },
        {
          text: '新登记劳动年龄流动人口数',
          value: 1055556,
          unit: '万千瓦时',
        },
        {
          text: '汽车上牌数',
          value: 58886,
          unit: '个',
        },
        {
          text: '银联消费总数',
          value: 1055556,
          unit: '亿元',
        },
        {
          text: '工业用电',
          value: 1055556,
          unit: '万千瓦时',
        },
      ],
      // 流动人口
      businessArea: [
        {
          name: 'business',
          pointArr: [
            [
              119.64656, 29.10645, 119.648235, 29.10613, 119.64853, 29.107293,
              119.649669, 29.10711, 119.649948, 29.10723, 119.65051, 29.107088,
              119.650251, 29.106115, 119.649936, 29.106102, 119.649582,
              29.10466, 119.650567, 29.104498, 119.649882, 29.102764,
              119.647889, 29.103119, 119.648112, 29.104964, 119.646086,
              29.105286, 119.64656, 29.10645,
            ],
            [
              119.668513, 29.096051, 119.674155, 29.099799, 119.677227,
              29.095008, 119.670885, 29.091644, 119.668472, 29.096092,
              119.668513, 29.096051,
            ],
          ],
        },
        {
          name: 'scenic',
          pointArr: [
            [
              119.61889, 29.202458, 119.620484, 29.203879, 119.621738,
              29.204944, 119.627943, 29.205299, 119.630045, 29.202103,
              119.627604, 29.199439, 119.62774, 29.196124, 119.625061,
              29.195414, 119.62123, 29.195562, 119.619941, 29.199173,
              119.620518, 29.201511, 119.61889, 29.202458,
            ],
            [
              120.308038, 29.193908, 120.325836, 29.184343, 120.338346,
              29.176989, 120.352838, 29.175227, 120.358627, 29.162298,
              120.348228, 29.146753, 120.340916, 29.135091, 120.327114,
              29.121901, 120.304427, 29.102987, 120.286267, 29.096481,
              120.26283, 29.094137, 120.251758, 29.110019, 120.263517,
              29.118987, 120.274479, 29.135508, 120.284223, 29.164007,
              120.297422, 29.188278, 120.308038, 29.193908,
            ],
            [
              119.643138, 29.061629, 119.633471, 29.060557, 119.624536,
              29.058268, 119.624739, 29.049963, 119.628008, 29.047944,
              119.628375, 29.046388, 119.629648, 29.045123, 119.630334,
              29.043997, 119.632748, 29.041048, 119.633376, 29.040946,
              119.63498, 29.041202, 119.635724, 29.041401, 119.636988,
              29.048854, 119.638039, 29.050209, 119.639288, 29.050703,
              119.642336, 29.054261, 119.643338, 29.059252, 119.643133,
              29.061657, 119.643138, 29.061629,
            ],
          ],
        },
        {
          name: 'industry',
          pointArr: [
            [
              119.5927, 29.03735, 119.592827, 29.031081, 119.598409, 29.030987,
              119.598445, 29.025512, 119.604273, 29.025727, 119.60555,
              29.030699, 119.607097, 29.034752, 119.611008, 29.032381,
              119.612582, 29.03473, 119.613049, 29.037586, 119.598332,
              29.037255, 119.593928, 29.037088, 119.5927, 29.03735,
            ],
            [
              120.320049, 29.311895, 120.321037, 29.311992, 120.321833,
              29.312033, 120.322032, 29.31061, 120.322032, 29.310103,
              120.321355, 29.310012, 120.320312, 29.309936, 120.320169,
              29.311019, 120.320049, 29.311895,
            ],
          ],
        },
        {
          name: 'asian',
          pointArr: [
            [
              119.636886, 29.048174, 119.641486, 29.047595, 119.640549,
              29.041728, 119.635718, 29.040914, 119.636819, 29.048057,
              119.636886, 29.048174,
            ],
            [
              119.645155, 29.143244, 119.64806, 29.135924, 119.644385, 29.13536,
              119.632456, 29.131034, 119.628834, 29.130348, 119.63009,
              29.130461, 119.627622, 29.135655, 119.628092, 29.138769,
              119.629664, 29.139095, 119.630999, 29.139796, 119.636614,
              29.139885, 119.6372, 29.141287, 119.645195, 29.143253, 119.645155,
              29.143244,
            ],
          ],
        },
        {
          name: 'station',
          pointArr: [
            [
              119.628271, 29.112879, 119.629142, 29.111541, 119.633941,
              29.112032, 119.632002, 29.113845, 119.628624, 29.113072,
              119.628271, 29.112879,
            ],
            [
              119.729358, 29.120465, 119.731371, 29.120558, 119.738473,
              29.120587, 119.737733, 29.123931, 119.741594, 29.104349,
              119.735429, 29.103923, 119.732893, 29.114242, 119.73026,
              29.114124, 119.729341, 29.120188, 119.672743, 29.095773,
            ],
          ],
        },
        {
          name: 'epidemic',
          pointArr: [
            [
              119.572212, 29.0841, 119.572816, 29.084045, 119.573445, 29.084001,
              119.573672, 29.083803, 119.573596, 29.083166, 119.573508,
              29.082528, 119.57347, 29.082033, 119.573395, 29.081714,
              119.572942, 29.081659, 119.572753, 29.081395, 119.572715,
              29.081208, 119.573282, 29.080988, 119.573156, 29.080713,
              119.57225, 29.080977, 119.571583, 29.081153, 119.571142,
              29.081263, 119.571344, 29.081923, 119.571507, 29.082561,
              119.571822, 29.083199, 119.572036, 29.083814, 119.572212, 29.0841,
            ],
            [
              119.565865, 29.08579, 119.566176, 29.085743, 119.566482,
              29.085678, 119.566745, 29.085631, 119.56697, 29.085593,
              119.567227, 29.085556, 119.567453, 29.085499, 119.567689,
              29.085443, 119.567893, 29.085429, 119.568054, 29.085345,
              119.568016, 29.085176, 119.567898, 29.085003, 119.56785,
              29.084787, 119.567796, 29.084524, 119.567687, 29.084237,
              119.567424, 29.08427, 119.567076, 29.084321, 119.566797,
              29.084396, 119.566496, 29.084443, 119.566099, 29.084518,
              119.565708, 29.084579, 119.565477, 29.08465, 119.565509,
              29.084912, 119.565606, 29.085184, 119.565665, 29.085409,
              119.565745, 29.085596, 119.565815, 29.085742, 119.565865,
              29.08579,
            ],
          ],
        },
        {
          name: 'huddle',
          pointArr: [],
        },
      ],
      startTime: '',
      endTime: '',
      num0: 0,
      num1: 0,
      hotId1: null,
      hotId2: null,
      hotId3: null,
      idList: [],

      absoIndex: -1,
      exponentArr: [],

      allData: [],
      // 是否加载二环热力图
      isHot: true,
    },

    mounted() {
      this.getStressData()
      this.getPopulation()
      // this.creditAuth().then((res) => {
      //   this.getPopulation()

      //   // this.getStressData()
      // })

      this.getInOrOut()
      this.getCurrentDate()
      // this.setEcharts()
      this.initFun()
    },
    methods: {
      initFun() {
        let that = this
        $api('cstz_ssrks').then((res) => {
          //加载实时人口
          this.populationTotal = res[0].populationCount
        })
        $api('cstzLeft008').then((res) => {
          //加载重点区域
          let jhData = res.filter((item) => {
            return item.area_name == '金华市'
          })
          let start = new Date(jhData[0].start_date)
          let end = new Date(jhData[0].end_date)
          that.time =
            start.getFullYear() +
            '年' +
            (start.getMonth() + 1) +
            '月' +
            start.getDate() +
            '日' +
            '-' +
            end.getFullYear() +
            '年' +
            (end.getMonth() + 1) +
            '月' +
            end.getDate() +
            '日'
          that.jjzbList[0].value = jhData[0].index_value
          that.jjzbList[1].value = jhData[2].index_value
          that.jjzbList[2].value = jhData[1].index_value
          that.jjzbList[3].value = jhData[3].index_value
          that.jjzbList[4].value = jhData[4].index_value
        })
        $api('cstzLeft009').then((res) => {
          //加载重点区域
          let data = res.filter((item) => {
            return item.area_name == '金华市'
          })
        })
      },

      //实时人口切换地图
      async handoffMap() {
        this.isHot = true
        top.vm.showRealpersonMap = !top.vm.showRealpersonMap
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmhotPowerMap',
          })
        )
        top.vm.clearWall()
        top.document.getElementById('map').contentWindow.Work.change3D(9)

        var textData = await $get('personInTime')
        $api('cstz_ssrks_qx').then((res) => {
          //获取各区人口
          for (let i = 0; i < res.length; i++) {
            for (let y = 0; y < textData.length; y++) {
              if (res[i].addressName == textData[y].name) {
                textData[y].value[2] = res[i].populationCount
              }
            }
          }
          textData.map((item) => {
            //科学技术
            item.value[0] = setAct(item.value[0])
            item.value[1] = setAct(item.value[1])
            item.value[2] = setAct(item.value[2])
          })
          top.vm.openMap3D(textData)
        })
      },
      /*
       * 鉴权
       */
      creditAuth() {
        /*
         * 密钥
         */
        var appKey = 'zj_jh-API',
          masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

        var timestamp = Number(
          Math.round(new Date().getTime() / 1000).toString()
        )

        var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

        var o_str = appKey + time_md5 + masterSecret,
          sha256_str = CryptoJS.SHA256(o_str).toString()

        var sign = sha256_str + masterSecret

        const reqParams = {
          appKey: appKey,
          sign: sign,
          timestamp: timestamp,
          version: 'v1.0',
        }

        return axios({
          method: 'post',
          url: baseURL.url + '/typeq/api/auth/creditAuth',
          data: reqParams,
        }).then(function (res) {
          if (res.data.errno === 0) {
            window.accessToken = res.data.data.accessToken

            axios.defaults.headers.common['Access-Token'] =
              res.data.data.accessToken
          }
        })
      },

      // 获取实时人口数据,全市的人口热力图
      // getRealTimeFun() {
      //   axios({
      //     method: 'post',
      //     url: requestType
      //       ? 'https://csdn.dsjj.jinhua.gov.cn:8101/typeq/api/population//count/getHeatMapByCode'
      //       : 'http://localhost:8101/typeq/api/population//count/getHeatMapByCode',
      //     data: {
      //       "area_code": "330700",
      //       "ranges": []
      //     },
      //   }).then(function (res) {
      //     let hotMapData = []
      //     let i = 0;
      //     res.data.data.heatmap.map(item => {
      //       let point = []
      //       point[0] = item.lng
      //       point[1] = item.lat
      //       i++
      //       if (i % 6 == 0) {
      //         hotMapData.push(point)
      //       }
      //     })
      //     console.log("hotMapData--->", hotMapData);
      //     const mapData = {
      //       funcName: 'hotPowerMap',
      //       hotPowerMapData: hotMapData,
      //     }
      //     top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(mapData))
      //     const flyData = {
      //       funcName: 'flyto',
      //       flyData: {
      //         postion: {
      //           x: hotMapData[0][0],
      //           y: hotMapData[0][1],
      //           z: 3000,
      //         },
      //         rotation: {
      //           x: 90,
      //           y: 0,
      //           z: 0,
      //         },
      //       },
      //     }
      //     top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(flyData))
      //   })
      // },

      /*
       * 省内省外 地图事件
       */
      inPersonMap() {
        console.log('点击省内')
        console.log(top.vm)
        if (top.vm.activeName === 'second') {
          console.log(1111111111)
          top.vm.TabHandleClick()
          return
        }
        // let arr = this.inToMapArr.slice(0, 10)
        let arr = []
        let arrAll = this.inToMapArr.slice(0, 10)
        arrAll.forEach((item, i) => {
          let str = {
            name: item.name,
            coord: item.coord,
            num: item.num,
            pm: i + 1,
          }
          arr.push(str)
        })
        top.vm.qxMapData = arr
        top.vm.drawQxMap()
        this.isHot = true
        top.vm.showMap = true
        // top.vm.showQxMap = !top.vm.showQxMap
        // top.vm.showQxMap==true?top.vm.showMiddleNew=false:top.vm.showMiddleNew=true
        top.vm.openWind = ['showQxMap', 'showMiddleNew']
        top.vm.colseOtherWin()
        top.vm.closeWinZdqY()
      },
      outPersonMap() {
        // let arr = this.outToMapArr.slice(0, 10)

        let arr = []
        let arrAll = this.outToMapArr.slice(0, 10)
        arrAll.forEach((item, i) => {
          let str = {
            name: item.name,
            coord: item.coord,
            num: item.num,
            pm: i + 1,
          }
          arr.push(str)
        })
        top.vm.qgMapData = arr
        top.vm.drawQgMap()
        this.isHot = true
        top.vm.showQgMap = !top.vm.showQgMap
        top.vm.showQgMap == true
          ? (top.vm.showMiddleNew = false)
          : (top.vm.showMiddleNew = true)
        top.vm.openWind = ['showQgMap', 'showMiddleNew']
        top.vm.colseOtherWin()
        top.vm.closeWinZdqY()
        // 清除墙体和锥体
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'rmActiveWall',
        //   })
        // )
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'rmConePoint',
        //   })
        // )
        // this.removeTop5Map()
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'top5FlyLine',
        //     "data": arr
        //   })
        // )
      },
      removeTop5Map() {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmflyLine',
          })
        )
      },
      /*
       * 获取省内，省外数据  // 1:省内，0省外
       */
      getInOrOut() {
        $api('cstz_top5').then((res) => {
          var inTop10 = [],
            outTop10 = [],
            inAll = 0,
            outAll = 0

          for (let i = 0; i < res.length; i++) {
            var num = +res[i].addressCount
            var num1 = +res[i].addressLongitude
            var num2 = +res[i].addressLatitude
            var obj = {
              name: res[i].addressName,
              coord: [num1, num2],
              num: res[i].addressCount,
            }
            if (res[i].topsState == 1) {
              inTop10.push(res[i])
              inAll += num
              this.inToMapArr.push(obj)
            } else {
              outTop10.push(res[i])
              outAll += num
              this.outToMapArr.push(obj)
            }
          }
          inTop10 = inTop10.slice(0, 5)
          outTop10 = outTop10.slice(0, 5)

          let top10Data = []

          inTop10.forEach((item, i) => {
            if (i === 0) {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
                e: item.addressCount,
              })
            } else if (i === 1) {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #df8f30, #faff78)',
                e: item.addressCount,
              })
            } else if (i === 2) {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #304ddf, #7882ff)',
                e: item.addressCount,
              })
            } else {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                e: item.addressCount,
              })
            }
          })

          outTop10.forEach((item, i) => {
            if (i === 0) {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
                e: item.addressCount,
              })
            } else if (i === 1) {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #df8f30, #faff78)',
                e: item.addressCount,
              })
            } else if (i === 2) {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #304ddf, #7882ff)',
                e: item.addressCount,
              })
            } else {
              top10Data.push({
                top: i + 1,
                a: item.addressName,
                b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                c: parseInt(item.addressRate * 10000),
                d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                e: item.addressCount,
              })
            }
          })

          this.top10Data = top10Data

          this.inProvince = inAll
          this.outProvince = outAll
        })
      },

      /*
       * 获取流动人口列表
       */
      // getDashboard() {
      //   return axios({
      //     method: 'post',
      //     url: baseURL.url + '/typeq/api/population/dashboard/getList',
      //     data: {
      //       code: '330700',
      //     },
      //   })
      // },

      /*
       * 获取 top10
       */
      // getTop10() {
      //   return axios({
      //     method: 'post',
      //     url: baseURL.url + '/typeq/api/population/dashboard/getOriginCity2',
      //     data: {
      //       code: '3307',
      //     },
      //   })
      // },

      /*
       * 根据上面接口 计算流动人口
       */
      getPopulation() {
        // axios.all([this.getDashboard(), this.getTop10()]).then(
        //   axios.spread((res1, res2) => {
        //     if (res1.data.errno === 0 && res2.data.errno === 0) {
        //       const count = res1.data.data.count,
        //         res2Data = res2.data.data

        //       this.flowTotal = Math.floor(
        //         (1 - +res2Data.local) * count
        //       ).toString()
        //     }
        //   })
        // )
        $api('cstz_ldrk').then((res) => {
          //获取各区人口
          this.flowTotal = res[0].flowCount.toString()
        })
      },

      /*
       * 显示人流量弹窗
       */
      showFlowDialog(code, index) {
        console.log('显示人流量弹窗')
        // 做一个判断
        top.vm.openWind = [
          'showTime',
          'showWorkDay',
          'showCard',
          'flowDialogVisible',
          'showMiddleNew',
        ]
        top.vm.colseOtherWin()
        if (this.leftIndex == index) {
          this.leftIndex = -1
          top.vm.flowDialogVisible = !top.vm.flowDialogVisible
          top.vm.showCard = !top.vm.showCard
          top.vm.showWorkDay = !top.vm.showWorkDay
          top.vm.showTime = !top.vm.showTime
          top.vm.showInfo = !top.vm.showInfo
        } else {
          top.vm.flowDialogVisible = true
          top.vm.showCard = top.vm.showWorkDay = top.vm.showTime = true
          top.vm.showInfo = false
          this.leftIndex = index
        }
        let type =
          index == 0
            ? 'business'
            : index == 1
            ? 'scenic'
            : index == 2
            ? 'industry'
            : index == 3
            ? 'asian'
            : index == 4
            ? 'station'
            : ''
        let that = this
        axios({
          method: 'get',
          url: baseURL.url + '/api/?indexid=cstz_ssrkjj_el',
          params: {
            number: code,
          },
          headers: {
            ptid: 'PT0001',
            portToken: sessionStorage.getItem('token'),
          },
        }).then(function (ele) {
          let eleData = ele.data.data
          if (eleData != '' && eleData != null) {
            top.vm.flowData = eleData
            top.vm.areaImg1 = that.flowObj[type].img1
            top.vm.areaImg2 = that.flowObj[type].img2
          } else {
            top.vm.flowData = []
          }
          if (type == 'scenic' || type == 'industry') {
            top.vm.areaImg3 = that.flowObj[type].img3
          }
        })

        if (top.vm.flowDialogVisible) {
          top.vm.showMiddleNew = false
          top.vm.showTimeFun(1)
          this.isHot = false
          // 切换视角
          const flyData = {
            funcName: 'flyto',
            flyData: {
              postion: {
                x: 119.65267181396484,
                y: 29.07222557067871,
                z: 7544.85791015625,
              },
              rotation: {
                x: 78.94806671142578,
                y: 354.9329528808594,
                z: -0.002412805799394846,
              },
            },
          }
          top.document
            .getElementById('map')
            .contentWindow.Work.funChange(JSON.stringify(flyData))
          // $api('cstz_rlt', {
          //   number: 31
          // }).then(function (res2) {
          //   let hotMapData = []
          //   if (res2 && res2[0] && res2[0].hotChart) {
          //     res2[0].hotChart.map((item) => {
          //       let arr = []
          //       arr[0] = item.lng
          //       arr[1] = item.lat
          //       arr[2] = item.count
          //       arr[3] = item.geohash
          //       hotMapData.push(arr)
          //     })

          //     const mapData = {
          //       funcName: 'hotPowerMap',
          //       hotPowerMapData: hotMapData,
          //       offset: 256,
          //       heatMapId: "cstzLeftHot1",
          //       threshold: 3000,
          //       distance:1000

          //     }
          //     top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(mapData))
          //   }

          // })

          let dates = new Date()
          let times = dates.toLocaleDateString()
          let hh = dates.getHours()
          let h = null
          h = hh < 9 ? '0' + hh : hh
          let timeStr = times.replace('/', '-') + ' ' + h + ':00:00'
          $api('cstz_sjz_rlt_top5', { time: timeStr }).then((res) => {
            let pointData = []
            let textData = []
            res[0].heatmap.forEach((item, index) => {
              let str = {
                data: {},
                point: item.lng + ',' + item.lat,
              }
              let textStr = {
                pos: [item.lng, item.lat, 20],
                text: `top${index + 1}: ${item.count}`,
              }
              pointData.push(str)
              textData.push(textStr)
              let arr = [str]
              let num = index + 1
              top.document.getElementById('map').contentWindow.Work.funChange(
                JSON.stringify({
                  funcName: 'pointLoad', //功能名称
                  pointType: 'rlt-top' + num, //点位类型图标
                  pointId: 'rltTop' + num,
                  pointData: arr,
                  size: [0.08, 0.08, 0.08, 0.08],
                  popup:{
                    offset:[50,-100]
                    }
                })
              )
            })
            // top.document.getElementById('map').contentWindow.Work.funChange(
            //     JSON.stringify({
            //         funcName: 'pointLoad', //功能名称
            //         pointType: "air-point-6", //点位类型图标
            //         pointId: "热力图topPoint",
            //         pointData: pointData,
            //         size: [0.08, 0.08, 0.08, 0.08],
            //     })
            // )
            // top.document.getElementById('map').contentWindow.Work.funChange(
            //   JSON.stringify({
            //     funcName: '3Dtext',
            //     id:"热力图top文字",
            //     textData: textData,
            //     textSize: 20,
            //     color: [255,255,255,1]
            //   })
            // )
          })

          $api('/cstz_qxrlt_new').then((res) => {
            let hotMapData = []
            let heatArr = []
            let len = res[0].heatmap.length
            let sumLen = 9000 - len
            if (len >= 9000) {
              heatArr = res[0].heatmap.slice(0, 9000)
            } else {
              heatArr = res[0].heatmap
              for (let j = 0; j < sumLen; j++) {
                let a = {
                  count: 0,
                  geohash: 0,
                  lat: 0,
                  lng: 0,
                }
                heatArr.push(a)
              }
            }
            heatArr.map((item) => {
              // 画热力图的数据
              let pointArr = []
              pointArr[0] = item.lng
              pointArr[1] = item.lat
              pointArr[2] = item.count
              pointArr[3] = item.geohash
              hotMapData.push(pointArr)
            })
            const mapData = {
              funcName: 'hotPowerMap',
              hotPowerMapData: hotMapData,
              offset: 256,
              heatMapId: 'rkztHot0',
              threshold: 6000,
              distance: 800,
              alpha: 0.3,
            }
            top.document
              .getElementById('map')
              .contentWindow.Work.funChange(JSON.stringify(mapData))
          })

          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'rmActiveWall',
            })
          )
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: 'rmConePoint',
            })
          )
        } else {
          top.vm.showMiddleNew = true
          // 暂时注释加载墙体和锥体
          // top.document.getElementById('map').contentWindow.Work.coneAndWall()
          top.vm.closeWinZdqY()
        }

        this.mapFlag = true
      },

      // 获取重点区域实时人员的数量
      async getStressData() {
        const date = new Date()
        this.startTime = this.getNowFormatDate(date)

        const res = await axios({
          method: 'get',
          url: baseURL.url + '/api/?indexid=cstz_ssrkjj',
          headers: {
            ptid: 'PT0001',
            portToken: sessionStorage.getItem('token'),
          },
        })
        let arr = res.data.data
        let that = this
        arr.forEach((item) => {
          for (let i = 0; i < that.firstFlowObj.length; i++) {
            if (that.firstFlowObj[i].code == item.code) {
              that.firstFlowObj[i].data = item.data || 0
            }
          }
        })
      },

      /*
       * 根据点位划区域
       */
      async getAreaByPoints() {
        const date = new Date()

        this.endTime = this.getNowFormatDate(date)

        date.setHours(date.getHours() - 2)

        this.startTime = this.getNowFormatDate(date)
        // 接口：存储了businessArea数据
        // await this.getAreaPoints()

        for (let i = 0; i < this.businessArea.length; i++) {
          const subArr = this.businessArea[i].pointArr

          let flowNum = 0

          for (let j = 0; j < subArr.length; j++) {
            let pointArr = subArr[j],
              shapeStr = ''

            for (let index = 0; index < pointArr.length; index = index + 2) {
              shapeStr =
                shapeStr + pointArr[index] + ',' + pointArr[index + 1] + '|'
            }

            shapeStr = shapeStr.substring(0, shapeStr.length - 1)

            const reqParams = {
              shape: shapeStr,
              geohashlist: [],
              precision: 7,
            }

            const res = await axios({
              method: 'post',
              url: baseURL.url + '/typeq/api/getu/project/create',
              data: reqParams,
            })

            let id = res.data.data

            this.idList.push(id)

            const projectRes = await axios({
              method: 'post',
              url: baseURL.url + '/typeq/api/getu/project/get',
              data: {
                id: id,
                type: 2,
                start_time: this.startTime,
                end_time: this.endTime,
              },
            })

            flowNum += projectRes.data.data || 0
            this.allData.push(projectRes.data.data)

            // console.log('vistorFlow11-----------', this.visitorFlow12)
          }

          $('.cstz-top-flow').find('.wrapper>div').eq(i).find('b').text(flowNum)
        }
      },

      /*
       * 获取时间
       */
      getNowFormatDate(date) {
        /*
         * 获取时间并格式化
         */
        var seperator1 = '-',
          seperator2 = ':'

        var month = date.getMonth() + 1
        var strDate = date.getDate()
        if (month >= 1 && month <= 9) {
          month = '0' + month
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = '0' + strDate
        }
        var currentdate =
          date.getFullYear() +
          seperator1 +
          month +
          seperator1 +
          strDate +
          ' ' +
          date.getHours() +
          seperator2 +
          date.getMinutes() +
          seperator2 +
          date.getSeconds()
        return currentdate
      },

      /*
       * 获取实时时间
       */
      getCurrentDate() {
        var currentDate = new Date()

        this.titleDate =
          currentDate.getFullYear() +
          '年' +
          (currentDate.getMonth() + 1) +
          '月' +
          currentDate.getDate() +
          '日'
      },

      /*
       * 获取区域点位
       */
      async getAreaPoints() {
        const { data: res } = await axios.get(
          '/static/citybrain/csdn/data/getAreaCoord.json'
        )

        if (res.code !== 0) return

        this.businessArea = res.data
      },

      /*
       * 展示生产指数弹窗
       */
      showProIndexDialog(index, text, value, isUp, type) {
        this.absoIndex = index

        const topVm = top.window.vm
        this.isHot = true
        topVm.showInfo = true
        top.vm.openWind = ['productDialogVisible']
        top.vm.colseOtherWin()
        top.vm.closeWinZdqY()
        top.vm.rmPopFun()
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmhotPowerMap',
          })
        )
        topVm.productText = text

        topVm.productValue = value

        topVm.productIsUp = isUp

        topVm.indicatorType = type
        // 获取经济指标数据
        topVm.getExponentList(type)

        topVm.indicatorItemIndex = -1

        topVm.productDialogVisible = true
      },

      // echarts实例
      setupEChart(id, options) {
        const chart = echarts.init(document.getElementById(id))
        chart.clear()
        chart.setOption(options)
        window.onresize = () => {
          chart.resize()
        }
      },
      // setEcharts() {
      //   var xData = [">14天", "8-14天", "3-7天", "1-2天"];
      //   var yData = [4, 3, 2, 1];
      //   let option = {
      //     backgroundColor: "#061326",
      //     grid: {
      //       top: "25%",
      //       left: "5%",
      //       bottom: "5%",
      //       right: "10%",
      //       containLabel: true,
      //     },
      //     tooltip: {
      //       show: true,
      //     },
      //     animation: false,
      //     xAxis: [
      //       {
      //         type: "category",
      //         data: xData,
      //         axisTick: {
      //           alignWithLabel: true,
      //         },
      //         nameTextStyle: {
      //           color: "#82b0ec",
      //         },
      //         axisLine: {
      //           show: false,
      //           lineStyle: {
      //             color: "#82b0ec",
      //           },
      //         },
      //         axisLabel: {
      //           textStyle: {
      //             color: "#fff",
      //           },
      //           margin: 30,
      //         },
      //       },
      //     ],
      //     yAxis: [
      //       {
      //         show: false,
      //         type: "value",
      //         axisLabel: {
      //           textStyle: {
      //             color: "#fff",
      //           },
      //         },
      //         splitLine: {
      //           lineStyle: {
      //             color: "#0c2c5a",
      //           },
      //         },
      //         axisLine: {
      //           show: false,
      //         },
      //       },
      //     ],
      //     series: [
      //       {
      //         name: "",
      //         type: "pictorialBar",
      //         symbolSize: [40, 10],
      //         symbolOffset: [0, -6],
      //         symbolPosition: "end",
      //         z: 12,
      //         // "barWidth": "0",
      //         label: {
      //           normal: {
      //             show: true,
      //             position: "top",
      //             fontSize: 15,
      //             fontWeight: "bold",
      //             color: "#34DCFF",
      //           },
      //           // formatter: function (yData) {
      //           // for (var i = 0; i < yData.length; i++) {
      //           //   return yData[i] + '人'
      //           // }
      //           // }
      //         },
      //         color: "#2DB1EF",
      //         data: yData,
      //       },
      //       {
      //         name: "",
      //         type: "pictorialBar",
      //         symbolSize: [40, 10],
      //         symbolOffset: [0, 7],
      //         // "barWidth": "20",
      //         z: 12,
      //         color: "#2DB1EF",
      //         data: yData,
      //       },
      //       {
      //         name: "",
      //         type: "pictorialBar",
      //         symbolSize: [50, 15],
      //         symbolOffset: [0, 12],
      //         z: 10,
      //         itemStyle: {
      //           normal: {
      //             color: "transparent",
      //             borderColor: "#2EA9E5",
      //             borderType: "solid",
      //             borderWidth: 1,
      //           },
      //         },
      //         data: yData,
      //       },
      //       {
      //         name: "",
      //         type: "pictorialBar",
      //         symbolSize: [70, 20],
      //         symbolOffset: [0, 18],
      //         z: 10,
      //         itemStyle: {
      //           normal: {
      //             color: "transparent",
      //             borderColor: "#19465D",
      //             borderType: "solid",
      //             borderWidth: 2,
      //           },
      //         },
      //         data: yData,
      //       },
      //       {
      //         type: "bar",
      //         //silent: true,
      //         barWidth: "40",
      //         barGap: "10%", // Make series be overlap
      //         barCateGoryGap: "10%",
      //         itemStyle: {
      //           normal: {
      //             color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
      //               {
      //                 offset: 0,
      //                 color: "#0B3147",
      //               },
      //               {
      //                 offset: 1,
      //                 color: "#38B2E6",
      //               },
      //             ]),
      //             opacity: 0.8,
      //           },
      //         },
      //         data: yData,
      //       },
      //     ],
      //   };

      //   this.setupEChart('zEcharts0', option)
      // },
    },
    beforeDestroy() {
      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmhotPowerMap',
        })
      )
    },
  })
})()
