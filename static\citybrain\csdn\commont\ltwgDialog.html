<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>立体网格弹窗</title>
  <script src="/static/citybrain/hjbh/js/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/jslib/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
  <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
  <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
  <script src="/static/js/home_services/md5.js"></script>
  <script src="https://csdn.dsjj.jinhua.gov.cn:8101/static/js/jslib/simplify.js"></script>
  <script src="/static/js/jslib/turf.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <style>
    [v-cloak] {
      display: none;
    }

    .contentmain {
      width: 500px;
      height: 1250px;
      background: linear-gradient(180deg, rgba(2, 51, 110, 0.85), rgba(8, 37, 71, 0.85));
      position: relative;
      color: #fff;
      font-size: 30px;
      background-size: 100% 100%;
      padding: 40px 20px 20px 20px;
      box-sizing: border-box;
    }

    .header {
      height: 50px;
      line-height: 50px;
      font-size: 30px;
      font-weight: bolder;
      padding-left: 25px;
      background: url('/static/citybrain/tckz/img/main_mapIcon/header.svg') no-repeat;
      background-size: 100% 100%;
    }

    .items-title {
      height: 30px;
      margin-bottom: 10px;
    }

    .items-title>div {
      float: right;
      padding: 0 16px;
      height: 35px;
      cursor: pointer;
      /* background-image: linear-gradient(180deg,hsla(0,0%,100%,.4),hsla(0,0%,100%,0)); */
      border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
      transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
      background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
    }

    .items-title>div:hover,
    #active {
      background: #2960cb;
      color: white;
    }

    /* 表格样式修改 */
    .hearder_h2 {
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 30px;
      font-weight: 500;
      text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
      background: linear-gradient(180deg, #caffff 0%, #caffff 0%, #ffffff 0%, #00c0ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .hearder_h2>span {
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 10px;
      white-space: nowrap;
    }

    .el-table {
      max-height: 420px !important;
      overflow: hidden;
      overflow-y: auto;
      color: rgb(197, 192, 192);
      padding-right: 5px !important;
      background: transparent !important;
    }

    .el-table th,
    .el-table tr {
      font-size: 22px !important;
    }

    .el-table tr {
      background: url('/static/citybrain/csdn/img/table_tr_bg.png') no-repeat;
      background-size: 99% 97%;
    }

    .el-table td,
    .el-table th.is-leaf {
      border: 0 !important;
    }

    .el-table tbody tr:hover>td {
      background: #1d4a7acb !important;
    }

    .el-table::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 2px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .el-table::-webkit-scrollbar-thumb {
      border-radius: 2px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 10px;
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }

    .el-checkbox,
    .el-checkbox__input {
      zoom: 120%;
    }

    .el-table .cell {
      line-height: normal;
    }

    .el-table .el-table__cell {
      padding: 8px 0 15px !important;
    }

    .el-checkbox__inner {
      width: 15px;
      height: 15px;
    }

    .number {
      display: inline-block;
      font-size: 35px;
    }

    .number .numbg {
      display: inline-block;
      width: 30px;
      height: 41px;
      line-height: 41px;
      text-align: center;
      background: url('/static/citybrain/hjbh/img/rkzt/numBg.png') no-repeat;
      background-size: contain;
      margin: 0 2px;
      border-radius: 8px;
    }

    /* el */
    .el-input {
      font-size: 30px;
    }

    .el-input__inner {
      height: 40px;
      line-height: 30px;
      background-color: rgba(25, 27, 35, 0);
      color: hsla(0, 0%, 100%, 0.8);
    }

    .el-input__icon {
      height: 30px;
      line-height: 30px;
    }

    .popper__arrow {
      display: none !important;
    }

    .el-select {
      margin: 0 20px;
    }

    .el-select-dropdown {
      background-color: rgba(25, 27, 35, 0.96);
    }

    .el-popper[x-placement^='bottom'] {
      margin-top: -2px;
    }

    .el-select-dropdown__item {
      color: #fff;
      height: 40px;
      line-height: 40px;
      font-size: 30px;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #2a4b8b;
    }

    .el-select-dropdown__item.selected {
      background-color: #2a4b8b;
      color: #fff;
      font-weight: 400;
    }

    #circle_sou>div {
      font-size: 30px;
      padding: 2px 7px 3px 4px;
      border-radius: 14px;
      cursor: pointer;
    }

    .icon-nav-start {
      /* background: url('/zhddjhaqcsztqd/static/img/zhddzx/box/nav_start.png') no-repeat; */
      /* width: 30px;
      height: 40px;
      transform: scale(1.5); */
      /*position: absolute;*/
      /*left: 2%;*/
      background: linear-gradient(45deg, #3a3636, #0001b9);
    }

    .icon-nav-start:hover,
    .icon-nav-start-active {
      background: linear-gradient(45deg, #2f2c2c, #3579f8);
    }

    .el-slider__runway {
      width: 400px;
      height: 10px;
      border-radius: 10px;
      margin-left: 20px;
    }

    .el-slider__bar {
      height: 10px;
    }

    .el-slider__stop {
      height: 10px;
      width: 10px;
      background-color: #294e7ad4;
    }

    .el-slider__button {
      width: 7.7px;
      height: 7.7px;
      margin-top: -8px;
      margin-left: 10px;
      background-color: #0083ff;
      border-color: #fff;
      border-radius: 0;
      border-width: 1px;
      transform: translateX(-50%) rotate(45deg) !important;
    }

    .el-slider__button:after {
      content: '';
      position: absolute;
      background-image: url('/static/citybrain/tckz/img/main_mapIcon/slider.svg');
      width: 30px;
      height: 30px;
      left: -11px;
      top: -11px;
      transform: rotate(45deg);
      background-size: 100%;
    }

    .el-slider__runway.show-input {
      margin-right: 145px;
    }

    .el-input-number--small .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }

    .el-input-number--small .el-input-number__decrease,
    .el-input-number--small .el-input-number__increase {
      width: 25px;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
      background: transparent;
      color: #fff;
      font-weight: bold;
    }

    [class*=' el-icon-'],
    [class^='el-icon-'] {
      font-weight: 700;
    }
  </style>
</head>

<body>
  <div id="ltwgDialog">
    <div id="result" class="contentmain">
      <div class="header" id="header" style="display: flex;justify-content: space-between;align-items: center">
        <span>北斗立体单元网格</span>
        <i class="el-icon-circle-close" @click="closeIframe" style="cursor: pointer"></i>
      </div>
      <div
        style="display: flex;flex-direction: column;justify-content: space-evenly;align-items: flex-start;margin-top: 20px">
        <div class="s-flex s-row-center s-font-26 s-c-white s-text-center">
          <p class="s-m-10">网格级别：</p>
          {{dialogData.wg}}
        </div>
        <div class="s-flex s-row-center s-font-26 s-c-white s-text-center">
          <p class="s-m-10">全球编码：</p>
          {{dialogData.code}}
        </div>
        <div class="s-flex s-row-center s-font-26 s-c-white s-text-center">
          <p class="s-m-10">实时人数：</p>
          <div class="s-flex">
            <div class="number s-c-yellow-gradient" v-for="(item, i) in String(dialogData.peopleNumber)" :key="i">
              <span class="numbg">
                <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                  class="s-c-yellow-gradient"></count-to>
              </span>
            </div>
            人
          </div>
        </div>
      </div>
      <template>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark"
          style="width: 100%;max-height: 800px !important; margin-top: 20px" :show-header="false"
          @selection-change="handleSelectionChange">
          <el-table-column prop="name" label="名称">
            <template slot-scope="scope">
              <div class="s-flex">
                <img :src="scope.row.icon" alt="" width="60px" height="60px" />
                <span class="s-c-blue-gradient1" style="font-size: 30px;">{{scope.row.name}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="num,unit" label="总值">
            <template slot-scope="scope">
              <div style="font-size: 30px">{{scope.row.points.length}}{{scope.row.unit}}</div>
            </template>
          </el-table-column>
          <el-table-column type="selection" width="60" height="60"></el-table-column>
        </el-table>
      </template>
    </div>
  </div>
  <script>
    let vm = new Vue({
      el: "#ltwgDialog",
      data: {
        dialogData: {},
        tableData: [],
        clickObj: {}
      },
      computed: {

      },
      mounted() {
        const that = this;
        window.addEventListener("message", function (event) {
          //子获取父消息
          let newData;
          if (typeof event.data == "object") {
            newData = event.data;
          } else {
            newData = JSON.parse(event.data.argument);
          }
          console.log(newData, "接收到的数据");
          that.dialogData = newData.result;
          that.clickObj = that.dialogData.clickObj;
          that.tableData = [].concat.apply([], that.dialogData.formatPointObj.formatArr);
        });
        // this.getDetail(this.searchInput,localStorage.getItem("year"));
      },
      methods: {
        handleSelectionChange(e) {
          console.log(e, "e");
          this.clearPointLayer()
          if (e.length > 0) {
            e.forEach((item, i) => {
              this.loadPoint(item, this.clickObj)
            })
          }
        },
        //加载点位图层
        loadPoint(obj, wg) {
          console.log(obj, wg, "point");
          let layerId = `ltwgPoint${obj.name}`
          let PointData = []
          if (obj.pointType == 'gongye') {
            obj.points.forEach(ele => {
              let marker = {
                lng: ele.geometry.x,
                lat: ele.geometry.y,
                data: ele
              }
              PointData.push(marker)
            })
          } else {
            obj.points.forEach(ele => {
              if (ele.attributes?.point_x && ele.attributes?.point_y) {
                let marker = {
                  lng: ele.attributes.point_x,
                  lat: ele.attributes.point_y,
                  data: ele
                }
                PointData.push(marker)
              }
            })
          }

          if (top.mapUtil.layers[layerId]) {
            this.clearPointLayer(layerId)
          } else {
            if (obj.pointType == 'common') {
              top.mapUtil.loadPointLayer({
                data: PointData,
                layerid: layerId,
                iconcfg: { image: `${baseURL.url + obj.icon}`, iconSize: 1 },
                cluster: true, //是否聚合
                onclick: (e) => this.showPointPop(e, layerId),
                popcfg: {
                  offset: [50, -100],
                  show: false,
                },
              })
            }
            if (obj.pointType == 'gongye') {
              top.mapUtil.loadPointLayer({
                data: PointData,
                layerid: layerId,
                iconcfg: { image: `${baseURL.url + obj.icon}`, iconSize: 1 },
                cluster: true, //是否聚合
                onclick: (e) => this.showPointPop1(e, layerId),
                popcfg: {
                  offset: [50, -100],
                  show: false,
                },
              })
            }
            if (obj.pointType == 'video') {
              top.mapUtil.loadPointLayer({
                layerid: layerId,
                data: obj.points,
                onclick: this.openPointVideoMassage,
                cluster: true, //是否定义为聚合点位：true/false
                iconcfg: {
                  image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                  iconSize: 0.5,
                  iconlist: {
                    field: 'pointType',
                    list: [
                      {
                        value: '告警视频',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/warningVideo.png`,
                      },
                      {
                        value: '枪机在线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                      },
                      {
                        value: '枪机离线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
                      },
                      {
                        value: '球机在线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                      },
                      {
                        value: '球机离线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
                      },
                      {
                        value: '半球机在线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                      },
                      {
                        value: '半球机离线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
                      },
                      {
                        value: '高点在线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                      },
                      {
                        value: '高点离线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                      },
                      {
                        value: '未知在线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                      },
                      {
                        value: '未知离线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                      },
                      {
                        value: '铁塔在线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-tieta.png`,
                      },
                      {
                        value: '铁塔离线',
                        size: '50',
                        src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-tieta.png`,
                      }
                    ],
                  },
                },
              })
            }
          }
        },
        //点位弹窗打开
        showPointPop(e, id) {
          console.log(e, id, "point");
          let key = [{ name: "名称", key: "shortname" }, { name: "全称", key: "name" }, { name: "地址", key: "address" }];
          let data = e.data.attributes
          let arr = key.map((a, i) => {
            return { name: a.name, value: data[a.key] }
          })
          let str = this.createPops(arr)
          let objData = {
            layerid: id,
            position: [data.point_x, data.point_y],
            popup: {
              offset: [50, -50],
              closeButton: true,
            },
            content: str,
          }

          top.mapUtil._createPopup(objData)
        },
        //工业企业点位弹窗打开
        showPointPop1(e, id) {
          console.log(e, id, "point");
          let key = [{ name: "企业名称", key: "qymc" }, { name: "行业类别", key: "hylb" }, { name: "企业地址", key: "qydz" }];
          let data = e.data.attributes
          let arr = key.map((a, i) => {
            return { name: a.name, value: data[a.key] }
          })
          let str = this.createPops(arr)
          let objData = {
            layerid: id,
            position: [e.esX, e.esY],
            popup: {
              offset: [50, -50],
              closeButton: true,
            },
            content: str,
          }

          top.mapUtil._createPopup(objData)
        },
        // 查看地图视频点位点击的详情
        openPointVideoMassage(e, list) {
          console.log("被点击的点", e, list)
          let this_ = this
          // let item = e
          top.mapUtil.removeLayer('syr')
          top.mapUtil.removeLayer('syr1')
          if (e.pointId == 'video') {
            top.mapUtil.flyTo({
              destination: [e.esX, e.esY],
              offset: [0, -999],
            })
            let item = {
              obj: {
                // chn_name: e.data.name,
                chn_name: e.data.video_name,
                pointList: list,
              },
              video_code: e.data.addinfo.chncode,
              csrk: true,
            }

            let iframe1 = {
              type: 'openIframe',
              name: 'video_main_code',
              src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
              width: '100%',
              height: '100%',
              left: '0',
              top: '0',
              zIndex: '1000',
              argument: item,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else if (e.data != undefined && e.data.pointId == 'video' && e.data.videoSource != 2) {
            // if (e.is_online == '离线') {
            //   this.$message('设备离线')
            //   return
            // }
            let item = {
              obj: {
                // chn_name: e.data.name,
                chn_name: e.data.video_name,
                pointList: list,
              },
              video_code: e.data.addinfo.chncode,
              is_online: e.is_online,
              csrk: true,
            }
            top.mapUtil.flyTo({
              destination: [e.esX, e.esY],
              offset: [0, -999],
            })
            let iframe1 = {
              type: 'openIframe',
              name: 'video_main_code',
              src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
              width: '100%',
              height: '100%',
              left: '0',
              top: '0',
              zIndex: '1000',
              argument: item,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else if (e.data != undefined && e.data.pointId == 'video' && e.data.videoSource == 2) {
            axios({
              method: 'get',
              url: baseURL.url + '/adm-api/mis/system/videos/webPlay/' + e.data.chnCode,
              headers: {
                Authorization: sessionStorage.getItem('Authorization')
              },
            }).then(function (res) {
              let left1 = (window.screen.availLeft || 0) + (screen.width - 500) / 2
              let top1 = (screen.height - 700) / 2
              window.open(res.data.data, "_blank", 'height=600, width=700, top=' + top1 + ', left=' + left1 + ', crollbars=no, location=no, status=no, alwaysRaised=yes')
            })
          }
        },
        //创建点位弹窗
        createPops(arr) {
          let countStr = ''
          for (let index = 0; index < arr.length; index++) {
            countStr += `<p style="
                      width: 650px;
                      font-size: 30px;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                    ">
                    ${arr[index].name} :<span style="color: #ffff" title="${arr[index].value}">${arr[index].value}</span>
                  </p>`
          }
          let str = `<div
              style="
                position: relative;
                background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                background-size: 100% 100%;
                width: max-content;
                min-height: 250px;
              ">
              <nav
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-end;
                  padding-bottom: 10px;
                  margin: 0 20px;
                  border-bottom: 1px solid;
                  border-bottom: 2px solid;
                  border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                  padding-left: 20px;
                ">
                <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">${arr[0].value}</h2>
                <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none';">
                  <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
                </span>
              </nav>
              <header
                style="
                  padding-bottom: 15%;
                  margin: 10px 20px 0;
                  display: flex;
                  justify-content: space-between;
                  font-size: 25px;
                ">
                <div style="margin-left: 40px;color:#fff">${countStr}</div>
              </header>
            </div>`
          return str
        },
        //清除点位
        clearPointLayer(layerid) {
          if (layerid) {
            top.mapUtil.removeLayer(layerid)
          } else {
            // 清除所有点位图层
            const LayerRegex = /ltwgPoint/;
            const layerKeys = Object.keys(top.mapUtil.layers).filter(item => LayerRegex.test(item));
            try {
              if (layerKeys && layerKeys.length) {
                top.mapUtil.removeAllLayers(layerKeys);
              } else {
                return
              }
            } catch (error) {
              console.error('Failed to remove ltwgPointLayers:', error);
            }
          }
        },
        //关闭弹窗
        closeIframe() {
          this.resetWg()
          let data = JSON.stringify({
            type: 'closeIframe',
            name: 'L4ltwgDialog'
          })
          top.postMessage(data, '*')
          top.closeIframeByNames(['L4ltwgDialog'])
        },
        //恢复镂空网格显示
        resetWg() {
          if (top.L4wgLayer) {
            top.L4wgLayer.definitionExpression = localStorage.getItem('currentXzqhCode')
          } else {
            top.mapUtil.layers[`ltwg${localStorage.getItem('layerId')}`].definitionExpression = ""
          }
        },
      },
    });
  </script>
</body>

</html>