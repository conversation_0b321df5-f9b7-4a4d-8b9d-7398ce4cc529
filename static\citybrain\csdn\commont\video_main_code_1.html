<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>视频详情弹窗</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/citybrain/csdn/js/video/xgplayer-2.9.6.js"></script>
    <script src="/static/citybrain/csdn/js/video/xgplayer-hls.js"></script>
    <script src="/static/citybrain/csdn/js/video/xgplayer-flv.js"></script>
    <style>
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 1680px;
        background-color: transparent;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sjzx-middle {
        position: relative;
        width: 2327px;
        height: 1680px;
        background: url('/static/citybrain/tckz/img/video_bg.png') no-repeat;
        background-size: 100% 100%;
        overflow: hidden;
      }

      .head {
        width: 100%;
        height: 100px;
        line-height: 100px;
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        position: relative;
        text-align: center;
        top: 10px;
      }

      .head > div {
        font-size: 50px !important;
        font-weight: 500;
        color: #fff;
        margin: 0 20px;
        display: flex;
      }

      .head > img {
        /* cursor: pointer;
          margin-top: 5px; */
      }

      .head > img:nth-of-type(1) {
        animation: jump3 2s infinite;
      }

      .head > img:nth-of-type(2) {
        animation: jump4 2s infinite;
      }

      .head:hover > img {
        animation: none;
      }

      .img {
        display: inline-block;
        margin: 20px;
        width: 50px;
        height: 50px;
        background-image: url('/static/citybrain/csdn/img/cstz2-middle/close-hover.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        /* position: absolute; */
        right: 70px;
        top: 35px;
        cursor: pointer;
      }

      .ding {
        position: absolute;
        left: 30px;
        top: 90px;
        cursor: pointer;
      }

      .ding > img {
        transform: rotateY(180deg);
        width: 40px;
        height: 40px;
      }

      .sjzx-middle-con {
        width: 100%;
        height: 100%;
        /* height: calc(100% - 100px); */
        padding: 50px 75px;
        box-sizing: border-box;
      }

      .m-con {
        width: 100%;
        height: 100%;
      }

      .table-css {
        width: 100%;
        height: 100%;
        font-size: 40px;
        color: #fff;
      }

      .table-th {
        width: 100%;
        height: 100px;
        background-image: linear-gradient(0deg, #00506a 0%, #0097c8 100%);
        border-bottom: solid 1px #00c0ff;
        display: flex;
      }

      .th-css {
        font-size: 48px;
        line-height: 100px;
        text-align: center;
        font-weight: bold;
      }

      .th-css:first-child {
        flex: 1;
        border-right: 1px solid #00c0ff;
      }

      .table-tr {
        width: 100%;
        display: flex;
        height: 100%;
        flex-direction: column;
      }

      .tr-left {
        width: 2538px;
      }

      .tr-right {
        width: 620px;
        height: 100%;
        position: relative;
        overflow-y: scroll;
        margin-left: 50px;
      }

      .yjyp-item {
        font-size: 42px;
        margin: 30px 10px;
      }

      #videoBox {
        position: absolute;
        left: 53px;
        top: 153px;
      }

      .icon {
        font-size: 40px;
        color: orange;
      }

      .s-col-bottom {
        position: absolute;
        right: 0px;
        top: 0px;
      }

      .lables {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
      }

      .el-tag {
        height: 50px;
        line-height: 50px;
        font-size: 36px;
        padding: 0 20px;
        margin: 10px 10px 0px 0;
      }

      .el-input {
        font-size: 35px;
      }

      /* 设置滚动条的样式 */
      ::-webkit-scrollbar {
        width: 10px;
        height: 20px;
      }

      /* 滚动槽 */
      ::-webkit-scrollbar-track {
        border-radius: 5px;
      }

      /* 滚动条滑块 */
      ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(35, 144, 207, 0.1);
      }

      ::-webkit-scrollbar-thumb:window-inactive {
        background: rgba(27, 146, 215, 0.4);
      }

      .el-input__inner {
        background: none;
        color: white;
        border: none;
        padding: 0;
      }

      .el-input.is-disabled .el-input__inner {
        border-color: #409eff;
        background: none;
        color: white;
      }

      .el-input__suffix {
        right: 0;
      }

      .el-cascader {
        margin-top: 10px;
        width: 100%;
      }

      .el-cascader .el-input .el-input__inner:focus,
      .el-cascader .el-input.is-focus .el-input__inner {
        border-color: #409eff;
        background: none;
      }

      .el-cascader .el-input .el-icon-arrow-down {
        font-size: 20px;
      }

      .el-cascader__dropdown {
        background-color: rgb(4 55 85 / 60%);
        border: 1px solid #409eff;
      }

      .el-cascader-menu {
        width: 300px;
      }

      .el-cascader-node {
        font-size: 30px;
        color: white;
        height: 45px;
      }

      .el-cascader-node:not(.is-disabled):focus,
      .el-cascader-node:not(.is-disabled):hover {
        background: #133f81;
      }

      .el-tag {
        height: 40px;
        line-height: 38px;
        font-size: 35px;
        padding: 0 25px 0 10px;
        margin: 5px 5px 0px 5px;
        position: relative;
        background-color: rgb(13 112 145 / 60%);
        border: none;
        color: #e3e2d3;
        border-radius: 10px;
      }

      .el-tag .el-icon-close {
        position: absolute;
        top: 16px;
        right: 5px;
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .el-tag + .el-tag {
        margin-left: 5px;
      }

      .button-new-tag {
        margin: 5px;
        height: 49px;
        line-height: 45px;
        padding-top: 0;
        padding-bottom: 0;
        font-size: 35px !important;
        color: #e3e2d3;
        border-radius: 10px;
        background-color: rgb(13 112 145 / 60%);
        border: none;
      }

      .input-new-tag {
        height: 47px !important;
        width: 300px;
        margin-left: 5px;
        vertical-align: bottom;
      }

      .el-input--small .el-input__inner {
        height: 50px;
        line-height: 45px;
        padding: 0 5px;
        padding-right: 60px;
        font-size: 35px;
        color: #e3e2d3;
        background-color: rgb(13 112 145 / 60%);
        border: none;
        border-radius: 10px;
      }

      .el-input .el-input__count .el-input__count-inner {
        font-size: 20px;
        background: none;
      }

      .el-cascader,
      .el-tag {
        display: inline !important;
      }

      .el-cascader-menu__wrap {
        height: 250px;
      }

      .el-message-box {
        background-color: rgb(4 83 108) !important;
        border: none !important;
      }

      .el-message-box__title {
        color: #fff;
        font-size: 30px;
      }

      .el-message-box__content {
        color: #fff;
        font-size: 28px;
      }

      .el-button--small {
        font-size: 28px;
        padding: 5px 15px;
      }

      .el-message-box__wrapper {
        top: -160px;
        left: 2550px;
      }

      .el-cascader-node__prefix {
        left: 0;
      }

      .top-guang {
        position: absolute;
        top: -55px;
        left: 200px;
        animation: jump1 5s infinite;
      }

      .bottom-guang {
        position: absolute;
        bottom: -108px;
        right: 200px;
        animation: jump2 5s infinite;
      }

      @keyframes jump1 {
        0% {
          transform: translate(0px, 0px);
          /*开始位置*/
        }

        50% {
          transform: translate(2200px, 0px);
          /* 可配置跳动方向 */
        }

        100% {
          transform: translate(0px, 0px);
          /*结束位置*/
        }
      }

      @keyframes jump2 {
        0% {
          transform: translate(0px, 0px);
          /*开始位置*/
        }

        50% {
          transform: translate(-2200px, 0px);
          /* 可配置跳动方向 */
        }

        100% {
          transform: translate(0px, 0px);
          /*结束位置*/
        }
      }

      @keyframes jump3 {
        0% {
          transform: translate(0px, 0px);
          /*开始位置*/
        }

        50% {
          transform: translate(15px, 0px);
          /* 可配置跳动方向 */
        }

        100% {
          transform: translate(0px, 0px);
          /*结束位置*/
        }
      }

      @keyframes jump4 {
        0% {
          transform: translate(0px, 0px);
          /*开始位置*/
        }

        50% {
          transform: translate(-15px, 0px);
          /* 可配置跳动方向 */
        }

        100% {
          transform: translate(0px, 0px);
          /*结束位置*/
        }
      }

      .bottom {
        width: 641px;
        height: 250px;
        margin: 0 auto;
        background: url('/static/citybrain/tckz/img/video-bottom.png') no-repeat 0 -90px;
        position: relative;
        clip-path: polygon(0 0, 50% 100%, 100% 0);
      }

      .bottom > img {
        position: absolute;
        left: calc(50% - 86px);
        top: -10px;
      }

      .point {
        cursor: pointer !important;
      }

      .disPoint {
        cursor: not-allowed !important;
      }

      .titleName {
        max-width: 2500px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .left-arrow {
        width: 80px;
        height: 160px;
        background-image: url('/static/citybrain/tckz/img/arrow-left.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
        position: absolute;
        top: 45%;
        left: 0;
      }

      .right-arrow {
        width: 80px;
        height: 160px;
        background-image: url('/static/citybrain/tckz/img/arrow-left.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        transform: rotateY(180deg);
        cursor: pointer;
        position: absolute;
        top: 45%;
        right: 0;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <div class="sjzx-middle">
        <div class="sjzx-middle-con">
          <div style="display: flex; margin: 16px 0; justify-content: space-between; align-items: center">
            <div class="head-title">视频广场</div>
            <div style="display: flex; align-items: center">
              <div class="head-type" @click="handleType">{{typeText}}</div>
              <div class="img" @click="closeMiddleIframe('video_main_code_1')"></div>
            </div>
          </div>
          <div class="m-con">
            <div class="table-css">
              <div class="table-tr">
                <!-- <div style="display: flex; flex-wrap: wrap; position: absolute; z-index: 98">
                  <div class="video_default" v-for="(item,index) in 9" :key="index">
                    <div class="video_title">选择视频</div>
                  </div>
                </div> -->
                <div class="left-arrow" @click="handleLeftClick"></div>
                <div class="right-arrow" @click="handleRightClick"></div>
                <div
                  style="
                    display: flex;
                    flex-wrap: wrap;
                    z-index: 99;
                    width: 2177px;
                    height: 1400px;
                    position: absolute;
                    left: 3210px;
                    top: 473px;
                  "
                  id="videobox_all"
                >
                  <!-- <div class="video_default" v-for="(item,index) in list" :key="index">
                    <template v-if="index<3">
                      <div
                        class="video_box"
                        :id="'videoBox_'+index"
                        :style="{
                        top: '460px', 
                        left: 3180+(index%3)*725+'px',
                      }"
                      ></div>
                      <div class="video_div">
                        <div class="video_title" :title="item.videoName">{{item.videoName||'选择视频'}}</div>
                        <div v-if="item.videoName" @click="handleFullscreen(index)" style="cursor: pointer">全屏</div>
                      </div>
                    </template>
                    <template v-if="3<=index && index<6">
                      <div
                        class="video_box"
                        :id="'videoBox_'+index"
                        :style="{
                        top: 460+495+'px', 
                        left: 3180+(index%3)*725+'px',
                      }"
                      ></div>
                      <div class="video_div">
                        <div class="video_title" :title="item.videoName">{{item.videoName||'选择视频'}}</div>
                        <div v-if="item.videoName" @click="handleFullscreen(index)" style="cursor: pointer">全屏</div>
                      </div>
                    </template>
                    <template v-if="6<=index&& index<9">
                      <div
                        class="video_box"
                        :id="'videoBox_'+index"
                        :style="{
                        top: 460+495+495+'px', 
                        left: 3180+(index%3)*725+'px',
                      }"
                      ></div>
                      <div class="video_div">
                        <div class="video_title" :title="item.videoName">{{item.videoName||'选择视频'}}</div>
                        <div v-if="item.videoName" @click="handleFullscreen(index)" style="cursor: pointer">全屏</div>
                      </div>
                    </template>
                  </div> -->
                </div>
                <div class="tr-left">
                  <div
                    id="videoBox"
                    style="width: 2538px; height: 1400px"
                    v-show="showCollection != null || csrkStatus"
                  ></div>

                  <div
                    id="videoBox1"
                    style="width: 2538px; height: 1400px"
                    v-show="showCollection == null && !csrkStatus"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="bottom">
        <img class="breath-light" src="/static/citybrain/tckz/img/video-dian.png" alt="" />
        <canvas
          id="canvas"
          ref="canvas"
          style="
            z-index: 100;
            position: fixed;
            top: 0;
            width: 641px;
            height: 250px;
            position: absolute;
            transform: rotate(180deg);
          "
        ></canvas>
      </div> -->
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    window.addEventListener('message', function (event) {
      // console.log(event.data)
      if (Array.isArray(event.data)) {
        video.localList = event.data
        video.list = event.data.slice(0, video.videoCount)
        video.create()
        video.num = 1

        video.switchFun = setInterval(video.switchVideo, 30 * 1000)

        return
      }
      if (Object.prototype.toString.call(event.data) === '[object Object]' && event.data) {
        console.log(event.data)
        console.log('video.list', video.list)

        video.list[video.count] = event.data
        video.count = video.count + 1
        video.$forceUpdate()
        if (event.data.obj && event.data.obj.is_collection != undefined) {
          video.showCollection = event.data.obj.is_collection
          video.getInfoData(event.data)
        } else if (event.data.chnCode) {
          video.csrkStatus = event.data.csrk || ''
          video.getInfoData(event.data, video.count - 1)
        } else {
          video.getHLSVideo(event.data)
        }
      }
    })
    var video = new Vue({
      el: '#app',
      data: {
        showCollection: null,
        csrkStatus: null,
        titleName: '',
        videoCode: null,
        ws: top.DHWsInstance,
        dataList: [{ name: '设备名称', value: '金华西路' }],
        paramsData: {
          chn_code: '', //通道编码
          user_access: top.commonObj.userId, //账号id
          // user_access: 109,
        },
        conLabel: [],
        conLabelTmp: [],
        tagLabel: [],
        inputVisible: false,
        inputValue: '',
        options: [],
        optionList: [],
        queryData: {
          chnCode: '',
          domainCode: '',
          description: '',
        },
        canvas: '',
        ctx: '',
        W: '',
        H: '',
        angle: 0,
        mp: 3000,
        particles: [],
        t: 0,
        pointList: [],
        titleList: [],
        pointIndex: 0,
        titleListShow: false,
        leftDisabled: true,
        rightDisabled: false,
        list: [0, 1, 2, 3, 4, 5, 6, 7, 8],
        count: 0,
        localList: [],
        num: 0,
        // 默认九宫格
        videoCount: 9,
        typeText: '3x3',
        switchFun: undefined,
      },
      mounted() {
        top.emiter &&
          top.emiter.on('showCollection', (res) => {
            this.showCollection = res
          })

        // this.showCollection = 1
        // let data = {
        //   video_code: '33079952001321083431',
        //   obj: {
        //     chn_name: '市政府顶楼西北角',
        //     pointList: []
        //   }
        // }
        // this.getInfoData(data)

        // this._initCavas()
      },
      directives: {
        //注册一个局部的自定义指令 v-focus
        focus: {
          // 指令的定义
          inserted: function (el) {
            // 聚焦元素
            el.querySelector('input').focus()
          },
        },
      },
      methods: {
        switchVideo() {
          this.list = this.localList.slice(this.videoCount * this.num, this.videoCount * (this.num + 1))
          this.num = this.num + 1
          this.create()
        },
        handleLeftClick() {
          clearInterval(this.switchFun)
          if (this.num > 0) {
            this.num = this.num - 1
            this.list = this.localList.slice(this.videoCount * this.num, this.videoCount * (this.num + 1))
            this.create()
            this.switchFun = setInterval(this.switchVideo, 30 * 1000)
          }
        },
        handleRightClick() {
          clearInterval(this.switchFun)
          if (this.num < this.localList.length / this.videoCount) {
            this.num = this.num + 1
            this.list = this.localList.slice(this.videoCount * this.num, this.videoCount * (this.num + 1))
            this.create()
            this.switchFun = setInterval(this.switchVideo, 30 * 1000)
          }
        },
        handleType() {
          if (this.videoCount === 16) {
            this.videoCount = 9
            this.typeText = '3x3'
          } else {
            this.videoCount = 16
            this.typeText = '4x4'
          }
          this.list = this.localList.slice(this.videoCount * this.num, this.videoCount * (this.num + 1))
          this.num = this.num + 1
          this.create()
        },
        handleFullscreen(index) {
          let iframe1 = {
            type: 'openIframe',
            name: 'video_main_code_fullscreen',
            src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code_fullscreen.html',
            width: '2340px',
            height: '1680px',
            left: '3090px',
            top: '320px',
            zIndex: '999',
            argument: { list: this.list, index: index },
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
          this.closeMiddleIframe('video_main_code_1')
        },
        dingFun() {
          let this_ = this
          top.postMessage(
            {
              type: 'dingFun',
              data: {
                chnCode: this_.videoCode,
                chnName: this_.titleName,
              },
            },
            '*'
          )
        },
        _initCavas() {
          this.canvas = document.getElementById('canvas')
          this.ctx = this.canvas.getContext('2d')

          //canvas dimensions
          this.W = window.innerWidth - 30
          this.H = window.innerHeight - 10
          this.canvas.width = this.W
          this.canvas.height = this.H

          //snowflake particles
          //雪花数量
          this.mp = 100
          this.particles = []
          for (var i = 0; i < this.mp; i++) {
            this.particles.push({
              x: Math.random() * this.W * 5, //x-coordinate
              y: Math.random() * this.H, //y-coordinate
              //改变大小
              r: Math.random() * 20 + 10, //radius
              d: Math.random() * this.mp, //density
            })
          }
          clearInterval(localStorage.getItem('interval'))
          localStorage.setItem('interval', setInterval(this.draw, 25))
        },
        draw() {
          this.ctx.clearRect(0, 0, this.W, this.H)
          this.ctx.fillStyle = 'rgba(146,192,227,1)'
          this.ctx.fillStyle = 'border: 1px solid rgb(37, 211, 236,0.2)'
          this.ctx.fillStyle = 'box-shadow: 0px 0px 10px 5px rgba(145,198,239,1)'
          this.ctx.beginPath()
          for (var i = 0; i < this.mp; i++) {
            var p = this.particles[i]
            this.ctx.moveTo(p.x, p.y)
            this.ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2, true)
          }
          this.ctx.fill()
          this.update()
        },
        update() {
          // this.angle += 0.01;
          for (var i = 0; i < this.mp; i++) {
            var p = this.particles[i]
            p.y += Math.cos(this.angle + p.d) + 1 + p.r / 2
            p.x += Math.sin(this.angle) * 2

            if (p.x > this.W || p.x < 0 || p.y > this.H) {
              if (i % 3 > 0) {
                this.particles[i] = {
                  x: Math.random() * this.W,
                  y: -10,
                  r: p.r,
                  d: p.d,
                }
              } else {
                if (Math.sin(this.angle) > 0) {
                  //Enter fromth
                  this.particles[i] = {
                    x: -5,
                    y: Math.random() * this.H,
                    r: p.r,
                    d: p.d,
                  }
                } else {
                  this.particles[i] = {
                    x: this.W + 5,
                    y: Math.random() * this.H,
                    r: p.r,
                    d: p.d,
                  }
                }
              }
            }
          }
        },
        updateVideoContentOrLabel() {
          axios({
            method: 'get',
            url: baseURL.admApi + '/mis/system/videos/updateVideoContentOrLabel',
            // url: "http://*************:8081/adm-api/system/videos/updateVideoContentOrLabel",
            params: this.queryData,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: top.commonObj.Authorization,
              // Authorization: 'eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NzYzNjY4MDUsImxvZ2luX3VzZXJfa2V5IjoiZWVhNWU1YmMtNTY0Ny00NzIzLWIwN2EtZjBlYTBjM2UyYTgwIn0.5iUM1SGXV-aIp2b3hA2iLvQOnMY8D1ugVXnOmQSW4uc7EKFDcjGaVbCpyYEUxLkm97HM4EuhqfC6svAh8VCFiA',
              ptid: 'PT0001',
            },
          }).then((res) => {
            if (res.data.code == 200) {
              this.$message({
                type: 'success',
                message: '修改成功!',
              })
            }
          })
        },

        handleClose(tag) {
          this.tagLabel.splice(this.tagLabel.indexOf(tag), 1)
          this.queryData.domainCode = null
          this.queryData.description = this.tagLabel.join('，')
          this.updateVideoContentOrLabel()
        },

        showInput() {
          this.inputVisible = true
          // this.$nextTick(() => {
          //     this.$refs.saveTagInput.$refs.input.focus();
          // });
        },

        handleInputConfirm() {
          let inputValue = this.inputValue
          if (inputValue) {
            this.tagLabel.push(inputValue)
            this.queryData.domainCode = null
            this.queryData.description = this.tagLabel.join('，')
            this.updateVideoContentOrLabel()
          }
          this.inputVisible = false
          this.inputValue = ''
        },
        tagChange(value) {
          this.$confirm('是否确认修改?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              // console.log(value);
              let lable = this.optionList.filter((item) => {
                return item.labelName == value[1]
              })
              this.queryData.domainCode = lable[0].lableNumber
              this.queryData.description = null
              this.updateVideoContentOrLabel()
            })
            .catch(() => {
              this.conLabel = this.conLabelTmp
              this.$message({
                type: 'info',
                message: '已取消修改',
              })
            })
        },
        // 视频详情
        getInfoData(item, index) {
          if (item.obj && item.obj.pointList) {
            this.pointList = item.obj.pointList
            if (this.pointList.length > 1) {
              this.titleListShow = true
            }
          }
          this.titleName = item.videoName
          this.videoCode = item.chnCode
          this.queryData.chnCode = item.chnCode
          let status = item.is_online
          this.getInfo(status, index)
        },
        prev() {
          this.pointIndex--
          // console.log(this.pointIndex);
          if (this.pointIndex < 0) {
            this.leftDisabled = true
            this.pointIndex = 0
            return
          } else if (this.pointIndex == 0) {
            this.leftDisabled = true
            this.rightDisabled = false
            this.pointIndex = 0
          } else {
            this.rightDisabled = false
          }
          // this.titleName = this.pointList[this.pointIndex].data.video_name || this.pointList[this.pointIndex].data.name
          this.titleName = this.pointList[this.pointIndex].data.video_name
          this.videoCode =
            this.pointList[this.pointIndex].data.chn_code || this.pointList[this.pointIndex].data.addinfo.chncode
          this.queryData.chnCode = this.videoCode
          let status = this.pointList[this.pointIndex].is_online
          this.getInfo(status)
        },
        next() {
          this.pointIndex++
          // console.log(this.pointIndex);
          if (this.pointIndex > this.pointList.length - 1) {
            this.rightDisabled = true
            this.pointIndex = this.pointList.length - 1
            return
          } else if (this.pointIndex == this.pointList.length - 1) {
            this.rightDisabled = true
            this.leftDisabled = false
            this.pointIndex = this.pointList.length - 1
          } else {
            this.leftDisabled = false
          }
          this.titleName = this.pointList[this.pointIndex].data.video_name
          this.videoCode =
            this.pointList[this.pointIndex].data.chn_code || this.pointList[this.pointIndex].data.addinfo.chncode
          this.queryData.chnCode = this.videoCode
          let status = this.pointList[this.pointIndex].is_online
          this.getInfo(status)
        },
        getInfo(status, index) {
          // let code = status == '离线' ? '12' : this.videoCode
          let code = this.videoCode
          this.create(code, index)
          // $api('csdnsy_gis06', {
          //   code: this.videoCode,
          // }).then((res) => {
          //   // console.log("视频详情============", res);
          //   let arr = Object.keys(res[0]).map((ele) => {
          //     return {
          //       name: ele,
          //       value: res[0][ele],
          //     }
          //   })
          //   this.dataList = arr.filter((item) => {
          //     return item.name != '类型名称' && item.name != '报送时间' && item.name != '视频内容'
          //   })
          // })
          // $api('xxwh_bqcx_name', {
          //   chnCode: this.videoCode,
          // }).then((res) => {
          //   this.conLabelTmp = res[0].lableName.split(',').slice(0, 2)
          //   this.conLabel = this.conLabelTmp
          //   this.tagLabel = res[0].description == '无' ? [] : res[0].description.split('，')
          // })
          // this.getTreeData()
        },
        getTreeData() {
          axios({
            method: 'get',
            url: baseURL.admApi + '/mis/system/lables/listNonleaf',
            params: {
              pageNum: 1,
              pageSize: 10,
            },
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: top.commonObj.Authorization,
              // Authorization: 'eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2NzYzNjY4MDUsImxvZ2luX3VzZXJfa2V5IjoiZWVhNWU1YmMtNTY0Ny00NzIzLWIwN2EtZjBlYTBjM2UyYTgwIn0.5iUM1SGXV-aIp2b3hA2iLvQOnMY8D1ugVXnOmQSW4uc7EKFDcjGaVbCpyYEUxLkm97HM4EuhqfC6svAh8VCFiA',
              ptid: 'PT0001',
            },
          }).then((res) => {
            // console.log(res.data.rows);
            this.optionList = res.data.rows.filter((item) => {
              return item.labelName != '收藏'
            })
            this.options = this.handleTree(this.optionList, 'lableNumber', 'parentCode')
          })
        },
        handleTree(data, id, parentId, children, rootId) {
          id = id || 'id'
          parentId = parentId || 'parentId'
          children = children || 'children'
          rootId = rootId || 0
          //对源数据深度克隆
          const cloneData = JSON.parse(JSON.stringify(data))
          //循环所有项
          const treeData = cloneData.filter((father) => {
            let branchArr = cloneData.filter((child) => {
              //返回每一项的子级数组
              return father[id] === child[parentId]
            })
            branchArr.length > 0 ? (father.children = branchArr) : ''
            //返回第一层
            return father[parentId] === rootId
          })
          return treeData != '' ? treeData : data
        },
        getHLSVideo(item) {
          this.titleName = item.obj.chn_name
          this.videoCode = item.obj.chn_url
          // console.log(item);
          this.funHLSVideo({ id: `videoBox1`, url: this.videoCode })
          this.dataList = [{ name: '详细地址', value: item.data.address }]
        },

        // 视频
        create(code) {
          // 调用创建控件接口
          // if (!this.isLogin) {
          //     this.$Message.info('正在登陆客户端，请稍等......');
          //     return false;
          // }
          let _this = this

          // let videoObj = [
          //   {
          //     ctrlType: 'playerWin',
          //     ctrlCode: 'video_main_code',
          //     ctrlProperty: {
          //       displayMode: 1,
          //       splitNum: 1,
          //       channelList: [
          //         {
          //           channelId: code,
          //           // channelId:  _this.videoCode,
          //         },
          //       ],
          //     },
          //     visible: true,
          //     domId: 'videoBox_' + index,
          //     dom: document.getElementById('videoBox_' + index),
          //   },
          // ]
          // let videoObj = this.list
          //   .filter((item) => item.chnCode)
          //   .map((item, index) => {
          //     return {
          //       ctrlType: 'playerWin',
          //       ctrlCode: 'video_main_code_' + index,
          //       ctrlProperty: {
          //         displayMode: 1,
          //         splitNum: 1,
          //         channelList: [
          //           {
          //             channelId: item.chnCode,
          //             // channelId:  _this.videoCode,
          //           },
          //         ],
          //       },
          //       visible: true,
          //       domId: 'videoBox_' + index,
          //       dom: document.getElementById('videoBox_' + index),
          //     }
          //   })

          const channelList = this.list
            .filter((item) => item.chnCode)
            .map((item, index) => {
              return {
                channelId: item.chnCode,
                // channelId:  _this.videoCode,
              }
            })
          console.log('channelList', channelList)
          const videoObj = [
            {
              ctrlType: 'playerWin',
              ctrlCode: 'video_main_code_1',
              ctrlProperty: {
                displayMode: 1,
                splitNum: this.videoCount,
                channelList: channelList,
              },
              visible: true,
              domId: 'videobox_all',
              dom: document.getElementById('videobox_all'),
            },
          ]
          console.log('videoObj===>', videoObj)

          setTimeout(function () {
            _this.ws
              .createCtrl(videoObj)
              .then((res) => {
                console.log(res)
              })
              .catch((e) => {
                console.log(e)
              })
            _this.ws.on('createCtrlResult', (res) => {
              console.warn(res)
            })
          }, 2000)
        },
        //HLS视频
        funHLSVideo(params) {
          // console.log(params);
          let { id, url, width = 2538, height = 1400 } = params
          let player = new HlsJsPlayer({
            id: id,
            autoplay: true,
            isLive: true,
            volume: 0,
            url: url,
            poster: '',
            playsinline: true,
            controls: false,
            height: height,
            width: width,
          })
        },
        // 关闭弹窗
        closeMiddleIframe(name) {
          console.log('closeMiddleIframe', name)
          let data = JSON.stringify({
            type: 'closeIframe',
            name: name,
          })
          window.parent.postMessage(data, '*')
          const arr = this.list.map((item, index) => {
            return 'video_main_code_' + index
          })
          top.DHWsInstance.destroyCtrl(arr)
          this.closeIframe()
          if (top.frames['videoManage'].vm.nowClickData != null) {
            top.frames['videoManage'].vm.lableClick2(top.frames['videoManage'].vm.nowClickData)
          }
        },
        closeIframe() {
          window.parent.postMessage(
            JSON.stringify({
              type: 'closeIframe',
              name: 'collect_',
            }),
            '*'
          )
        },
        openCollect() {
          let iframe = {
            type: 'openIframe',
            name: 'collect_',
            src: baseURL.url + '/static/citybrain/csdn/commont/collection_video.html',
            width: '700px',
            height: '900px',
            left: '5500px',
            top: '250px',
            zIndex: '10000',
            argument: {
              status: 'collect_',
              videoCode: this.videoCode,
            },
          }
          top.window.parent.postMessage(JSON.stringify(iframe), '*')
        },
        cancelCollection() {
          this.paramsData.chn_code = this.videoCode
          axios({
            method: 'post',
            url: baseURL.admApi + '/mis/dir/bindingVideo',
            params: this.paramsData,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: top.commonObj.Authorization,
              // Authorization:
              //   'eyJhbGciOiJIUzUxMiJ9.eyJleHAiOjE2Njg4NTE5ODAsImxvZ2luX3VzZXJfa2V5IjoiOTkyMjYyMjgtOTNjNS00NGQ1LWI0MTItNDBlY2Q3OTI0Zjk2In0.GyO1inRykSjHbnSqocuuSAwuTAr6-9kybh9G_fgTbDM1JeA2lWODpsO80OJNkKzetgVteWgZ_p7OOnwiVdibDQ',
              ptid: 'PT0001',
            },
          }).then((res) => {
            this.$message({
              type: 'success',
              message: '取消成功!',
            })
            this.showCollection = 0
          })
        },
      },
    })
  </script>
  <style>
    .head-title {
      height: 46px;
      font-size: 48px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 46px;
    }
    .head-type {
      height: 32px;
      font-size: 32px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      line-height: 32px;
      cursor: pointer;
    }
    .video_default {
      width: 702px;
      height: 466px;
      background: #01091c;
      border: 2px solid #28416f;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      margin-right: 20px;
      margin-bottom: 24px;
    }
    .video_box {
      position: absolute;
      height: 371px;
      width: 700px;
    }
    .video_div {
      display: flex;
      height: 93px;
      line-height: 93px;
      background: #071c3c;
      color: #fff;
      margin-top: 371px;
    }
    .video_title {
      height: 93px;
      line-height: 93px;
      background: #071c3c;
      color: #fff;
      padding-left: 32px;
      text-overflow: ellipsis;
      width: 540px;
      overflow: hidden;
      white-space: nowrap;
    }
  </style>
</html>
