<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>Document</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />

  <script src="/static/citybrain/csdn/Vue/vue.js"></script>
  <script src="../jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <style>
    body {
      margin: 0;
    }

    ul {
      margin: 0;
      padding: 0;
    }

    #cstz3_left1 {
      width: 900px;
      height: 1660px;
      background-color: #092c4e;
      /* padding-top: 20px; */
      overflow: hidden;
      box-sizing: border-box;
    }

    .text {
      color: #fff !important;
      font-size: 55px;
    }

    .djtl_container {
      width: 100%;
      height: 1660px;
      /* overflow-y: scroll; */
      box-sizing: border-box;
    }

    .djtl_item {
      width: 97%;
      height: 400px;
      /* background-color: #132d57; */
      margin: 0 15px;
    }

    .djtl_container::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .djtl_container::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .djtl_item_title {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 32px;
      text-align: center;
      line-height: 46px;
      margin: 25px 0;
    }

    .djtl_item_title .name {
      line-height: 48px;
    }

    .item_small_title {
      color: #fff;
      font-size: 38px;
      text-align: center;
    }

    .djtl_item_content {
      display: flex;
    }

    .right_content,
    .left_content {
      width: 50%;
      height: 330px;
    }

    .echarts_content {
      display: flex;
    }

    .content_line {
      width: 100%;
      margin-top: 11px;
    }

    .bottomBox .item {
      margin-top: 0px;
    }

    .bottomBox {
      /* width: 100%; */
      height: 300px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      padding: 0 10px;
      box-sizing: border-box;
    }

    .number {
      font-size: 32px;
      letter-spacing: 0;
      /*margin-top: 31px;*/
      white-space: nowrap;
      /* font-weight: 800; */
      display: flex;
      align-items: center;
    }

    .unit {
      font-size: 32px;
      margin-top: 10px;
    }

    .name {
      width: fit-content;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      font-family: AdobeHeitiStd-Regular;
      font-size: 38px;
      letter-spacing: 0;
      color: #d6e7f9;
      margin-left: 12px;
      text-align: center;
    }
  </style>
</head>

<body>
  <div id="cstz3_left1">
    <!-- <nav style="padding: 0px 0 0 10px">
        <s-header-title-2 title="党建统领" />
      </nav> -->
    <ul class="djtl_container">
      <li class="djtl_item">
        <div class="djtl_item_title">
          <span class="name"> 一网通办总数 </span>
          <s-num :value="serverNum" color="lg-yellow" :unit="serverUnit" unit-color="white" />
        </div>
        <!-- <div id="chart01" style="width: 100%; height: 330px"></div> -->
        <div style="width: 100%;height: 330px;display: flex;justify-content: space-evenly;align-items: center;">
          <div id="echarts001" style="width: 860px;height: 320px;"></div>
          <!-- <div id="echarts002" style="width: 260px;height: 200px;"></div>
          <div id="echarts003" style="width: 260px;height: 200px;"></div> -->
        </div>
      </li>
      <li class="djtl_item">
        <div class="djtl_item_title">
          <span class="name"> 图书馆总数 </span>
          <s-num value="11" color="lg-yellow" unit="个" unit-color="white" />
        </div>
        <div>
          <div class="content_line">
            <div class="bottomBox">
              <div class="bottom-item" style="display: flex;" v-for="(item,i) in library0" :key="i">
                <img :src="item.pic" style="margin-right: 10px; width: 100px; height: 90px" alt="" />
                <div class="item" style="height: 122px; text-align: center">
                  <div class="name" style="font-size: 32px; margin: 0">
                    {{item.name}}
                  </div>
                  <div class="number s-c-blue-gradient">
                    {{item.num}}
                    <span class="unit">{{item.unit}}</span>
                  </div>
                </div>
              </div>
              <div class="bottom-item" style="display: flex;" v-for="(item,index) in library1" :key="index+11">
                <img :src="item.pic" style="margin-right: 10px; width: 100px; height: 90px" alt="" />
                <div class="item" style="height: 122px; text-align: center">
                  <div class="name" style="font-size: 32px; margin: 0">
                    {{item.name}}
                  </div>
                  <div class="number s-c-blue-gradient">
                    {{item.num}}
                    <span class="unit">{{item.unit}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>
      <li class="djtl_item">
        <div class="djtl_item_title">
          <span class="name"> 酒店、民宿入住率 </span>
          <!-- <s-num value="29069" color="lg-yellow" unit="人" unit-color="white" /> -->
        </div>

        <div class="echarts_content" style="justify-content: center;">
          <div id="charts2" style="width: 450px; height: 285px"></div>
          <div id="charts3" style="width: 450px; height: 285px"></div>
        </div>
      </li>
      <li class="djtl_item">
        <div class="djtl_item_title" style="margin-top: 0;">
          <span class="name"> 今年接待游客情况 </span>
          <!-- <s-num value="5955" color="lg-yellow" unit="人" unit-color="white" /> -->
        </div>
        
        <div class="content_line">
          <div class="bottomBox" style="padding: 0;">
            <div class="bottom-item" style="display: flex;width: 291px; " v-for="(item,i) in scenicspot" :key="i">
              <img :src="`/static/citybrain/djtl/img/szwh/icon00${i+1}.png`" style=" width: 90px; height: 80px" alt="" />
              <div class="item" style="height: 122px; text-align: center">
                <div class="name" style="font-size: 32px; margin: 0">
                  {{item.ymbq}}
                </div>
                <div class="number s-c-blue-gradient">
                  {{item.value}}
                  <span class="unit">{{item.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</body>

</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
  var vm = new Vue({
    el: '#cstz3_left1',
    data: {
      // 一网通办总数
      ywtSum:"",
      ywtUnit:"",

      library0: [
        {
          name: '图书馆藏书',
          num: 0,
          pic: '/static/citybrain/djtl/img/szwh/tsg.png',
          unit: '万册'
        },
        {
          name: '借书证办证',
          num: 0,
          pic: '/static/citybrain/djtl/img/szwh/jsz.png',
          unit: '人'
        },
        {
          name: '借阅人数',
          num: 0,
          pic: '/static/citybrain/djtl/img/szwh/jsr.png',
          unit: '人'
        },
      ],
      library1:[
        {
          name: '文化馆',
          num: 0,
          unit: "个",
          pic: '/static/citybrain/djtl/img/szwh/whcg.png'
        },
        {
          name: '文保单位',
          num: 0,
          unit: "处",
          pic: '/static/citybrain/djtl/img/szwh/wbdw.png',
        },
        {
          name: '博物馆',
          num: 0,
          unit: "家",
          pic: '/static/citybrain/djtl/img/szwh/bwg.png',
        }
      ],
      
      scenicspot:[],
      serverNum: "0",
      serverUnit: "件",    
      showData: [{ name: "网上", value: 37185 }, { name: "掌上", value: 27043 }, { name: "窗口", value: 3380 }],
    },
    mounted() {
      this.initFun()
      //横向柱状图
      // this.getChart01('chart01')
    },
    methods: {
      initFun() {
        let that = this
                     
        $api('yxzl_szwh_left005').then((res) => {
          that.scenicspot=res
        }) 
        // $api('csdnsy_left11').then((res) => {
        //   that.ywtSum=res[0].value
        //   that.ywtUnit=res[0].unit

        //   that.getEcharts("echarts001", res[1].value, '', res[1].ymbq)
        //   that.getEcharts("echarts002", res[2].value, '', res[2].ymbq)
        //   that.getEcharts("echarts003", res[3].value, '', res[3].ymbq)
        // })
          $api("syywtb0830_top12").then(res => {
            let total=0            
            this.showData = res.map((item, i) => {
              total += +item.num
              return { name: item.label, value: Number(item.num), }
            })
            this.serverNum=total.toString()
            this.initCharts6()
          })
            
        $api('yxzl_szwh_left003').then((res) => {
          //酒店
          let num=100-res[0].ysValue
          that.initCharts1('charts2', '/static/citybrain/djtl/img/szwh/002.png', '酒店入住率', res[0].ysValue, num, 80)
         //投诉
          let num1=100-res[1].ysValue
          that.initCharts1('charts3', '/static/citybrain/djtl/img/szwh/004.png', '民宿入住率', res[1].ysValue, num1, 90)
          
        })    
                   
        $api('yxzl_szwh_right003').then((res) => {
          res.forEach((item,index)=>{
            that.library1[index].name=item.mkmc
            that.library1[index].unit=item.sysUnit
            that.library1[index].num=item.sysValue
          })          
        }) 

        $api('yxzl_szwh_right005').then((res) => {
          let data=res.slice(0,3)
          data.forEach((item,index)=>{
            that.library0[index].name=item.ymbq
            that.library0[index].num=item.VALUE
            that.library0[index].unit=item.unit
          })
        })  



      },
      //params 要处理的字符串
      //length 每行显示长度
      getEqualNewlineString (params, length) {
         let text = ''
         let count = Math.ceil(params.length / length) // 向上取整数
         // 一行展示length个
         if (count > 1) {
           for (let z = 1; z <= count; z++) {
             text += params.substr((z - 1) * length, length)
             if (z < count) {
               text += '\n'
             }
           }
         } else {
           text += params.substr(0, length)
         }
         return text
       },

      
      initCharts6() {
          let that = this
          let myChart = echarts.init(document.getElementById("echarts001"));
          // let myChart = echarts.init(document.getElementById("myEcharts3"));
          let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
          let data = this.showData
          let option = {
            color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3', '#009D9D', '#A47905'],

            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}件<br/> {d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '25',
              },
            },
            legend: {
              orient: 'vertical',
              left: '32%',
              top: '5%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 10,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 24,
                    color: '#ffffff',
                    padding: [0, 10, 10, 5]
                  },
                  value: {
                    fontSize: 24,
                    color: '#2CC6F9',
                    // padding: [0, 20, 0, 0]
                  },
                }
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                var p = ((tarValue / total) * 100).toFixed(2)
                // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
                // return '{name|' + name + '}{value|' + tarValue + '件  ' + p + '%}'
                
                // let names=that.getEqualNewlineString(name, 5) 
                // return '{name|' + names + '}{value|' + tarValue + '件}'
                return '{name|' + name + '}\n{value|' + tarValue + '件}'
              },
            },
            graphic: [
              {
                z: 4,
                type: "image",
                id: "logo",
                left: "11.1%",
                top: "24.4%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [-480, -230], //中心点
                scale: [0.8, 0.8], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['15%', '50%'],
                roseType: '',
                itemStyle: {
                  borderRadius: 5,
                },
                label: {
                  show: false,
                },
                data: data,
              },
            ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', param => {
            myChart.getZr().setCursorStyle('default')
          })
        },
      getChart01(id) {
        let myEc = echarts.init(document.getElementById(id))
        var myColor = ['#00e9db', '#00c0e9', '#0096f3', '#33CCFF', '#33FFCC']
        option = {
          // backgroundColor: "#0e2147",
          grid: {
            left: '10%',
            top: '12%',
            right: '10%',
            bottom: '10%',
            containLabel: true,
          },
          xAxis: [
            {
              show: false,
            },
          ],
          yAxis: [
            {
              axisTick: 'none',
              axisLine: 'none',
              offset: '27',
              axisLabel: {
                textStyle: {
                  color: '#ffffff',
                  fontSize: '24',
                },
              },
              data: [
                '南昌转运中心',
                '广州转运中心',
                '杭州转运中心',
                '宁夏转运中心',
                '兰州转运中心',
              ],
            },
            {
              axisTick: 'none',
              axisLine: 'none',
              axisLabel: {
                textStyle: {
                  color: '#ffffff',
                  fontSize: '28',
                },
              },
              data: ['5', '4', '3', '2', '1'],
            },
            {
              name: '分拨延误TOP 10',
              nameGap: '50',
              nameTextStyle: {
                color: '#ffffff',
                fontSize: '24',
              },
              axisLine: {
                lineStyle: {
                  color: 'rgba(0,0,0,0)',
                },
              },
              data: [],
            },
          ],
          series: [
            {
              name: '条',
              type: 'bar',
              yAxisIndex: 0,
              data: [4, 13, 25, 29, 38],
              label: {
                normal: {
                  show: true,
                  position: 'right',
                  textStyle: {
                    color: '#ffffff',
                    fontSize: '20',
                  },
                },
              },
              barWidth: 12,
              itemStyle: {
                normal: {
                  color: function (params) {
                    var num = myColor.length
                    return myColor[params.dataIndex % num]
                  },
                },
              },
              z: 2,
            },
            {
              name: '白框',
              type: 'bar',
              yAxisIndex: 1,
              barGap: '-100%',
              data: [99, 99.5, 99.5, 99.5, 99.5],
              barWidth: 20,
              itemStyle: {
                normal: {
                  color: '#0e2147',
                  barBorderRadius: 5,
                },
              },
              z: 1,
            },
            {
              name: '外框',
              type: 'bar',
              yAxisIndex: 2,
              barGap: '-100%',
              data: [100, 100, 100, 100, 100],
              barWidth: 24,
              itemStyle: {
                normal: {
                  color: function (params) {
                    var num = myColor.length
                    return myColor[params.dataIndex % num]
                  },
                  barBorderRadius: 5,
                },
              },
              z: 0,
            },
            {
              name: '外圆',
              type: 'scatter',
              hoverAnimation: false,
              data: [0, 0, 0, 0, 0],
              yAxisIndex: 2,
              symbolSize: 35,
              itemStyle: {
                normal: {
                  color: function (params) {
                    var num = myColor.length
                    return myColor[params.dataIndex % num]
                  },
                  opacity: 1,
                },
              },
              z: 2,
            },
          ],
        }
        myEc.setOption(option)
        myEc.getZr().on('mousemove', param => {
          myEc.getZr().setCursorStyle('default')
        })
      },

      getEcharts(dom, num, tel1, tel) {
        let myChart = echarts.init(document.getElementById(dom));
        let col = num < 20 ? '#760443' : (num < 60 ? new echarts.graphic.LinearGradient(1, 0, 0, 1, [{
          offset: 1,
          color: '#760443'
        }, {
          offset: 0,
          color: '#12DE83'
        }]) : new echarts.graphic.LinearGradient(1, 0, 0, 1, [{
          offset: 1,
          color: '#760443'
        }, {
          offset: 0.5,
          color: '#FAD276'
        }, {
          offset: 0,
          color: '#12DE83'
        }]))
        var datas = {
          value: num,
          title: tel1,
        };
        let startAngle = 180, endAngle = 0;
        var fontColor = "#00f6f7";
        var seriesName = "";
        let noramlSize = 16;
        let state = "";
        let center = ["50%", "100%"];
        let wqradius = 0, nqradius = 0, kdradius;
        wqradius = "130%";
        nqradius = "110%";
        kdradius = "130%";
        let min = 0, max = 100;
        let nqColor = [
          [datas.value / 100, col],
        ];
        let wqColor = "rgba(22, 138, 255, 0.9)";
        let circleLineW = 2;
        myChart.setOption({
          title: {//标题
            show: true,
            x: "center",
            top: dom == "echar0001" ? '80%' : '0%',
            text: tel,
            textStyle: {
              fontWeight: "500",
              fontSize: 30,
              color: "#fff",
            },
          },
          series: [
            {
              type: "gauge",
              radius: "140%",
              startAngle,
              endAngle,
              center,
              pointer: {
                show: false,
              },
              // data: dataArr,
              title: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  //   color: "rgb(4, 145, 139)",
                  color: "#fff",
                  width: 2,
                  shadowOffsetX: 0,
                  shadowOffsetY: 0,
                  opacity: 1,
                },
              },
              axisTick: {
                show: true,
                splitNumber: 4,
                length: 8,
                lineStyle: {
                  width: 1,
                  color: "#20c998",
                },
              },
              splitLine: {
                length: 15, //刻度节点线长度
                lineStyle: {
                  width: 2,
                  color: "#20c998",
                }, //刻度节点线
              },
              axisLabel: {
                show: false,
              },
              detail: {
                show: 0,
              },
              animationDuration: 4000,
            },
            {
              name: "白色圈刻度",
              type: "gauge",
              radius: kdradius,
              center,
              startAngle, //刻度起始
              endAngle, //刻度结束
              z: 7,
              splitNumber: 10,
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
                color: fontColor,
                fontSize: noramlSize,
                formatter: "{value}%",
              }, //刻度节点文字颜色
              pointer: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              detail: {
                show: false,
              },
            },
            {
              name: "外层圈", //刻度背景
              type: "gauge",
              z: 2,
              radius: wqradius,
              startAngle,
              endAngle,
              center, //整体的位置设置
              axisLine: {
                // 坐标轴线
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  color: [[1, wqColor]],
                  width: circleLineW,
                  opacity: 1, //刻度背景宽度
                },
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              pointer: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              detail: {
                show: 0,
              },
            },
            {
              name: "指针",
              type: "gauge",
              z: 1,
              radius: "70%",
              startAngle,
              endAngle,
              center, //整体的位置设置
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              min,
              max,
              //指针样式位置
              pointer: {
                show: true,
                width: 4,
                length: "30%",
                offsetCenter: [0, -100],
              },
              itemStyle: {
                normal: {
                  color: wqColor,
                },
              },
              detail: {
                show: true,
                offsetCenter: [0, -25],
                formatter: ["{value|" + datas.value + "}\n" + "{tel|" + datas.title + "}"].join("\n"),
                rich: {
                  value: {
                    fontSize: 30,
                    lineHeight: 50,
                    color: fontColor,
                    fontWeight: "700",
                  },
                  tel: {
                    fontSize: 30,
                    lineHeight: 20,
                    color: '#3EE579',
                    fontWeight: "500",
                  },
                },
              },
              data: [datas.value], //指针位置
            },
            {
              name: "内层盘",
              type: "gauge",
              z: 6,
              radius: nqradius,
              startAngle,
              endAngle,
              center, //整体的位置设置
              axisLine: {
                lineStyle: {
                  // 属性lineStyle控制线条样式//控制外圈位置
                  color: nqColor,
                  width: 15,
                  opacity: 0.9, //控制外圈位置，颜色，宽度，透明度
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              pointer: {
                show: false,
              },
              detail: {
                show: 0,
              },
            },
            {
              name: "内层小环",
              type: "gauge",
              z: 6,
              radius: "90%",
              startAngle,
              endAngle,
              center: center, //整体的位置设置
              axisLine: {
                lineStyle: {
                  // 属性lineStyle控制线条样式//控制外圈位置
                  color: [[1, wqColor]],
                  width: circleLineW,
                  // opacity: 0.9 //控制外圈位置，颜色，宽度，透明度
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              pointer: {
                show: false,
              },
              detail: {
                show: 0,
              },
            },
          ],
        });
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      },
      initCharts1(dom, img, tel, num, num1, imgLeft) {
        let myChart = echarts.init(document.getElementById(dom));
        // 指定图表的配置项和数据
        let option = {
          graphic: {//图形中间图片
            elements: [{
              type: "image",
              style: {
                image: img,//你的图片地址
              },
              left: imgLeft,
              top: 85,
            }],
          },
          tooltip: {
            textStyle: {
              fontSize: 36,
              color: '#fff'
            },
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
            borderWidth: 0,
            trigger: 'item',
            formatter: function (val) {
              let point = '<span style="display:inline-block;margin-right:5px;border-radius:100px;width:24px;height:24px;background-color:#00c0ff"></span>'
              let res = `<div>
                          <div style="margin-top: 10px">${point} 占比 : ${val.percent}%</div>
                      </div>`
              return res
            }
          },
          series: [
            {
              name: tel,
              type: 'pie',
              radius: ['80%', '67%'],
              center: ["30%", "50%"],
              avoidLabelOverlap: false,
              hoverAnimation: false,
              label: {
                normal: {
                  show: true,
                  position: "outside",
                  textStyle: {
                    fontSize: 30,
                    color: "#ffe2b0"
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true,
                  length: 40,
                  length2: 20,
                  lineStyle: {
                    color: '#d2d2d2'
                  }
                },
              },
              data: [
                {
                  value: num,
                  selected: false,
                  itemStyle: {
                    color: "#00c0ff",
                  },
                  label: {
                    normal: {
                      // 是显示标签
                      show: true,
                      position: 'outside',
                      fontSize: 30,
                      // 标签内容格式器，支持字符串模板和回调函数两种形式，字符串模板与回调函数返回的字符串均支持用 \n 换行
                      formatter: function (val) {
                        console.log(val);
                        return val.seriesName + "\n" + val.percent + '%'
                      },
                    }
                  },
                },
                {
                  value: num1,
                  name: "",
                  itemStyle: {
                    color: "#E9EEF4",
                  },
                  label: {
                    normal: {
                      // 是显示标签
                      show: false,
                      position: 'outside',
                      fontSize: 30,
                      // 标签内容格式器，支持字符串模板和回调函数两种形式，字符串模板与回调函数返回的字符串均支持用 \n 换行
                      formatter: '住宿游客 {d}',
                    }
                  },
                },
              ]
            }
          ]
        };
        // 使用刚指定的配置项和数据显示图表。画布为 myCharts  true为显示
        myChart.setOption(option, true);
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      },








    },
  })
</script>