<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>视频监控</title>
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/elementui/css2/index.css" />
  <script src="/elementui/js2/index.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <style>
    body,
    html,
    ul,
    li {
      margin: 0;
      padding: 0;
    }

    [v-cloak] {
      display: none;
    }

    /* 设置滚动条的样式 */
    ::-webkit-scrollbar {
      width: 6px;
    }

    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      border-radius: 5px;
    }

    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(35, 144, 207, 0.4);
    }

    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(27, 146, 215, 0.6);
    }

    .toolbar {
      width: 1700px;
      height: 1300px;
      background: linear-gradient(180deg, rgba(2, 51, 110, .85), rgba(8, 37, 71, .85));
      position: relative;
      color: #fff;
      font-size: 30px;
      background-size: 100% 100%;
      padding: 40px 20px 20px 20px;
      box-sizing: border-box;
    }

    .header {
      height: 50px;
      line-height: 50px;
      font-size: 30px;
      font-weight: bolder;
      padding-left: 25px;
      background: url('/static/citybrain/tckz/img/main_mapIcon/header.svg') no-repeat;
      background-size: 100% 100%;
    }

    .main {
      width: 450px;
      padding: 20px;
      box-sizing: border-box;
      height: 1200px;
      overflow: hidden auto;
    }

    .items-title {
      height: 50px;
      line-height: 50px;
      cursor: pointer;
      /* margin-bottom: 10px; */
    }

    .content {
      display: flex;
      height: calc(100% - 50px);
      width: 100%;
    }

    .detail {
      width: calc(100% - 450px);
      height: 97%;
    }

    .text {
      width: 390px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
    }

    .col-green {
      color: #02ebfa;
    }

    .col-red {
      background-color: #d31515;
    }

    .col-gre {
      background-color: #1de258;
    }

    /* .d_title {
      text-align: center;
    } */
    .videoDetail {
      padding: 20px;
      box-sizing: border-box;
    }

    .about-history {
      position: relative;
      top: -20px;
    }

    .flex-viewport {
      padding: 0 35px;
    }

    .flex-viewport::-webkit-scrollbar {
      width: 3px;
      height: 10px;
    }

    .flex-viewport::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: #146390;
      height: 50px;
    }

    .line {
      position: absolute;
      top: 153px;
      right: 0;
      border-top: 4px solid #1b6e99;
      width: 100%;
    }

    .slides li {
      position: relative;
      width: 196px;
      height: 310px;
      margin-right: 28px;
    }

    .slides li .item {
      position: absolute;
      right: -32px;
      bottom: 0;
      left: -32px;
      min-height: 95px;
      padding: 20px 0 0 0;
      /* background-color: #00c0ff;
      border: 1px solid #00baf8; */
      transition: all 0.2s ease;
    }

    .slides li .item .point {
      position: absolute;
      top: -47px;
      left: 81px;
      width: 15px;
      height: 15px;
      overflow: hidden;
      margin-left: -6px;
      -webkit-border-radius: 100%;
      -moz-border-radius: 100%;
      border-radius: 100%;
      content: '';
    }

    .slides li:nth-child(even) .item {
      top: 0;
      bottom: auto;
      padding: 0 0 20px;
    }

    .slides li:nth-child(even) .item:before {
      top: auto;
      bottom: -49px;
    }

    .slides li:nth-child(even) .item .point {
      top: auto;
      bottom: -47px;
    }

    .slides li .item h3 {
      position: absolute;
      top: 0;
      right: 0;
      height: 90px;
      line-height: 44px;
      margin: 0;
      font-size: 25px;
      font-weight: 400;
      color: #fff;
      text-align: center;
      background-color: #264e72;
      -webkit-transition: all 0.2s ease;
      -moz-transition: all 0.2s ease;
      -ms-transition: all 0.2s ease;
      -o-transition: all 0.2s ease;
      transition: all 0.2s ease;
    }

    .slides li .item h3:before {
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-left: -9px;
      border-width: 0 9px 18px;
      border-style: solid;
      border-color: transparent transparent #264e72;
      content: '';
    }

    .slides li:nth-child(even) .item h3 {
      top: 20px;
      bottom: 0;
    }

    .slides li:nth-child(even) .item h3:before {
      top: 100%;
      bottom: auto;
      border-width: 18px 9px 0;
      border-color: #264e72 transparent transparent;
    }

    .el-input {
      margin: 15px 0;
      font-size: 25px;
    }

    .el-input__inner {
      background: #132c4e;
      border: 1px solid #359cf8;
      font-size: 25px;
      height: 50px;
      color: #fff;
    }

    .el-input__icon {
      line-height: 50px;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 40px;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <div class="toolbar" id="toolbar">
      <div class="header" id="header">
        <span>视频监控</span>
      </div>
      <div class="content">
        <div class="main">
          <el-input v-model="searchValue" @keyup.enter.native="initApi" placeholder="请输入关键字"
            prefix-icon="el-icon-search" clearable @clear="setEmpty"></el-input>
          <div style="margin-bottom: 20px;" v-for="(item,index) in videoList" :key="index">
            <div class="items-title">
              <span class="text" @click="showDetail(index,item)">
                <span class="col-green" style="margin-right: 5px;">[{{item.index}}]</span>
                <span :class="selectId==index?'col-green':''" :title="item.title">{{item.title}}</span>
              </span>
            </div>
          </div>
        </div>
        <div class="detail">
          <div id="videoDom" style="width: 1200px; height: 700px;"></div>
          <div class="videoDetail" v-show="logData.length!=0">
            <p class="d_title">视频名称：{{vodeoTitle}}</p>
            <p class="d_title">历史状态：</p>
            <div class="about-history">
              <div class="flex-viewport" style="overflow-x: auto; position: relative;">
                <ul class="slides clearfix list" style="width: max-content;position: relative;">
                  <div class="line"></div>
                  <li style="width: 100px; float: left; display: block" v-for="(item, index) in logData">
                    <div class="item">
                      <h3>{{item.video_time}}</h3>
                      <div class="point" :class="item.is_open==1?'col-gre':'col-red'"></div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  const DHWsInstance = DHWs.getInstance({
    reConnectCount: 2,
    connectionTimeout: 30 * 1000,
    messageEvents: {
      loginState() {
        console.log('aaaa')
      },
    },
  })
  var video = new Vue({
    el: '#app',
    data() {
      return {
        videoList: [],
        searchValue: null,
        ws: DHWsInstance,
        isLogin: false,
        ctrlCode: 'video',
        selectId: -1,
        vodeoTitle: null,
        logData: [],
        // activities: [
        //   {
        //     content: '支持使用图标',
        //     timestamp: '2018-04-12 20:46',
        //     size: 'large',
        //     type: 'primary',
        //     icon: 'el-icon-more'
        //   }, {
        //     content: '支持自定义颜色',
        //     timestamp: '2018-04-03 20:46',
        //     color: '#0bbd87'
        //   }, {
        //     content: '支持自定义尺寸',
        //     timestamp: '2018-04-03 20:46',
        //     size: 'large'
        //   }, {
        //     content: '默认样式的节点',
        //     timestamp: '2018-04-03 20:46'
        //   }
        // ],
      }
    },
    computed: {

    },
    created() {
      this.login()
    },
    mounted() {
      this.initApi();
    },
    methods: {
      initApi() {
        $api('/yybz_video_lb', {
          searchValue: this.searchValue,
        }).then((res) => {
          this.videoList = res.map((el, index) => {
            let str = {
              channelId: el.chn_code,
              title: el.chn_name,
              index: index + 1,
            }
            return str
          })
          console.log(this.videoList);
        })
      },
      setEmpty() {
        this.searchValue = null
        this.initApi()
      },
      showDetail(i, val) {
        this.selectId = i
        console.log(val);
        this.queryLog(val.channelId)
        this.realTimeVideo(val.channelId)
        this.vodeoTitle = val.title
      },
      queryLog(id) {
        $api('/yy_video_url_log', { chn_code: id, }).then((res) => {
          this.logData = res
        })
      },

      //登录
      login() {
        this.ws.detectConnectQt().then((res) => {
          if (res) {
            // 连接客户端成功
            this.ws.login({
              loginIp: '*************',
              loginPort: '7902',
              userName: 'csdn',
              userPwd: 'Jhcsdn2024$',
              token: '',
              https: 1,
            })
            console.log('登录中...')
            this.ws.on('loginState', (res) => {
              this.isLogin = res
              if (res) {
                console.log('登录成功')
                this.create()
              } else {
                console.log('登录失败')
              }
            })
          } else {
            // 连接客户端失败
            console.log('请重新安装客户端')
          }
        })
      },
      logout() {
        this.ws.logout({
          loginIp: '*************',
        })
      },
      //打开视频弹框
      openVideo(id) {
        this.ws.openVideo([id])
      },
      //创建视频窗口
      create() {
        let _this = this
        let videoObj = [
          {
            ctrlType: 'playerWin',
            ctrlCode: _this.ctrlCode,
            ctrlProperty: {
              displayMode: 1,
              splitNum: 1,
              channelList: [
                {
                  channelId: null,
                  // channelId: id,
                },
              ],
            },
            visible: true,
            domId: 'videoDom',
            dom: document.getElementById('videoDom'),
          },
        ]

        setTimeout(function () {
          _this.ws
            .createCtrl(videoObj)
            .then((res) => {
              console.log(res)
            })
            .catch((e) => {
              console.log(e)
            })
          _this.ws.on('createCtrlResult', (res) => {
            console.warn(res)
            _this.showDetail(0, _this.videoList[0])
          })
        }, 2000)
      },
      //切换实时视频
      realTimeVideo(id) { // 调用控件实时播放接口
        if (!this.isLogin) {
          console.log('正在登陆客户端，请稍等......');
          return false;
        }
        if (this.ws.ctrls.length === 0) {
          console.log('请先创建控件！');
        }
        const params = {
          ctrlCode: this.ctrlCode,
          channelIds: [id]
        };
        this.ws.openCtrlPreview(params);
      },

    },
  })
</script>

</html>