@keyframes marquee {
  0% {
    left: 2000px;
  }

  100% {
    left: -1000px;
  }
}

@keyframes marqueeT {
  0% {
    left: -1700px;
  }

  100% {
    left: 3000px;
  }
}
[v-cloak] {
  display: none;
}

.opacity-5 {
  opacity: 0.5;
}

.gzw {
  width: 100%;
  padding: 0px 20px;
  padding-right: 0;
  box-sizing: border-box;
}
.gzw-top {
  width: 85%;
  font-size: 32px;
  color: #fff;
  display: flex;
  align-items: center;
  padding-left: 20px;
  box-sizing: border-box;
  justify-content: space-between;
}
.gzw-top > span {
  margin-right: 10px;
}
.item-num {
  margin-left: 20px;
  display: flex;
  align-items: flex-end;
  margin: 10px 0;
}
.item-num > span {
  margin-left: 10px;
}
.gzw-top-left,
.gzw-top-right {
  display: flex;
  align-items: flex-end;
}
.gzw-top-left .tt,
.gzw-top-right .tt {
  margin-bottom: 20px;
}
.gzw-top-left {
  padding-left: 150px;
}
.item-num > div {
  width: 44px;
  height: 52px;
  line-height: 52px;
  font-size: 50px;
  margin: 0 5px;
  padding: 5px;
  text-align: center;
  background-image: url('/static/citybrain/csdn/img/ywt/num-bg.png');
  background-size: 100% 100%;
}
.gzw-bottom {
  width: 100%;
  height: 300px;
  margin-top: 10px;
  display: flex;
}
.gzw-sp {
  display: flex;
  margin-left: 58px;
}
.gzw-sp-item {
  width: 400px;
  height: 100%;
  font-size: 28px;
  text-align: center;
  color: #fff;
  background-image: url(./img/ywt/gdsp.png);
  background-size: 100% 100%;
}
.gzw-sp-item:first-child {
  margin-right: 50px;
  background-image: url(./img/ywt/xlsp.png);
}
.gzw-img {
  width: 320px;
  height: 230px;
  margin-bottom: 20px;
}
.gzw-img > img {
  width: 100%;
  height: 100%;
}
.gzw-item {
  /* flex: 0.98; */
  width: 1070px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  position: relative;
}
.gzw-item-0 {
  display: flex;
  align-content: center;
  font-size: 32px;
  margin-bottom: 20px;
}
.gzw-item-img {
  width: 120px;
  height: 115px;
  margin-right: 10px;
}
.gzw-item-img > img {
  width: 100%;
  height: 100%;
}
.gzw-tiem-name {
  font-size: 32px;
  color: #fff;
}

.red-color {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent;
}
.yel-color {
  background: linear-gradient(to bottom, #ffeccb, #f4f1ff, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.blue-color {
  background-image: linear-gradient(#00e5ff 10%, #ffffff 60%, #00e5ff 10%);
  -webkit-background-clip: text;
  color: transparent;
}
.warn-red {
  background-image: url('./img/ywt/warn-red.png') !important;
}
.warn-orange {
  background-image: url('./img/ywt/warn-orange.png') !important;
}
.warn-yel {
  background-image: url('./img/ywt/warn-yel.png') !important;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.work-class {
  width: 100%;
  height: 530px;
  background-image: url(./img/ywt/btn-bg.png);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 230px 0;
  font-family: SourceHanSansCN-Regular;
  display: flex;
}
.left-btn-active {
  background-image: url('./img/ywt/left-btn-act.png') !important;
}
.left-ul {
  width: 365px;
  height: 100%;
  padding-top: 85px;
  box-sizing: border-box;
}
.left-ul > li {
  width: 100%;
  height: 100px;
  font-size: 34px;
  color: #fff;
  margin-bottom: 30px;
  text-align: center;
  background-image: url('./img/ywt/left-btn.png');
}
.left-ul > li > span:nth-child(2) {
  font-size: 60px;
  font-weight: 700;
}
.left-ul > li > span:nth-child(3) {
  font-size: 32px;
  font-weight: bold;
}
.right-ul {
  width: 474px;
  height: 100%;
  margin-left: 100px;
  padding-top: 20px;
  box-sizing: border-box;
}
.right-ul > li {
  width: 100%;
  height: 148px;
  margin-bottom: 20px;
  font-size: 32px;
  color: #fff;
  text-align: center;
  background-color: #0f2b4d;
  padding-top: 15px;
  box-sizing: border-box;
}
.right-ul > li > span:first-child {
  display: block;
  padding: 0;
}
.right-ul > li > span:nth-child(2) {
  font-size: 60px;
  font-weight: 700;
}
.right-ul > li > span:nth-child(3) {
  font-size: 32px;
  font-weight: bold;
}

.top-text {
  width: 100%;
  height: 60px;
  margin-top: 20px;
  background-image: url('./img/ywt/bg.png');
  background-repeat: no-repeat;
  display: flex;
  justify-content: space-between;
  padding: 0 50px;
  box-sizing: border-box;
}
.top-num {
  width: 80px;
  font-size: 32px;
  color: #fff;
  line-height: 55px;
}
.top-num > span {
  font-size: 60px;
}
.top-lb {
  flex: 0.95;
  font-size: 32px;
  color: #fff;
  margin-right: 100px;
  overflow: hidden;
}
.text-lb {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}
.text-lb > div {
  width: 900px;
  display: flex;
  align-items: center;
  position: absolute;
  left: -900px;
  animation: marquee 15s linear infinite;
  -webkit-animation: marquee 15s linear infinite;
}

.text-lb > div:first-child {
  animation-delay: 0s;
  -webkit-animation-delay: 0s;
}
.text-lb > div:nth-child(2) {
  animation-delay: 5s;
  -webkit-animation-delay: 5s;
}
.text-lb > div:last-child {
  animation-delay: 10s;
  -webkit-animation-delay: 10s;
}
.text-lb > div > span:last-child {
  margin-left: 10px;
}
.top-icon {
  width: 43px;
  height: 39px;
  background-image: url('./img/ywt/top-icon.png');
  background-size: contain;
  background-repeat: no-repeat;
}
.top-item {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
}
.topbox {
  /* position: relative; */
  width: 100%;
}

.topbox div {
  position: absolute;
  top: 210px;
  width: 500px;
  height: 220px;
  text-align: center;
  color: #fff;
}

.topbox div span {
  margin: 10px 0;
}

.topbox div p {
  margin: 0;
}

.el-carousel__indicators {
  left: 200px;
  display: flex;
}

.el-carousel__indicator {
  margin: 10px 10px 0 10px;
}

.item-list {
  position: fixed;
  left: 5610px;
  top: 1690px;
  width: 2000px;
  height: 440px;
  font-size: 40px;
  margin: 0 auto;
  color: #fff;
  display: flex;
  justify-content: space-between;
}

.item-list .item {
  width: 450px;
  height: 100%;
  text-align: center;
}

.item-head {
  position: relative;
  left: -5560px;
  top: -230px;
  height: 68px;
  line-height: 68px;
  background-color: #226db3;
}

.item-body {
  width: 100%;
  height: 370px;
  /* background-color: rgb(20, 132, 188); */
}

.carousel {
  position: relative;
  display: flex;
}

.carousel li {
  width: 360px;
  margin-left: 30px;
  list-style-type: none;
}

.active-bar {
  width: 346px;
  height: 5px;
  position: fixed;
  bottom: 100px;
  z-index: 999;
  left: 0;
  background-color: #fff;
}

.el-tabs__active-bar {
  background-color: transparent;
}

.el-tabs__nav-wrap::after {
  background-color: #356596;
}

.el-tabs__item.is-active {
  color: #fff;
}

.el-tabs__item {
  color: #7f93a8;
  font-size: 40px;
  margin: 20px 230px;
}

.app-box {
  overflow: hidden;
  position: relative;
  width: 2045px;
  height: 1850px;
  /* background: url('/img/right-bg.png') no-repeat;
  background-size: contain; */
  /* background: #081b32; */
  padding: 10px 25px 30px;
}

.title-bg {
  background: url('/static/citybrain/csdn/img/cstz_title_bg.png') no-repeat;
  background-size: 100% 100%;
}

.cont-bg {
  width: 992px;
  height: 483px;
  background: url('/static/citybrain/csdn/img/cstz_r_cont_bg.png') no-repeat;
  background-size: 100%;
}

.cont-lable-1 {
  width: 850px;
  height: 62px;
  margin: 50px 0px 0px 70px;
  text-align: center;
  color: #fff;
  font-size: 34px;
  line-height: 62px;
  background: url('/static/citybrain/csdn/img/cstz_r_1.png') no-repeat;
  background-size: 100%;
}

.cont-lable-2 {
  width: 850px;
  height: 62px;
  margin: 50px 0px 0px 70px;
  text-align: center;
  color: #fff;
  font-size: 34px;
  line-height: 62px;
  background: url('/static/citybrain/csdn/img/cstz_r_2.png') no-repeat;
  background-size: 100%;
}

.cstz-yjbg {
  width: 300px;
  height: 276px;
  background: url('/static/citybrain/csdn/img/cstz_r_2780.png') no-repeat;
  background-size: 100%;
}

.line2 {
  width: 2000px;
  height: 200px;
  position: absolute;
  left: 80px;
  top: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitoring {
  display: block;
  margin: 30px auto;
  width: 875px;
  height: 328px;
  border-radius: 12px;
  /* position: absolute;
    top: 0;
    left: 600px; */
}

@-webkit-keyframes rowup {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    -webkit-transform: translate3d(0, -307px, 0);
    transform: translate3d(0, -307px, 0);
  }
}

@keyframes rowup {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    -webkit-transform: translate3d(0, -307px, 0);
    transform: translate3d(0, -307px, 0);
  }
}

.demo-list {
  width: 100%;
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.demo-list .rowup {
  -webkit-animation: 15s rowup linear infinite normal;
  animation: 15s rowup linear infinite normal;
  position: relative;
}

.s-table {
  flex: 1;
  margin-top: 10px;
}

.s-table-head {
  /* width: 1309px; */
  height: 70px;
  background-color: #0e3a65;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 4px;
}

.s-table-head-cell {
  /* flex: 0.169; */
  font-family: SourceHanSansCN-Regular;
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #77b3f1;
  text-align: center;
}

.s-table-body {
  height: 240px;
  overflow-y: auto;
}

.s-table-row {
  height: 61px;
  display: flex;
  margin-bottom: 4px;
  background-color: #0d223d;
  justify-content: center;
  align-items: center;
}

.s-table-body-cell {
  /* flex: 0.169; */
  font-family: SourceHanSansCN-Medium;
  font-size: 30px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #d6e7f9;
  text-align: center;
}

.s-table-body::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 4px;
  /* scrollbar-arrow-color: red; */
}

.s-table-body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

/* 表格自动滚动 */
@keyframes rowUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }
}

/*
        .s-table-row {
          animation: 10s rowUp linear infinite normal;
          -webkit-animation: 10s rowUp linear infinite normal;
        } */

.middle {
  display: flex;
  width: 100%;
  margin-top: 10px;
  height: 240px;
}
.middle-part {
  width: 50%;
}
.part-table {
  width: 100%;
  margin: 10px 0 10px 20px;
  box-sizing: border-box;
}
.part-table-thead {
  width: 928px;
  height: 100px;
  background: #00396f;
  font-size: 40px;
  color: #77b3f1;
  line-height: 100px;
  display: flex;
  text-align: center;
  /* justify-content: space-around; */
}
.part-table-tbody {
  width: 928px;
  height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: center;
}
.part-table-tbody::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.part-table-tbody::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}
.part-table-td {
  width: 928px;
  height: 113px;
  background: #0f2b4d;
  font-size: 30px;
  color: #ffffff;
  line-height: 47px;
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
  padding: 10px 0;
  box-sizing: border-box;
}
.part-table-td :nth-child(4) {
  text-align: center;
}
.container-item {
  background: url('/static/citybrain/csdn/img/ywt/ywt-right-top-bc.png') no-repeat;
  width: 441px;
  height: 228px;
  background-size: 100% 100%;
}

.container-item1 {
  width: 441px;
  height: 228px;
}
.container-item-title {
  margin-top: 20px;
  font-size: 34px;
  font-family: FZZhengHeiS-DB-GB;
  font-weight: 400;
  color: #ffffff;

  background: linear-gradient(180deg, #ffffff 0%, #ffc460 50.244140625%, #ffffff 53.0029296875%, #ffeccb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}
.container-item-subTitle {
  font-size: 30px;
  font-family: PangMenZhengDao;
  font-weight: 400;
  font-style: italic;
  text-align: center;
  color: #ffffff;
  margin-top: 10px;
}
.top-box {
  display: flex;
  justify-content: space-evenly;
  width: 70%;
  margin-left: 300px;
  margin-top: 10px;
}
.top-box-item {
  width: 254px;
  height: 67px;
  background-image: url('/static/citybrain/csdn/img/ywt/doubleclick.png');
  /* background-image: url("./img/ywt/doubleclick.png"); */
  font-size: 32px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 67px;
  text-align: center;
  background-color: transparent;
  border: unset;
}
.btnActive {
  width: 254px;
  height: 67px;
  background-image: url('/static/citybrain/csdn/img/ywt/doubleclick-active.png');
  /* background-image: url("./img/ywt/doubleclick-active.png"); */
  font-size: 32px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 67px;
  text-align: center;
  background-color: transparent;
  border: unset;
}
.top-box-item:focus {
  background-image: url('./img/ywt/doubleclick-active.png');
}

.dwcj-left {
  width: 1900px;
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
}
.dwcj-left p span {
  /* display: block; */
  /* margin-left: 20px; */
  font-size: 28px;
}
.dwcj-left p {
  white-space: nowrap;
  display: inline-block;
  font-size: 28px;
  font-family: FZZhengHeiS-DB-GB;
  font-weight: 700;
  /* color: #d6e7f9;
        background: linear-gradient(
          to bottom,
          #baeeee,
          #ffffff,
          #00f6f7,
          #ffffff
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent; */
}
.dwcj-left-item {
  display: flex;
  align-items: center;
}
.dwcj-right {
  display: flex;
  justify-content: space-around;
  width: 450px;
}
.dwjcy-bottom {
  display: flex;
  height: 166px;
}
.dwcj-right-item {
  font-size: 36px;
  width: 256px;
  height: 143px;
  background-image: url('./img/ywt/dwjc-right-bc.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
}
.dwcj-right-item p {
  font-size: 40px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #ffffff;
  margin: 7px 0;
}
.dwcj-right-item div {
  font-size: 50px;
  font-family: Bebas Neue;
  font-weight: 400;
  color: #9aa9bf;
  font-weight: 700;

  background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.dwcj-right-item div span {
  font-size: 40px;
}
.class-item {
  /* display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 192px;
        height: 234px; */
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('/static/citybrain/csdn//img/ywt/dqjc-bc.png');
}
.class-item-name {
  margin-top: 10px;
  font-size: 32px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  color: #ffffff;
}
.class-item .tooltiptext {
  visibility: visible;
  white-space: nowrap;
  font-size: 24px;
  color: #fff;
  text-align: center;
  /* 定位 */
  position: absolute;
  bottom: -35px;
  z-index: 1;
}
/*.class-item:hover .tooltiptext {
          visibility: visible;
      }*/

.right-img {
  position: absolute;
  right: -5px;
  top: 25%;
  z-index: 999;
}
.left-img {
  position: absolute;
  left: 0px;
  top: 25%;
  z-index: 999;
}
.middle {
  position: relative;
  display: flex;
  margin-bottom: 2px;
  justify-content: space-around;
}
.middle-item {
  cursor: pointer;
  display: flex;
  width: 185px;
  font-size: 34px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  color: #ffffff;

  justify-content: center;
  background-image: url('/static/citybrain/csdn/img/ywt/znmkq.png');
}
.middle-item-black {
  /* cursor: not-allowed; */
  cursor: default;
  color: #b1b1b1;
  background-image: url('/static/citybrain/csdn/img/ywt/znmkq-1.png');
}
.middle-item div {
  margin-top: 72px;
  width: 75%;
  text-align: center;
}
.ig {
  position: absolute;
  top: 100px;
}
.img01 {
  /* left: 387px;*/
  /* left: 488px; */
  left: 310px;
}
.img02 {
  /* left: 790px; */
  /* left: 992px; */
  left: 655px;
}
/* .img03 {
        left: 1090px;
      } */
.img03 {
  /* left: 1200px; */
  left: 993px;
}
.img04 {
  left: 1333px;
}
.img05 {
  left: 1678px;
}
.middle-lb-left,
.middle-lb-right {
  position: absolute;
  width: 41px;
  height: 67px;
  z-index: 999;
}
.middle-lb-left {
  left: 20px;
  top: 95px;
}
.middle-lb-right {
  right: 20px;
  top: 95px;
}
.el-carousel__item.is-animating {
  -webkit-transition: -webkit-transform 3s ease-in-out;
  transition: -webkit-transform 3s ease-in-out;
  transition: transform 3s ease-in-out;
  transition: transform 3s ease-in-out, -webkit-transform 3s ease-in-out;
}
.el-carousel__indicators {
  display: none;
}
.gzw-right {
  /* position: relative; */
  display: flex;
  /* overflow: hidden; */
}
.el-carousel__container {
  height: 260px;
}
.video-text {
  font-size: 28px;
  text-align: center;
  color: white;
}
.bg-video {
  margin-right: 10px;
}
.videoBtn {
  position: relative;
  top: 65px;
  left: 180px;
  color: white;
  cursor: pointer;
}

/*  顶部改版02开始*/
.top-item-02 {
  width: 99%;
  height: 250px;
  margin-bottom: 3px;
  display: flex;
  justify-content: center;
}
.item-02 {
  height: 100%;
  font-size: 32px;
  color: #fff;
  text-align: center;
  margin: 0 30px;
  background-image: url('/static/citybrain/csdn/img/ywt/top-bg-02.png');
  background-size: 100% 100%;
  /* padding: 5px 40px; */
  padding: 5px 40px 5px 43px;
}
ul,
li {
  list-style: none;
}
.text-02 {
  width: 100%;
  text-align: center;
  font-size: 40px;
  margin-top: 10px;
}
.text-02 > img {
  display: block;
  width: 70px;
  margin: 0 auto;
  margin-top: 10px;
}
.ul-02 {
  width: 100%;
  margin-top: 10px;
}
.ul-02 > li {
  width: 100%;
  display: flex;
  /* justify-content: center; */
  align-items: center;
}
.ul-02 > li > img {
  width: 15px;
  height: 15px;
  margin-right: 5px;
}
.num-02 {
  margin-left: 10px;
  font-size: 40px;
}
.ul-02 > li > div {
  min-width: 120px;
  text-align: left;
  background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

/*  顶部改版02样式结束*/
