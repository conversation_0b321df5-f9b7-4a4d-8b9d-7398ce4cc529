* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.container {
  width: 2070px;
  height: 1904px;
  padding: 10px 50px 30px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 100% 100%; */
}

.top-con {
  width: 100%;
  /* height: 60%; */
  display: flex;
}

.bottom-con {
  width: 100%;
  /* height: 40%; */
}

.szsh {
  width: 50%;
  height: 100%;
}

.szwh {
  width: 50%;
  height: 100%;
}

.head {
  width: 100%;
  height: 150px;
  font-size: 50px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 85px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csrk_3840/csrk_v2/img/header.png) no-repeat; */
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-position: -60px;
  background-size: 100% 100%;
  cursor: pointer;
}

.head1 {
  width: 100%;
  height: 150px;
  font-size: 50px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 85px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csrk_3840/csrk_v2/img/header.png) no-repeat; */
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-position: -35px;
  background-size: 100% 100%;
  cursor: pointer;
}

.box {
  width: 100%;
  height: calc(100% - 100px);
  padding: 0px 20px;
  box-sizing: border-box;
}

.wlsq {
  width: 100%;
  height: 500px;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 50px 0; */
  position: relative;
}

.wlsq-img {
  position: absolute;
  top: -50px;
  left: 50px;
  animation: jump 2s infinite;
  height: 493px;
}

.wlsq-t {
  width: 270px;
  text-align: center;
  font-size: 36px;
  font-family: PingFang SC;
  /* font-weight: bolder; */
  color: #ffffff;
  line-height: 60px;
  z-index: 1;
  white-space: pre-line;
}

.wlsq-t:first-child {
  position: absolute;
  top: 60px;
  left: 330px;
}

.wlsq-t:first-child .ys,
.wlsq-t:nth-child(4) .ys {
  font-size: 60px;
  /* font-weight: bold; */
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.wlsq-t:nth-child(2) {
  position: absolute;
  top: 160px;
  left: 16px;
}

.wlsq-t:nth-child(2) .ys,
.wlsq-t:nth-child(3) .ys {
  font-size: 60px;
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.wlsq-t:nth-child(3) {
  position: absolute;
  top: 160px;
  right: 20px;
}

.wlsq-t:nth-child(4) {
  position: absolute;
  top: 270px;
  left: 330px;
}

.wlsq-t-1 {
  /* 370px+126px */
  width: 460px;
  /* width: 650px; */
  height: 121px;
  font-size: 30px;
  font-family: PingFang SC;
  /* font-weight: bolder; */
  color: #ffffff;
  display: flex;
  z-index: 1;
  white-space: pre-line;
  margin-bottom: 24px;
}

.wlsq-t-1:nth-child(7) {
  width: 700px;
}

.wlsq-t-1-img {
  height: 121px;
  width: 126px;
  margin-right: 24px;
}

.wlsq-t-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.yljg {
  width: 100%;
  height: 580px;
  box-sizing: border-box;
  padding-top: 80px;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 30px 0; */
}

.yljg-t:first-child {
  position: absolute;
  top: 115px !important;
  left: 40px !important;
  line-height: 90px;
}

.yljg-t:nth-child(2) {
  position: absolute;
  top: 115px !important;
  left: 657px !important;
  line-height: 90px;
}

.yljg-t:nth-child(1) .ys1 {
  color: #00eaef;
  font-size: 50px;
}

.yljg-t:nth-child(2) .ys1 {
  color: #fba701;
  font-size: 50px;
}

.tlt {
  position: absolute;
  left: 370px;
  bottom: 65px;
  color: #fff;
  font-size: 40px;
}

.wbdw {
  width: 100%;
  height: 459px;
  display: flex;
  justify-content: space-between;
}

.wbdw-t:first-child {
  position: absolute;
  top: 4px;
  left: 122px;
}

.wbdw-t:nth-child(2) {
  position: absolute;
  top: 137px;
  left: -70px;
}

.wbdw-t:nth-child(3) {
  position: absolute;
  top: 140px;
  right: -60px;
}

.wbdw>div:nth-of-type(1) {
  width: 570px;
  height: 100%;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 0px 150px; */
  position: relative;
  /* margin-top: 42px; */
}

.szwh-img {
  position: absolute;
  top: 150px;
  right: 0px;
}

.wbdw>div:nth-of-type(2) {
  width: 475px;
  height: 100%;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat -100px 0px; */
  position: relative;
  margin-top: 50px;
}

.tsg-img {
  position: absolute;
  top: 15px;
  right: -40px;
}

.tsg-t {
  position: absolute;
  left: 120px !important;
  top: 105px !important;
  font-size: 60px;
}

.jq_jd {
  width: 100%;
  height: 476px;
  /* padding-top: 50px; */
  box-sizing: border-box;
}

.jq_jd-back {
  width: 100%;
  height: 170px;
  background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 0px 50px;
  position: relative;
  font-size: 36px;
  color: #fff;
}

.jq_jd-title {
  margin-left: 140px;
}

.col-green {
  font-size: 60px;
  color: #00fffc;
}

.col-blue {
  font-size: 60px;
  color: #3d83e4;
}

.s-c-yellow-gradientNew {
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.zdjg-img {
  position: absolute;
  top: 0px;
  left: 70px;
  /* animation: rotate 10s linear infinite; */
}

.szjj-con {
  display: flex;
  margin-top: -22px;
}

.zdjd {
  position: relative;
  width: 35%;
  height: 540px;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 100px 40px; */
  transform-style: preserve-3d;
}

.trans {
  width: 460px;
  height: 430px;
  position: relative;
}

.zdjd-t:first-child {
  position: absolute;
  top: 70px;
  left: 380px;
  font-size: 36px;
  /*position: absolute;
  top: 23px;
  left: 236px;
  font-size: 36px;
  animation: animX 6.5s cubic-bezier(0.36, 0, 0.64, 1) -3s infinite alternate,
    animY 6.5s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate; */
}

.zdjd-t:nth-child(2) {
  position: absolute;
  top: 70px;
  left: 40px;
  font-size: 36px;
  /* position: absolute;
  top: 316px;
  left: -12px;
  font-size: 36px;
  animation: animX 6.5s cubic-bezier(0.36, 0, 0.64, 1) -7.4s infinite alternate,
    animY 6.5s cubic-bezier(0.36, 0, 0.64, 1) -4.4s infinite alternate; */
}

.zdjd-t:nth-child(3) {
  position: absolute;
  top: 345px;
  right: -30px;
  font-size: 36px;
  /* position: absolute;
  top: 316px;
  right: -12px;
  font-size: 36px;
  animation: animX 6.5s cubic-bezier(0.36, 0, 0.64, 1) -11.8s infinite alternate,
    animY 6.5s cubic-bezier(0.36, 0, 0.64, 1) -8.8s infinite alternate; */
}

.pazs {
  width: 65%;
  height: 540px;
  background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat -60px 420px;
}

.pazs-title {
  font-size: 40px;
  color: #ffffff;
  display: flex;
}

.mr27 {
  margin-right: 27px;
}

/* 动画 */
@keyframes jump {
  0% {
    transform: translate(0px, 0px);
    /*开始位置*/
  }

  50% {
    transform: translate(0px, 20px);
    /* 可配置跳动方向 */
  }

  100% {
    transform: translate(0px, 0px);
    /*结束位置*/
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes animX {
  0% {
    left: -5%;
  }

  100% {
    left: 95%;
  }
}

@keyframes animY {
  0% {
    top: -5%;
  }

  100% {
    top: 95%;
  }
}

@keyframes scale {
  0% {
    transform: scale(0.5);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
}

.tablist {
  width: 957px;
  height: 359px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 101% 95%;
  padding-left: 260px;
  padding-right: 0;
  padding-top: 60px;
  box-sizing: border-box;
  position: relative;
  background-position: 16px 32px;
}

.test {
  width: 80px;
  height: 80px;
  position: absolute;
  left: 103px;
  top: 60px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 100% 100%;
  transform-style: preserve-3d;
  animation-name: rotate0;
  animation-duration: 2s;
  animation-delay: 0;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-fill-mode: backwards;
  /* opacity: 0.6; */
}

.test:nth-child(2) {
  width: 90px;
  height: 90px;
  top: 100px;
  left: 97px;
  animation-name: rotate1;
}

.test:nth-child(3) {
  width: 100px;
  height: 100px;
  top: 140px;
  left: 92px;
  animation-name: rotate1;
}

.test:nth-child(4) {
  width: 120px;
  height: 120px;
  top: 190px;
  left: 82px;
  animation-name: rotate1;
}

.test:nth-child(5) {
  width: 160px;
  height: 160px;
  top: 240px;
  left: 62px;
  animation-name: rotate2;
}

@keyframes rotate0 {
  0% {
    transform: rotateX(246deg) rotateZ(360deg);
  }

  100% {
    transform: rotateX(246deg) rotateZ(0deg);
  }
}

@keyframes rotate1 {
  0% {
    transform: rotateX(254deg) rotateZ(360deg);
  }

  100% {
    transform: rotateX(254deg) rotateZ(0deg);
  }
}

@keyframes rotate2 {
  0% {
    transform: rotateX(257deg) rotateZ(360deg);
  }

  100% {
    transform: rotateX(257deg) rotateZ(0deg);
  }
}

.table-head {
  width: 96%;
  font-size: 32px;
  color: #fff;
  display: flex;
  justify-content: space-between;
}

.table-head span,
.tablist li span {
  /* width: 300px; */
  text-align: center;
  /* display: block; */
}

.tablist li {
  width: 96%;
  height: 90px;
  line-height: 90px;
  font-size: 28px;
  color: #fff;
  margin-top: 6px;
  display: flex;
  list-style: none;
  justify-content: space-between;
}

.tablistspan:nth-child(1) {
  text-align: left;
  width: 270px;
}

.tablistspan:nth-child(2) {
  width: 200px;
  text-align: left;
}

.tablistspan:nth-child(3) {
  /* width: 180px; */
}

.light-line-r {
  position: absolute;
  top: 106px;
  right: 0px;
  width: 698px;
  height: 10px;
}

.right-top-r {
  transform: rotate(0deg) rotateY(180deg);
}

/* 渐变流光效果线条，要将横向宽度设置为超过100%的值，否则无动画效果 */
.line-block {
  position: relative;
  width: 100%;
  height: 6px;
  background: linear-gradient(-90deg,
      #f5d997 1%,
      #f4d48a 4%,
      transparent 12%,
      transparent 16%,
      /* #ffefca 16%,
    #f4c559 19%, */
      transparent 27%,
      transparent 33%,
      #ffefca 33%,
      #f5d17f 36%,
      /* transparent 44%,
    transparent 50%,
    #ffefca 50%,
    #f0cf85 53%,  
    transparent 61%,
    transparent 66%,
    #ffefca 66%,
    #f4cf7a 69%,*/
      /* transparent 77%,
    transparent 84%,
    #ffefca 84%,
    #e6c371 87%, */
      transparent 95%,
      transparent 100%);
  background-size: 200% 100%;
}

/* 指定使用Gradient动画，5s完成一次动画，匀速，无限循环 */
.gradient {
  animation: Gradient 3s linear infinite;
  -webkit-animation: Gradient 3s linear infinite;
  -moz-animation: Gradient 3s linear infinite;
}

/* 定义Gradient动画效果：初始时显示最右端，结束时显示最左端（向右滚动） */
@keyframes Gradient {
  0% {
    background-position: 100% 100%;
  }

  100% {
    background-position: 0% 100%;
  }
}

/* 兼容写法.. */
@-webkit-keyframes Gradient {
  0% {
    background-position: 100% 100%;
  }

  100% {
    background-position: 0% 100%;
  }
}

.right-top-wrapper {
  display: flex;
  width: 778px;
  flex-wrap: wrap;
}

.right-top {
  background: url(/static/citybrain/csdn/img/home/<USER>
  width: 389px;
  height: 110px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;
  box-sizing: border-box;
  font-size: 32px;
  color: #fff;
}

.right-bottom-wrapper {
  display: flex;
  width: 778px;
  margin-top: 40px;
  justify-content: space-evenly;
}

.right-bottom {
  white-space: pre-wrap;
  background: url(/static/citybrain/csdn/img/home/<USER>
  width: 284px;
  height: 251px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  font-size: 32px;
  color: #fff;
  text-align: center;
}

.right-bottom:nth-child(2) {
  background: url(/static/citybrain/csdn/img/home/<USER>
}

.right-bottom-value {
  margin-left: 50px;
}

.szwm-left-wrapper {
  background: url(/static/citybrain/csdn/img/home/<USER>
  height: 507px;
  width: 506px;
}

#piechart {}