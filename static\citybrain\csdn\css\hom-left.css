* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.container {
  width: 2070px;
  height: 1904px;
  padding: 10px 50px 30px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 100% 100%; */
}

.top-con {
  width: 100%;
  height: 56%;
  display: flex;
}

.bottom-con {
  width: 100%;
  height: 44%;
}

.djtl {
  width: 55%;
  height: 100%;
}

.szsh {
  width: 45%;
  height: 100%;
  position: relative;
}

.head {
  width: 100%;
  height: 150px;
  font-size: 50px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 85px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csrk_3840/csrk_v2/img/header.png) no-repeat; */
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-position: -60px;
  background-size: 100% 100%;
  cursor: pointer;
}

.head1 {
  width: 100%;
  height: 150px;
  font-size: 50px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 85px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csrk_3840/csrk_v2/img/header.png) no-repeat; */
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-position: -35px;
  background-size: 100% 100%;
  cursor: pointer;
}

.tj {
  width: 100%;
  height: 35%;
  display: flex;
  padding: 26px 0px;
  box-sizing: border-box;
}

.tj li {
  width: 33%;
  height: 100%;
  list-style: none;
  position: relative;
}

.tj li img {
  width: 190px;
  height: 174px;
}

.jdt {
  width: 100%;
  height: 350px;
  box-sizing: border-box;
}

.tj li:first-child .sp-ys {
  font-size: 48px;
  color: #00fffc;
}

.tj li:nth-child(2) .sp-ys {
  font-size: 48px;
  color: #08a0f5 !important;
}

.tj li:nth-child(3) .sp-ys {
  font-size: 48px;
  color: #ffb436 !important;
}

.jdt-back {
  width: 100%;
  height: 170px;
  background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 40px 50px;
  position: relative;
  font-size: 36px;
  color: #fff;
}

.jdt-title {
  margin-left: 140px;
}

.col-green {
  color: #00fffc;
}

.col-blue {
  color: #3d83e4;
}

.sp {
  font-size: 36px;
  color: #fff;
  position: absolute;
  top: -13px;
  left: 155px;
  font-weight: bolder;
}

.szjj-bg {
  width: 920px;
  height: 406px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 100% 100%;
  /* margin: 89px 0px; */
  margin-top: 205px;
  position: relative;
  color: #fff;
  font-size: 36px;
  text-align: center;
  line-height: 80px;
  list-style: none;
}

.szjj-bg li {
  width: 325px;
}

.szjj-bg li:first-child {
  position: absolute;
  top: -30px;
  left: 0px;
}

.szjj-bg li:nth-child(2) {
  position: absolute;
  top: -26px;
  right: 0px;
}

.szjj-bg li:nth-child(3) {
  position: absolute;
  bottom: -15px;
  left: 0px;
}

.szjj-bg li:nth-child(4) {
  position: absolute;
  bottom: -15px;
  right: 0px;
}

.szjj-bg li:nth-child(5) {
  position: absolute;
  top: 120px;
  right: 308px;
}

.szjj-bg .sz {
  font-size: 60px;
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hh {
  line-height: 45px;
}

.szjj {
  width: 100%;
  height: 90%;
  margin-top: -67px;
}

.tablist {
  width: 1990px;
  height: 658px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 101% 95%;
  padding-left: 441px;
  padding-right: 63px;
  padding-top: 60px;
  box-sizing: border-box;
  position: relative;
  background-position: 16px 32px;
}

.test {
  width: 179px;
  height: 179px;
  position: absolute;
  left: 123px;
  top: 55px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 100% 100%;
  transform-style: preserve-3d;
  animation-name: rotate;
  animation-duration: 2s;
  animation-delay: 0;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-fill-mode: backwards;
  /* opacity: 0.6; */
}

.dian {
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  box-shadow: inset 0 0 18px #fff, inset 6px 0 18px violet, inset -6px 0 18px #0ff, inset 6px 0 30px violet,
    inset -6px 0 30px #0ff, 0 0 18px #fff, -4px 0 18px violet, 4px 0 18px #0ff;
}

.test:nth-child(2) {
  width: 221px;
  height: 221px;
  top: 135px;
  left: 101px;
  animation-name: rotate1;
}

.test:nth-child(3) {
  width: 249px;
  height: 249px;
  top: 217px;
  left: 90px;
  animation-name: rotate1;
}

.test:nth-child(4) {
  width: 270px;
  height: 270px;
  top: 307px;
  left: 80px;
  animation-name: rotate1;
}

.test:nth-child(5) {
  width: 323px;
  height: 323px;
  top: 382px;
  left: 55px;
  animation-name: rotate2;
}

@keyframes rotate {
  0% {
    transform: rotateX(246deg) rotateZ(360deg);
  }

  100% {
    transform: rotateX(246deg) rotateZ(0deg);
  }
}

@keyframes rotate1 {
  0% {
    transform: rotateX(254deg) rotateZ(360deg);
  }

  100% {
    transform: rotateX(254deg) rotateZ(0deg);
  }
}

@keyframes rotate2 {
  0% {
    transform: rotateX(257deg) rotateZ(360deg);
  }

  100% {
    transform: rotateX(257deg) rotateZ(0deg);
  }
}

#gdp-chart {
  width: 100%;
  height: 35%;
}

.table-head {
  width: 96%;
  font-size: 40px;
  color: #fff;
  display: flex;
  justify-content: space-between;
}

.table-head span,
.tablist li span {
  width: 300px;
  text-align: center;
  display: block;
}

.tablist li {
  width: 96%;
  height: 90px;
  line-height: 90px;
  font-size: 36px;
  color: #fff;
  margin-top: 24px;
  display: flex;
  list-style: none;
  justify-content: space-between;
}

b {
  font-weight: normal;
}

.szjj-bg li:nth-child(5) .sz {
  font-size: 40px;
  font-family: DIN;
  font-weight: bold;
  color: #000000;
  line-height: 55px;
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.light-line-r {
  position: absolute;
  top: 130px;
  right: 0px;
  width: 1655px;
  height: 10px;
}

.yg-active {
  border: 7px solid #77e8c6 !important;
}

/* 渐变流光效果线条，要将横向宽度设置为超过100%的值，否则无动画效果 */
.line-block {
  position: relative;
  width: 1665px;
  height: 6px;
  background: linear-gradient(-90deg,
      #f5d997 1%,
      #f4d48a 4%,
      transparent 12%,
      transparent 16%,
      /* #ffefca 16%,
    #f4c559 19%, */
      transparent 27%,
      transparent 33%,
      #ffefca 33%,
      #f5d17f 36%,
      /* transparent 44%,
    transparent 50%,
    #ffefca 50%,
    #f0cf85 53%,  
    transparent 61%,
    transparent 66%,
    #ffefca 66%,
    #f4cf7a 69%,*/
      /* transparent 77%,
    transparent 84%,
    #ffefca 84%,
    #e6c371 87%, */
      transparent 95%,
      transparent 100%);
  background-size: 200% 100%;
}

/* 指定使用Gradient动画，5s完成一次动画，匀速，无限循环 */
.gradient {
  animation: Gradient 3s linear infinite;
  -webkit-animation: Gradient 3s linear infinite;
  -moz-animation: Gradient 3s linear infinite;
}

/* 定义Gradient动画效果：初始时显示最右端，结束时显示最左端（向右滚动） */
@keyframes Gradient {
  0% {
    background-position: 100% 100%;
  }

  100% {
    background-position: 0% 100%;
  }
}

/* 兼容写法.. */
@-webkit-keyframes Gradient {
  0% {
    background-position: 100% 100%;
  }

  100% {
    background-position: 0% 100%;
  }
}

.right-top-r {
  transform: rotate(0deg) rotateY(180deg);
}

#myCanavas {
  position: absolute;
  left: 242px;
  top: 356px;
}

/* 绘制雷达外圈 */
.radar {
  /* 隐藏多余部分 */
  overflow: hidden;
  position: relative;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  border: 1px solid #00fffc;
  /* 避免出现边框问题 */
  box-sizing: border-box;
  opacity: 0.3;
}

/* 绘制雷达中间十字线的竖线 */
.radar::before {
  width: 200px;
  height: 400px;
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  box-sizing: border-box;
  border-right: 1px solid #00fffc;
}

/* 绘制雷达中间十字线的横线 */
.radar::after {
  width: 400px;
  height: 200px;
  content: '';
  display: block;
  box-sizing: border-box;
  border-bottom: 1px solid #00fffc;
}

/* 绘制雷达内圈 */
.fan {
  position: absolute;
  top: 50%;
  left: 50%;
  /* 居中 */
  transform: translate(-50%, -50%);
  border-radius: 50%;
  box-sizing: border-box;
  border: 1px solid #00fffc;
  width: 200px;
  height: 200px;
}

/* 绘制雷达扫描的扇形 */
.fan::after {
  content: '';
  width: 200px;
  height: 200px;
  display: block;
  box-sizing: border-box;
  position: relative;
  top: -50%;
  right: -50%;
  /* 移动旋转原点 */
  transform-origin: 0% 100%;
  border-bottom: 3px solid transparent;
  border-image: linear-gradient(to right, transparent, #00fffc);
  /* border-image-slice: 3; */
  background: transparent;
  background-image: linear-gradient(to right, transparent, #00fffc);
  animation: rotateAnimate 2s linear infinite;
}

/* 动画 */
@keyframes rotateAnimate {
  from {
    transform: rotate(0deg);
    /* transform: rotate(0deg) skew(-10deg); */
  }

  to {
    transform: rotate(360deg);
    /* transform: rotate(360deg) skew(-10deg); */
  }
}

.djtl .gll-navs {
  width: 960px;
  height: 500px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: cover;
  box-sizing: border-box;
  padding: 15px 28px;
}

.djtl .gll-navs .gll-nav-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.djtl .gll-navs .gll-nav-item:not(:last-child) {
  margin-bottom: 27px;
}

.djtl .gll-navs .gll-nav-item img {
  width: 127px;
  height: auto;
}

.djtl .gll-navs .gll-nav-item .item {
  position: relative;
  width: 393px;
  padding: 0 30px;
  box-sizing: border-box;
  text-align: center;
}

.djtl .gll-navs .gll-nav-item .item:not(:last-child)::before {
  position: absolute;
  z-index: 300;
  right: 0;
  width: 2px;
  height: 103px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: cover;
  content: '';
}

.djtl .gll-navs .gll-nav-item .item .item-title {
  /* margin-bottom: 16px; */
  font-size: 32px;
  color: #fff;
  text-align: center;
}

.djtl .gll-navs .gll-nav-item .item .item-value .value {
  font-size: 60px;
  color: #3cfdff;
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: Bebas Neue;
}

.djtl .gll-navs .gll-nav-item .item .item-value .unit {
  color: #fff;
  margin-left: 20px;
  font-size: 28px;
}

.djtl .szdw-navs {
  display: flex;
  align-items: center;
  margin-top: 3px;
  width: 960px;
  height: 268px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: cover;
  box-sizing: border-box;
  padding: 15px 28px;
}

.djtl .szdw-navs .szdw-nav-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.djtl .szdw-navs .szdw-nav-item:not(:last-child) {
  margin-bottom: 27px;
}

.djtl .szdw-navs .szdw-nav-item img {
  width: 132px;
  height: auto;
}

.djtl .szdw-navs .szdw-nav-item .item {
  position: relative;
  width: 268px;
  padding: 0 16px;
  box-sizing: border-box;
  text-align: center;
}

.djtl .szdw-navs .szdw-nav-item .item:not(:last-child)::before {
  position: absolute;
  z-index: 300;
  right: 0;
  width: 2px;
  height: 103px;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: cover;
  content: '';
}

.djtl .szdw-navs .szdw-nav-item .item .item-title {
  margin-bottom: 16px;
  font-size: 28px;
  color: #fff;
  text-align: center;
}

.djtl .szdw-navs .szdw-nav-item .item .item-value .value {
  font-size: 60px;
  color: #3cfdff;
  background: linear-gradient(0deg, #ffffff 0%, #0bfffe 99.31640625%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: Bebas Neue;
}

.djtl .szdw-navs .szdw-nav-item .item .item-value .unit {
  color: #fff;
  margin-left: 20px;
  font-size: 28px;
}

.szjj-main {
  position: relative;
  width: 1540px;
  height: 683px;
  margin: 50px auto;
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: cover;
}

.szjj-main .left .szjj-item:nth-child(1) {
  top: 76px;
}

.szjj-main .left .szjj-item:nth-child(2) {
  top: 264px;
}

.szjj-main .left .szjj-item:nth-child(3) {
  top: 445px;
}

.szjj-main .left .szjj-item {
  position: absolute;
  z-index: 100;
  right: 1128px;
  left: -80px;
  text-align: center;
}

.szjj-main .right .szjj-item:nth-child(1) {
  top: 76px;
}

.szjj-main .right .szjj-item:nth-child(2) {
  top: 264px;
}

.szjj-main .right .szjj-item:nth-child(3) {
  top: 445px;
}

.szjj-main .right .szjj-item {
  position: absolute;
  z-index: 100;
  left: 1132px;
  right: -100px;
  text-align: left;
}

.szjj-main .szjj-item .title {
  color: #fff;
  font-size: 40px;
  margin-bottom: 5px;
}

.szjj-main .szjj-item .value div:first-child {
  margin-right: 18px;
}

.szjj-main .left .szjj-item .value {
  display: flex;
  align-items: flex-end;
  justify-content: right;
}

.szjj-main .right .szjj-item .value {
  display: flex;
  align-items: flex-end;
  justify-content: left;
}

.szjj-main .szjj-item .value .rate {
  color: #1bb0f7;
  font-size: 32px;
}

.szjj-main .szjj-item .value .money .money-num {
  font-size: 56px;
  font-family: Bebas Neue;
  /* color: #00ffff; */
  background: linear-gradient(0deg, #ffffff 0%, #0bfffe 99.31640625%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.szjj-main .szjj-item .value .money .money-unit {
  font-size: 32px;
  ont-family: Bebas Neue;
  /* color: #00ffff; */
  background: linear-gradient(0deg, #ffffff 0%, #0bfffe 99.31640625%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}