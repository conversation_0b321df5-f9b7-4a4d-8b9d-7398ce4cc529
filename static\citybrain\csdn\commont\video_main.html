<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>视频详情弹窗</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <style>
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 2160px;
        background-color: #00000065;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .sjzx-middle {
        position: relative;
        /* width: 2168px;
        height: 1617px; */
        width: 3262px;
        height: 1626px;
        background-color: #031827;
        box-shadow: -3px 2px 35px 0px #000000;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
        border-image-slice: 1;
      }
      .head {
        width: 100%;
        height: 100px;
        line-height: 100px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .head > span {
        font-size: 48px !important;
        font-weight: 500;
        color: #fff;
      }

      .img {
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url('/static/citybrain/csdn/img/cstz2-middle/close-hover.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .sjzx-middle-con {
        width: 100%;
        height: calc(100% - 100px);
        padding: 50px;
        box-sizing: border-box;
      }
      .m-con {
        width: 100%;
        height: 100%;
        border: solid 1px #00c0ff;
        background-color: #043755;
      }
      .table-css {
        width: 100%;
        height: 100%;
        font-size: 40px;
        color: #fff;
      }
      .table-th {
        width: 100%;
        height: 100px;
        background-image: linear-gradient(0deg, #00506a 0%, #0097c8 100%);
        border-bottom: solid 1px #00c0ff;
        display: flex;
      }
      .th-css {
        font-size: 48px;
        line-height: 100px;
        text-align: center;
        font-weight: bold;
      }
      .th-css:first-child {
        flex: 1;
        border-right: 1px solid #00c0ff;
      }
      .table-tr {
        width: 100%;
        display: flex;
        height: 100%;
      }
      .tr-left {
        /* width: 1448px; */
        width: 2538px;
      }
      .tr-right {
        width: 620px;
        height: 100%;
        border-left: 1px solid #00c0ff;
      }
      .yjyp-item {
        font-size: 42px;
        margin: 30px 10px;
      }
      #videoBox {
        position: absolute;
        left: 53px;
        top: 153px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="sjzx-middle">
        <div class="head">
          <span>{{titleName}}</span>
          <div class="img" @click="closeMiddleIframe('video_main')"></div>
        </div>
        <div class="sjzx-middle-con">
          <div class="m-con">
            <div class="table-css">
              <div class="table-tr">
                <div class="tr-left">
                  <div id="videoBox" style="width: 2538px; height: 1428px"></div>
                </div>

                <div class="tr-right">
                  <div class="sjzx_middle_right">
                    <div class="sjzx_middle_right_content">
                      <div class="sjzx_middle_right_container">
                        <div class="yjyp-item" v-for="(item,i) in dataList">
                          <span style="color: #2299e2" v-if="item.name!='报送时间'">
                            {{item.name}}：
                            <br />
                            <span class="s-c-grey-light">{{item.value}}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    window.addEventListener('message', function (event) {
      if (Object.prototype.toString.call(event.data) === '[object Object]' && event.data) {
        console.log('video_main=======>', event.data)
        video.getInfoData(event.data)
      }
    })
    var video = new Vue({
      el: '#app',
      data: {
        titleName: '',
        videoCode: null,
        ws: top.DHWsInstance,
        dataList: [{ name: '设备名称', value: '金华西路' }],
      },
      mounted() {},
      methods: {
        // 视频详情
        getInfoData(item) {
          this.titleName = item.name
          this.videoCode = item.addinfo.chncode
          this.create()
          $api('csdnsy_gis02', {
            code: 'video',
            id: item.id,
          }).then((res) => {
            // console.log("视频详情============", res);
            let arr = Object.keys(res[0]).map((ele) => {
              return {
                name: ele,
                value: res[0][ele],
              }
            })
            this.dataList = arr
          })
        },

        // 视频
        create() {
          // 调用创建控件接口
          // if (!this.isLogin) {
          //     this.$Message.info('正在登陆客户端，请稍等......');
          //     return false;
          // }
          let _this = this

          let videoObj = [
            {
              ctrlType: 'playerWin',
              ctrlCode: 'video_main',
              ctrlProperty: {
                displayMode: 1,
                splitNum: 1,
                channelList: [
                  {
                    channelId: _this.videoCode,
                  },
                ],
              },
              visible: true,
              domId: 'videoBox',
              dom: document.getElementById('videoBox'),
            },
          ]
          console.log('videoObj===>', videoObj)

          setTimeout(function () {
            _this.ws
              .createCtrl(videoObj)
              .then((res) => {
                console.log(res)
              })
              .catch((e) => {
                console.log(e)
              })
            _this.ws.on('createCtrlResult', (res) => {
              console.warn(res)
            })
          }, 2000)
        },
        // 关闭弹窗
        closeMiddleIframe(name) {
          let data = JSON.stringify({
            type: 'closeIframe',
            name: name,
          })
          window.parent.postMessage(data, '*')
          top.DHWsInstance.destroyCtrl(['video_main'])
        },
      },
    })
  </script>
</html>
