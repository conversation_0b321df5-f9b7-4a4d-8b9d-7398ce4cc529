<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-08-20 08:47:47
 * @LastEditors: wjb
 * @LastEditTime: 2024-08-20 08:47:47
-->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!--自动将http服务升级到https -->
  <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" /> -->
  <title>centerBot</title>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csdn/Vue/vue.js"></script>

  <script src="../echarts/echarts.min.js"></script>

  <link rel="stylesheet" href="/static/css/sigma.css" />

  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <script src="/static/citybrain/csdn/js/DHWs_es5.js"></script>
  <script src="/static/citybrain/csdn/js/iview.js"></script>
  <script src="/static/citybrain/csdn/js/vue-seamless-scroll.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/jslib/datav.min.vue.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>

  <style>
    * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }

    [v-cloak] {
      display: none;
    }

    body {
      overflow: hidden;
    }

    .center_container {
      position: relative;
      width: 3379px;
      height: 813px;
    }

    .center_bg {
      background: url('/static/citybrain/csdn/img/ywt/center-bc.png') no-repeat 100% 100%;
      background-size: 100%;
    }

    .center_bg2 {
      background: url('/static/citybrain/csdn/img/ywt/center-bc2.png') no-repeat 100% 100%;
      background-size: 100%;
    }

    .mouse-pointer {
      cursor: pointer;
    }

    .mouse-not {
      /*cursor: not-allowed;*/
      cursor: default;
    }

    .title_container {
      display: flex;
      height: 126px;
      align-items: center;
    }

    .title_container div {
      width: 1427px;
      height: 64px;
    }

    .title_container h3 {
      font-size: 40px;
      font-family: Source Han Sans SC;
      font-weight: 700;
      color: #d6e7f9;
      /* margin: 0 22px;
         */
      margin-left: 50px;
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .title_container .left-img {
      margin-left: -40px;

      background: url('/static/citybrain/csdn/img/ywt/center-title-left.png') no-repeat 100% 100%;
    }

    .title_container .right-img {
      margin-left: -40px;

      background: url('/static/citybrain/csdn/img/ywt/center-title-rght.png') no-repeat 100% 100%;
    }

    .content {
      padding: 0 30px;
      margin-top: -24px;
    }

    .content-top {
      display: flex;
      height: 315px;
      width: 100%;
    }

    .sanicon {
      display: inline-block;
      width: 40px;
      height: 27px;
      background: url('/static/citybrain/csdn/img/ywt/三级标题图标.png') no-repeat;
    }

    .content_top_left {
      width: 50%;
    }

    .content_top_right,
    .content_top_middle {
      width: 25%;
    }

    .dwjcy-bottom {
      display: flex;
      height: 166px;
    }

    .btnActive {
      width: 254px;
      height: 67px;
      /* background-image: url('/static/citybrain/csdn/img/ywt/doubleclick-active.png'); */
      /* background-image: url("./img/ywt/doubleclick-active.png"); */
      font-size: 32px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 67px;
      text-align: center;
      background-color: transparent;
      border: unset;
      border-bottom: 2px solid #fff;
    }

    .top-box-item:focus {
      background-image: url('./img/ywt/doubleclick-active.png');
    }

    .top-box {
      display: flex;
      justify-content: space-evenly;
      width: 70%;
      margin-left: 300px;
      margin-top: 10px;
      border-bottom: 2px solid #123c60;
    }

    .top-box-item {
      width: 254px;
      height: 67px;
      /* background-image: url('/static/citybrain/csdn/img/ywt/doubleclick.png'); */
      /* background-image: url("./img/ywt/doubleclick.png"); */
      font-size: 32px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 67px;
      text-align: center;
      background-color: transparent;
      border: unset;
      opacity: 0.6;
    }

    .el-carousel__item.is-animating {
      -webkit-transition: -webkit-transform 0s ease-in-out;
      transition: -webkit-transform 0s ease-in-out;
      transition: transform 0s ease-in-out;
      transition: transform 0s ease-in-out, -webkit-transform 0s ease-in-out;
    }

    .el-carousel__indicators {
      display: none;
    }

    .gzw-right {
      /* position: relative; */
      display: flex;
      /* overflow: hidden; */
    }

    .el-carousel__container {
      height: 250px;
    }

    .dwcj-left {
      width: 1900px;
      display: flex;
      justify-content: space-evenly;
      margin-top: 50px;
    }

    .dwcj-left p span {
      /* display: block; */
      /* margin-left: 20px; */
      font-size: 28px;
    }

    .dwcj-left p {
      white-space: nowrap;
      display: inline-block;
      font-size: 28px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 700;
      /* color: #d6e7f9;
        background: linear-gradient(
          to bottom,
          #baeeee,
          #ffffff,
          #00f6f7,
          #ffffff
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent; */
    }

    .dwcj-left-item {
      display: flex;
      align-items: center;
    }

    .dwcj-right {
      display: flex;
      justify-content: space-around;
      width: 450px;
    }

    .dwjcy-bottom {
      display: flex;
      height: 166px;
    }

    .dwcj-right-item {
      font-size: 36px;
      width: 256px;
      height: 143px;
      background-image: url('./img/ywt/dwjc-right-bc.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
    }

    .dwcj-right-item p {
      font-size: 40px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #ffffff;
      margin: 7px 0;
    }

    .dwcj-right-item div {
      font-size: 50px;
      font-family: Bebas Neue;
      font-weight: 400;
      color: #9aa9bf;
      font-weight: 700;

      background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .dwcj-right-item div span {
      font-size: 40px;
    }

    .item-imgLeft {
      animation: jumpBoxHandler1 2s infinite;
    }

    .item-imgRight {
      animation: jumpBoxHandler2 2s infinite;
    }

    @keyframes jumpBoxHandler1 {
      0% {
        transform: translate(0px, 0px);
        /*开始位置*/
      }

      50% {
        transform: translate(10px, 0px);
        /* 可配置跳动方向 */
      }

      100% {
        transform: translate(0px, 0px);
        /*结束位置*/
      }
    }

    @keyframes jumpBoxHandler2 {
      0% {
        transform: translate(0px, 0px);
        /*开始位置*/
      }

      50% {
        transform: translate(-10px, 0px);
        /* 可配置跳动方向 */
      }

      100% {
        transform: translate(0px, 0px);
        /*结束位置*/
      }
    }

    .ig {
      /* position: absolute; */
      width: 50px;
      height: 40px;
      /* top: 100px; */
    }

    .img01 {
      left: 180px;
    }

    .img02 {
      left: 385px;
    }

    .img03 {
      left: 590px;
    }

    .img04 {
      left: 916px;
    }

    .img05 {
      left: 1154px;
    }

    .middle-lb-left,
    .middle-lb-right {
      position: absolute;
      width: 88px;
      /* height: 67px; */
      z-index: 999;
    }

    .middle-lb-left {
      left: -30px;
      top: 77px;
    }

    .middle-lb-right {
      right: -37px;
      top: 77px;
    }

    .middle-item {
      cursor: pointer;
      display: flex;
      width: 145px;
      font-size: 34px;
      font-family: Source Han Sans SC;
      font-weight: 500;
      color: #ffffff;
      background-size: cover;
      justify-content: center;
      background-image: url('/static/citybrain/csdn/img/ywt/znmkq.png');
      z-index: 9;
    }

    .middle-item-black {
      /* cursor: not-allowed; */
      cursor: default;
      color: #b1b1b1;
      background-image: url('/static/citybrain/csdn/img/ywt/znmkq-1.png');
    }

    .middle-item div {
      margin-top: 57px;
      width: 79%;
      font-size: 28px;
      text-align: center;
    }

    /* .right-img {
        position: absolute;
        right: -5px;
        top: 25%;
        z-index: 999;
      }
      .left-img {
        position: absolute;
        left: 0px;
        top: 25%;
        z-index: 999;
      } */
    .middle {
      position: relative;
      display: flex;
      margin-bottom: 2px;
      justify-content: space-evenly;
      height: 100%;
      padding-top: 25px;
    }

    .el-carousel__container {
      overflow: hidden;
    }

    .content-bottom {
      display: flex;
    }

    .content-bottom>div:nth-child(1) {
      width: 25%;
    }

    .content-bottom>div:nth-child(2) {
      width: 25%;
    }

    .content-bottom>div:nth-child(3) {
      width: 50%;
    }

    .zwy_item {
      display: flex;
      align-items: center;
      width: 390px;
      height: 161px;
      background-size: 100% 100%;
      /* line-height: 120px; */
      padding-left: 170px;
      box-sizing: border-box;
      margin-top: 55px;
      margin-left: 30px;
      padding-right: 20px;
    }

    .wlaq_item {
      width: 185px;
      height: 215px;
      background-image: url('/static/citybrain/csdn/img/ywt/wlaq.png');
      background-size: 100% 100%;
      text-align: center;
    }

    .wlaq_item>div:nth-child(1) {
      margin-top: 24px;
    }

    .wlaq_item>div:nth-child(2) {
      margin-top: 44px;
    }

    .wlaq_item:nth-child(3) {
      cursor: pointer;
    }

    .gzw_left {
      display: flex;
      /* align-items: center; */
      width: 299px;
      margin-top: 50px;
      margin-right: 41px;
      position: relative;
    }

    /* .gzw_left > div {
        height: 300px;
      } */
    .gzw_left img {
      width: 157px;
      height: 157px;
    }

    .gzw_right {
      height: 300px;
      padding-top: 40px;
    }

    .gzw-right {
      display: flex;
      /* margin-top: -50px; */
      height: 300px;
    }

    .gzw-item {
      /* flex: 0.98; */

      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      position: relative;
    }

    .class-item {
      /* display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 192px;
        height: 234px; */
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      background-image: url('/static/citybrain/csdn//img/ywt/item.png');
      background-size: 100% 100%;
    }

    .gzw-item {
      /* flex: 0.98; */
      width: 987px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-content: space-between;
      position: relative;
    }

    .gzw-item-0 {
      display: flex;
      align-content: center;
      font-size: 32px;
      margin-bottom: 20px;
    }

    .gzw-item-img {
      width: 120px;
      height: 115px;
      margin-right: 10px;
    }

    .gzw-item-img>img {
      width: 100%;
      height: 100%;
    }

    .gzw-tiem-name {
      font-size: 32px;
      color: #fff;
    }

    .gzw-sp {
      display: flex;
      margin-left: 58px;
    }

    .gzw-sp-item {
      width: 400px;
      height: 100%;
      font-size: 28px;
      text-align: center;
      color: #fff;
      background-image: url(./img/ywt/gdsp.png);
      background-size: 100% 100%;
    }

    .gzw-sp-item:first-child {
      margin-right: 50px;
      background-image: url(./img/ywt/xlsp.png);
    }

    .gzw-img {
      width: 320px;
      height: 230px;
      margin-bottom: 20px;
    }

    .gzw-img>img {
      width: 100%;
      height: 100%;
    }

    .class-item .tooltiptext {
      visibility: visible;
      white-space: nowrap;
      font-size: 24px;
      color: #fff;
      text-align: center;
      position: absolute;
      bottom: 10px;
      z-index: 1;
    }

    .dwjcy_bottom {
      position: relative;
      width: 90%;
      height: 92px;
      margin: 0 auto;
      background-image: url('/static/citybrain/csdn/img/ywt/dwjcy-bottom.png');
      background-size: 100% 100%;
      margin-top: 90px;
    }

    .dwjcy_item {
      position: absolute;
      top: -76px;
    }

    .dwjcy_bottom {}

    .wlaq_child {
      display: flex;
    }

    .wlaq_child>div {
      display: flex;
      flex-direction: column;
    }

    .item-num {
      margin-left: 20px;
      display: flex;
      align-items: flex-end;
      margin-top: 10px;
    }

    .item-num>span {
      margin-left: 10px;
      font-size: 28px;
    }

    .item-num>div {
      width: 44px;
      height: 64px;
      line-height: 52px;
      font-size: 50px;
      margin: 0 5px;
      padding: 5px;
      text-align: center;
      background-image: url('/static/citybrain/csdn/img/ywt/num-bg.png');
      background-size: 100% 100%;
    }

    .yel-color {
      background: linear-gradient(to bottom, #ffeccb, #f4f1ff, #ffe2b0, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
    }

    .video_item {
      width: 336px;
    }

    .video {
      width: 278px;
      height: 173px;
      /*background-color: #00c0ff;*/
      margin-left: 28px;
      border-radius: 20px;
    }

    .video_bottom {
      width: 336px;
      height: 40px;
      background-image: url(/static/citybrain/csdn/img/ywt/video-bottom.png);
      background-size: 100% 100%;
      position: relative;
      top: -20px;
      left: 8px;
    }

    .video_name {
      color: #fff;
      font-size: 32px;
      text-align: center;
      position: relative;
      top: -16px;
    }

    .gzw-info {
      color: rgb(255, 255, 255);
      position: absolute;
      top: 177px;
      font-size: 28px;
    }

    .item-bck {
      width: 100%;
      height: 40px;
      margin-top: 75px;
      position: absolute;
      display: flex;
      justify-content: space-evenly;
      padding: 0px 50px;
      box-sizing: border-box;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="center_container" v-cloak>
      <div class="title_container">
        <!-- <img
            src="/static/citybrain/csdn/img/ywt/center-title-left.png"
            alt=""
          /> -->
        <div class="left-img"></div>
        <h3 style="cursor: pointer" onclick="top.commonObj.openMenuFun('yyjczx-sy')">
          一体化智能化公共数据平台
          <img src="/static/images/common/header/click-1.png" alt="" />
        </h3>
        <div class="right-img"></div>
        <!-- <img
            src="/static/citybrain/csdn/img/ywt/center-title-right.png"
            alt=""
          /> -->
      </div>

      <div class="content">
        <div class="content-top">
          <div class="content_top_left">
            <s-header-title2 title="数据资源" style="margin: 0 auto" htype="1" :click-flag="true"
              @click="openwinUrl('数据资源')"></s-header-title2>
            <div style="
                  display: flex;
                  justify-content: space-evenly;
                  margin-top: 6px;
                ">
              <!-- :class="[item.name=='安全'?'mouse-pointer':'','wlaq_item']" -->
              <div class="wlaq_item" v-for="(item,index) in leftTopInfo" :key="`wlaq`+index"
                @click="openwinUrl(item.name)">
                <div style="color: #fff; font-size: 40px; white-space: nowrap">
                  {{item.value}}
                  <span style="font-size: 28px">{{item.unit}}</span>
                </div>
                <div style="color: #fff; font-size: 34px">{{item.name}}</div>
                <div style="color: #fff; font-size: 26px; white-space: nowrap; width: 250px; margin-left: -35px">
                  {{item.subname}}
                </div>
              </div>
            </div>
          </div>
          <div class="content_top_middle">
            <s-header-title2 title="智能要素" style="margin: 0 auto" :click-flag="true" :active="true"
              @indexsm="indexInfo('智能要素',znmkIndexData)" style="cursor: pointer; margin: -20px auto 0"
              @click="openwinUrl('智能要素')"></s-header-title2>
            <div style="width: 100%; height: 100%">
              <div class="dwjcy_bottom">
                <div class="dwjcy_item" :class="index===0 ? 'mouse-pointer' : ''" v-for="(item,index) in dwjcyList"
                  :key="`dwjcy`+index" :style="{'left':(20+index*185)+'px'}" @click="openView(index)">
                  <div class="s-c-blue-gradient1" style="font-size: 40px; margin-bottom: -30px; text-align: center">
                    {{item.num}}
                    <span style="font-size: 28px">(个)</span>
                  </div>
                  <img class="breath-light" src="/static/citybrain/csdn/img/ywt/dwjcy-item.png" alt="" />
                  <div style="font-size: 34px; color: #fff; text-align: center; margin-top: 10px">{{item.label}}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="content_top_right">
            <s-header-title2 title="智能模块" :click-flag="true" style="cursor: pointer; margin: 0 auto"
              @click="openwinUrl('智能模块')"></s-header-title2>
            <div class="znmkq-container" style="position: relative; height: 250px">
              <img class="middle-lb-left" src="/static/citybrain/csdn/img/ywt/swiper-left.png" @click="pre" alt="" />
              <img class="middle-lb-right" src="/static/citybrain/csdn/img/ywt/swiper-right.png" @click="next" alt="" />

              <el-carousel indicator-position="outside" :autoplay="false" :interval="10000" ref="zmd_top" arrow="never"
                style="height: 250px">
                <el-carousel-item v-for="(item,index) in newZhinList" :key="`new`+index">
                  <div class="middle">
                    <div class="item-bck">
                      <img v-for="(item,index) in newZhinList[index].length-1" class="img01 ig breath-light"
                        src="/static/citybrain/csdn/img/ywt/znmkq2.png" alt="" />
                    </div>
                    <!--item.sfnb==0外网   item.sfnb==1内部跳转 item.sfnb==2弹窗-->
                    <!-- item.isuse可点击 item.sfnb==0外网-->
                    <div v-for="(item,i) in newZhinList[index]" :key="i" v-if="item.isuse==1 && item.sfnb==0"
                      class="middle-item" @click="openNewPage(item.url)">
                      <div>{{item.znmkmc}}</div>
                    </div>
                    <!-- item.isuse可点击 item.sfnb==1内部跳转
                        onclick="top.commonObj.openMenuFun(item.url)"
                      -->
                    <div v-else-if="item.isuse==1 && item.sfnb!=0" class="middle-item" @click="openPage(item,item.url)">
                      <div>{{item.znmkmc}}</div>
                    </div>
                    <!-- 不可点middle-item-black-->
                    <div v-else-if="item.isuse==0" class="middle-item middle-item-black">
                      <div>{{item.znmkmc}}</div>
                    </div>

                    <!-- <img class="img01 ig breath-light" src="/static/citybrain/csdn/img/ywt/znmkq2.png" alt="" />
                      <img class="img02 ig breath-light" src="/static/citybrain/csdn/img/ywt/znmkq2.png" alt="" />
                      <img class="img03 ig breath-light" src="/static/citybrain/csdn/img/ywt/znmkq2.png" alt="" /> -->
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </div>

        <div class="content-bottom">
          <div class="conten_bottom_2">
            <s-header-title2 title="政务云" style="margin: 0 auto"></s-header-title2>
            <div style="display: flex">
              <div class="zwy_item" v-for="(item,index) in zwyList" :key="index"
                :style="`background-image:url('/static/citybrain/csdn/img/ywt/${item.name}-2.png') ;`">
                <div style="font-size: 32px; color: #fff">
                  {{item.name}}
                  <div style="white-space: nowrap">
                    <span class="s-c-yellow-gradient" style="font-size: 38px">{{item.value}}</span>
                    <span s-c-yellow-gradient style="font-size: 28px">{{item.unit}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="conten_bottom_3">
            <s-header-title2 title="网络安全" :click-flag="true" style="margin: 0 auto; cursor: pointer"
              @click="showInternetDialog()"></s-header-title2>
            <!-- <s-header-title2 title="网络安全" style="margin: 0 auto"></s-header-title2> -->
            <div style="
                  display: flex;

                  justify-content: space-evenly;
                  margin-top: 75px;
                ">
              <div class="wlaq_child" v-for="(item,index) in wlaqList" :key="index">
                <img src="/static/citybrain/csdn/img/ywt/wl.png" alt="" />
                <div>
                  <div class="s-c-blue-gradient1" style="font-size: 50px">
                    {{item.value}}
                    <span style="font-size: 28px">{{item.unit}}</span>
                  </div>
                  <span style="font-size: 32px; color: #fff">{{item.name}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="conten_bottom_4">
            <!-- <s-header-title2
                title="多维感知网"
                htype="1"
                style="margin: 0 auto"
                :click-flag="true"
                onclick="top.commonObj.openMenuFun('gzw-sy')"
              ></s-header-title2> -->
            <s-header-title2 title="多维感知网" htype="1" style="margin: 0 auto"></s-header-title2>
            <div style="display: flex">
              <div class="gzw_left" style="width: 20%; cursor: pointer" @click="openWxyg">
                <img src="/static/citybrain/csdn/img/ywt/wxyg_1.png" alt="" />
                <div style="text-align: center">
                  <div style="color: #fff" class="s-font-32">卫星遥感</div>
                  <div class="s-font-35 s-c-yellow-gradient1" style="font-weight: 600">3类</div>
                  <div class="item-num yel-color">
                    <div v-for="(item,index) in wxygNum" :key="index">
                      <!-- <span class="yel-color">{{item}}</span> -->
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                        class="count-toNum s-c-yellow-gradient"></count-to>
                    </div>
                    <span>颗</span>
                  </div>
                  <div class="gzw-info">成像28万km²</div>
                </div>
              </div>
              <!-- @click="openGzsb" -->
              <div class="gzw_left" style="width: 30%; ">
                <img src="/static/citybrain/csdn/img/ywt/qdgz.png" alt="" />
                <div style="text-align: center">
                  <div style="color: #fff" class="s-font-32">前端感知设备</div>
                  <div class="s-font-35 s-c-yellow-gradient1" style="font-weight: 600">36类</div>
                  <div class="item-num yel-color">
                    <div v-for="(item,index) in qdgzsbNum" :key="index">
                      <!-- <span class="yel-color">{{item}}</span> -->
                      <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                        class="count-toNum s-c-yellow-gradient"></count-to>
                    </div>
                    <span v-if="qdgzsbNum.length>0">个</span>
                  </div>
                </div>
              </div>
              <div class="gzw_right" style="flex: 1; position: relative">
                <div style="display: flex; justify-content: space-between">
                  <div class="video_item">
                    <div class="video">
                      <div id="dom0" style="width: 278px; height: 173px"></div>
                    </div>
                    <div class="video_bottom"></div>
                    <div class="video_name">{{titleList[0]}}</div>
                  </div>
                  <div class="video_item">
                    <div class="video">
                      <div id="dom1" style="width: 278px; height: 173px"></div>
                    </div>
                    <div class="video_bottom"></div>
                    <div class="video_name">{{titleList[1]}}</div>
                  </div>
                  <!-- <div class="video_item">
                      <div class="video">
                        <div id="dom2" style="width: 278px; height: 173px"></div>
                      </div>
                      <div class="video_bottom"></div>
                      <div class="video_name">{{titleList[2]}}</div>
                    </div> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script src="/static/js/home_services/bmjr.js"></script>
<script>
  var vm = new Vue({
    el: '#app',
    data() {
      return {
        qdgzsbNum: '',
        wxygNum: '50',
        tab: 0,

        sfCount: '',
        mxCount: '',
        zsCount: '',
        zjCount: '',

        numList: [83, 26, 35, 44, 25],
        numList1: [126, 38, 56, 32, 16],
        numList3: [43, 56, 26, 30, 51],
        zhinList: [],
        zwyList: [
          {
            name: '综合算力',
            value: 23264,
            unit: '核',
          },
          {
            name: '总储存',
            value: 2265176,
            unit: 'GB',
          },
        ],
        ggsjptList: [
          {
            name: '编目',
            subTitle: '今年新增目录数',
            value: 462,
            unit: '个',
          },
          {
            name: '归集',
            subTitle: '今年归集数据量',
            value: 462.5,
            unit: '亿条',
          },
          {
            name: '治理',
            subTitle: '今年数据治理总量',
            value: 462.5,
            unit: '万条',
          },
          {
            name: '共享',
            subTitle: '今年接口调用总量',
            value: 462.5,
            unit: '万次',
          },
          {
            name: '开放',
            subTitle: '今年新增发放数据集',
            value: 462,
            unit: '个',
          },
          {
            name: '安全',
            subTitle: '今年加密与脱敏',
            value: 462,
            unit: '个',
          },
        ],
        wlaqList: [
          {
            name: '防护应用数量',
            unit: '个',
            value: 2053,
          },
          {
            name: '拦截次数',
            unit: '亿次',
            value: 1.94,
          },
        ],
        wlgzsb: [
          '车辆GPS',
          '水质监测',
          '水量监测',
          '水位监测',
          '土壤墒情',
          '大气质量',
          '雨量监测',
          '桥梁监测',
          '重量计量',
          '位移监测',
          '烟雾监测',
          '温度监测',
          '用电量',
          '电压监测',
          '水压监测',
          '路侧地磁',
          '倾角监测',
          '裂缝监测',
          '路灯监测',
          '垃圾桶',
        ],
        jkspList: [
          '位移监测',
          '电压监测',
          '水压监测',
          '水位监测',
          '土壤墒情',
          '路侧地磁',
          '倾角监测',
          '裂缝监测',
          '路灯监测',
          '垃圾桶',
        ],

        wlgzId: {
          车辆GPS: {
            url: '8aada4a47dbbe931017dc7550a2f22b0',
            name: '公交车',
          },
          水量监测: {
            url: '8aada4a47cc5876a017cc59215cd0005',
            name: '水雨情监测点',
          },
          水质监测: {
            url: '8aada4a47be36b72017be37a466b0006',
            name: '饮用水质传感器',
          },
          水位监测: {
            url: '8aada4a47d123213017d4b114e6c00a0',
            name: '水库大坝监测站',
          },
          土壤墒情: {
            url: '8aada4a47f4d661c017fb5a31eec0017',
            name: '地质灾害监测',
          },
        },

        leftTopInfo: [],

        leftTopInfoText: [
          {
            name: '编目',
            title: '今年新增目录数',
            unit: '个',
          },
          {
            name: '归集',
            title: '今年归集数据量',
            unit: '亿条',
          },
          {
            name: '治理',
            title: '今年数据治理总量',
            unit: '万条',
          },
          {
            name: '共享',
            title: '今年接口调用总量',
            unit: '万次',
          },
          {
            name: '开放',
            title: '今年新增开放数据集',
            unit: '个',
          },
          {
            name: '安全',
            title: '今年新增开放数据集',
            unit: '个',
            num: 55,
          },
        ],
        tabList1: [],
        videoList: [],
        ws: top.DHWsInstance,
        Interval: null,
        titleList: [],
        isWxygShow: false,
        isGzsbShow: false,
        userId: top.commonObj.userId,
        active: false,
        znmkIndexData: []
      }
    },
    created() {
      this.getApi()
    },
    mounted() {
      if (top.vm.middleShow) {
        this.getVideoList()
      }

      // 关闭弹窗
      let _this = this
      window.addEventListener('message', function (event) {
        let info = event.data
        if (info.type == '关闭弹窗') {
          top.commonObj.destroyModalOverlay()
          top.commonObj.funCloseIframe({ name: info.name })
          // template2
          let isTrue =
            window.parent.document.getElementById('template2') ||
            window.parent.document.getElementById('csrkv2_index') ||
            null
          if (isTrue && (info.name != 'template2' || info.name != 'csrkv2_index')) {
            top.vm.middleShow = false
            return
          }
          if (top.frames['csrkv2_index'] && top.frames['zhdd3840']) return
          top.vm.righttcShow = true
          top.vm.middleShow = true
          // top.commonObj.destroyModalOverlay()
        } else if (info.type == '跳转指挥调度') {
          top.vm.middleShow = false
          setTimeout(() => {
            let zhddUrl =
              'https://csdn.dsjj.jinhua.gov.cn:9601/zhddjhaqcsztqd/templates/zhddzx3840SLD/zhdd3840.html?requestSource=screen&yjid=' +
              info.name
            // let zhdd = _this.zhinList.filter((ele) => ele.znmkmc == '指挥调度')
            // _this.openPage(zhdd[0], zhdd[0].url + (zhdd[0].url.includes('?') ? '&yjid=' : '?yjid=') + info.name)
            top.commonObj.openWinHtml('3840', '2160', zhddUrl, 'zhddWin')
          }, 500)
        } else if (info.type == '跳转模块页面') {
          let userId = top.commonObj.userId
          let nickName = top.commonObj.userInfo.nickName
          let deptName = top.commonObj.userInfo.deptName
          let urlStr =
            info.mainMes.url +
            '?userId=' +
            userId +
            '&nickName=' +
            nickName +
            '&token=' +
            top.commonObj.token +
            '&Authorization=' +
            top.commonObj.Authorization
          top.commonObj.openWinHtml(info.mainMes.width, info.mainMes.height, urlStr, info.mainMes.name)
        } else if (info.type == '打开智能模块') {
          _this.openPage(info.obj, info.obj.url)
        }
      })
    },
    computed: {
      dwjcyList() {
        // let arr = [
        //   {
        //     name: "算法",
        //     value: this.sfCount,
        //     unit: "类",
        //   },
        //   {
        //     name: "模型",
        //     value: this.mxCount,
        //     unit: "类",
        //   },
        //   {
        //     name: "知识",
        //     value: this.zsCount,
        //     unit: "个",
        //   },
        //   {
        //     name: "组件",
        //     value: this.zjCount,
        //     unit: "个",
        //   },
        // ];
        return this.tabList1
      },
      newZhinList() {
        let newArr = []
        for (let i = 0; i < this.zhinList.length; i += 4) {
          newArr.push(this.zhinList.slice(i, i + 4))
        }

        return newArr
      },
      newArr() {
        let newArr = []
        for (let i = 0; i < this.wlgzsb.length; i += 10) {
          newArr.push(this.wlgzsb.slice(i, i + 10))
        }

        return newArr
      },
      newArr1() {
        let newArr = []
        for (let i = 0; i < this.jkspList.length; i += 10) {
          newArr.push(this.jkspList.slice(i, i + 10))
        }

        return newArr
      },
    },
    methods: {
      showInternetDialog() {
        // top.vm.righttcShow = false;
        // top.vm.middleShow = false;
        // top.emiter &&
        //   top.emiter.on("beforeCloseIframe", () => {
        //     top.vm.righttcShow = true;
        //     top.vm.middleShow = true;
        //   });
        // top.commonObj.openWinHtml(
        //   "3840",
        //   "2160",
        //   "/static/citybrain/csdn/WLAQ/GZZX.html"
        // );
        bmjr.toOpen('bmjr138')
      },
      //指标接口明细
      indexInfo(indexName, indexData) {
        top.commonObj.openIndexInfoDialog(indexName, indexData)
      },
      openUrl(name) {
        let token = sessionStorage.getItem('token')
        if (name == '物联感知') {
          url = 'https://csdn.dsjj.jinhua.gov.cn:8802/home'
        } else {
          url = 'http://10.45.12.37:8086/?token=' + token
        }
        window.open(url)
      },
      openWxyg() {
        this.isWxygShow = !this.isWxygShow
        top.emiter.emit('isWxygShow', this.isWxygShow)
      },
      openGzsb() {
        this.isGzsbShow = !this.isGzsbShow
        top.emiter.emit('isGzsbShow', this.isGzsbShow)
      },
      openView(index) {
        if (index === 0) {
          // top.commonObj.openWinHtml( '3840', '2160',"http://10.24.160.88:8020/");
          // top.vm.middleShow = false
          // top.emiter.on('showVideo',()=>{
          //   top.vm.middleShow = true
          // })
          // window.open("http://10.24.160.88:8020/", "_blank", "resizable,scrollbars,status");
          //*/ 获取智能要素超市免登链接地址
          let url = ''
          axios({
            method: 'get',
            url: baseURL.url + baseURL.admApi + '/screen/znyscs/link',
            params: { appName: 'znyscs', isuse: 1 },
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: 'Bearer ' + sessionStorage.getItem('Authorization').replaceAll('"', ''),
            },
          }).then((res) => {
            url = res.data.data
            window.open(url)
          })
        }
        //*/
      },
      tabBtn(val) {
        this.tab = val
      },
      openwinUrl(name) {
        console.log(name, 'openwinUrl-name')
        // top.commonObj.openWinHtml("3840","2160", "http://10.24.160.89:7070/runner?project=63098ad84cad1432366b7601")

        if (name == '智能要素') {
          //*/ 如果从基层智治免登进入，则将智能要素跳转链接修改为如下
          if (sessionStorage.getItem('isJczz') === 'true') {
            window.open('http://59.202.175.11:18020/')
          } else {
            top.vm.righttcShow = false
            top.vm.middleShow = false
            top.emiter &&
              top.emiter.once('beforeCloseIframe', () => {
                top.vm.righttcShow = true
                top.vm.middleShow = true
              })
            top.commonObj.openWinHtml('3840', '2160', '/static/citybrain/csdn/dwjcy.html')
            // top.commonObj.openWinHtml('3840', '2160', '/static/citybrain/csdn/bmjr-dialog1.html')
          }
          //*/
        } else if (name == '智能模块') {
          //*/ 如果从基层智治免登进入，则将智能模块跳转链接修改为如下
          if (sessionStorage.getItem('isJczz') === 'true') {
            window.open('http://59.202.175.11:19999/')
          } else {
            top.vm.righttcShow = false
            top.vm.middleShow = false
            // let iframe4 = {
            //   type: 'openIframe',
            //   name: 'template2',
            //   src: '/static/citybrain/template_page/template2.html',
            //   width: '3380px',
            //   height: '1710px',
            //   left: '2150px',
            //   top: '225px',
            //   zIndex: '999',
            // }
            let iframe4 = {
              type: 'openIframe',
              name: 'template2',
              src: '/static/citybrain/template_page/smartModule.html',
              width: '3920px',
              height: '2160px',
              left: '1920px',
              top: '0',
              zIndex: '999',
            }
            top.commonObj.createModalOverlay()
            window.parent.postMessage(JSON.stringify(iframe4), '*')
          }
          //*/
        } else if (name == '数据资源') {
          // bmjr.toOpen('bmjr043')

          // top.vm.middleShow = false
          // top.emiter &&
          //   top.emiter.once('beforeCloseIframe', () => {
          //     top.vm.middleShow = true
          //   })
          // window.open(
          //   'http://idw.jinhua.gov.cn/#/LargeScreen',
          //   '数据资源',
          //   'directories=no, location=no, scrollbars=yes, resizable=yes, height=1080, width=3840, top=540, left=1920'
          // )
          window.open(
            'http://59.202.175.11:18090/',
            '数据资源',
            'directories=no, location=no, scrollbars=yes, resizable=yes, height=1080, width=1920, top=540, left=2880'
          )
          // window.open('http://idw.jinhua.gov.cn/#/LargeScreen')
        } else if (name === '治理') {
          top.vm.middleShow = false
          top.emiter &&
            top.emiter.once('beforeCloseIframe', () => {
              top.vm.middleShow = true
            })
          top.commonObj.openWinHtml('7680', '2160', '/static/citybrain/csdn/commont/ywt-centerBot-zlDialog.html')
        }
        // else if (name == '安全') {
        // window.open(
        //   'http://10.45.12.234/las/a/login',
        //   '安全',
        //   'directories=no, location=no, scrollbars=yes, resizable=yes, height=2160' +
        //     ', width = 7680' +
        //     ', top=0, left=0'
        // )
        // }
      },
      openNewPage(url) {
        window.open(url)
      },
      openPage(item, url) {
        console.log(item)
        let userId = top.commonObj.userId
        let nickName = top.commonObj.userInfo.nickName
        let deptName = top.commonObj.userInfo.deptName
        let urlStr =
          url +
          (url.includes('?') ? '&userId=' : '?userId=') +
          userId +
          '&nickName=' +
          nickName +
          '&token=' +
          top.commonObj.token +
          '&Authorization=' +
          top.commonObj.Authorization
        // 是否是大屏
        localStorage.setItem('isFull', 'ywt')
        if (item.sfnb == 3) {
          top.vm.righttcShow = false
          top.vm.middleShow = false
          let num = Number(item.width)
          let moveLeft = (7680 - num) / 2
          let ddmIframe = {
            type: 'openIframe',
            name: item.iframeName || item.iframename,
            src: urlStr,
            width: item.width + 'px',
            height: item.higth + 'px',
            left: moveLeft + 'px',
            top: '0',
            zIndex: '999',
          }
          top.commonObj.createModalOverlay()
          window.parent.postMessage(JSON.stringify(ddmIframe), '*')
        } else if (item.sfnb == 2) {
          top.vm.righttcShow = false
          top.vm.middleShow = false
          top.emiter &&
            top.emiter.once('beforeCloseIframe', (name) => {
              if (name == undefined) return
              top.vm.righttcShow = true
              top.vm.middleShow = true
              top.DHWsInstance.destroyCtrl([
                'video_main',
                'video_main_code',
                'zhddDom0',
                'zhddDom1',
                'zhddDom2',
                'zhddDom3',
                'spddDom',
                'video_main_shape',
              ])
            })
          top.commonObj.openWinHtml(item.width || '3840', item.higth || '2160', urlStr)
          // top.commonObj.openWinHtml(item.width || '3840', item.higth || '2160', "/static/citybrain/szjc/pages/szjc-index.html") //tpy
        } else if (item.sfnb == 4) {
          let num = Number(item.width)
          let higth = Number(item.higth)
          let moveLeft = (7680 - num) / 2
          let moveHigth = (2160 - higth) / 2
          window.open(
            item.url,
            '婺城区',
            'directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=' +
            item.higth +
            ', width=' +
            item.width +
            ', top=' +
            moveHigth +
            ', left=' +
            moveLeft +
            ''
          )
        } else if (item.sfnb == 5) {
          window.open(item.url)
        } else {
          top.commonObj.openMenuFun(urlStr)
        }
      },
      getQdgzsbNum() {
        $api('/wlgzLeft002').then((res) => {
          this.qdgzsbNum = res[0].device_num.toString()
        })
      },
      getApi() {
        let that = this
        //
        $api('/csdnsydwjc_left11', { code: isTmpData }).then((res) => {
          this.tabList1 = res
          this.znmkIndexData = top.commonObj.filterIndexData(res, 'label')
        })

        // $api('/wlgzLeft002').then((res) => {
        //   this.qdgzsbNum = res[0].device_num.toString()
        // })
        that.getQdgzsbNum()
        $api('/csdnsy_right21', { limit: 12, pagenum: 1, userId: this.userId }).then((res) => {
          this.zhinList = res[0].result
        })
        // $api("/csdnsy_right22", { code: "算法" }).then((res) => {
        //   this.sfCount = res.length;
        // });
        // $api("/csdnsy_right22", { code: "模型" }).then((res) => {
        //   this.mxCount = res.length;
        // });
        // $api("/csdnsy_right22", { code: "知识" }).then((res) => {
        //   this.zsCount = res.length;
        // });
        // $api("/csdnsy_right22", { code: "组件" }).then((res) => {
        //   this.zjCount = res.length;
        // });

        // $api("/khpjLeft111", { code: 1 }).then((res) => {
        //   //$api("/dnyxLeft002").then(res=>{
        //   let result = [];
        //   // res.forEach((item) => {
        //   //   that.leftTopInfoText.forEach((obj) => {
        //   //     if (item.name == obj.name) {
        //   //       console.log(item)
        //   //       result.push({ ...item, text: obj.title, unit: obj.unit })
        //   //     }
        //   //   })
        //   // })
        //   // console.log(result)
        //   // result.push({
        //   //   name: '安全',
        //   //   description: '今年加密与脱敏',
        //   //   unit: '个',
        //   //   num: 462,
        //   // })
        //   that.leftTopInfo = res;
        //   console.log(that.leftTopInfo);
        // });
        $api('/ggsjpt_D11').then((res) => {
          that.leftTopInfo = res.slice(0, 6)
          that.zwyList = res.slice(6, 8)
          that.wlaqList = res.slice(8, 10)
        })
        // let resData = [
        //   {
        //     ID: 1,
        //     EJLB: '新增目录数',
        //     ShuLiang: 5926,
        //     YJLB: '数据编目',
        //     Remark: '全市范围新增的目录数',
        //     unit: '个',
        //   },
        //   {
        //     ID: 2,
        //     EJLB: '归集数据量',
        //     ShuLiang: 137,
        //     YJLB: '数据归集',
        //     Remark: '市本级的现有归集数据总量',
        //     unit: '亿条',
        //   },
        //   {
        //     ID: 3,
        //     EJLB: '数据治理总量',
        //     ShuLiang: 7799,
        //     YJLB: '数据治理',
        //     Remark: '全市范围累计完成数据治理量',
        //     unit: '万条',
        //   },
        //   {
        //     ID: 4,
        //     EJLB: '数据共享接口',
        //     ShuLiang: '1284',
        //     YJLB: '数据共享',
        //     Remark: '全市范围支持数据共享的接口个数',
        //     unit: '万次',
        //   },
        //   {
        //     ID: 5,
        //     EJLB: '新增开放数据集',
        //     ShuLiang: '174',
        //     YJLB: '数据开放',
        //     Remark: '新增开放数据集数量',
        //     unit: '个',
        //   },
        //   {
        //     ID: 6,
        //     EJLB: '今年加密与脱敏数据项',
        //     ShuLiang: 53297,
        //     YJLB: '数据安全',
        //     Remark: '金华市本级加密与脱敏数据项',
        //     unit: '条',
        //   },
        // ]
        // this.leftTopInfo = JSON.parse(
        //   JSON.stringify(resData)
        //     .replace(/EJLB/g, 'description')
        //     .replace(/ShuLiang/g, 'num')
        //     .replace(/YJLB/g, 'name')
        // )

        // this.leftTopInfo =
        // const res0 = await axios({
        //   method: 'get',
        //   url: 'http://dw.jinhua.gov.cn/micoservice-provider/Hwb9CbcSOfcNd2L4.htm',
        // })
        // console.log(res0)
      },
      wlgzpre() {
        this.$refs.wlgz_swper.prev()
      },
      wlgznext() {
        this.$refs.wlgz_swper.next()
      },

      pre() {
        this.$refs.zmd_top.prev()
      },
      next() {
        this.$refs.zmd_top.next()
      },
      getVideoList() {
        let that = this
        $api('/csdn/cstz/cstzRight009', {
          code: 1,
        }).then((res) => {
          that.videoList = res.map((ele, index) => {
            let str = {
              channelId: ele.channelId,
              title: ele.title,
              dom: 'dom' + index,
              num: index,
            }
            return str
          })
          that.videoListShow = that.videoList.slice(0, 2)
          that.videoListShow.forEach((item) => {
            that.titleList.push(item.title)
          })
          that.create()
        })
      },
      create() {
        // 调用创建控件接口
        // if (!this.isLogin) {
        //     this.$Message.info('正在登陆客户端，请稍等......');
        //     return false;
        // }
        let _this = this

        var paramList = []
        console.log(this.videoListShow)
        for (let index = 0; index < 2; index++) {
          //index < 4
          let item = this.videoListShow[index]
          console.log(item)
          paramList[index] = {
            ctrlType: 'playerWin',
            ctrlCode: 'ctrl' + item.num,
            ctrlProperty: {
              displayMode: 1,
              splitNum: 1,
              channelList: [
                {
                  channelId: item.channelId,
                },
              ],
            },
            visible: true,
            domId: item.dom,
            dom: document.getElementById(item.dom),
          }
        }
        console.log('paramList===>', paramList)

        setTimeout(function () {
          _this.ws
            .createCtrl(paramList)
            .then((res) => {
              console.log(res)
            })
            .catch((e) => {
              console.log(e)
            })
          _this.ws.on('createCtrlResult', (res) => {
            console.warn(res)
          })
        }, 2000)
      },
    },
  })

  top.emiter &&
    top.emiter.on('beforeDestroedIframe', () => {
      top.DHWsInstance.destroyCtrl(['ctrl0', 'ctrl1', 'ctrl2'])
    })

  top.emiter &&
    top.emiter.on('leftIframeHide', () => {
      top.vm.righttcShow = false
      top.vm.middleShow = false
    })
  top.emiter &&
    top.emiter.on('leftIframeShow', () => {
      top.vm.righttcShow = true
      top.vm.middleShow = true
    })
</script>

</html>
<style>
  #dom0 {
    position: absolute;
    left: 2173px;
    top: 1330px;
  }

  #dom1 {
    position: absolute;
    /* left: 2561px; */
    left: 2590px;
    top: 1330px;
  }

  /* #dom2 {
    position: absolute;
    left: 2951px;
    top: 1330px;
  } */
</style>