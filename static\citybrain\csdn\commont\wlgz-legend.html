<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/scjg/css/hjbh-right/common.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>

  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .sjzx_middle_left {
      /* position: absolute;
        top: 0;
        left: 48px; */
      width: 550px;
      min-height: 265px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      /* box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56); */
      /* opacity: 0.85; */
      border-radius: 10px;
    }

    .sjzx_middle_title {
      font-size: 36px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #d6e7f9;
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .sjzx_middle_title p {
      margin-top: 10px;
      height: 82px;
      line-height: 83px;
      white-space: nowrap;
    }

    .sjzx_middle_title p:before {
      content: '';
      height: 1px;
      top: -3%;
      position: relative;
      width: 8%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-right: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title p:after {
      content: '';
      top: -3%;
      position: relative;
      width: 8%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-left: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title .before {
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title .after {
      /* display: inline-block; */
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title p .tab {
      cursor: pointer;
    }

    .el-checkbox__input {
      float: right;
      margin-right: 30px;
    }

    .el-tree-node__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;

      color: #c0d6ed;
      line-height: 58px;
    }

    .el-tree-node__content {
      height: 50px !important;
      margin-bottom: 10px;
    }

    .is-focusable {
      background-color: unset;
    }

    .el-checkbox {
      /* position: absolute;
        right: 0; */
      display: block;
      border-radius: 15px;
      margin-bottom: 20px;
      margin-right: 0;
    }

    .el-checkbox-group .el-checkbox:hover {
      background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox-group .is-checked {
      background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;
      /* font-style: italic; */
      color: #c0d6ed;
      line-height: 58px;
    }

    .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 15px;
      background-color: #344d67;
    }

    .auth-tree .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 21px;
      background-color: #344d67;
    }

    .sjzx_middle_left_container {
      padding: 11px 4px 0 15px;
    }

    .checkbox-box-img {
      width: 42px;
      height: 42px;
      position: relative;
      top: 10px;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #252316;
      border-color: #ffc561;
    }

    .el-checkbox__inner::after {
      width: 7px;
      height: 18px;
      left: 10px;
      color: #ffc561 !important;
    }

    .sjzx_middle_right {
      position: absolute;
      left: 2800px;
      top: 0;
      width: 620px;
      height: 1350px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56);
    }

    .sjzx_middle_right_container {
      margin-top: 10px;
      margin-left: 90px;
      border-left: 8px solid #00ffff;
      /* height: 1200px; */
      /* overflow-y: auto; */
    }

    .sjzx_middle_right_content {
      height: 1000px;
      overflow-y: scroll;
    }

    .sjzx_middle_right_content::-webkit-scrollbar {
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .sjzx_middle_right_content::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .btn_right {
      padding: 0 50px;
      min-width: 250px;
      width: auto;
      height: 53px;
      background-image: url('/static/citybrain/djtl/img/sjzx-middle/btn.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      font-size: 24px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-shadow: 0px 2px 5px #000000;
      background-color: transparent;
      border: unset;
      margin-left: 40px;
      margin-bottom: 20px;
    }

    .yjyp-item p {
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      margin-left: 50px;
      line-height: 40px;
    }

    .yjyp-item {
      margin-bottom: 70px;
    }

    .red {
      background: linear-gradient(180deg, #ffffff 0%, #ffcdcd 50.244140625%, #ff4949 53.0029296875%, #ffcdcd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .yellow {
      background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffc460 53.0029296875%, #ffeccb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .blue {
      color: #22e8e8 !important;
    }

    .item-s {
      margin-bottom: 20px;
    }

    .item-time {
      position: relative;
    }

    .item-time::before {
      position: absolute;
      left: -65px;
      content: '';
      display: inline-block;
      width: 51px;
      height: 25px;
      background-image: url('/static/citybrain/djtl/img/sjzx-middle/circle.png');
      background-size: 100% 100%;
    }

    .center_bottom {
      position: absolute;
      top: 1365px;
      left: 45px;

      display: flex;
      justify-content: center;
    }

    .shijian {
      width: 549px;
      height: 263px;
      background: url('/static/citybrain/csdn/img/ywt/dwjc-right-bc.png') no-repeat;
      background-size: 100% 100%;
      margin: 20px auto;
    }

    .shijian #eventMain {
      display: inline-block;
      width: 93.5%;
      margin: 25px;
    }

    .shijian .contain {
      overflow-y: auto;
      height: 239px;
      overflow-x: hidden;
      width: 525px;
    }

    .el-tree {
      background-color: unset;
    }

    .sjzx_middle_left_container {
      height: 485px;
      overflow-y: scroll;
    }

    .shijian .contain::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .shijian .contain::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }
  </style>
</head>

<body>
  <div id="sjzx-middle">
    <!-- 预警状态筛选 -->
    <div class="sjzx_middle_left">
      <div class="sjzx_middle_title">
        <p>
          <span class="before"></span>
          <span class="tab" @click="tabShow=true" :class="tabShow ? 's-c-yellow-gradient' : 's-c-blue-gradient'">
            点位图例
          </span>
          /
          <span class="tab" @click="tabShow=false" :class="!tabShow ? 's-c-yellow-gradient' : 's-c-blue-gradient'">
            设备分类
          </span>
          <span class="after"></span>
        </p>
      </div>
      <div class="sjzx_middle_left_container" v-show="tabShow">
        <el-checkbox-group v-model="jcztSelectList" :min="1">
          <el-checkbox v-for="item in jcztList" :label="`${item.icon}`" :key="item.icon" class="checkbox-box"
            @change="jcztFun">
            <img class="checkbox-box-img" :src="'/static/citybrain/csdn/img/ywt/yg/'+item.icon+'.png'" alt="" />
            <span style="margin-left: 15px">{{item.name}}</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="sjzx_middle_left_container" v-show="!tabShow">
        <el-checkbox-group v-model="sbflSelectList" :min="1">
          <el-checkbox v-for="(item,index) in sbflList" :label="`${item.code}:${item.name}`" :key="index"
            @change="sbflFun" class="checkbox-box">
            <img class="checkbox-box-img"
              :src="'/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/'+item.icon+'.png'" alt="" />
            <span style="margin-left: 15px">{{item.name}}</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</body>

</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: '#sjzx-middle',
    data: {
      modelName: "",//当前菜单名称
      layerArr: ["红wg"],//预警区域多边形id
      jcztSelectList: [],//当前预警复选值
      sbflSelectList: [],
      tabShow: true,
      //预警数据列表
      jcztList: [
        {
          name: "在线",
          code: "zx",
          icon: ""
        },
        {
          name: "离线",
          code: "lx",
          icon: ""
        },
        {
          name: "监测数据",
          code: "jc",
          icon: ""
        },
      ],
      sbflList: [],
      wlgzTitle: "空气质量监测",
      pointAllData: []
    },
    mounted() {
      let that = this;
      window.addEventListener('message', function (e) {
        if (e.data && e.data.status == 'wlgz-legend') {
          that.wlgzTitle = e.data.name
          that.initData(that.wlgzTitle)
          that.getPointData(that.wlgzTitle)
        }
      })
    },

    created() {
      this.initData(this.wlgzTitle)
    },
    methods: {
      initData(name) {
        this.jcztList[0].icon = name
        this.jcztList[1].icon = name + '-lx'
        this.jcztList[2].icon = name + '-jc'
        this.jcztList.forEach((item) => {
          this.jcztSelectList.push(item.icon)
        })
        $get('/wlgzsb').then((res) => {
          console.log(res)
          this.sbflList = res[name]
          console.log(this.sbflList)
        })
      },
      jcztFun(flag, e) {
        let label = e.target._value
        console.log(label)
        //勾选上点
        if (flag) {
          this.filterData(label, this.pointAllData)
        } else {
          this.rmPoint(label)
        }
      },
      sbflFun(flag, e) {
        let label = e.target._value
        console.log(label)
        //勾选上点
        if (flag) {
          // this.filterData(label, this.pointAllData)
        } else {
          this.rmPoint(label)
        }
      },
      //初始化地图，默认上红色预警点位
      getPointData(name) {
        let that = this;
        $api('/wlgz_sbdwjc', { name: name }).then((allRes) => {
          let pointData = []
          let textData = []
          let twoText = []
          allRes.forEach((item, index) => {
            let til = item.type_name === '公交车GPS' ? ['车牌号'] : ['名称']
            item.device_state = item.device_state == "ONLINE" ? "在线" : "离线";
            item.device_type = item.gather_value_list.length > 0 && item.gather_value_list[0].device_data_type ? item.gather_value_list[0].device_data_type : ''
            var keyMap = {
              latitude: 'lat',
              longitude: 'lng',
            }
            for (var key in item) {
              var newKey = keyMap[key]
              if (newKey) {
                item[newKey] = item[key]
                delete item[key]
              }
            }
            pointData.push(item)
          })
          console.log(this.sbflList)
          that.pointAllData = pointData;
          that.filterData(name, pointData)
          that.filterData(name + '-lx', pointData)
          that.filterData(name + '-jc', pointData)
        })
      },
      filterData(name, data) {
        let arr = []
        if (name == this.wlgzTitle) {
          arr = data.filter((i) => {
            return i.device_state == '在线' && i.device_type == ''
          })
        } else if (name == this.wlgzTitle + '-lx') {
          arr = data.filter((i) => {
            return i.device_state == '离线' && i.device_type == ''
          })
        } else {
          arr = data.filter((i) => {
            return i.device_state == '在线' && i.device_type !== ''
          })
        }
        this.pointTextMapFun(name, arr, name)
      },
      // 加载3D文字和地图点位的方法
      pointTextMapFun(icon, pointData, pointId) {
        if (pointData.length > 0) {
          top.mapUtil.flyTo({
            destination: [119.65842342884746,
              28.97890877935061,],
          })
          // top.mapUtil.flyTo({
          //   destination: [pointData[0].lng,
          //   pointData[0].lat],
          // })
          let imgs = baseURL.url + `/static/citybrain/csdn/img/ywt/yg/${icon}.png`
          top.mapUtil.loadPointLayer({
            data: pointData,
            layerid: pointId, //图层id
            iconcfg: { image: imgs, iconSize: 0.3 }, //图标
            // datacfg: datacfg,
            onclick: this.onclickWlgz,
            // popcfg: popCfig,
          })
        }
      },
      onclickWlgz(e) {
        console.log(e)
        if (e.device_type) {
          top.mapUtil.removeLayer("popdialog")
          top.mapUtil.flyTo({
            destination: [e.lng, e.lat],
            // zoom: 15,
            offset: [0, -666]
          })
          let iframe1 = {
            type: 'openIframe',
            name: 'wlgz-dialog',
            src: baseURL.url + '/static/citybrain/csdn/commont/wlgz-dialog.html',
            width: '7680px',
            height: '2160px',
            left: '0',
            top: '0',
            zIndex: '998',
            argument: e,
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
        } else {
          let objData = {
            layerid: "popdialog",
            position: [e.lng, e.lat],
            offset: [55, -40],
            closeButton: true,
            content: `
                                <div
                                  style="
                                    position: relative;
                                    background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                                    background-size: 100% 100%;
                                    width: max-content;
                                    min-height: 250px;
                                  "
                                >
                                  <nav
                                    class="s-flex s-m-l-20 s-m-r-20 s-row-between s-p-b-10 s-col-bottom"
                                    style="
                                      border-bottom: 1px solid;
                                      border-bottom: 2px solid;
                                      border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                                      padding-left: 20px;
                                    "
                                  >
                                <h2 style="margin-top: 20px; white-space: nowrap;color: #fff;font-size:35px;" class="s-flex">${e.type_two}</h2>
                                    <span
                                      class="s-m-l-20 s-font-30 s-c-white"
                                      style="cursor: pointer"
                                      onclick="this.parentNode.parentNode.style.display = 'none'"
                                    >
                                      <img style="vertical-align: middle;" src="/static/citybrain/csdn/img/close.png" alt="" />
                                    </span>
                                  </nav>
                                  <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" style="padding-bottom: 13%;">
                                      <div class="s-m-l-40">
                                        <p style="width: 650px;font-size: 30px; color: #fff;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">名&nbsp; &nbsp;   &nbsp;称 ：<span style="color: #fff" title="${e.device_name}">${e.device_name}</span></p>
                                        <p style="width: 650px;font-size: 30px; color: #fff;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">设备状态 ：<span style="color: #fff" title="${e.device_state}">${e.device_state}</span></p>
                                        <p style="width: 650px;font-size: 30px; color: #fff;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">设备类型 ：<span style="color: #fff" title="${e.type_name}">${e.type_name}</span></p>
                                        <p style="width: 650px;font-size: 30px; color: #fff;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">承载单位 ：<span style="color: #fff" title="${e.dept_name}">${e.dept_name}</span></p>
                                        <p style="width: 650px;font-size: 30px; color: #fff;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">所属区域 ：<span style="color: #fff" title="${e.carrier_addr}">${e.carrier_addr || '--'}</span></p>
                                        <p style="width: 650px;font-size: 30px; color: #fff;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">采集时间 ：<span style="color: #fff" title="${e.create_time}">${e.create_time}</span></p>
                                      </div>
                                  </header>
                                </div>
                              `,
          }
          top.mapUtil._createPopup(objData)
        }
      },
      // 清除点位
      rmPoint(id) {
        top.mapUtil.removeLayer(id)
      },
      rmAllPiont(pointArr) {
        top.mapUtil.removeAllLayers(pointArr)
      },

    },
  })
</script>