<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
  <script src="/Vue/vue.js"></script>
  
  <title>scene</title>
  <style>
    * { box-sizing: border-box; }
    html,body{
      margin: 0;
      padding: 0;
      font-family: sans-serif;
      text-align: center;
      background:#222;
    }
    .container{
      width:3332px;
      height:2000px;
      display: flex;
      flex-wrap: wrap;
    } 
    .scene {
      border: 1px solid #CCC;
      margin: 40px 0;
      position: relative;
      width: 210px;
      height: 140px;
      margin: 80px auto;
      perspective: 1000px;
    }

    .carousel {
      width: 100%;
      height: 100%;
      position: absolute;
      transform: translateZ(-288px);
      transform-style: preserve-3d;
      transition: transform 1s;
    }

    .carousel__cell {
      position: absolute;
      width: 190px;
      height: 120px;
      left: 10px;
      top: 10px;
      border: 2px solid black;
      line-height: 116px;
      font-size: 80px;
      font-weight: bold;
      color: white;
      text-align: center;
      transition: transform 1s, opacity 1s;
    }

    .carousel__cell:nth-child(9n+1) { background: hsla(  0, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+2) { background: hsla( 40, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+3) { background: hsla( 80, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+4) { background: hsla(120, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+5) { background: hsla(160, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+6) { background: hsla(200, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+7) { background: hsla(240, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+8) { background: hsla(280, 100%, 50%, 0.8); }
    .carousel__cell:nth-child(9n+0) { background: hsla(320, 100%, 50%, 0.8); }

    .carousel__cell:nth-child(1) { transform: rotateY(  0deg) translateZ(288px); }
    .carousel__cell:nth-child(2) { transform: rotateY( 180deg) translateZ(288px); }
    .carousel__cell:nth-child(3) { transform: rotateY( 360deg) translateZ(288px); }
    /* .carousel__cell:nth-child(4) { transform: rotateY(120deg) translateZ(288px); }
    .carousel__cell:nth-child(5) { transform: rotateY(160deg) translateZ(288px); }
    .carousel__cell:nth-child(6) { transform: rotateY(200deg) translateZ(288px); }
    .carousel__cell:nth-child(7) { transform: rotateY(240deg) translateZ(288px); }
    .carousel__cell:nth-child(8) { transform: rotateY(280deg) translateZ(288px); }
    .carousel__cell:nth-child(9) { transform: rotateY(320deg) translateZ(288px); } */

    .carousel-options {
      text-align: center;
      position: relative;
      z-index: 2;
      background: hsla(0, 0%, 100%, 0.8);
    }
    
  </style>
</head>
<body>
  <div id="app">
    <div class="scene">
      <div class="carousel">
        <div class="carousel__cell">1</div>
        <div class="carousel__cell">2</div>
        <div class="carousel__cell">3</div>
        <div class="carousel__cell">4</div>
        <div class="carousel__cell">5</div>
        <div class="carousel__cell">6</div>
        <div class="carousel__cell">7</div>
        <div class="carousel__cell">8</div>
        <div class="carousel__cell">9</div>
        <div class="carousel__cell">10</div>
        <div class="carousel__cell">11</div>
        <div class="carousel__cell">12</div>
        <div class="carousel__cell">13</div>
        <div class="carousel__cell">14</div>
        <div class="carousel__cell">15</div>
      </div>
    </div>
    
    <div class="carousel-options">
      <p>
        <label>
          Cells
          <input class="cells-range" type="range" min="3" max="15" value="9" v-model="rangeValue" @change="changeCarousel"/>
        </label>
      </p>
      <p>
        <button class="previous-button" @click="toPrevious">Previous</button>
        <button class="next-button" @click="toNext">Next</button>
      </p>
      <p>
        Orientation:
        <label>
          <input type="radio" name="orientation" value="horizontal" v-model="radioValue" @change="change" checked />
          horizontal
        </label>
        <label>
          <input type="radio" name="orientation" value="vertical" v-model="radioValue" @change="change"/>
          vertical
        </label>
      </p>
    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var vm = new Vue({
      el: '#app',
      data: {
        carousel:null,
        cells:[],
        cellCount:null,
        selectedIndex:0,
        cellWidth:null,
        cellHeight:null,
        isHorizontal:true,
        rotateFn:null,
        radius:null,
        theta:null,
        radioValue:'vertical',
        rangeValue:'9',
      },
      mounted() {
        this.carousel = document.querySelector('.carousel');
        this.cells = document.querySelectorAll('.carousel__cell');
        this.cellWidth = this.carousel.offsetWidth;
        this.cellHeight = this.carousel.offsetHeight;
        this.rotateFn = this.isHorizontal ? 'rotateY' : 'rotateX';
        
        this.change()
      },
      methods: {
        change(){
          console.log(this.radioValue);
          if(this.radioValue == 'horizontal'){
            this.isHorizontal = true
            this.rotateFn  = 'rotateY'
            this.changeCarousel();
          }else{
            this.isHorizontal = false
            this.rotateFn  = 'rotateX'
            this.changeCarousel();
          }
        },
        changeCarousel(){
          console.log(this.rangeValue);
          this.cellCount = this.rangeValue;
          this.theta = 360 / this.cellCount;
          var cellSize = this.isHorizontal ? this.cellWidth : this.cellHeight;
          this.radius = Math.round( ( cellSize / 2) / Math.tan( Math.PI / this.cellCount ) );
          console.log(this.cells);
          for ( var i=0; i < this.cells.length; i++ ) {
            var cell = this.cells[i];
            if ( i < this.cellCount ) {
              cell.style.opacity = 1;
              var cellAngle = this.theta * i;
              cell.style.transform = this.rotateFn + '(' + cellAngle + 'deg) translateZ(' + this.radius + 'px)';
            } else {
              // hidden cell
              cell.style.opacity = 0;
              cell.style.transform = 'none';
            }
          }
          this.rotateCarousel();
        },
        toPrevious(){
          this.selectedIndex++;
          this.rotateCarousel();
        },
        toNext(){
          this.selectedIndex--;
          this.rotateCarousel();
        },
        rotateCarousel() {
          var angle = this.theta * this.selectedIndex * -1;
          this.carousel.style.transform = 'translateZ(' + -this.radius + 'px) ' + 
            this.rotateFn + '(' + angle + 'deg)';
        },
      }
    });

    
  </script>
</body>

</html>
