<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
  <title>table</title>
  <script src="/Vue/vue.js"></script>
</head>
<body>
  <div id="app">
    <div class="container">
      <div class="table table1">
          <div class="th">
            <div class="th_td" style="flex: 0.2">序号</div>
            <div class="th_td" style="flex: 0.2">表头1</div>
            <div class="th_td" style="flex: 0.3">表头2</div>
            <div class="th_td" style="flex: 0.3">表头3</div>
          </div>
          <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
            <div class="tr"  v-for="(item ,i) in 20">
              <div class="tr_td s-w7" style="flex: 0.2">{{i+1}}</div>
              <div class="tr_td" style="flex: 0.2">内容</div>
              <div class="tr_td" style="flex: 0.3">内容</div>
              <div class="tr_td" style="flex: 0.3">内容</div>
            </div>
          </div>
      </div>
    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var vm = new Vue({
      el: '#app',
      data: {
        time:null,
        dom:null,
        scpDom:null,
      },
      mounted() {
        this.dom = document.getElementById('box0')
        // this.scpDom = document.getElementsByClassName('text')
        this.time = setInterval(() => {
          // this.dom.scrollTop+=1.5
          this.dom.scrollBy({
              top: 82,
              behavior: 'smooth',
            })
          if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
            this.dom.scrollTop = 0
          }
        }, 20)
      },
      methods: {
        mouseenterEvent() {
          clearInterval(this.time)
        },
        mouseleaveEvent() {
          this.time = setInterval(() => {
            this.dom.scrollTop+=1.5
            if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
              this.dom.scrollTop = 0
            }
          }, 20)
        }
      }
    })
  </script>
</body>
<style>
  html,body{
    margin: 0;
    padding: 0;
  }
  .container{
    width:1920px;
    height:1080px;
    /*display: flex;*/
    flex-wrap: wrap;
  }
  .table{
    width:960px;
    height:500px;
    padding:10px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .table1{
    width:960px;
    height:500px;
    padding:10px;
    box-sizing: border-box;
  }
  .table1 .th {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    font-weight: 700;
    font-size: 32px;
    line-height: 80px;
    background: #00396f;
    color: #FFFFFF;
  }
  .table1 .th_td {
    letter-spacing: 0px;
    text-align: center;
  }
  .table1 .tbody {
    width:100%;
    height:calc(100% - 80px);
    /* overflow-y: auto; */
    overflow: hidden;
  }
  .table1 .tbody:hover {
    overflow-y: auto;
  }
  .table1 .tbody::-webkit-scrollbar {
    width: 4px;/*滚动条整体样式*/
    height: 4px;/*高宽分别对应横竖滚动条的尺寸*/
  }

  .table1 .tbody::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }
  .table1 .tr {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    line-height: 60px;
    font-size: 28px;
    color: #FFFFFF;
    cursor: pointer;
  }
  .table1 .tr:nth-child(2n){
    background: #00396f;
  }
  .table1 .tr:nth-child(2n+1){
    background: #035b86;
  }
  .table1 .tr:hover {
    background-color: #6990b6;
  }
  .table1 .tr_td {
    letter-spacing: 0px;
    text-align: center;
    box-sizing: border-box;
  }
</style>
<style>
  .table2 table{
    width:100%;
    height:100%;
    overflow: hidden;
  }
  .table2 tr th{
    text-align: center;
    padding:10px 20px;
    box-sizing: border-box;
    white-space: nowrap;
  }
  .table2 tr td{
    text-align: center;
    padding:10px 20px;
    box-sizing: border-box;
    word-break: break-all;
  }
  
</style>
</html>
