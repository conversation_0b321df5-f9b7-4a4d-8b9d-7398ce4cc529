body,
html,
p {
  margin: 0;
  padding: 0;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
/* 效果 */
.two-title-active {
  background-image: url('../img/cstz3/two-bg-active.png') !important;
}
.dot-img-act {
  background-image: url('../img/cstz3/two-dot-acitve.png') !important;
}
.d-none {
  display: none;
}
.d-block {
  display: block;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.3);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(27, 146, 215, 0.5);
}

#cstz3-right {
  width: 2000px;
  height: 1844px;
  display: flex;
  position: relative;
  justify-content: space-between;
}
#cstz3-left {
  /* width: 2000px;
  height: 1844px; */
  width: 2045px;
  height: 1850px;
  display: flex;
  position: relative;
  justify-content: space-between;
  /* background-image: url(/img/left-bg.png); */
}
.part {
  width: 1000px;
  height: 100%;
  padding: 30px 50px;
  box-sizing: border-box;
  /* background-image: url("/static/citybrain/csdn/img/cstz2-middle/bg.png");
  background-size: 100% 100%; */
  overflow: hidden;
}
.two-title-item-bg {
  background: linear-gradient(180deg, #0e1a40, #0640691a);
}
.two-title-item {
  width: 100%;
  padding-bottom: 20px;
  margin-bottom: 20px;
  box-sizing: border-box;
}
.two-title {
  width: 100%;
  height: 120px;
  font-size: 40px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 15px;
  box-sizing: border-box;
  background-image: url('../img/cstz3/two-bg.png');
  background-size: 100% 100%;
  position: relative;
  z-index: 2;
}
.icon2 {
  display: inline-block;
  width: 20px;
  height: 40px;
  margin-left: 20px;
  background-image: url('../img/cstz3/two-title-icon1.png');
  background-size: 100% 100%;
}

.icon1 {
  display: inline-block;
  width: 20px;
  height: 40px;
  margin-right: 20px;
  background-image: url('../img/cstz3/two-title-icon2.png');
  background-size: 100% 100%;
}

.two-title-content {
  width: 98%;
  margin: 0 auto;
  padding: 20px 0;
  font-size: 32px;
  background-color: #0a60914d;
  margin-top: -20px;
  position: relative;
  max-height: 900px;
  overflow-y: auto;
  overflow-x: hidden;
}
.two-title-content::-webkit-scrollbar-thumb {
  background-color: transparent !important;
}
.two-title-content > li {
  width: 100%;
  line-height: 60px;
}
.three-title {
  width: 100%;
  height: 60px;
  color: #d6e7f9;
  margin-top: 20px;
  position: relative;
  background: linear-gradient(90deg, #006d91, #006d9100);
}
.three-title > span {
  margin-left: 20px;
}
.dot {
  width: 37px;
  height: 6px;
  background-size: 100% 100%;
  position: absolute;
  right: 20px;
  top: 25px;
}
.dot-img {
  background-image: url('../img/cstz3/two-dot.png');
}

.three-title-content {
  width: 100%;
  position: relative;
  max-height: 500px;
  padding-left: 80px;
  padding-right: 50px;
  padding-top: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}
.three-title-content > li {
  position: relative;
}
.con-icon {
  width: 30px;
  height: 30px;
  background-image: url('/static/citybrain/csdn/img/cstz2-middle/con-icon.png');
  background-size: 100% 100%;
  position: absolute;
  right: -40px;
  top: 8px;
}

.top-menus {
  width: 100%;
  margin-top: 10px;
}
.menu-name {
  /* width: 240px; */
  height: 80px;
  background-image: url('/static/citybrain/csdn/img/cstz2-middle/btn-new-bg.png');
  background-size: 100% 100%;
  text-align: center;
  cursor: pointer;
  line-height: 80px;
  font-weight: bold;
  font-size: 32px;
  padding: 0 25px;
  /* margin: 10px 26px;
  box-sizing: border-box; */
}
.menu-name-act {
  background-image: url('/static/citybrain/csdn/img/cstz2-middle/btn-new-active-bg.png');
  background-size: 100% 100%;
}
.el-col-24 {
  width: auto;
}
.top-con {
  width: 100%;
  /* height: 1660px; */
  height: 1530px;
  margin-top: 20px;
  overflow-y: auto;
  background-color: #0a60914d;
}
.top-con::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: transparent !important;
}
.echarts-content {
  width: 100%;
  height: 1660px;
  margin-top: 35px;
}
.top-con-1 {
  height: 1445px;
}
.con-title {
  width: 100%;
  height: 80px;
  text-align: left;
  /* background:url("/static/citybrain/csdn/img/cstz2-middle/two-title-bg.png");
  background-size: 100% 100%; */
  background: linear-gradient(90deg, #006d91, #006d9100);
  color: white;
  line-height: 80px;
  font-weight: bold;
  margin-bottom: 20px;
  font-size: 32px;
}
.con-title-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('/static/citybrain/csdn/img/cstz2-middle/two-icon.png');
  background-size: 100% 100%;
  margin: 0 8px 0 20px;
}
.con-list {
  box-sizing: border-box;
  margin-bottom: 20px;
}
.con-list-item {
  display: flex;
  justify-content: space-between;
  font-size: 32px;
  letter-spacing: 2px;
  color: #ffffff;
  line-height: 50px;
  position: relative;
  padding: 10px 30px;
  padding-right: 70px;
  box-sizing: border-box;
  cursor: pointer;
}
.con-list-item-un {
  color: #a5a1a1;
  /* cursor: not-allowed; */
  cursor: default;
}
/* .con-list-item:nth-child(2n){
    background-color: #17407b80;
} */
.first-title {
  width: 100%;
}
.el-row {
  display: flex;
  flex-wrap: wrap;
  margin: 40px 0;
}

#cstz3-sy {
  width: 7680px;
  display: flex;
  justify-content: space-between;
  /* background: url('/static/images/common/bg-main.png') no-repeat;
    background-size: 100% 100%; */
}

/* 中间css */
.middle-container {
  /* background:url('./img/zb-bg.png') no-repeat; */
  /* width:3420px;
  height:1844px; */
  position: relative;
  background-position: center;
}
.middle-con-title {
  width: 1222px;
  height: 192px;
  background: url('./img/cstz3-middle/title-back.png') no-repeat;
  background-size: 100% 100%;
  margin: 20px auto;
  display: flex;
  justify-content: space-around;
  font-size: 32px;
  color: #ffffff;
}
.middle-con-title > div > p {
  white-space: nowrap;
}
.middle-con-title > div > p:nth-child(1) {
  margin-top: 30px;
}
.middle-title-icon {
  width: 125px;
  height: 125px;
  background: url('./img/cstz3-middle/title-icon.png') no-repeat;
  background-size: 100% 100%;
}
.middle-con-chart {
}
.middle-part {
  width: 600px;
  height: 288px;
  position: absolute;
}
.middle-part:nth-child(1) {
  left: -1230px;
  top: 184px;
}
.middle-part:nth-child(2) {
  left: -1230px;
  top: 552px;
}
.middle-part:nth-child(3) {
  left: -1230px;
  top: 920px;
}
.middle-part:nth-child(4) {
  left: -1230px;
  top: 1288px;
}
.middle-part:nth-child(5) {
  right: -1200px;
  top: 40px;
}
.middle-part:nth-child(6) {
  right: -1200px;
  top: 408px;
}
.middle-part:nth-child(7) {
  right: -1200px;
  top: 776px;
}
.middle-part:nth-child(8) {
  right: -1200px;
  top: 1144px;
}
.middle-part:nth-child(9) {
  right: -1200px;
  top: 1512px;
}
.systemName {
  font-size: 28px;
}
.name-div {
  display: flex;
  align-items: center;
  max-width: 90%;
}
.icon-tooltip {
  width: 40px;
  height: 40px;
}
