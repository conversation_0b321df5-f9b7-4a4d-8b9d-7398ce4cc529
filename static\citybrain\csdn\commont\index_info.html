<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>指标说明弹窗</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.min.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/citybrain/hjbh/js/date.js"></script>
  <style>
    html,
    body {
      padding: 0;
      margin: 0;
    }

    ::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 4px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 4px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    #index-info {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 2160px;
      background-color: #00000065;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .container {
      width: 2000px;
      height: 1300px;
      background: url('/static/citybrain/csdn/img/cstz3/new/back.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
    }

    .close {
      position: absolute;
      right: 40px;
      top: 50px;
      font-size: 50px;
      color: white;
      font-weight: bold;
    }

    .top {
      width: 100%;
      height: 93px;
      line-height: 93px;
      background: url('/static/citybrain/csdn/img/cstz3/new/top-back.png') no-repeat;
      background-size: 100% 100%;
      text-align: center;
      font-size: 50px;
      font-weight: bold;
      color: #ffffff;
      position: relative;
      top: 50px;
    }

    .title {
      width: 100%;
      text-align: center;
      font-size: 40px;
      color: #ffffff;
      margin: 100px auto 30px;
    }

    .title-img {
      margin-bottom: -7px;
    }

    .contant {
      width: 100%;
      /* height: 1090px; */
      display: flex;
      padding: 0px 50px;
      box-sizing: border-box;
    }

    .con-left {
      width: 50%;
    }

    .con-right {
      width: 50%;
    }

    .left-box {
      padding: 0px 60px 0 200px;
      box-sizing: border-box;
      /* display: flex;
      flex-wrap: wrap; */
    }

    .grid-item {
      margin-top: 40px;
      display: flex;
      min-width: 50%;
    }

    .grid-item>img {
      width: 31px;
      height: 31px;
      margin-top: 13px;
    }

    .grid-item>span {
      margin-left: 30px;
      white-space: nowrap;
    }

    .grid-item>div {
      margin-left: 30px;
    }

    .mouse-pointer {
      cursor: pointer;
    }

    .mouse-not {
      cursor: not-allowed;
    }

    .auto {
      max-height: 230px;
      overflow-y: auto;
    }

    /*色卡*/
    .color-card {
      width: 662px;
      height: 100px;
      /* background: rgba(12, 38, 65, 0.9);
      border: 1px solid; */
      /* padding: 18px 0px 0px 24px; */
      box-sizing: border-box;
      position: relative;
      top: -27px;
    }

    .color {
      width: 660px;
      height: 35px;
      background: linear-gradient(92deg,
          #caf681 0%,
          #ffd053 25%,
          #ffd053 40%,
          #ffd053 45%,
          #ffa144 52%,
          #ed1f33 80%,
          #fd0100 88%);
      /* opacity: 0.8; */
      position: absolute;
      top: 55px;
      border-radius: 20px;
    }

    .number {
      width: 451px;
      height: 20px;
      justify-content: space-between;
      display: flex;
      /* display: flex; */
      position: absolute;
      top: 35px;
      left: 80px;
    }

    .bg {
      width: 12px;
      height: 31px;
      display: block;
      background: url(/static/citybrain/csdn/img/cstz/下三角.png);
      background-size: 100% 100%;
    }

    .kd {
      position: absolute;
      top: -29px;
      font-size: 24px;
    }

    .desc {
      font-size: 24px;
      color: #fff;
      position: absolute;
      left: -40px;
      top: 19px;
      line-height: 35px;
    }

    .el-input__inner {
      cursor: pointer;
      width: 100%;
      height: 70px;
      color: #fff;
      background-color: rgba(0, 0, 0, 0) !important;
      border: none;
      font-size: 50px;
      font-weight: bold;
      text-align: center;
    }

    .el-date-editor .el-range-input {
      background: transparent;
      color: #fff;
      font-size: 22px;
    }

    .el-select-dropdown__item {
      font-size: 36px;
      color: #d2d3d4;
      line-height: 60px;
      height: 60px;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #3f6db3;
    }

    .el-select .el-input .el-select__caret {
      font-size: 40px;
    }

    .el-input__icon {
      width: 40px;
    }

    .el-input__prefix,
    .el-input__suffix {
      top: -10px;
      right: -40px;
    }

    .el-select-dropdown {
      max-width: 265px;
      /* left: 800px !important; */
      border: 1px solid #1b5ad7;
      background-color: rgba(19, 44, 78, 0.816);
    }

    .el-scrollbar__wrap {
      overflow-y: scroll !important;
      /* margin-bottom: -30px !important; */
      margin-right: -66px !important;
    }
  </style>
</head>

<body>
  <div id="index-info">
    <div class="container">
      <!-- <div class="top">指标详情</div> -->
      <div class="top">
        <el-select v-model="currentZbId" ref="kkRef" @change="getInfo" filterable placeholder="请选择指标"
          v-if="zbSelectList.length>1">
          <el-option v-for="item in zbSelectList" :key="item.zbid" :label="item.zbname" :value="item.zbid">
          </el-option>
        </el-select>
        <div v-else>{{indexName}}</div>
      </div>
      <div class="close mouse-pointer" @click="close"><i class="el-icon-close"></i></div>
      <div class="title">
        <img class="title-img" src="/static/citybrain/csdn/img/cstz3/new/btn-left.png" alt="" />
        <span>{{title.name}}</span>
        <span class="s-c-yellow-gradient">
          {{title.value}}
        </span>
        <img class="title-img" src="/static/citybrain/csdn/img/cstz3/new/btn-right.png" alt="" />
      </div>
      <div class="contant">
        <div class="con-left" style="width: 100%;">
          <nav style="padding: 30px 160px 0">
            <s-header-title-2 title="基本信息" htype="2" />
          </nav>
          <div class="left-box">
            <div class="grid-item" v-for="(el,index) in ulList" :key="index">
              <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="" />
              <span class="s-c-blue-gradient s-font-35">{{el.name || "-"}}:</span>
              <div class="s-c-white s-font-35" :class="index===0 ? 'auto' : ''">{{el.value || "-"}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
<script>
  var vm = new Vue({
    el: '#index-info',
    data: {
      zbSelectList: [],
      currentZbId: null,
      ulList: [
        { name: '指标定义', value: '--' },
        // { name: '指标数值', value: '--' },
        // { name: '指标单位', value: '--' },
        { name: '所属系统', value: '--' },
        { name: '责任部门', value: '--' },
        { name: '业务责任人', value: '--' },
        { name: '分管领导', value: '--' },
        { name: '更新频率', value: '--' },
        { name: '阈值标准', value: '--' },
        { name: '阈值结果', value: '--' },
        { name: '更新时间', value: '--' },


      ],
      infoObj: {
        '指标定义': 'indicator_name',
        // '指标数值': 'current_value',
        // '指标单位': 'unit',
        '所属系统': 'belong_system',
        '责任部门': 'dept_resp',
        // '业务责任人': 'update_time',
        // '分管领导': 'update_freq',
        '更新频率': 'update_freq',
        '阈值标准': 'threshold_rule',
        '阈值结果': 'threshold_result',
        '更新时间': 'update_time',

      },
      indexName: "",
      loading: false,
      title: {
        name: '',
        value: '',
      }
    },
    mounted() {
      let that = this
      window.addEventListener('message', function (event) {
        if (event.data.type == '指标明细') {
          console.log(event.data);
          that.indexName = event.data.indexName;
          that.zbSelectList = event.data.data;
          that.currentZbId = that.zbSelectList[0].zbid;
          that.getInfo()
        }
      })
    },
    methods: {

      getInfo() {
        this.loading = true;
        $api('cstz_gy_zbmx', { zbid: this.currentZbId }).then((res) => {
          this.loading = false;
          let result = res[0];
          if (result) {
            this.title.name = result.indicator_name
            this.title.value = result.current_value + '' + result.unit
            this.ulList.forEach((item) => {
              let index = this.infoObj[item.name]
              console.log(index)
              item.value = result[index] || '--'
            })
          }
        })
      },
      close() {
        window.parent.postMessage(
          JSON.stringify({
            type: 'closeIframe',
            name: 'index-info',
          }),
          '*'
        )
      },
    },
  })
</script>