//绘制圆形区域的函数

let createGeoJSONCircle = function (center, radiusInKm, points) {
  if (!points) points = 64
  const coords = {
    latitude: center[1],
    longitude: center[0],
  }
  const km = radiusInKm
  const ret = []
  const distanceX = km / (111.32 * Math.cos((coords.latitude * Math.PI) / 180))
  const distanceY = km / 110.574

  let theta, x, y
  for (let i = 0; i < points; i++) {
    theta = (i / points) * (2 * Math.PI)
    x = distanceX * Math.cos(theta)
    y = distanceY * Math.sin(theta)

    ret.push([coords.longitude + x, coords.latitude + y])
  }
  ret.push(ret[0])

  return {
    type: 'Feature',
    geometry: {
      type: 'Polygon',
      coordinates: [ret],
    },
  }
}

let points = []
let starCoords = []
let isMousemove = false
let isFilst = true
let filstCoords = []
let moveCoords = []
let isDraws = true
let radius = 0
let jsonCircle = {
  type: 'FeatureCollection',
  features: [],
}
let _pixelRadius = 0
let callback = null
var map_ = top.mapUtil.map
// 鼠标点击事件
const clickEvent = (_e) => {
  let map = map_

  // const map = window.mainMap; // 地图实例对象
  if (isDraws) {
    starCoords = []
    starCoords.push(_e.lngLat.lat)
    starCoords.unshift(_e.lngLat.lng)
    if (isFilst) {
      filstCoords = starCoords
      isFilst = false
    }
    points.push(starCoords)
    map.getSource('circle').setData(createGeoJSONCircle(starCoords, 0))
    isMousemove = true
    isDraws = true
  }
}
// 鼠标移动事件
const mouseMoveClick = (_e) => {
  let map = map_
  // const map = window.mainMap; // 地图实例对象
  if (isDraws && isMousemove) {
    isDraws = true
    let centerCoords = []

    moveCoords = [_e.lngLat.lng, _e.lngLat.lat]
    if (filstCoords.length != 0) {
      centerCoords = [
        (parseFloat(filstCoords[0]) + parseFloat(moveCoords[0])) / 2,
        (parseFloat(filstCoords[1]) + parseFloat(moveCoords[1])) / 2,
      ]
      const _points = []
      _points.push(moveCoords)
      _points.unshift(filstCoords)
      //points.concat([moveCoords]);
      const line = turf.lineString(_points)
      let len = turf.length(line)
      if (len < 0.11) {
        _pixelRadius = len
        //m
        // len = Math.round(len * 1000);
        //  map.getSource('circle').setData(createGeoJSONCircle(filstCoords, len));
      } else {
        //km
        len = len.toFixed(2)
        _pixelRadius = len
        map.getSource('circle').setData(createGeoJSONCircle(filstCoords, len))
      }
    }
  }
}
// 鼠标双击事件
const dbclickEvent = (_e) => {
  let map = map_

  let len
  // const map = window.mainMap; // 地图实例对象
  if (isDraws) {
    map.getCanvas().style.cursor = 'grab'
    jsonCircle = {
      type: 'FeatureCollection',
      features: [],
    }
    //map.getSource('circle').setData(jsonCircle);
    isMousemove = false
    isDraws = false
    map.getCanvas().style.cursor = ''
    const endCoords = [_e.lngLat.lng, _e.lngLat.lat]
    if (filstCoords.length != 0) {
      // debugger
      //var _points = points.concat([endCoords]);
      const _points = []
      _points.push(moveCoords)
      _points.unshift(filstCoords)
      const line = turf.lineString(_points)
      len = turf.length(line)
      if (len < 0.1) {
        _pixelRadius = len
        // len = Math.round(len * 1000);
        // map.getSource('circle').setData(createGeoJSONCircle(filstCoords, len));
      } else {
        len = len.toFixed(2)
        _pixelRadius = len
        map.getSource('circle').setData(createGeoJSONCircle(filstCoords, len))
      }
    }
    if (callback) {
      callback({
        radius: len,
        center: filstCoords,
      })
    }
  }
}

// 绘制圆
function drawCircle(params = {}) {
  let map = map_
  // const map = window.mainMap;
  map.getCanvas().style.cursor = 'pointer'
  // e.stopPropagation();
  clearLayerAndSource()
  callback = params.callback // 绘制圆回调
  // 禁止双击缩放
  map.doubleClickZoom.disable()

  const source = map.getSource('circle')
  if (source) {
    //map.getSource('circle').setData(jsonCircle);
  } else {
    map.addSource('circle', {
      type: 'geojson',
      data: jsonCircle,
    })
    map.addLayer({
      id: 'circle',
      type: 'fill',
      source: 'circle',
      layout: {},
      paint: {
        'fill-outline-color': '#fff',
        'fill-color': '#fff',
        'fill-opacity': 0.2,
      },
    })
    map.addLayer({
      id: 'circle_line',
      type: 'line',
      source: 'circle',
      layout: {},
      paint: {
        'line-color': '#fff',
        'line-width': 1.5,
      },
    })
  }

  map.on('click', clickEvent)
  map.on('mousemove', mouseMoveClick)
  map.on('dblclick', dbclickEvent)
}

// /清除draw资源图层
function clearLayerAndSource(clearEvent = false) {
  if (top.mapUtil == null) {
    return
  }

  try {
    let map = map_

    // const map = window.mainMap;
    map.getCanvas().style.cursor = ''
    if (map.getLayer('circle')) {
      map.removeLayer('circle')
      map.removeLayer('circle_line')
    }
    if (map.getSource('circle')) {
      map.removeSource('circle')
    }
    if (clearEvent) {
      // 取消事件
      map.off('mousemove', mouseMoveClick)
      map.off('click', clickEvent)
      map.off('dblclick', dbclickEvent)
      map.doubleClickZoom.enable()
      points = []
      starCoords = []
      isMousemove = false
      isFilst = true
      filstCoords = []
      moveCoords = []
      isDraws = true
      radius = 0
      jsonCircle = {
        type: 'FeatureCollection',
        features: [],
      }
      _pixelRadius = 0
    }
  } catch (error) {}
}

// $("#circle").click(function (e) {
//     // const map = 易利地图
//     const map = window.mainMap;
//
// })
