<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>切换地图场景</title>
    <link rel="stylesheet" href="/static/css/animate.css" />
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <style>
      .box {
        width: 380px;
        height: 420px;
        background-color: #162b48;
        position: relative;
      }
      .box:hover .close {
        display: block;
      }
      .close {
        display: none;
        width: 25px;
        height: 25px;
        position: absolute;
        top: 10px;
        right: 10px;
        background-image: url('/static/citybrain/csdn/img/cstz2-middle/close.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100%;
        cursor: pointer;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <div class="box">
        <div class="close" @click="closeIframe"></div>
        <iframe
          width="380px"
          height="420px"
          frameborder="0"
          scrolling="0"
          id="mainIframe"
          :src="`https://login-pro.ding.zj.gov.cn/oauth2/auth.htm?response_type=code&client_id=ydzwbsditu_dingoa&redirect_uri=https://sdi.zjzwfw.gov.cn/resources-server/auth/usage/zheZhengDing2Token.do?token=${token}&scope=get_user_info&authType=QRCODE&embedMode=true`"
          sandbox="allow-scripts allow-top-navigation allow-same-origin"
        ></iframe>
      </div>
    </div>
    <script>
      var vm = new Vue({
        el: '#app',
        data: { token: null },
        mounted() {
          let that = this
          this.token = sessionStorage.getItem('token')
          window.addEventListener(
            'message',
            (event) => {
              that.receiveMessageFromIframe(event)
            },
            true
          )
        },
        methods: {
          receiveMessageFromIframe(event) {
            if (event.origin === 'https://login-pro.ding.zj.gov.cn') {
              if (event?.data?.code && this.token) {
                // 扫码成功
                let code = event?.data?.code
                localStorage.setItem('QRCode', event?.data?.code)
                let url = `https://sdi.zjzwfw.gov.cn/gqservices/wmts/imgmap/default/local?token=${this.token}&code=${code}&其他常规图层调用参数`
                top.frames['main_changeMap'].changeMap.changeMap0()
                this.closeIframe()
              } else {
                this.$Message.error('扫码失败, 请重新扫码')
              }
            }
          },
          closeIframe() {
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'scan-box',
            })
            window.parent.postMessage(data, '*')
          },
        },
      })
    </script>
  </body>
</html>
