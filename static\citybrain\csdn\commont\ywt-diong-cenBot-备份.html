<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="../jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="../echarts/echarts.min.js"></script>

    <link rel="stylesheet" href="/static/css/sigma.css" />

    <style>
      .bgNum {
        display: inline-block;
        margin: 0 5px;
        text-align: center;
        width: 42px;
        height: 61px;
        background: url("/static/images/home_services/dnyx/numbg.png") no-repeat
          100% 100%;
      }
      .tel-bg {
        width: 286px;
        height: 62px;
        line-height: 60px;
        margin: 10px auto;
        background: url("/static/citybrain/csdn/img/ywt/tel-bg.png") no-repeat;
      }
      .box-bot {
        width: 3380px;
        height: 520px;
        background: url("/static/citybrain/csdn/img/cstz2-middle/middle2-bottom.png")
          no-repeat;
      }
      .box-bot header {
        height: 50px;
        text-align: center;
        line-height: 82px;
        color: #fff;
      }
      .box-bot .bot-main {
        width: 98%;
        height: 400px;
        margin-left: 1%;
        margin-top: 1.5%;
      }
      .box-bot .bot-left {
        text-align: center;
        font-size: 32px;
        color: #fff;
      }
      .box-bot .bot-tab {
        width: 100%;
        height: 100%;
        background: url("/static/citybrain/csdn/img/ywt/table-bg.png") 10%
          no-repeat;
      }
      .content0_2_0 {
        /* width: 1309px; */
        height: 80px;
        background-color: #0e3a65;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-right: 4px;
      }

      .content0_2_0_0 {
        /* flex: 0.169; */
        font-family: SourceHanSansCN-Regular;
        font-size: 36px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #77b3f1;
        text-align: center;
      }

      .content0_2_1 {
        height: 250px;
        overflow-y: auto;
      }
      .content0_2_1_0:hover {
        background-color: #035b86;
      }
      .activeTab {
        background-color: #035b86 !important;
      }

      .content0_2_1::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 4px;
        /* scrollbar-arrow-color: red; */
      }

      .content0_2_1::-webkit-scrollbar-thumb {
        border-radius: 10px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */

        background: #20aeff;
        height: 8px;
      }

      .content0_2_1_0 {
        height: 70px;
        display: flex;
        margin-top: 10px;
        background-color: #0f2b4d;
        justify-content: center;
        align-items: center;
      }

      .content0_2_1_0_0 {
        /* flex: 0.169; */
        font-family: SourceHanSansCN-Medium;
        font-size: 30px;
        width: 100%;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #8caac9;
        text-align: center;
      }
      /* 表格自动滚动 */
      @keyframes rowUp {
        0% {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
        }

        100% {
          transform: translate3d(0, -100%, 0);
          -webkit-transform: translate3d(0, -100%, 0);
          -moz-transform: translate3d(0, -100%, 0);
          -ms-transform: translate3d(0, -100%, 0);
          -o-transform: translate3d(0, -100%, 0);
        }
      }

      .content0_2_1_0 {
        animation: 10s rowUp linear infinite normal;
        -webkit-animation: 10s rowUp linear infinite normal;
      }
      /* 鼠标禁用事件 */
      .mouse-pointer {
        cursor: pointer;
      }
      .mouse-not {
        cursor: not-allowed;
      }
    </style>
  </head>

  <body>
    <div id="yw">
      <div class="box-bot">
        <!-- <header class="s-font-38">协同处置全流程</header> -->
        <header class="s-font-38" style="cursor: pointer" @click="upPoint()">
          事件监控
        </header>
        <div style="display: flex" class="bot-main">
          <div style="width: 15%" class="bot-left">
            <div style="margin-top: 50px">
              <p class="tel-bg">今日自动推送</p>
              <div>
                <span v-for="item of (nowts).toString()" class="bgNum">
                  <count-to
                    :start-val="0"
                    :end-val="Number(item)"
                    :duration="3000"
                    class="s-c-orange-gradient s-font-50 s-w7"
                  ></count-to>
                </span>
                <span class="s-c-yellow-gradient">件</span>
              </div>
            </div>
            <div style="margin-top: 40px">
              <p class="tel-bg">累计推送</p>
              <div>
                <span v-for="(item,i) of (lhts).toString()" class="bgNum">
                  <count-to
                    :start-val="0"
                    :end-val="Number(item)"
                    :duration="3000"
                    class="s-c-orange-gradient s-font-50 s-w7"
                  ></count-to>
                </span>
                <span class="s-c-yellow-gradient">件</span>
              </div>
            </div>
          </div>
          <div style="width: 81%" class="bot-tab">
            <div class="content0_2" style="margin: 46px 10px">
              <div class="content0_2_0">
                <div class="content0_2_0_0" style="flex: 0.15">
                  智能发现方式
                </div>
                <div class="content0_2_0_0" style="flex: 0.22">协同事项</div>
                <div class="content0_2_0_0" style="flex: 0.15">推送时间</div>
                <div class="content0_2_0_0" style="flex: 0.17">推送部门</div>
                <div class="content0_2_0_0" style="flex: 0.15">接收时间</div>
                <div class="content0_2_0_0" style="flex: 0.17">接收部门</div>
                <div class="content0_2_0_0" style="flex: 0.3">处置状态</div>
              </div>
              <div class="content0_2_1">
                <div
                  v-for="(item ,i) in dataArr"
                  @click="openDiong(i,item)"
                  :class="[tab_index==i ? 'content0_2_1_0 activeTab' : 'content0_2_1_0','mouse-pointer']"
                >
                  <div class="content0_2_1_0_0" style="flex: 0.15">
                    {{item.type}}
                  </div>
                  <div
                    class="content0_2_1_0_0"
                    style="flex: 0.22"
                    :title="item.a"
                  >
                    {{item.a}}
                  </div>
                  <div class="content0_2_1_0_0" style="flex: 0.15">
                    {{item.b}}
                  </div>
                  <div class="content0_2_1_0_0" style="flex: 0.17">
                    {{item.c}}
                  </div>
                  <div
                    class="content0_2_1_0_0"
                    style="flex: 0.15; position: relative"
                  >
                    {{item.d}}
                  </div>
                  <div class="content0_2_1_0_0" style="flex: 0.17">
                    {{item.e}}
                  </div>
                  <div class="content0_2_1_0_0" style="flex: 0.3; height: 70px">
                    <div
                      :id="'echarts'+i"
                      style="width: 100%; height: 70px"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
  window.addEventListener("message", function (event) {
    if (event.data.type == "mapInit") {
      vm.showMapFun();
    }

    //监听点位点击事件
    if (event.data.data.pointId == 1) {
      let data = JSON.parse(event.data.data.data);
      vm.clickType(data);
    }
  });

  var vm = new Vue({
    el: "#yw",
    data: {
      typePointid: null,
      tab_index: null,
      clickid: "",
      nowts: null,
      nowts_tmp: null,
      lhts: null,
      lhts_tmp: null,
      dataArr: [],
      dataArr_tmp: [],
      jrtsData: [],
    },
    created() {
      this.getDataArr();
    },
    watch: {
      dataArr: {
        handler() {
          let that = this;
          this.$nextTick(() => {
            for (let i = 0; i < that.dataArr.length; i++) {
              let str = "echarts" + i;
              let data =
                that.dataArr[i].state == 0
                  ? [100]
                  : that.dataArr[i].state == 1
                  ? [100, 100]
                  : [100, 100, 100];

              setTimeout(() => {
                that.getEle(str, data);
              }, 1000);
            }
          });
        },
      },
      immediate: true,
    },
    mounted() {
      let that = this;
      setInterval(function () {
        that.getDataArr();
      }, 30000);
      that.showMapFun();
    },
    methods: {
      // 点击事件类型点位
      clickType(data) {
        // if (this.clickid == data.id) {
        //   top.document.getElementById('map').contentWindow.Work.funChange(
        //     JSON.stringify({
        //       funcName: 'pointLoad',
        //       pointType: 'icon_sg3_bule', // 点位类型（图标名称）
        //       pointId: 'icon_sg3_bule', // 点位唯一id
        //       setClick: true,
        //       pointData: [
        //         {
        //           data: data,
        //           point: data.lng,
        //         },
        //       ],
        //     })
        //   )
        // } else {
        //   top.document.getElementById('map').contentWindow.Work.funChange(
        //     JSON.stringify({
        //       funcName: 'rmPoint',
        //       pointId: 'icon_sg3_bule',
        //     })
        //   )
        //   top.document.getElementById('map').contentWindow.Work.funChange(
        //     JSON.stringify({
        //       funcName: 'pointLoad',
        //       pointType: 'icon_sg3_bule', // 点位类型（图标名称）
        //       pointId: 'icon_sg3_bule', // 点位唯一id
        //       setClick: true,
        //       pointData: [
        //         {
        //           data: data,
        //           point: data.lng,
        //         },
        //       ],
        //     })
        //   )
        // }

        if (this.typePointid == data.id) {
          this.typePointid = null;
          let data = JSON.stringify({
            type: "closeIframe",
            name: "sjzx_middle_main",
          });
          window.parent.postMessage(data, "*");
        } else {
          let obj = { id: data.id, ly: "znyp" };
          this.typePointid = obj.id;
          let leftData = {
            type: "openIframe",
            name: "sjzx_middle_main",
            src:
              baseURL.url +
              "/static/citybrain/djtl/commont/sjzx_middle_main.html",
            width: "640px",
            height: "1350px",
            left: "4915px",
            top: "210px",
            zIndex: "555",
            argument: obj,
          };
          // let leftData = {
          //   type: "openIframe",
          //   name: "ywt-diong-tab",
          //   src: baseURL.url + "/static/citybrain/csdn/commont/ywt-diong-tab.html",
          //   width: "1332px",
          //   height: "1059px",
          //   left: "4363px",
          //   top: "531px",
          //   zIndex: 555,
          //   argument: data,
          // };
          window.parent.postMessage(JSON.stringify(leftData), "*");
        }
      },
      getDataArr() {
        let that = this;
        // $get("/a").then((res) => {
        //   that.dataArr = res;
        // });
        $api("/sysjk001").then((res) => {
          that.dataArr_tmp = res;
          if (that.dataArr_tmp.length !== that.dataArr.length) {
            that.dataArr = that.dataArr_tmp;
            this.showMapFun();
            console.log(that.dataArr);
          } else {
            var aa = JSON.stringify(that.dataArr_tmp);
            var bb = JSON.stringify(that.dataArr);
            if (aa != bb) {
              that.dataArr = that.dataArr_tmp;
              this.showMapFun();
              console.log(that.dataArr);
            }
          }
        });

        $api("/sysjk002").then((res) => {
          that.jrtsData = res;
          that.nowts_tmp = res[0].value;
          that.lhts_tmp = res[1].value;
          if (that.nowts_tmp !== that.nowts) {
            that.nowts = that.nowts_tmp;
            console.log(that.nowts);
          }
          if (that.lhts_tmp !== that.lhts) {
            that.lhts = that.lhts_tmp;
            console.log(that.lhts);
          }
        });
      },

      upPoint() {
        this.showMapFun();
      },
      openDiong(index, item) {
        this.tab_index = index;
        //点击列表行 飞行到固定点
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "flyto",
            flyData: {
              center: [item.lng.split(",")[0], item.lng.split(",")[1]],
              zoom: 15,
            },
          })
        );
        // 点击切换相同图片的不通颜色
        // if (this.clickid == item.id) {
        //   top.document.getElementById("map").contentWindow.Work.funChange(
        //     JSON.stringify({
        //       funcName: "pointLoad",
        //       pointType: "icon_sg3_bule", // 点位类型（图标名称）
        //       pointId: "icon_sg3_bule", // 点位唯一id
        //       setClick: true,
        //       pointData: [
        //         {
        //           data: item,
        //           point: item.lng,
        //         },
        //       ],
        //     })
        //   );
        // } else {
        //   top.document.getElementById("map").contentWindow.Work.funChange(
        //     JSON.stringify({
        //       funcName: "rmPoint",
        //       pointId: "icon_sg3_bule",
        //     })
        //   );
        //   top.document.getElementById("map").contentWindow.Work.funChange(
        //     JSON.stringify({
        //       funcName: "pointLoad",
        //       pointType: "icon_sg3_bule", // 点位类型（图标名称）
        //       pointId: "icon_sg3_bule", // 点位唯一id
        //       setClick: true,
        //       pointData: [
        //         {
        //           data: item,
        //           point: item.lng,
        //         },
        //       ],
        //     })
        //   );
        // }
      },
      showMapFun() {
        let arr = this.dataArr;
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmPoint",
            pointId: "",
          })
        );
        for (let i = 0; i < arr.length; i++) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad",
              pointType: arr[i].typeValue, // 点位类型（图标名称）
              // imageConfig: { iconSize: 1.5 },//放大图片大小
              pointId: arr[i].id, // 点位唯一id
              setClick: true,
              pointData: [
                {
                  pointId: 1,
                  data: arr[i],
                  point: arr[i].lng,
                },
              ],
            })
          );
        }
      },
      // 清除地图撒点
      clearPoint() {
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmPoint",
            pointId: "",
          })
        );
      },
      getEle(dom, numArr) {
        let charts01 = echarts.init(document.getElementById(dom));
        //数据
        var XName = ["推送", "处置中", "完成"];
        var data1 = [[100, 100, 100], numArr];
        var Line = ["线1", "线2"];
        var color = ["#256589", "#2952D7"];

        //数据处理
        var datas = [];
        Line.map((item, index) => {
          if (index == 0) {
            datas.push({
              symbolSize: 20,
              symbol: "circle",
              hoverAnimation: false,
              name: item,
              type: "line",
              data: data1[index],
              itemStyle: {
                normal: {
                  borderWidth: 10,
                  color: color[index],
                },
              },
            });
          } else {
            datas.push({
              symbolSize: 25,
              symbol: "circle",
              hoverAnimation: false,
              name: item,
              type: "line",
              data: data1[index],
              itemStyle: {
                normal: {
                  borderWidth: 22,
                  color: color[index],
                },
              },
            });
          }
        });

        option = {
          grid: {
            left: "0%",
            top: "40%",
            bottom: "60%",
            right: "5%",
          },
          yAxis: [
            {
              type: "value",
              position: "right",
              max: 100,
              splitLine: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
            },
          ],
          xAxis: [
            {
              type: "category",
              axisTick: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#6A989E",
                },
              },
              axisLabel: {
                inside: true,
                show: true,
                textStyle: {
                  color: "#90deff", // x轴颜色
                  fontWeight: "normal",
                  fontSize: "20",
                  lineHeight: -70,
                },
              },
              data: XName,
            },
          ],
          series: datas,
        };

        charts01.setOption(option);
      },
    },
  });
</script>
