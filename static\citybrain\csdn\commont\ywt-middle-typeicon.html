<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>事件类型</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <style>
      /* 事件类型列表样式 */
      .diong-litype {
        position: absolute;
        /* right: 30px;
        top: 780px; */
        width: 340px;
        height: 350px;
        color: #fff;
        background-image: linear-gradient(
            0deg,
            rgba(7, 56, 89, 0.9) 0%,
            rgba(0, 32, 52, 0.9) 100%
          ),
          linear-gradient(
            0deg,
            rgba(103, 200, 255, 0.2) 0%,
            rgba(110, 176, 231, 0.1) 100%
          );
        background-blend-mode: normal, normal;
      }
      .diong-litype ul {
        list-style: none;
        max-height: 485px;
        overflow: hidden;
        overflow-y: auto;
      }
      .diong-litype ul li .uldiv {
        margin-left: -5px;
        padding: 2px 30px;
        border-top-left-radius: 50px;
        border-bottom-left-radius: 50px;
        width: 77%;
        height: 65px;
        line-height: 50px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
      }
      .diong-litype ul li .uldiv:hover {
        background: linear-gradient(
          to right,
          rgba(3, 95, 154, 0.9),
          rgba(5, 30, 47, 0.1)
        );
      }
      .diong-litype ul li .uldiv .numRight {
        float: right;
      }
      .diong-litype ul::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 7px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 4px;
        /* scrollbar-arrow-color: red; */
      }
      .diong-litype ul::-webkit-scrollbar-thumb {
        border-radius: 10px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 事件类型弹窗 -->
      <div class="diong-litype">
        <!-- <h2 class="s-m-20 s-text-center s-w4 s-c-blue2-gradient s-font-38">
          事件类型
        </h2> -->
        <ul style="margin-left: -20px">
          <li v-for="(item,i) in typeUl">
            <div class="uldiv">
              <span
                style="
                  display: inline-block;
                  display: flex;
                  align-items: center;
                "
              >
                <img :src="item.img" width="40px" style="margin-top: 8px" />
                <span class="s-font-30 s-m-l-10 s-m-l-20">{{item.name}}</span>
              </span>
              <!-- <span class="s-font-45 s-c-orange-gradient s-w7 numRight"
                  >{{item.num}}</span
                > -->
            </div>
          </li>
        </ul>
      </div>
    </div>
  </body>

  <script>
    var vm = new Vue({
      el: "#app",
      data: {
        typeUl: [
          {
            img: "/static/citybrain/csdn/img/ywt/tb001.png",
            name: "卫星遥感识别",
            num: 0,
          },
          {
            img: "/static/citybrain/csdn/img/ywt/tb003.png",
            name: "Al智能研判",
            num: 0,
          },
          {
            img: "/static/citybrain/csdn/img/ywt/tb002.png",
            name: "物联感知监测",
            num: 0,
          },
          {
            img: "/static/citybrain/csdn/img/ywt/tb004.png",
            name: "舆情自动发现",
            num: 0,
          },
          // {
          //   img: "/static/citybrain/csdn/img/ywt/liicon05.png",
          //   name: "小区",
          //   num: 0,
          // },
          // {
          //   img: "/static/citybrain/csdn/img/ywt/liicon06.png",
          //   name: "单位",
          //   num: 0,
          // },
          // {
          //   img: "/static/citybrain/csdn/img/ywt/liicon07.png",
          //   name: "其他",
          //   num: 0,
          // },
        ],
      },
    });
  </script>
</html>
