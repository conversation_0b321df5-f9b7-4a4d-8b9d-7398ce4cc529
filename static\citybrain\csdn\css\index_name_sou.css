body {
  margin: 0;
}
[v-cloak] {
  display: none;
}

.souBox {
  width: 1279px;
  /* height: 1185px; */
  height: 240px;
  overflow: hidden;
  position: relative;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/bg.png');
  background-size: 100% 100%;
  padding-top: 50px;
  box-sizing: border-box;
}

.souBox .topSou {
  width: 1150px;
  height: 50px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
/* 展开收缩的样式 */
.show-hide-btn {
  width: 150px;
  height: 30px;
  position: absolute;
  bottom: 10px;
  left: 565px;
}
.show-img {
  background-image: url('/static/citybrain/tckz/img/indexNameSou/show.png');
  background-size: 100% 100%;
}
.hide-img {
  background-image: url('/static/citybrain/tckz/img/indexNameSou/hide.png');
  background-size: 100% 100%;
}

.el-input-group {
  height: 100%;
  font-size: x-large;
}

.el-input-group--append .el-input__inner,
.el-input-group__prepend {
  background: #132c4e;
  border: 0.3px solid #afdcfb;
  height: 100%;
  color: #fff;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.el-input-group__append {
  width: 80px !important;
  background: #009ace;
  text-align: center;
  color: #fff !important;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.el-input__suffix {
  margin-right: 20px;
}

.el-icon-circle-close:before {
  line-height: 55px;
  font-size: 30px;
}

.el-icon-search {
  font-size: 26px;
}

.el-input__clear {
  font-size: 26px;
}

/*  */
.el-menu.el-menu--horizontal {
  background: transparent;
  border-color: #356596;
  display: flex;
  justify-content: space-between;
}

.el-menu-item {
  font-size: 30px !important;
}

.el-menu--horizontal > .el-menu-item.is-active {
  background: transparent !important;
  border-bottom: 4px solid #fff !important;
  color: #fff !important;
}

.el-menu--horizontal > .el-menu-item:hover {
  background: transparent !important;
  border-bottom: 4px solid #fff !important;
  color: #fff !important;
}

.main {
  position: relative;
  width: 100%;
  height: 838px;
  background: url('/static/citybrain/tckz/img/indexNameSou/content-bg.png') no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
}

.main > ul {
  width: 96%;
  max-height: 78%;
  margin-left: 15px;
  margin-top: 20px;
  padding: 0;
  list-style: none;
  overflow: auto;
}

.main > ul li {
  cursor: pointer;
  font-size: 30px;
  color: #fff;
  border-bottom: 1px dashed #006688;
}

.main > ul li .name {
  display: flex;
  align-items: center;
}
.main > ul li .name .index-icon {
  display: inline-block;
  margin-right: 10px;
  width: 90px;
  height: 40px;
  line-height: 42px;
  text-align: center;
  font-size: 24px;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/index-icon.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.main > ul li .sptel i {
  width: 62px;
  height: 62px;
  line-height: 62px;
  text-align: center;
  display: inline-block;
  padding: 1px 5px;
  font-size: 24px;
  margin: 3px 13px 0 0;
  background: url('/static/citybrain/tckz/img/indexNameSou/spiconTel.png') no-repeat;
  background-size: 100% 100%;
}

.main > ul li:hover {
  background: #003e52;
}

.main > ul li:hover .name {
  /* background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff); */
  background: linear-gradient(0deg, #ffffff 0%, #ffc460 10.244140625%, #ffffff 60.0029296875%, #ffeccb 100%);
  -webkit-background-clip: text;
  color: transparent;
}
.main > ul li:hover .name .index-icon {
  display: inline-block;
  margin-right: 10px;
  width: 90px;
  height: 40px;
  line-height: 42px;
  text-align: center;
  font-size: 24px;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/index-active.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.click-no {
  opacity: 0.4;
  cursor: no-drop !important;
}

.click-no > div {
  pointer-events: none;
}

/*滚动条整体样式*/
.main ul::-webkit-scrollbar {
  width: 6px;
  height: 1px;
}

.main ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #0b4f76;
  height: 8px;
}

/* 多个ul */
/* .main .dmdzUl {
  max-height: 85% !important;
} */
/* .spjkUl {
  max-height: 75% !important;
} */
.iconspjk-img {
  padding: 0 15px;
  background: url('/static/citybrain/tckz/img/indexNameSou/iconspjk.png') no-repeat;
  background-size: 100% 100%;
}
.main .spjkUl .spjkicon {
  font-size: 24px;
  background-size: contain;
  padding: 2px 26px;
  background: url('/static/citybrain/tckz/img/indexNameSou/iconspjk.png') no-repeat;
  background-size: 100% 100%;
  color: #d6e7f9;
  margin-left: 10px;
}
.main .spjkUl .spjkicon:first-child {
  margin-left: 0;
}
.pointIcon {
  margin: 0 10px;
  width: 70px;
  height: 70px;
}

.qiuji {
  background: url('/static/citybrain/tckz/img/indexNameSou/qiuji.png') no-repeat;
  background-size: 100% 100%;
}

.qiangji {
  background: url('/static/citybrain/tckz/img/indexNameSou/qiangji.png') no-repeat;
  background-size: 100% 100%;
}
.address-icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/address-icon.png');
  background-size: 100% 100%;
  margin-right: 5px;
}
.bm-icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/bm-icon.png');
  background-size: 100% 100%;
  margin-right: 5px;
}

/* 分页 */
.el-pagination {
  padding: 2px 1px !important;
}

.el_page_sjzx > .el-pagination button {
  height: 52px;
  width: 40px;
  background: transparent !important;
}

.el_page_sjzx .el-pagination button .el-icon {
  font-size: 24px !important;
  color: #c1e2fa;
  font-weight: 400;
}

.el_page_sjzx .el-pagination button .el-icon:hover {
  color: #20aeff;
}

.el_page_sjzx .el-pagination__jump {
  font-size: 22px !important;
  font-weight: 400;
  color: #c1e2fa;
  line-height: 45px;
  margin-left: 0 !important;
}

.el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor {
  width: 140px;
}

.el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor input {
  overflow: hidden;
  width: 96px;
  overflow: auto;
  height: 56px !important;
  color: #c1e2fa;
  font-size: 24px;
  border: 2px solid #a7a889;
  border-radius: 4px;
  background: transparent !important;
}

.el_page_sjzx ul {
  margin-top: 2px !important;
}

.el_page_sjzx ul li {
  border: 2px solid transparent;
  margin-left: 0px !important;
  height: 37px;
  padding: 0 3px !important;
  font-size: 20px !important;
  color: #c1e2fa !important;
  background: transparent !important;
  font-weight: 500;
  line-height: 36px !important;
  border-radius: 4px;
}

.el_page_sjzx li.active {
  margin: 0;
  padding: 0;
  color: #fff !important;
  /* border: 2px solid #035b86; */
  background-color: #00c0ff !important;
}

.el_page_sjzx {
  margin: 0 auto;
  text-align: center;
  margin-top: -10px;
}

.el-pagination button,
.el-pagination span:not([class*='suffix']) {
  height: 42px !important;
  line-height: 42px !important;
}

.el-input {
  font-size: 26px !important;
}

.el-pagination__editor.el-input {
  width: 77px;
  margin: 0 10px;
}

.el-pagination__editor.el-input .el-input__inner {
  color: #fff;
  height: 33px;
  font-size: 24px;
  background: #132c4e;
  border: 1px solid;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
}

/* 左导航 */
.content-box {
  width: 100%;
  min-height: 270px;
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  margin-top: 20px;
}
.navBox {
  /* margin: 30px -32px 0 20px; */
  width: 100%;
  height: 75px;
  overflow: hidden;
  position: relative;
}

.navBox::-webkit-scrollbar {
  width: 0px;
  height: 1px;
}

.navBox::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #0b4f76;
  height: 8px;
}

.el-menu {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  border: 0 !important;
  background: transparent !important;
}

.countAll {
  font-size: 28px;
  writing-mode: rl-tb;
}

.el-menu > .el-menu-item {
  width: 306px;
  height: 70px;
  line-height: 45px;
  background: url('/static/citybrain/tckz/img/indexNameSou/tab_bg.png') no-repeat !important;
  background-size: 100% 100% !important;
  padding: 11px 0px !important;
}
.el-menu > .el-menu-item .textLie {
  font-weight: bold;
  font-style: italic;
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #548fab, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.el-menu-item,
.el-submenu__title {
  height: auto;
}

.el-menu > .el-menu-item.is-active {
  background: transparent !important;
  color: #fff !important;
  background: url('/static/citybrain/tckz/img/indexNameSou/tab_active.png') no-repeat !important;
  background-size: 100% 100% !important;
}

.el-menu > .el-menu-item:hover {
  background: transparent !important;
  color: #fff !important;
  background: url('/static/citybrain/tckz/img/indexNameSou/tab_active.png') no-repeat !important;
  background-size: 100% 100% !important;
}

.el-menu > .el-menu-item.is-active .textLie {
  background: linear-gradient(0deg, #ffffff 0%, #22e8e8 50.244140625%, #ffffff 53.0029296875%, #caffff 100%);
  -webkit-background-clip: text;
  color: transparent;
}

.el-menu > .el-menu-item:hover .textLie {
  background: linear-gradient(0deg, #ffffff 0%, #22e8e8 50.244140625%, #ffffff 53.0029296875%, #caffff 100%);
  -webkit-background-clip: text;
  color: transparent;
}

.el-menu-item {
  text-align: center !important;
  color: #ccc !important;
  font-size: 28px !important;
}

@keyframes jumpBoxHandler1 {
  0% {
    transform: translate(0px, 0px);
    /*开始位置*/
  }

  50% {
    transform: translate(0px, -150px);
    /* 可配置跳动方向 */
  }

  100% {
    transform: translate(0px, 0px);
    /*结束位置*/
  }
}

/* 下拉框样式 */
.select {
  display: inline-block;
  width: 200px;
  height: 55px;
  text-align: right;
  z-index: 100;
  line-height: 75px;
  margin-top: 10px;
}

.ul {
  width: 100%;
  height: 40px;
  text-align: center;
  font-size: 24px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  border-radius: 40px;
}

.ul > div {
  width: 100%;
  height: 40px;
  line-height: 40px;
}

.ul ul {
  display: none;
  margin: 5px;
  padding: 0;
}

.select ul > li {
  list-style: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  background-color: #132c4ef0;
  box-sizing: border-box;
}

.select ul > li:hover {
  background-color: #1384c6;
}

.ul-active {
  display: block !important;
}

.ul-active > li:last-of-type {
  border-radius: 0 0 20px 20px;
}

.select ul {
  overflow: hidden;
  max-height: 200px;
  overflow-y: auto;
}

.select ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.select ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 10px;
}

.flow-icon {
  width: 25px;
  position: absolute;
  top: 20px;
  right: 27px;
}

.flow-icon1 {
  top: 8px;
  transform: rotateX(180deg);
}
.flow-icon2 {
  top: -22px !important;
  transform: rotateX(180deg);
}

/* 错误提示框 */
.el-notification__icon {
  width: 30px !important;
  height: 31px !important;
  font-size: 30px !important;
}

.el-notification__title {
  color: #fff !important;
  font-size: 30px !important;
}

.el-notification__content {
  color: #ccc !important;
  font-size: 26px !important;
}

.el-notification__closeBtn {
  font-size: 26px !important;
}

.el-notification {
  top: 130px !important;
  border: 0.5px solid #afdcf7 !important;
  background-color: #051938 !important;
}

.circle-sou-css {
  width: 27px;
  height: 27px;
  color: #d6e7f9;
  box-sizing: border-box;
  cursor: pointer;
  position: absolute;
  left: 1105px;
  border: 1px solid rgb(2, 84, 137);
  border-radius:50%;
  /*background-image: url('/static/citybrain/tckz/img/indexNameSou/zb.png');*/
  /*background-size: 100% 100%;*/
}
.circle-sou-css:hover {
  /*background-image: url('/static/citybrain/tckz/img/indexNameSou/zb-click.png');*/
  border: 1px solid rgb(94, 189, 235);
  border-radius:50%;
}
.circle-sou-css-click {
  /*background-image: url('/static/citybrain/tckz/img/indexNameSou/zb-click.png');*/
  border: 1px solid rgb(98, 184, 252);
  border-radius:50%;
}
.line-sou-css {
  width: 27px;
  height: 27px;
  color: #d6e7f9;
  box-sizing: border-box;
  cursor: pointer;
  position: absolute;
  left: 1070px;
  background: linear-gradient(45deg, transparent 49.5%, #025489 49.5%, #025489 50.5%, transparent 50.5%);
  /*background-image: url('/static/citybrain/tckz/img/indexNameSou/zb.png');*/
  /*background-size: 100% 100%;*/
}
.line-sou-css:hover {
  background: linear-gradient(45deg, transparent 49.5%, #77cff8 49.5%, #6fb6f2 50.5%, transparent 50.5%);
  /*background-image: url('/static/citybrain/tckz/img/indexNameSou/zb-click.png');*/
}
.line-sou-css-click {
  background: linear-gradient(45deg, transparent 49.5%, #82d3f8 49.5%, #85c7f8 50.5%, transparent 50.5%);
   /*background-image: url('/static/citybrain/tckz/img/indexNameSou/zb-click.png');*/
}
.search-button {
  width: 180px;
  height: 50px;
  font-size: 28px;
  line-height: 48px;
  text-align: center;
  color: #d6e7f9;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/btn_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
}
.search-icon {
  display: inline-block;
  width: 35px;
  height: 35px;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/search-icon.png');
  background-size: 100% 100%;
  position: absolute;
  left: 30px;
  top: 5px;
}
.search-box {
  width: 1150px;
  height: 70px;
  margin-top: 10px;
  position: relative;
}
.search-box .el-input {
  position: absolute;
  top: -3px;
}
.search-box .el-input__inner {
  height: 70px;
  color: #fff;
  line-height: 80px;
  background-color: transparent;
  border-color: transparent;
  background-image: url('/static/citybrain/tckz/img/indexNameSou/input-bg2.png');
  background-size: 100% 100%;
  padding: 0 130px 0 80px;
}
.search-box .el-input__inner:hover {
  background-color: transparent;
  border-color: transparent;
}

/* .con-bottom-ul {
  width: 100%;
  height: 450px;
  margin-top: 20px;
  overflow: hidden;
  overflow-y: auto;
}
.con-bottom-ul::-webkit-scrollbar {
  width: 0;
  height: 0;
} */
.con-bottom-ul > li {
  height: 93px;
  margin-bottom: 20px;
  /* background-color: #00396f; */
}
.li-top {
  height: 46px;
  line-height: 46px;
  font-size: 28px !important;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}
.flex-align-center {
  display: flex;
  align-items: center;
}
.icon-jydw {
  width: 35px;
  height: 35px;
  background-size: 100% 100%;
  margin-right: 5px;
  margin-left: 10px;
}
.icon-jydw > img {
  width: 100%;
  height: 100%;
}
.li-width {
  width: 487px;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.color3 {
  color: #00bbf9;
}
.fs-18 {
  font-size: 18px;
}
.fs-28 {
  font-size: 28px;
}
.icon_lxdh {
  background: url('/static/citybrain/csdn/img/showDetails/icon_tel.png') no-repeat;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
  margin-left: 8px;
}
.flex-end {
  display: flex;
  justify-content: end;
  align-items: center;
}
.icon-jl {
  background: url('/static/citybrain/csdn/img/showDetails/icon_jl.png') no-repeat;
  width: 22px;
  height: 28px;
  margin-right: 10px;
}

.el-checkbox__input {
  float: right;
  margin-right: 20px;
}
.el-tree-node__label {
  font-size: 30px;
  font-family: PangMenZhengDao;
  font-weight: bold;

  color: #c0d6ed;
  line-height: 58px;
}
.el-tree-node__content {
  height: 50px !important;
  margin-bottom: 10px;
}
.is-focusable {
  background-color: unset;
}

.el-tree-node.is-current > .el-tree-node__content,
.el-tree-node__content:hover {
  background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
  border-radius: 0px 30px 30px 0px;
}
.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.el-checkbox {
  display: block;
  border-radius: 15px;
  margin-bottom: 2px;
  margin-right: 0;
}
.el-checkbox-group .el-checkbox:hover {
  background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
  border-radius: 0px 30px 30px 0px;
}
.el-checkbox-group .is-checked {
  background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
  border-radius: 0px 30px 30px 0px;
}

.el-checkbox__label {
  font-size: 32px;
  font-family: PangMenZhengDao;
  /* font-style: italic; */
  color: #c0d6ed;
  line-height: 50px;
  padding-left: 0;
}

.el-checkbox__inner {
  width: 28px;
  height: 28px;
  margin-top: 14px;
  margin-left: 15px;
  background-color: #344d67;
}
.auth-tree .el-checkbox__inner {
  width: 33px;
  height: 33px;
  margin-top: 21px;
  background-color: #344d67;
}
.sjzx_middle_left_container {
  /* height: 480px; */
  height: 100%;
  padding: 30px;
  box-sizing: border-box;
}
.checkbox-box-img {
  width: 25px;
  height: 25px;
  position: relative;
  top: 5px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #252316;
  border-color: #ffc561;
}
.el-checkbox__inner::after {
  width: 7px;
  height: 18px;
  left: 10px;
  color: #ffc561 !important;
}
.el-tree {
  background-color: unset;
}
.auth-tree > .el-tree-node > .el-tree-node__content .el-checkbox {
  display: none;
}

.el-icon-caret-left:before {
  font-size: 20px;
}
.el-tree-node__expand-icon {
  position: absolute;
  right: 0;
}
.el-tree-node__label {
  padding-left: 15px;
}
.el-tree-node__expand-icon.expanded {
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #c0d6ed;
}
.el-tree-node__content > label.el-checkbox {
  position: absolute;
  right: 0;
}
.checkbox-title {
  width: 100%;
  background-color: #003e52;
  font-size: 30px;
  text-align: center;
  color: #fff;
}
.checkbox-ul {
  width: 100%;
}

.checkbox-ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.checkbox-ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 10px;
}

.checkbox-ul .el-tag {
  font-size: 24px;
}

.el-tag.el-tag--info {
  background-color: transparent;
  border-color: transparent;
  color: #fff;
}
.el-input__suffix {
  right: -10px;
}
.checkbox-ul .el-input__inner {
  background-color: #132c4e;
  border: 1px solid #359cf8 !important;
}
.checkbox-ul .el-select .el-input .el-select__caret {
  color: #fff;
  font-size: 24px;
}
.el-select .el-tag__close.el-icon-close {
  background-color: #fff !important;
}
.el-select .el-tag.el-tag--info .el-tag__close:hover {
  color: #20aeff;
}

.el-tag .el-icon-close {
  width: 20px;
  height: 20px;
  line-height: 20px;
}
.el-popper[x-placement^='bottom'] .popper__arrow {
  display: none !important;
}
.el-popper[x-placement^='bottom'] {
  margin-top: 3px;
}
.el-select-dropdown {
  background-color: #132c4e;
  border-color: transparent;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #1384c6;
}
.el-select-dropdown__item {
  font-size: 24px;
  color: #fff;
  height: 40px;
  line-height: 40px;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  font-size: 24px;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  background-color: #132c4e;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  background-color: #132c4e;
}
.el-scrollbar__wrap {
  overflow: hidden;
  overflow-y: auto;
}
.el-select-dropdown__wrap {
  max-height: 350px;
}

.el-scrollbar__wrap::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 3px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.el-scrollbar__wrap::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 10px;
}

.spjkss-css {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 轮播 */
.middle-lb-left,
.middle-lb-right {
  position: absolute;
  width: 30px;
  z-index: 999;
}

.middle-lb-left {
  left: 10px;
  top: 10px;
}
.middle-lb-right {
  right: 10px;
  top: 10px;
}
.el-carousel__container {
  height: 75px;
}
.el-carousel__indicators--outside {
  display: none;
}
.middle {
  font-size: 26px;
  color: #fff;
  font-weight: sbold;
  font-style: italic;
  display: flex;
  align-items: center;
}
.middle-item {
  width: 300px;
  height: 100%;
  line-height: 75px;
  text-align: center;
  background-size: 100% 100%;
}
.middle-item:hover {
  background-image: url('/static/citybrain/tckz/img/indexNameSou/tab_active2.png');
}
.middle-item-active {
  background-image: url('/static/citybrain/tckz/img/indexNameSou/tab_active2.png');
}
/* 搜索框叉的样式 */
.el-input__suffix-inner .el-icon-circle-close.el-input__clear {
  margin-left: -190px;
  margin-top: 6px;
  color: #0a709c;
}
.el-input__suffix-inner .el-icon-circle-close.el-input__clear:hover {
  color: #02aee7;
}
