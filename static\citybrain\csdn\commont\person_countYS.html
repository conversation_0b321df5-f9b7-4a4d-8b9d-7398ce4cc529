<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>查询人数-兴趣点-弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <!-- <script src="/static/js/jslib/turf.js"></script> -->
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="https://csdn.dsjj.jinhua.gov.cn:8101/static/js/jslib/simplify.js"></script>

    <style>
      * {
        margin: 0;
        padding: 0;
      }

      .topBox001 {
        width: 750px;
        height: 700px;
        padding: 25px 15px;
        overflow: hidden;
        background: url('/static/citybrain/csrk_3840/img/commont/top-bg.png') no-repeat;
        background-size: cover;
        overflow: hidden;
        box-sizing: border-box;
        background-color: rgb(12 52 97 / 90%);
        border: 1px solid #00c0ff;
      }

      .hearder_h2 {
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 36px;
        font-weight: 500;
        text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
        background: linear-gradient(180deg, #caffff 0%, #caffff 0%, #ffffff 0%, #00c0ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .hearder_h2 > span {
        background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0 20px;
        white-space: nowrap;
      }

      .w50 {
        width: 50%;
      }

      /* 表格样式修改 */
      .el-table {
        max-height: 420px !important;
        overflow: hidden;
        overflow-y: auto;
        color: rgb(197, 192, 192);
        padding-right: 5px !important;
        background: transparent !important;
      }

      .el-table th,
      .el-table tr {
        font-size: 30px !important;
      }

      .el-table tr {
        background: url('/static/citybrain/csdn/img/table_tr_bg.png') no-repeat;
        background-size: 99% 97%;
      }

      .el-table td,
      .el-table th.is-leaf {
        border: 0 !important;
      }

      .el-table tbody tr:hover > td {
        background: #1d4a7acb !important;
      }

      .el-table::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 2px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .el-table::-webkit-scrollbar-thumb {
        border-radius: 2px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 10px;
      }

      .el-table--border::after,
      .el-table--group::after,
      .el-table::before {
        background-color: transparent;
      }

      .el-checkbox,
      .el-checkbox__input {
        zoom: 120%;
      }

      .el-table .cell {
        line-height: normal;
      }

      .el-table .el-table__cell {
        padding: 8px 0 15px !important;
      }

      .el-checkbox__inner {
        width: 18px;
        height: 18px;
      }

      .number {
        display: inline-block;
        font-size: 40px;
      }

      .number .numbg {
        display: inline-block;
        width: 34px;
        height: 48px;
        line-height: 48px;
        text-align: center;
        background: url('/static/citybrain/hjbh/img/rkzt/numBg.png') no-repeat;
        background-size: contain;
        margin: 0 4px;
        border-radius: 8px;
      }

      /* el */
      .el-input {
        font-size: 30px;
      }
      .el-input__inner {
        margin-top: 30px;
        height: 40px;
        line-height: 30px;
        background-color: rgba(25, 27, 35, 0);
        color: hsla(0, 0%, 100%, 0.8);
      }
      .el-input__icon {
        height: 30px;
        line-height: 30px;
      }
      .popper__arrow {
        display: none !important;
      }

      .el-select {
        margin: 0 20px;
      }
      .el-select-dropdown {
        background-color: rgba(25, 27, 35, 0.96);
      }
      .el-popper[x-placement^='bottom'] {
        margin-top: -2px;
      }
      .el-select-dropdown__item {
        color: #fff;
        height: 40px;
        line-height: 40px;
        font-size: 30px;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #2a4b8b;
      }
      .el-select-dropdown__item.selected {
        background-color: #2a4b8b;
        color: #fff;
        font-weight: 400;
      }
      .button {
        float: right;
        margin: 30px 20px;
        padding: 0 16px;
        width: auto;
        height: 35px;
        cursor: pointer;
        /* background-image: linear-gradient(180deg,hsla(0,0%,100%,.4),hsla(0,0%,100%,0)); */
        border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
        box-sizing: border-box;
        transition: 0.3s;
        line-height: 30px;
        font-size: 25px;
        background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
        color: white;
      }
      .button:hover {
        background: #2960cb;
        color: white;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <!-- static\EGS(v1.0.0)\lib\EGS(v1.0.0)\image\spritesImage -->
      <div class="topBox001">
        <el-select v-model="queryValue" placeholder="选择要素图层" @visible-change="layersCheck()">
          <el-option
            v-for="item in queryLyrs"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
        <div class="button" id="queryStart" @click="queryStart">点选查询</div>

        <p class="hearder_h2">
          <img src="/static/citybrain/csrk_3840/img/commont/tit-l.png" width="180px" alt="" />
          <span>区域情况</span>
          <img src="/static/citybrain/csrk_3840/img/commont/tit-r.png" width="180px" alt="" />
        </p>
        <div class="s-flex s-row-center s-font-32 s-c-white s-m-t-10 s-text-center">
          <p class="s-m-10">区域人数</p>
          <div class="s-flex">
            <div class="number s-c-yellow-gradient" v-for="(item, i) in count" :key="i">
              <span class="numbg" v-if="item!=','&&item!='.'">
                <count-to
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="s-c-yellow-gradient"
                ></count-to>
              </span>
              <span v-else>{{item}}</span>
            </div>
            人
          </div>
        </div>
        <template>
          <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%; margin-top: 20px"
            :show-header="false"
            @selection-change="handleSelectionChange"
          >
            <el-table-column prop="name" label="名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="s-flex">
                  <img
                    :src="'/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/'+scope.row.icon+'.png'"
                    alt=""
                    width="45px"
                  />
                  <span class="s-c-blue-gradient1">{{scope.row.name}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="num,unit" label="总值" show-overflow-tooltip>
              <template slot-scope="scope">{{scope.row.num}}{{scope.row.unit}}</template>
            </el-table-column>
            <el-table-column prop="avg,unit" label="均值" show-overflow-tooltip>
              <template slot-scope="scope">{{scope.row.avg}}{{scope.row.unit}}/每千人</template>
            </el-table-column>
            <el-table-column type="selection" width="80"></el-table-column>
          </el-table>
        </template>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      window.addEventListener('message', async (e) => {
        // 接口消息
        if (e.data) {
          if (typeof(e.data)=="object" && e.data.person_count_data) {
            vm.serchFun(e.data.person_count_data)
          }
          if (e.data && e.data.clear_point) {
            vm.clearPointList()
          }
        }
      })
      var vm = new Vue({
        el: '#app',
        data: {
          clickLayerId: '',
          nameId: {},
          queryValue: '',
          queryLyrs: [],
          distance: 0,
          centroid: [],
          count: '0',
          tableData: [
            {
              name: '医疗机构',
              code: '医院',
              codenum: '170101,170102,170103,170104,170105',
              icon: '医院c',
              num: 0,
              unit: '家',
              avg: 0,
              arr: [],
            },
            {
              name: '学校',
              icon: '学校c',
              num: 0,
              code: '学校',
              codenum: '160101, 160102,  160103, 160104,  160105, 160106,  160107',
              unit: '所',
              avg: 0,
              arr: [],
            },
            {
              name: '公交车站',
              code: '公交站',
              codenum: '230105',
              icon: '公交车站c',
              num: 0,
              unit: '个',
              avg: 0,
              arr: [],
            },
            {
              name: '公共厕所',
              code: '公厕',
              codenum: '210102',
              icon: '公共厕所c',
              num: 0,
              unit: '个',
              avg: 0,
              arr: [],
            },
            {
              name: '运动场馆',
              code: '游泳场池,高尔夫球场,场馆',
              codenum: '180101,180105,180106',
              icon: '运动场馆c',
              num: 0,
              unit: '个',
              avg: 0,
              arr: [],
            },
            {
              name: '公园广场',
              code: '植物园,公园,广场,动物园',
              codenum: '180303,180307,180402,180404,180405',
              icon: '公园广场c',
              num: 0,
              unit: '个',
              avg: 0,
              arr: [],
            },
          ],
          clickName: [],
          foreClickPolyCode:null
        },
        mounted() {},
        methods: {
          layersCheck() {
            this.queryLyrs = []

            let view = top.window.mapUtil.mapview
            Object.keys(top.mapUtil.layers).forEach((item) => {
              let lyr = top.mapUtil.layers[item]
              //将tcgkwg换成网格_,移除tcgl，移除map
              let newName = item.includes('tcqkwg')
                ? item.replace(new RegExp('tcqkwg', 'g'), '网格_')
                : item.includes('tcgl')
                ? item.replace(new RegExp('tcgl', 'g'), '')
                : item.includes('map')
                ? item.replace(new RegExp('map', 'g'), '')
                : item

              if (lyr.geometryType == 'polygon' && item != 'drawMine') {
                this.nameId[newName] = item
                this.queryLyrs.push({
                  value: item,
                  label: newName,
                  disabled: false,
                })
              }
            })
          },

          queryStart() {
            let clickEvt = top.ArcGisUtils.mapClickEventHandle._callbackEvent
            clickEvt = clickEvt.filter((item) => {
              return item.callback.name != 'queryCallback'
            })
            top.ArcGisUtils.mapClickEventHandle._callbackEvent = clickEvt
            vm.clearPointList()

            const pointEventId = top.ArcGisUtils.mapClickEventHandle.add(
              top.mapUtil.layers[vm.queryValue].id,
              function queryCallback(point, graphic) {
                let befor = [],
                  after = []
                if (graphic) {
                  graphic.attributes.position = point
                  // graphic.attributes.data = _attr_lower[graphic.attributes.guid]
                  // graphic.attributes.data_origin = _attr_origin[graphic.attributes.guid]
                  let _geometry_origin = top.ArcgisToGeojsonUtils.arcgisToGeoJSON(graphic.geometry)
                  let _geometry_new = { type: 'Polygon', coordinates: [] }
                  _geometry_origin.coordinates[0].map((item) => {
                    befor.push({
                      x: item[0],
                      y: item[1],
                    })
                  })

                  //点数大于500时抽稀，其余均计算最小外接矩形
                  if (befor.length >= 500) {
                    after = simplify(befor, 0.005, true)
                  } else {
                    vm.getRect([_geometry_origin.coordinates])[0].map(item => {
                      after.push({
                        x: item[0],
                        y: item[1],
                      })
                    })
                  }
                  _geometry_new.coordinates[0] = []
                  after.map((item) => {
                    _geometry_new.coordinates[0].push([item.x, item.y])
                  })
                  graphic.attributes.geometry = _geometry_origin
                  graphic.attributes.searchgeometry = _geometry_new
                  if (_geometry_origin.coordinates.length > 1) {
                    graphic.attributes.searchgeometry.coordinates = vm.getRect(_geometry_origin.coordinates)
                  }
                }
                if (graphic.attributes.geometry && vm.foreClickPolyCode != graphic.attributes.ADCODE) {
                  vm.foreClickPolyCode = graphic.attributes.ADCODE
                  window.parent.emiter &&
                    window.parent.emiter.emit('drawShapeSeachCount', {
                      searchgeometry: graphic.attributes.searchgeometry,
                      geometry: graphic.attributes.geometry,
                      area: Number(graphic.attributes.Shape_Area) || 0,
                      graphic,
                      hl: true,
                    })
                  top.mapUtil.removeLayer('video-point') //清除摄像头点位
                  vm.centroid = [graphic.geometry.centroid.x, graphic.geometry.centroid.y]
                  vm.distance = vm.calculateRadius(
                    graphic.geometry.centroid,
                    graphic.attributes.searchgeometry.coordinates[0]
                  )
                  vm.pointDataFun('zbjk') //加载摄像头点位
                  top.view.whenLayerView(graphic.layer).then(function (layerView) {
                    if (window.clickgra) window.clickgra.remove()
                    window.clickgra = layerView.highlight(graphic)
                  })
                }
              }
            )
          },
          async serchFun(data) {
            if (vm.queryValue && data.mapGeoJson.properties.arclayerid != top.mapUtil.layers[vm.queryValue].id) {
              window.parent.mapUtil.removeLayer('drawMine')
              return
            } //此处实现图层点击判断
            let _this = this
            _this.count = data.countAll.toString()
            let polygon = data.mapGeoJson.properties.searchgeometry
              ? {
                  type: 'Feature',
                  geometry: data.mapGeoJson.properties.searchgeometry,
                  properties: {},
                }
              : data.mapGeoJson
            let obj = { type: 'FeatureCollection', features: [polygon] }
            // const keys = window.parent.mapUtil.layers.drawMine ? 'searchgeometry' : 'geometry'
            // let obj = { type: 'FeatureCollection', features: [data.mapGeoJson] } //暂时注释、21日接口问题
            for (let index = 0; index < _this.tableData.length; index++) {
              !(function (index) {
                let axiosData = {
                  pageInfo: {
                    current: 1,
                    size: 10000,
                    totalSize: 0,
                  },
                  text: _this.tableData[index].codenum,
                  // text: _this.tableData[index].code,
                  tableNames: 'poi',
                  returnGeo: true,
                  sortField: '',
                  order: '',
                  geometry: JSON.stringify(obj),
                }
                // axiosData[keys] = JSON.stringify(obj)
                axios({
                  method: 'post',
                  url: baseURL.url + '/api2.0/solr-provider/api/data-sources/solr-search',
                  data: axiosData,
                  headers: {
                    'Content-Type': 'application/json',
                  },
                }).then((res) => {
                  top.mapUtil.removeLayer('personCount' + _this.tableData[index].name)
                  if (res.data.data == null) {
                    _this.tableData[index].arr = []
                    _this.tableData[index].num = '-'
                    _this.tableData[index].avg = _this.tableData[index].num == 0 ? 0 : '-'
                    return
                  }
                  _this.tableData[index].arr = res.data.data
                  _this.tableData[index].num =
                    res.data.pageInfo.totalSize > res.data.data.length
                      ? res.data.pageInfo.totalSize
                      : res.data.data.length
                  _this.tableData[index].avg =
                    _this.tableData[index].num == 0
                      ? 0
                      : data.countAll == 0
                      ? _this.tableData[index].num
                      : ((_this.tableData[index].num / data.countAll) * 1000).toFixed(1)
                })
              })(index)
            }
          },

          //计算多要素面及中空面最小外接矩形
          getRect(coordinates) {
            let minX
            let minY
            let maxX
            let maxY
            if (coordinates[0][0][0][0]) {
              //多要素面
              // 初始化最小外接矩形的坐标为第一个点
              minX = coordinates[0][0][0][0]
              minY = coordinates[0][0][0][1]
              maxX = coordinates[0][0][0][0]
              maxY = coordinates[0][0][0][1]
              // 遍历所有点，更新最小外接矩形的坐标
              for (let i = 0; i < coordinates.length; i++) {
                const polygon = coordinates[i]
                for (let j = 0; j < polygon.length; j++) {
                  const ring = polygon[j]
                  for (let k = 0; k < ring.length; k++) {
                    const point = ring[k]
                    const x = point[0]
                    const y = point[1]
                    minX = Math.min(minX, x)
                    minY = Math.min(minY, y)
                    maxX = Math.max(maxX, x)
                    maxY = Math.max(maxY, y)
                  }
                }
              }
            } else {
              //中空面
              // 初始化最小外接矩形的坐标为第一个点
              minX = coordinates[0][0][0]
              minY = coordinates[0][0][1]
              maxX = coordinates[0][0][0]
              maxY = coordinates[0][0][1]
              // 遍历所有点，更新最小外接矩形的坐标
              for (let i = 0; i < coordinates.length; i++) {
                for (let j = 0; j < coordinates[i].length; j++) {
                  const x = coordinates[i][j][0]
                  const y = coordinates[i][j][1]

                  minX = Math.min(minX, x)
                  minY = Math.min(minY, y)
                  maxX = Math.max(maxX, x)
                  maxY = Math.max(maxY, y)
                }
              }
            }
            // 构建最小外接矩形的嵌套数组
            const rectangle = [
              [
                [minX, minY],
                [maxX, minY],
                [maxX, maxY],
                [minX, maxY],
                [minX, minY],
              ],
            ]
            // 返回最小外接矩形的嵌套数组
            return rectangle
          },

          // 计算两个经纬度坐标之间的距离（单位：米）
          getDistance(lat1, lon1, lat2, lon2) {
            const R = 6371e3 // 地球半径（米）
            const φ1 = (lat1 * Math.PI) / 180 // 纬度1（弧度）
            const φ2 = (lat2 * Math.PI) / 180 // 纬度2（弧度）
            const Δφ = ((lat2 - lat1) * Math.PI) / 180 // 两纬度之差（弧度）
            const Δλ = ((lon2 - lon1) * Math.PI) / 180 // 两经度之差（弧度）

            const a =
              Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

            const distance = R * c
            return distance
          },

          // 计算最小外接圆的半径
          calculateRadius(center, coordinates) {
            let radius = 0 // 圆半径（米）
            // 遍历所有坐标
            coordinates.forEach((item) => {
              // 计算当前坐标与圆心的距离
              const lon = item[0]
              const lat = item[1]
              const distance = this.getDistance(center.y, center.x, lat, lon)
              // 更新圆半径
              if (distance > radius) {
                radius = distance
              }
            })

            return radius / 1000
          },
          // 打点
          pointDataFun(label) {
            let that = this
            axios({
              method: 'get',
              url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
              params: {
                type: 'zbjk',
                distance: that.distance, //单位：km
                point: that.centroid.toString(),
              },
            }).then(function (data) {
              let dataArr = []
              data.data.data.zbjk.pointData.map((item) => {
                let cameraType = item.isHighAltitude == 1 ? '高点' : item.cameraType
                let obj = {
                  cameraType: cameraType,
                  data: item.data,
                  is_online: item.is_online,
                  lat: item.lat,
                  lng: item.lng,
                  pointType: that.getPointType(item.is_online, cameraType),
                }
                dataArr.push(obj)
              })
              that.getManyPoint(dataArr)
            })
          },
          getPointType(is_online, cameraType) {
            let arr = is_online + '-' + cameraType
            let obj = {
              枪机在线: '在线-枪机',
              枪机离线: '离线-枪机',
              球机在线: '在线-球机',
              球机离线: '离线-球机',
              半球机在线: '在线-半球机',
              半球机离线: '离线-半球机',
              高点在线: '在线-高点',
              高点离线: '离线-高点',
              未知在线: '在线-未知',
              未知离线: '离线-未知',
            }
            for (key in obj) {
              if (obj[key] == arr) {
                return key
              }
            }
          },
          //一次绘制多种不同类型的点
          getManyPoint(pointData, pointId) {
            console.log('r', pointData)
            top.mapUtil.loadPointLayer({
              layerid: 'video-point',
              data: pointData,
              onclick: this.openPointMassage,
              onblur: this.onblur,
              cluster: true, //是否定义为聚合点位：true/false
              iconcfg: {
                image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                iconSize: 0.5,
                iconlist: {
                  field: 'pointType',
                  list: [
                    {
                      value: '枪机在线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                    },
                    {
                      value: '枪机离线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
                    },
                    {
                      value: '球机在线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                    },
                    {
                      value: '球机离线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
                    },
                    {
                      value: '半球机在线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                    },
                    {
                      value: '半球机离线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
                    },
                    {
                      value: '高点在线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                    },
                    {
                      value: '高点离线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                    },
                    {
                      value: '未知在线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                    },
                    {
                      value: '未知离线',
                      size: '50',
                      src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                    },
                  ],
                },
              },
            })
          },
          // 查看地图点位点击的详情
          openPointMassage(e, list) {
            let this_ = this
            // let item = e
            top.mapUtil.removeLayer('syr')
            top.mapUtil.removeLayer('syr1')
            if (e.pointId == 'sou') {
              console.log('sou-window====>', e)
              // if (e.data.dzqc && e.data.tablename == 'dmdz') { //地名地址判断弹窗
              //   this.showPop([e.data.x, e.data.y], e.data.dzqc, e.layerid)
              // }
              if (e.data.lonlat) {
                this.showPop([e.data.lonlat.split(' ')[0], e.data.lonlat.split(' ')[1]], e.data.address, e.layerid)
              } else {
                let point = e.data.jwd.split(',')
                this.getInfoData(e.data.subtype, e.data.id, point, e.layerid)
              }
            } else if (
              (e.subtype && e.subtype.indexOf('wlgz_') > -1) ||
              (e.data.subtype && e.data.subtype.indexOf('wlgz_') > -1)
            ) {
              let coor = e.point ? e.point.split(',') : e.data.jwd.split(',')
              let subtype = e.subtype || e.data.subtype
              this.getInfoData(subtype, e.data.id, coor, e.layerid)
            } else if (e.pointId == 'video') {
              top.mapUtil.flyTo({
                destination: [e.esX, e.esY],
                offset: [0, -999],
              })
              let item = {
                obj: {
                  // chn_name: e.data.name,
                  chn_name: e.data.video_name,
                  pointList: list,
                },
                video_code: e.data.addinfo.chncode,
                csrk: true,
              }

              let iframe1 = {
                type: 'openIframe',
                name: 'video_main_code',
                src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
                width: '100%',
                height: '100%',
                left: '0',
                top: '0',
                zIndex: '1000',
                argument: item,
              }
              window.parent.postMessage(JSON.stringify(iframe1), '*')
            } else if (e.data != undefined && e.data.pointId == 'video') {
              // if (e.is_online == '离线') {
              //   this.$message('设备离线')
              //   return
              // }
              let item = {
                obj: {
                  // chn_name: e.data.name,
                  chn_name: e.data.video_name,
                  pointList: list,
                },
                video_code: e.data.addinfo.chncode,
                is_online: e.is_online,
                csrk: true,
              }
              top.mapUtil.flyTo({
                destination: [e.esX, e.esY],
                offset: [0, -999],
              })
              let iframe1 = {
                type: 'openIframe',
                name: 'video_main_code',
                src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
                width: '100%',
                height: '100%',
                left: '0',
                top: '0',
                zIndex: '1000',
                argument: item,
              }
              window.parent.postMessage(JSON.stringify(iframe1), '*')
            }
          },
          //视屏移入事件
          onblur(e) {
            //onblur
            console.log(e)
            let info = e.data
            let str = ''
            if (e.is_online == 0 || e.is_online == '离线') {
              str = `<div onclick=" this.style.display = 'none'"
                        style="
                          width: 300px;
                          position: absolute;
                          border-radius: 5px;
                          background-color: rgba(6, 26, 48, 1);
                          box-shadow: inset 0 0 20px 0 #00bcfa;
                          z-index: 999999;
                          padding: 24px;">
                           <div
                            style="
                              width: 0px;
                              height: 0px;
                              border-top: 30px solid rgba(4, 91, 129, 0.8);
                              border-right: 15px solid transparent;
                              border-left: 15px solid transparent;
                              position: absolute;
                              bottom: -30px;
                              left: 150px;
                            "
                          ></div>
                    <div class="container1" style="font-size: 30px;color: white;text-align: center;">
                      设备离线中...
                    </div>
                  </div>`
              let objData = {
                layerid: 'syr',
                position: [e.lng, e.lat],
                content: str,
                offset: [50, 100],
              }
              top.mapUtil._createPopup(objData)
            } else {
              $api('xxwh_bqcx_name', { chnCode: info.addinfo.chncode }).then((res) => {
                $api('xxwh_dwzl_video_path', { chnCode: info.addinfo.chncode }).then((el) => {
                  let url = ''
                  let lable = ''
                  let des = ''

                  if (el[0] && el[0].path != null) {
                    url = baseURL.url + '/imgPath/' + el[0].path.split('fileServer/')[1]
                  } else {
                    url = '/static/citybrain/tckz/img/video/404.png'
                  }
                  if (res[0]) {
                    let labelName = res[0].lableName.split(',').slice(0, 3)
                    let description = res[0].description.split('，')
                    labelName.forEach((item) => {
                      lable += `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;">${item}
                              </div>`
                    })
                    description.forEach((item) => {
                      des += `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;">${item}
                              </div>`
                    })
                  } else {
                    lable = `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;">暂无
                              </div>`
                    des = `<div style="color: #dbdee2;
                                  font-size: 28px;
                                  height: 40px;
                                  line-height: 40px;
                                  padding: 0 20px;
                                  box-sizing: border-box;
                                  border-radius: 10px;
                                  margin-right: 10px;
                                  margin-bottom: 10px;">暂无
                          </div>`
                  }

                  str = `<div onclick=" this.style.display = 'none'"
                          style="
                            width: 800px;
                            position: absolute;
                            border-radius: 5px;
                            background-color: rgba(10, 31, 53, 0.8);
                            z-index: 999999;
                            box-shadow: inset 0 0 40px 0 #5ba3fa;
                            padding: 24px;">

                      <div class="container1">
                        <div style="display:flex;justify-content: space-between;">
                          <p title='${info.video_name}' style='height: 30px;line-height: 30px;color: #fff;font-size: 30px;
                                  white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'>${info.video_name}</p>
                        </div>
                        <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 10px;">
                          <span style="font-size:30px;color:#fff;line-height:40px;">标签：</span>
                          ${lable}
                        </div>
                        <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 0px;">
                          <span style="font-size:30px;color:#fff;line-height:40px;">视频内容：</span>
                          ${des}
                        </div>
                        <img src="${url}" alt="" style='width:100%;height:400px;margin-top: 10px;'>
                      </div>
                    </div>`
                  let objData = {
                    layerid: 'syr1',
                    position: [e.lng, e.lat],
                    content: str,
                    offset: [50, 100],
                  }
                  top.mapUtil._createPopup(objData)
                })
              })
            }
          },

          clearPointList() {
            top.mapUtil.removeLayer('video-point')
            this.tableData.map((res) => {
              top.mapUtil.removeLayer('personCount' + res.name)
            })
          },
          isHas(small, big) {
            let main = []
            if (small.length == 0) return big[0]
            for (let i = 0; i < small.length; i++) {
              big.filter((ele) => {
                if (ele != small[i]) {
                  main = ele
                }
              })
            }
            return main
          },

          handleSelectionChange(e) {
            if (this.clickName.length > e.length) {
              //减
              let item = this.isHas(e, this.clickName) //赛选减的目标
              top.mapUtil.removeLayer('personCount' + item.name)
            } else {
              //加
              let mainObj = this.isHas(this.clickName, e) //赛选减的目标
              let pointData = []
              mainObj.arr.map((item) => {
                let str = {
                  lng: item.x,
                  lat: item.y,
                  name: item.name,
                  address: item.address || item.city + item.county + item.town,
                }
                if (item.x != '' && item.x != null && item.x != undefined) {
                  pointData.push(str)
                }
              })
              let pointId = 'personCount' + mainObj.name
              this.pointTextMapFun(mainObj.icon, pointData, pointId, 1)
            }
            this.clickName = e
          },

          pointTextMapFun(icon, pointData, pointId, iconSize) {
            let popcfg = {
              offset: [50, -100],
              show: true,
              dict: { name: '名称', address: '地址' },
            }
            top.mapUtil.loadPointLayer({
              data: pointData,
              layerid: pointId, //图层id
              iconcfg: { image: icon, iconSize: iconSize }, //图标
              popcfg: popcfg,
            })
          },
        },
      })
      top.emiter.on('beforeCloseIframe', (name) => {
        if (name === 'person_countYS') {
          top.mapUtil.plotTool.close()
          vm.clearPointList()
          if (window.clickgra) window.clickgra.remove()
          let clickEvt = top.ArcGisUtils.mapClickEventHandle._callbackEvent
          clickEvt = clickEvt.filter((item) => item.callback.name !== 'queryCallback')
          top.ArcGisUtils.mapClickEventHandle._callbackEvent = clickEvt
        }
      })
      top.emiter &&
        top.emiter.on('longActiveType', () => {
          top.mapUtil.plotTool.close()
          vm.clearPointList()
        })
    </script>
  </body>
</html>
