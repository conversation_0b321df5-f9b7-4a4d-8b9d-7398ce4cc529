<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>centerBot</title>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csdn/Vue/vue.js"></script>

  <script src="../echarts/echarts.min.js"></script>

  <link rel="stylesheet" href="/static/css/sigma.css" />

  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <script src="/static/citybrain/csdn/js/DHWs_es5.js"></script>
  <script src="/static/citybrain/csdn/js/iview.js"></script>
  <script src="/static/citybrain/csdn/js/vue-seamless-scroll.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/jslib/datav.min.vue.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>

  <script src="/static/js/jslib/axios.min.js"></script>

  <style>
    * {
      padding: 0;
      margin: 0;
      box-sizing: border-box;
    }

    [v-cloak] {
      display: none;
    }

    body {
      overflow: hidden;
    }

    .center_container {
      position: relative;
      width: 3379px;
      height: 888px;
    }

    .title_container {
      display: flex;
      height: 96px;
      align-items: center;
      align-content: center;
    }

    .title_container h3 {
      font-size: 54px;
      font-family: Source Han Sans SC;
      font-weight: 700;
      color: #d6e7f9;
      margin: 0 12px;
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .title_container .left-img,
    .title_container .right-img {
      width: 22px;
      height: 54px;
      background: url('/static/citybrain/csdn/img/ywt/new/big_title_left.png') no-repeat 100% 100%;
    }

    .title_container .right-img {
      background: url('/static/citybrain/csdn/img/ywt/new/big_title_right.png') no-repeat 100% 100%;
    }

    .title_container .left-line {
      height: 2px;
      background: linear-gradient(90deg, #517796 0%, #00c0ff 100%);
    }

    .title_container .right-line {
      height: 2px;
      background: linear-gradient(270deg, #517796 0%, #00c0ff 100%);
    }

    .title-s-box h4 {
      font-size: 40px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #d6e7f9;
      margin: 0 10px;
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .title-s-box {
      display: flex;
      height: 88px;
      align-items: center;
      align-content: center;
      justify-content: center;
    }

    .title-s-box .left-img-s,
    .title-s-box .right-img-s {
      width: 57px;
      height: 53px;
      background: url('/static/citybrain/csdn/img/ywt/new/small_title_left.png') no-repeat 100% 100%;
    }

    .title-s-box .right-img-s {
      width: 57px;
      height: 53px;
      background: url('/static/citybrain/csdn/img/ywt/new/small_title_right.png') no-repeat 100% 100%;
    }

    .title-s-box .left-line {
      height: 2px;
      background: linear-gradient(270deg, rgba(0, 192, 255, 0.8) 0%, rgba(0, 192, 255, 0.1) 100%);
    }

    .title-s-box .right-line {
      height: 2px;
      background: linear-gradient(90deg, rgba(0, 192, 255, 0.8) 0%, rgba(0, 192, 255, 0.1) 100%);
    }

    .szboxWrap {
      display: flex;
      align-items: center;
    }

    .sz_box {
      width: calc(310px * 0.8);
      height: calc(193px* 0.8);
      background: url('/static/citybrain/csdn/img/ywt/new/index_center_sz.png') no-repeat 100% 100%;
      background-size: cover;
      margin: 44px auto 0;
    }

    .sz_box .count {
      font-family: Bebas Neue;
      font-weight: 400;
      font-size: 60px;
      color: #ffffff;
      background: linear-gradient(0deg, #ffffff 0%, #60cdff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .sz_box .unit {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      line-height: 21px;
      margin-top: 16px;
    }

    .sz_box .descSz {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      line-height: 21px;
      margin-top: 50px;
    }

    .szcl_box {
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;
      margin-top: 32px;
    }

    .szcl_box .szcl_item {
      width: 208px;
      height: 241px;
      background: url('/static/citybrain/csdn/img/ywt/new/index_center_sjcl.png') no-repeat 100% 100%;
      background-size: cover;
      margin: 0 20px;
    }

    .szcl_box .szcl_item .count {
      font-family: Bebas Neue;
      font-weight: 400;
      font-size: 60px;
      color: #ffffff;
      background: linear-gradient(0deg, #ffffff 0%, #60cdff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: 16px;
    }

    .szcl_box .szcl_item .unit_box {
      display: flex;
      justify-content: center;
      margin-top: 80px;
    }

    .szcl_box .szcl_item .unit_box .name {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 34px;
      color: #ffffff;
      line-height: 26px;
    }

    .szcl_box .szcl_item .unit_box .unit {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 23px;
      color: #ffffff;
      line-height: 26px;
    }

    .gzlsj_box {
      display: flex;
      flex-wrap: wrap;
    }

    .gzlsj_box .gzlsj_item {
      width: 192px;
      height: 127px;
      background: url('/static/citybrain/csdn/img/ywt/new/index_center_gzlsj.png') no-repeat 100% 100%;
      background-size: cover;
      margin-left: 74px;
      margin-top: 12px;
      box-sizing: border-box;
    }

    .gzlsj_box .gzlsj_item .name {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 32px;
      color: #ffffff;
      text-align: center;
      margin-top: 20px;
    }

    .gzlsj_box .gzlsj_item .count {
      font-family: Bebas Neue;
      font-weight: 400;
      font-size: 60px;
      color: #9aa9bf;
      background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: left;
      margin-left: 20px;
    }

    .wlss_box {
      display: flex;
      justify-content: center;
    }

    .wlss_box .wlss_item1 {
      width: 364px;
      height: 161px;
      background: url('/static/citybrain/csdn/img/ywt/new/index_center_wlss1.png') no-repeat 100% 100%;
      background-size: cover;
    }

    .wlss_box .wlss_item2 {
      width: 364px;
      height: 161px;
      background: url('/static/citybrain/csdn/img/ywt/new/index_center_wlss1.png') no-repeat 100% 100%;
      background-size: cover;
      margin-left: 82px;
    }

    .wlss_box .name {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      margin-left: 193px;
      margin-top: 24px;
    }

    .wlss_box .count_box {
      display: flex;
      margin-top: 10px;
      margin-left: 193px;
      align-items: baseline;
    }

    .wlss_box .wlss_item2 .count_box {
      display: flex;
      margin-top: 10px;
      margin-left: 161px;
    }

    .wlss_box .count_box .count {
      font-family: Bebas Neue;
      font-weight: 400;
      font-size: 40px;
      color: #ffffff;
      /* background: linear-gradient(0deg, #ffcc00 0.4150390625%, #ffffff 99.5849609375%); */
      background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .wlss_box .count_box .unit {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      /* background: linear-gradient(180deg, #ffffff 0%, #ffc460 50.244140625%, #ffffff 53.0029296875%, #ffeccb 100%); */
      background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-left: 16px;
    }

    .slss_box {
      display: flex;
      justify-content: space-around;
      margin-top: 16px;
    }

    .slss_box .slss_item {
      display: flex;
      align-items: center;
      align-content: center;
    }

    .slss_box .slss_item img {
      width: 144px;
      height: 114px;
    }

    .slss_box .slss_item .type_box {
      margin-left: 16px;
    }

    .slss_box .slss_item .type_box .type_A {
      font-family: Bebas Neue;
      font-weight: 400;
      font-size: 40px;
      color: #FFFFFF;
      background: linear-gradient(0deg, #FFFFFF 0%, #60CDFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .slss_box .slss_item .type_box .type_name {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 30px;
      color: #FFFFFF;
      line-height: 40px;
      margin-top: 14px;
    }

    .ltss_box {
      display: flex;
      justify-content: center;
      margin-top: 16px;
    }

    .ltss_box .ltss_item {
      display: flex;
      align-items: center;
      align-content: center;
    }

    .ltss_box .ltss_item img {
      width: 157px;
      height: 157px;
    }

    .ltss_box .ltss_item .type_box {
      margin-left: 16px;
    }

    .ltss_box .ltss_item .type_box .type_A {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 40px;
    }

    .ltss_box .ltss_item .type_box .type_name {
      font-family: Bebas Neue;
      font-weight: 400;
      font-size: 40px;
      color: #FFFFFF;
      background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: 14px;
    }

    .aqss_box {
      display: flex;
      justify-content: space-around;
    }

    .aqss_box .aqss_item {
      width: 205px;
      height: 401px;
      background: url('/static/citybrain/csdn/img/ywt/new/index_center_aqss.png') no-repeat 100% 100%;
      background-size: cover;
    }

    .aqss_box .aqss_item .name {
      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 36px;
      color: #FFFFFF;
      text-align: center;
      margin-top: 92px;
    }

    .content {
      padding: 0 30px;
      margin-top: -24px;
    }

    .content-top {
      display: flex;
      height: 340px;
      width: 100%;
    }

    .content_top_0 {
      width: 16%;
      text-align: center;
    }

    .content_top_21 {
      width: 9%;
      text-align: center;
    }

    .content_top_1 {
      width: 13%;
      text-align: center;
    }

    .content_top_2 {
      width: 12%;
      text-align: center;
    }

    .content_top_3 {
      width: 25%;
      text-align: center;
    }

    .content_top_4 {
      width: 50%;
      text-align: center;
    }

    .contentWrap {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .contentInnerTop {
      width: 100%;

    }

    .contentInnerBot {
      width: 100%;
      display: flex;
      /* align-items: center; */
    }

    .content-bottom {
      display: flex;
    }

    .content_top_31 {
      width: 58%;
      text-align: center;
    }

    .content_top_22 {
      /* width: 25%; */
      text-align: center;
    }

    .content_top_13 {
      /* width: 25%; */
      text-align: center;
    }

    .content_bottom_1,
    .content_bottom_3 {
      width: 30%;
      text-align: center;
    }

    .content_bottom_2,
    .content_bottom_4 {
      width: 20%;
      text-align: center;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="center_container" v-cloak>
      <div class="title_container">
        <div class="left-line" style="width: 1300px"></div>
        <div class="left-img"></div>
        <h3 style="cursor: pointer">一体化智能化数据平台</h3>
        <div class="right-img"></div>
        <div class="right-line" style="width: 1300px"></div>
      </div>
      <div class="content-top">
        <div class="content_top_0">
          <div class="title-s-box">
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">资源目录</h4>
            <div class="right-img-s"></div>
          </div>
          <div class="szboxWrap">
            <div class="sz_box">
              <div class="count">45127</div>
              <div class="unit">个</div>
              <div class="descSz">总数</div>
            </div>
            <div class="sz_box">
              <div class="count">272</div>
              <div class="unit">个</div>
              <div class="descSz">当年新增</div>
            </div>
          </div>

        </div>
        <div class="content_top_21">
          <div class="title-s-box">
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">数据指南</h4>
            <div class="right-img-s"></div>
          </div>
          <div class="sz_box">
            <div class="count">221</div>
            <div class="unit">个</div>
          </div>
        </div>
        <div class="content_top_3">
          <div class="title-s-box">
            <div class="left-line" style="width: 192px"></div>
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">数据处理</h4>
            <div class="right-img-s"></div>
            <div class="right-line" style="width: 192px"></div>
          </div>
          <div class="szcl_box">
            <div class="szcl_item">
              <div class="count">974.81</div>
              <div class="unit_box">
                <span class="name">归集</span>
                <span class="unit">(亿条)</span>
              </div>
            </div>
            <div class="szcl_item">
              <div class="count">369.83</div>
              <div class="unit_box">
                <span class="name">治理</span>
                <span class="unit">(亿条)</span>
              </div>
            </div>
            <div class="szcl_item">
              <div class="count">-</div>
              <div class="unit_box">
                <span class="name">标注</span>
                <span class="unit">(亿条)</span>
              </div>
            </div>
          </div>
        </div>

        <div class="contentWrap">
          <div class="contentInnerTop">
            <div class="title-s-box">
              <div class="left-line" style="width: 192px"></div>
              <div class="left-img-s"></div>
              <h4 style="cursor: pointer">高质量数据集语料库</h4>
              <div class="right-img-s"></div>
              <div class="right-line" style="width: 192px"></div>
            </div>
          </div>
          <div class="contentInnerBot">
            <div class="content_top_31">
              <div class="szcl_box">
                <div class="szcl_item">
                  <div class="count">9</div>
                  <div class="unit_box">
                    <span class="name">人口库</span>
                    <span class="unit">(类)</span>
                  </div>
                </div>
                <div class="szcl_item">
                  <div class="count">17</div>
                  <div class="unit_box">
                    <span class="name">法人库</span>
                    <span class="unit">(类)</span>
                  </div>
                </div>
                <div class="szcl_item">
                  <div class="count">8</div>
                  <div class="unit_box">
                    <span class="name">信用库</span>
                    <span class="unit">(类)</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="content_top_22">
              <div class="sz_box">
                <div class="count">126</div>
                <div class="unit">个</div>
                <div class="descSz">专题库</div>
              </div>
            </div>
            <div class="content_top_13">
              <div class="sz_box">
                <div class="count">387</div>
                <div class="unit">个</div>
                <div class="descSz">专题库目录</div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="content_top_4">
          <div class="title-s-box">
            <div class="left-line" style="width: 581px"></div>
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">高质量数据集语料库</h4>
            <div class="right-img-s"></div>
            <div class="right-line" style="width: 581px"></div>
          </div>
          <div class="gzlsj_box">
            <div class="gzlsj_item" v-for="(item, index) in wlkList">
              <div class="name">{{item.name}}</div>
              <div class="count">{{item.num?item.num:'-'}}</div>
            </div>
          </div>
        </div> -->
      </div>
      <div class="title_container">
        <div class="left-line" style="width: 1460px"></div>
        <div class="left-img"></div>
        <h3 style="cursor: pointer">数据基础设施</h3>
        <div class="right-img"></div>
        <div class="right-line" style="width: 1460px"></div>
      </div>
      <div class="content-bottom">
        <div class="content_bottom_1">
          <div class="title-s-box">
            <div class="left-line" style="width: 281px"></div>
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">网络设施</h4>
            <div class="right-img-s"></div>
            <div class="right-line" style="width: 281px"></div>
          </div>
          <div class="wlss_box">
            <div class="wlss_item1">
              <div class="name">5G基站</div>
              <div class="count_box">
                <div class="count">2.9</div>
                <div class="unit">万个</div>
              </div>
            </div>
            <div class="wlss_item2">
              <div class="name">5G用户</div>
              <div class="count_box" style="margin-left: 157px;">
                <div class="count">343.31</div>
                <div class="unit">万户</div>
              </div>
            </div>
          </div>
        </div>
        <div class="content_bottom_2">
          <div class="title-s-box">
            <div class="left-line" style="width: 188px"></div>
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">算力设施</h4>
            <div class="right-img-s"></div>
            <div class="right-line" style="width: 188px"></div>
          </div>
          <div class="slss_box">
            <div class="slss_item">
              <img src="/static/citybrain/csdn/img/ywt/new/index_center_slss1.png" alt="">
              <div class="type_box">
                <div class="type_A">1.76PFlops</div>
                <div class="type_name">超算算力</div>
              </div>
            </div>
            <div class="slss_item">
              <img src="/static/citybrain/csdn/img/ywt/new/index_center_slss2.png" alt="">
              <div class="type_box">
                <div class="type_A">1401PFlops</div>
                <div class="type_name">智算算力</div>
              </div>
            </div>
          </div>
        </div>
        <div class="content_bottom_3">
          <div class="title-s-box">
            <div class="left-line" style="width: 281px"></div>
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">流通设施</h4>
            <div class="right-img-s"></div>
            <div class="right-line" style="width: 281px"></div>
          </div>
          <div class="ltss_box">
            <div class="ltss_item">
              <img src="/static/citybrain/csdn/img/ywt/new/index_center_ltss1.png" alt="">
              <div class="type_box">
                <div class="type_A">数据空间</div>
                <div class="type_name">2741PB</div>
              </div>
            </div>
            <div class="ltss_item" style="margin-left:100px">
              <img src="/static/citybrain/csdn/img/ywt/new/index_center_ltss2.png" alt="">
              <div class="type_box">
                <div class="type_A">高速数据网</div>
                <div class="type_name">1000Mb/s</div>
              </div>
            </div>
          </div>
        </div>
        <div class="content_bottom_4">
          <div class="title-s-box">
            <div class="left-line" style="width: 188px"></div>
            <div class="left-img-s"></div>
            <h4 style="cursor: pointer">安全设施</h4>
            <div class="right-img-s"></div>
            <div class="right-line" style="width: 188px"></div>
          </div>
          <div class="aqss_box">
            <div class="aqss_item">
              <div class="name">隐私计算</div>
            </div>
            <div class="aqss_item">
              <div class="name">联邦学习</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script src="/static/js/home_services/bmjr.js"></script>
<script>
  var vm = new Vue({
    el: '#app',
    data() {
      return {
        //高质量数据集语料库
        wlkList: [
          { name: '工业制造', num: 0 },
          { name: '现代农业', num: 0 },
          { name: '商贸流通', num: 0 },
          { name: '交通运输', num: 0 },
          { name: '金融服务', num: 0 },
          { name: '科技创新', num: 0 },
          { name: '文化旅游', num: 0 },
          { name: '医疗健康', num: 0 },
          { name: '应急管理', num: 0 },
          { name: '气象服务', num: 0 },
          { name: '城市治理', num: 0 },
          { name: '绿色低碳', num: 0 },
        ],
      }
    },
    created() { },
    mounted() { },
    computed: {},
    methods: {},
  })
</script>

</html>