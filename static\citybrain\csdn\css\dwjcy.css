[v-cloak] {
  display: none;
}

html,
body,
p {
  margin: 0;
  padding: 0;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #20aeff;
  height: 8px;
}

.main {
  width: 100%;
  height: calc(100% - 154px);
  display: flex;
  flex-wrap: wrap;
  padding: 43px 16px 45px;
  box-sizing: border-box;
  justify-content: flex-start;
  position: relative;
}

.title-con>p {
  text-align: center;
  font-size: 80px;
  font-weight: bold;
  line-height: 140px;
  color: #ffffff;
  text-shadow: 0px 0px 0px rgba(255, 255, 255, 0.9);
  background: linear-gradient(0deg, #c2e5ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ul>ul {
  display: block;
}

.part {
  font-size: 60px;
  color: #fff;
  margin-right: 25px;
}

.part_name {
  margin: 0;
  padding: 0;
  font-size: 38px;
  line-height: 80px;
  margin-left: 30px;
}

.part>p {
  margin: 0;
  padding: 0;
  font-size: 38px;
  line-height: 80px;
  margin-left: 30px;
}

.container {
  width: 3840px;
  height: 2160px;
  background: url('/static/citybrain/csdn/img/dwjcy/back2.png') no-repeat;
  background-size: 100% 100%;
}

.title {
  width: 100%;
  height: 154px;
}

.title-con {
  width: 1694px;
  height: 154px;
  margin: 0 auto;
  background: url('/static/citybrain/csdn/img/dwjcy/top-back.png') no-repeat;
  background-size: 100% 100%;
}

.part-1 {
  width: 2435px;
  height: 869px;
  padding: 30px 50px;
  box-sizing: border-box;
  background: url('/static/citybrain/csdn/img/dwjcy/part1.png') no-repeat;
  background-size: 100% 100%;
}

.part-2 {
  width: 1336px;
  height: 869px;
  background: url('/static/citybrain/csdn/img/dwjcy/part2.png') no-repeat;
  background-size: 100% 100%;
}

.part-3 {
  width: 1450px;
  height: 513px;
  position: relative;
  top: 6px;
  background: url('/static/citybrain/csdn/img/dwjcy/part3.png') no-repeat;
  background-size: 100% 100%;
}

.part-4 {
  width: 1052px;
  height: 513px;
  position: relative;
  top: 6px;
  background: url('/static/citybrain/csdn/img/dwjcy/part4.png') no-repeat;
  background-size: 100% 100%;
}

.part-5 {
  width: 1252px;
  height: 513px;
  position: relative;
  top: 6px;
  background: url('/static/citybrain/csdn/img/dwjcy/part5.png') no-repeat;
  background-size: 100% 100%;
}

.part-6 {
  width: 3792px;
  height: 498px;
  position: relative;
  top: 17px;
  background: url('/static/citybrain/csdn/img/dwjcy/part1.png') no-repeat;
  background-size: 100% 100%;
}

/* part-1 */
.part-1-top {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  margin-top: 30px;
}

.top-item {
  display: flex;
  position: relative;
  cursor: pointer;
}

.top-item-active {
  width: 425px;
  height: 148px;
  background: url('/static/citybrain/csdn/img/dwjcy/top-item-active2.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: -12px;
  left: -50px;
}

.part-1-middle {
  width: 2268px;
  height: 19px;
  margin: 20px auto 0;
  background: url('/static/citybrain/csdn/img/dwjcy/hr.png') no-repeat;
  background-size: 100% 100%;
}

.part-1-bottom {
  width: 100%;
  height: 550px;
  /* margin-top: 80px; */
  padding: 0 60px;
  box-sizing: border-box;
  position: relative;
}

.bottom-item {
  width: 490px;
  height: 260px;
  background: url('/static/citybrain/csdn/img/dwjcy/bottom-item-back.png') no-repeat;
  background-size: 100% 100%;
}

.part-1-bottom .bottom-item {
  margin-right: 84px;
}

.part-1-bottom .bottom-item:nth-child(4n) {
  margin-right: 0;
}

.bottom-item-hover:hover .con-hover {
  display: block;
}

.bottom-item-hover:hover .con-img {
  display: none;
}

.bottom-item2 .con-hover {
  display: block;
}

.bottom-item2 .con-img {
  display: none;
}

.bottom-item-title {
  width: 100%;
  height: 54px;
  background: url('/static/citybrain/csdn/img/dwjcy/bottom-back.png') no-repeat;
  background-size: 100% 100%;
  font-size: 32px;
  text-align: center;
  line-height: 54px;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bottom-item-title2 {
  width: 100%;
  height: 44px;
  font-size: 32px;
  text-align: center;
  line-height: 44px;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 5px 20px;
  box-sizing: border-box;
}

.bottom-item-title-tooltip {
  font-size: 28px;
}

.btn-left {
  position: absolute;
  top: 170px;
  left: -20px;
  cursor: not-allowed;
  opacity: 0.75;
}

.btn-left:active {
  opacity: 1;
}

.btn-right {
  position: absolute;
  top: 170px;
  right: -20px;
  cursor: pointer;
  opacity: 0.75;
}

.btn-right:active {
  opacity: 1;
}

.bottom-item-con {
  font-size: 28px;
  color: #d6e7f9;
  padding: 10px 5px;
  box-sizing: border-box;
  height: 90px;
  overflow-y: auto;
  text-indent: 60px;
}

/* 设置滚动条的样式 */
.bottom-item-con::-webkit-scrollbar {
  width: 10px;
}

/* 滚动槽 */
.bottom-item-con::-webkit-scrollbar-track {
  border-radius: 5px;
}

/* 滚动条滑块 */
.bottom-item-con::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.1);
}

.bottom-item-con::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(27, 146, 215, 0.4);
}

.bottom-item-num {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.el-carousel {
  overflow: hidden;
}

.el-carousel__container {
  height: 540px;
}

.el-carousel__item {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.el-carousel__indicators {
  display: none;
}

.el-carousel__button {
  width: 10px;
  height: 10px;
  margin: 0 10px;
  border-radius: 50%;
}

.echarts-3-css {
  width: 98%;
  height: 400px;
  margin: auto;
}

.table {
  width: 100%;
  height: 100%;
}

.table-th {
  display: flex;
  display: -webkit-flex;
  width: 100%;
  height: 60px;
  background-color: #00396f;
  margin-bottom: 10px;
}

.th {
  flex: 1;
  width: 0;
  text-align: center;
  font-size: 32px;
  line-height: 60px;
  color: #77b3f1;
}

.table-tr {
  width: 100%;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.tr {
  display: flex;
  display: -webkit-flex;
  width: 100%;
  padding: 10px 0;
  background-color: #0f2b4d;
  margin-bottom: 10px;
}

.td {
  flex: 1;
  width: 0;
  text-align: center;
  word-break: break-all;
  font-size: 32px;
  color: #d6e7f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.td:nth-child(2),
.th:nth-child(2) {
  flex: 0.4;
}

.td:nth-child(5),
.th:nth-child(5) {
  flex: 0.8;
}

.td>div>div {
  background-size: 100% 100%;
}

.echarts-icon {
  position: relative;
}

.icon-img {
  position: absolute;
  left: 50px;
}

.icon-img li {
  width: 40px;
  height: 40px;
  margin-bottom: 32px;
}

.icon-img img {
  width: 40px;
  height: 40px;
}

.echarts-5-css {
  background-image: url('/static/citybrain/csdn/img/dwjcy/echarts-bottom.png');
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center 280px;
}

/* 下拉框 */

.select02 {
  display: inline-block;
  width: 260px;
  height: 60px;
  text-align: right;
  position: absolute;
  right: 10px;
  top: 0;
  z-index: 100;
}

.select02 .flow-icon {
  width: 25px;
  position: absolute;
  top: 0;
  right: 32px;
}

.select02 .ul {
  height: 100%;
  font-size: 38px;
  border-radius: 0;
}

.select02 .ul>div {
  height: 100%;
  line-height: 60px;
  border: 1px solid;
  border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 0.3 0.3;
}

.select02 .ul>ul>li:first-of-type {
  border-radius: 0;
}

.select02 .ul ul>li:hover {
  background-color: #244377;
}

.select02 .ul ul>li:hover>span {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.select02 .ul ul>li {
  background-color: #122b4a;
}

.part-1-tabs {
  width: 100%;
  height: 78px;
  font-size: 29px;
  display: flex;
  align-items: center;
  padding: 10px 218px 10px 100px;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 10px;
}

.part-1-tabs .el-carousel {
  width: 100%;
  height: 78px;
}

.tab_lb {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  /* overflow: hidden; */
}

.tab_left {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 10px;
  left: 40px;
}

.tab_right {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 10px;
  right: 150px;
}

.part-1-tabs-item {
  width: 300px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  background: url('/static/citybrain/csdn/img/ywt/doubleclick.png') no-repeat;
  background-size: 100% 100%;
}

.part-1-tabs-item-active {
  background: url('/static/citybrain/csdn/img/ywt/doubleclick-active.png') no-repeat;
  background-size: 100% 100%;
}

.part-1-tabs-item:last-child {
  margin-right: 0;
}

.more-css {
  position: absolute;
  right: 40px;
  width: 100px;
  height: 100%;
  text-align: right;
}

.arrow-css {
  display: inline-block;
  width: 30px;
  height: 30px;
  background: url('/static/citybrain/csdn/img/cstz3/up-02.png') no-repeat;
  background-size: 172% 152%;
  background-position: center;
}

.con-hover {
  display: none;
  padding: 10px 20px;
  box-sizing: border-box;
  animation: 2s moveHover;
  -webkit-animation: 2s moveHover;
  animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
}

.con-top-img {
  width: 100%;
  height: 200px;
  padding: 0 6px;
  box-sizing: border-box;
  border-radius: 30px 30px 0 0;
  overflow: hidden;
}

.con-top-img>img {
  width: 100%;
  height: 100%;
}

/* 鼠标悬浮动画 */
@keyframes moveHover {
  0% {
    opacity: 0.1;
  }

  100% {
    opacity: 1;
  }
}

.more-widows {
  width: 99%;
  height: 1965px;
  /* background-color: #01162E; */
  background-color: #091c3e;
  position: absolute;
  top: 0;
  z-index: 105;
  padding: 50px;
  box-sizing: border-box;
}

.more-back {
  width: 100%;
  height: 100px;
  font-size: 40px;
  text-align: right;
  color: #fff;
}

.more-con {
  width: 100%;
  height: calc(100% - 40px);
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  overflow: hidden;
  overflow-y: auto;
}

.more-con .bottom-item {
  width: 580px;
  height: 360px;
  margin-right: 40px;
  margin-bottom: 40px;
}

.more-con .bottom-item-con {
  height: 180px;
}

.more-con .con-top-img {
  height: 290px;
}

.more-con .bottom-item:nth-child(6n) {
  margin-right: 0;
}