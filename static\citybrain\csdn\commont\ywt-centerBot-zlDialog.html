<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>首页治理-弹窗1</title>
  <script src="../Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui2.15.12.js"></script>
  <style>
    #zl-step1 {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .step1-imgbox {
      position: relative;
      width: 3680px;
      height: 2160px;
    }

    .step1-imgbox .sjqx-btn {
      position: absolute;
      z-index: 10;
      left: 1045px;
      top: 580px;
      width: 300px;
      height: 100px;
    }

    .step2-imgbox {
      position: relative;
      width: 3729px;
      height: 2160px;
    }

    .back-button {
      position: absolute;
      z-index: 10;
      left: 6300px;
      top: 200px;
      width: 300px;
      font-size: 50px;
    }

    .step2-imgbox .tmt-bg1 {
      position: absolute;
      z-index: 40;
      top: 0;
      left: 0;
      width: 100%;
    }

    .step2-imgbox .tmt-bg2 {
      position: absolute;
      z-index: 40;
      top: 830px;
      left: 0;
      width: 100%;
    }

    .step2-imgbox .tmt-bg3 {
      position: absolute;
      z-index: 40;
      top: 1517px;
      left: 0;
      width: 100%;
    }

    .step-btns-area {
      position: absolute;
      z-index: 30;
      right: -1000px;
      top: 600px;
    }

    .step-btns-area .btn {
      width: 500px;
      height: 100px;
      line-height: 100px;
      margin: 40px 0;
      border-radius: 6px;
      text-align: center;
      background: #409eff;
      font-size: 50px;
      font-weight: bold;
      color: #fff;
    }

    .step-btns-area .btn2 {
      background: #67c23a;
    }

    .step-btns-area .btn3 {
      background: #e6a23c;
    }

    .step3-imgbox {
      position: relative;
      width: 2715px;
      height: 2160px;
    }

    .step4-imgbox {
      position: relative;
      width: 1312px;
      height: 2094px;
    }

    .step5-imgbox {
      position: relative;
      width: 1263px;
      height: 2100px;
    }
  </style>
</head>

<body>
  <div id="zl-step1">
    <el-button type="info" class="back-button" @click="goBack" v-show="step!==1">返回</el-button>
    <div class="step1-imgbox" v-show="step === 1">
      <el-image src="../img/zl/数据组汇报材料图.png"></el-image>
      <div class="sjqx-btn" @click="sjqxBtnClick"></div>
    </div>
    <div class="step2-imgbox" v-show="step === 2">
      <el-image style="width: 100%; height: 100%" src="../img/zl/城市大脑数据架构.png"></el-image>
      <el-image class="tmt-bg1" src="../img/zl/tmt-bg1.png" :preview-src-list="srcList" :initial-index="0">
      </el-image>
      <el-image class="tmt-bg2" src="../img/zl/tmt-bg2.png" :preview-src-list="srcList" :initial-index="1">
      </el-image>
      <el-image class="tmt-bg3" src="../img/zl/tmt-bg3.png" :preview-src-list="srcList" :initial-index="2">
      </el-image>
      <div class="step-btns-area">
        <div class="btn btn1" @click="goStep2(3)">数据质量和标准</div>
        <div class="btn btn2" @click="goStep2(4)">数据安全</div>
        <div class="btn btn3" @click="goStep2(5)">数据开发效率</div>
      </div>
    </div>
    <div class="step3-imgbox" v-show="step === 3">
      <el-image src="../img/zl/数据质量标准.png"></el-image>
    </div>
    <div class="step4-imgbox" v-show="step === 4">
      <el-image src="../img/zl/图片5.png"></el-image>
    </div>
    <div class="step5-imgbox" v-show="step === 5">
      <el-image src="../img/zl/图片6.png"></el-image>
    </div>
    <!-- <img v-if="step === 1" src="../img/zl/数据组汇报材料图1.png" alt="" /> -->
  </div>
  <script>
    var vm = new Vue({
      el: '#zl-step1',
      data() {
        return {
          step: 1,
          srcList: ['../img/zl/sjjg-top.png', '../img/zl/sjjg-center.png', '../img/zl/sjjg-bottom.png'],
        }
      },
      methods: {
        sjqxBtnClick() {
          this.step = 2
          console.log('sjqxBtnClick')
        },
        goBack() {
          if (this.step === 2) {
            this.step = 1
          }
          if (this.step === 3 || this.step === 4 || this.step === 5) {
            this.step = 2
          }
        },
        goStep2(i) {
          this.step = i
        },
      },
    })
  </script>
</body>

</html>