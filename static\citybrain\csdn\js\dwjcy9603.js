var vm = new Vue({
  el: '#dwjcy',
  data() {
    return {
      showMove: false,
      tabIndex1: 0,
      tabIndex2: 0,
      tabIndex20: 0,
      tabIndex21: 0,
      tabIndex22: 0,
      tabIndex23: 0,
      tabParenIndex: 0,
      tabParenIndex0: 0,
      tabParenIndex1: 0,
      tabParenIndex2: 0,
      tabParenIndex3: 0,
      tabList1: [
        {
          img: '/static/citybrain/csdn/img/dwjcy/top-1_9603.png',
          label: '算法',
          num: '89',
          unit1: '个',
          dycs: '64529',
          unit2: '次',
        },
        {
          img: '/static/citybrain/csdn/img/dwjcy/top-2_9603.png',
          label: '模型',
          num: '64',
          unit1: '个',
          dycs: '64529',
          unit2: '次',
        },
        {
          img: '/static/citybrain/csdn/img/dwjcy/top-3_9603.png',
          label: '知识',
          num: '64',
          unit1: '个',
          dycs: '64529',
          unit2: '次',
        },
        {
          img: '/static/citybrain/csdn/img/dwjcy/top-4_9603.png',
          label: '组件',
          num: '76',
          unit1: '个',
          dycs: '64529',
          unit2: '次',
        },
      ],
      lists: [],
      list1s: [],
      list2s: [],
      list3s: [],
      list: [],
      list1: [],
      list2: [],
      list3: [],
      label: ['calltime', 'zylx', 'zymc', 'callsystem', 'calldept'],
      thData: ['调用时间', '类别', '名称', '调用应用', '调用单位'],
      trData: [],
      showSelct: false,
      optionValue: '全部',
      showSelct1: false,
      optionValue1: '全部',
      optionsData: ['全部', '模型', '知识', '算法'],
      firstData: '全部',
      tabData: [
        {
          label: '全部',
          num: '448',
        },
        {
          label: '用户组织',
          num: '0',
        },
        {
          label: '政务服务',
          num: '16',
        },
      ],
      tabData0: [
        {
          label: '全部',
          num: '448',
        },
        {
          label: '用户组织',
          num: '0',
        },
        {
          label: '政务服务',
          num: '16',
        },
      ],
      tabData1: [
        {
          label: '全部',
          num: '448',
        },
        {
          label: '用户组织',
          num: '0',
        },
        {
          label: '政务服务',
          num: '16',
        },
      ],
      tabData2: [
        {
          label: '全部',
          num: '448',
        },
        {
          label: '用户组织',
          num: '0',
        },
        {
          label: '政务服务',
          num: '16',
        },
      ],
      tabData3: [
        {
          label: '全部',
          num: '448',
        },
        {
          label: '用户组织',
          num: '0',
        },
        {
          label: '政务服务',
          num: '16',
        },
      ],

      currentTarget: 0,
      currentTarget0: 0,
      currentTarget1: 0,
      currentTarget2: 0,
      currentTarget3: 0,
      loading: false,
      loading0: false,
      loading1: false,
      loading2: false,
      loading3: false,
    }
  },
  computed: {
    tabDataList() {
      let newArr = []
      for (let i = 0; i < this.tabData.length; i += 7) {
        newArr.push(this.tabData.slice(i, i + 7))
      }
      return newArr
    },
    tabDataList0() {
      let newArr = []
      for (let i = 0; i < this.tabData0.length; i += 7) {
        newArr.push(this.tabData0.slice(i, i + 7))
      }
      return newArr
    },
    tabDataList1() {
      let newArr = []
      for (let i = 0; i < this.tabData1.length; i += 7) {
        newArr.push(this.tabData1.slice(i, i + 7))
      }
      console.log('tabDataList1', newArr)
      return newArr
    },
    tabDataList2() {
      let newArr = []
      for (let i = 0; i < this.tabData2.length; i += 7) {
        newArr.push(this.tabData2.slice(i, i + 7))
      }
      return newArr
    },
    tabDataList3() {
      let newArr = []
      for (let i = 0; i < this.tabData3.length; i += 7) {
        newArr.push(this.tabData3.slice(i, i + 7))
      }
      return newArr
    },
  },
  mounted() {
    this.changeTab2('全部', '算法', 0, 0, 'list')
    this.changeTab2('全部', '模型', 0, 0, 'list1')
    this.changeTab2('全部', '知识', 0, 0, 'list2')
    this.changeTab2('全部', '组件', 0, 0, 'list3')
    this.changeTab1(this.tabIndex1, '算法', 0)
    this.changeTab1(this.tabIndex1, '模型', 1, false)
    this.changeTab1(this.tabIndex1, '知识', 2, false)
    this.changeTab1(this.tabIndex1, '组件', 3, false)

    this.init()
  },
  methods: {
    openWin(url) {
      // let token = sessionStorage.getItem('token')
      // let urlStr = url + '?token=' + token
      // if (url != undefined) {
      //   // top.commonObj.openWinHtml('3840', '2160', url)
      //   window.open(
      //     urlStr,
      //     '安全',
      //     'directories=no, location=no, scrollbars=yes, resizable=yes, height=2160' +
      //       ', width = 3840' +
      //       ', top=0, left=1920'
      //   )
      // }
    },
    init() {
      $api('/csdnsydwjc_left11', { code: isTmpData }).then((res) => {
        let result = []
        this.tabList1.forEach((item) => {
          res.forEach((obj) => {
            if (item.label === obj.label) {
              result.push({ ...item, num: obj.num, dycs: obj.dycs })
            }
          })
        })
        this.tabList1 = result
      })
      $api('csdnsydwjc_left21').then((res) => {
        this.trData = res
      })
      // $api('csdnsydwjc_left22', { code: '全部' }).then((res) => {
      //   let dataNew = res
      //   this.showLineEcharts(dataNew, 'echarts4')
      // })
      // $api('csdnsydwjc_left23').then((res) => {
      //   this.showLineEcharts1('echarts5', res)
      // })
      $api('csdnsydwjc_left31', { code: '全部' }).then((res) => {
        this.showLineEcharts2('echarts6', res)
      })
    },
    // 控制显示隐藏
    controlShow(num) {
      if (num == 0) {
        this.showSelct = !this.showSelct
      } else if (num == 1) {
        this.showSelct1 = !this.showSelct1
      }
    },
    //切换调用轮播数据
    changeTab1(index, label, index2, boolean = true) {
      $api('csdnsydwjc_right11', { code: label }).then((res) => {
        if (boolean) {
          this.showPart2_chart('part2_chart', res)
        }

        res[0].labelname = '全部'
        this[`tabData${index2}`] = res
          .filter((el) => {
            return el.level < 3
          })
          .map((item) => {
            item.label = item.labelname
            return item
          })
      })
      this.tabIndex1 = index
      this.firstData = label
      // this.$refs.tab.setActiveItem(0)
      // this.changeTab2('全部', label, 0, 0)
    },
    changeTab2(code, lx, index, parenIndex, field = 'list', btnIndex = 0) {
      console.log('code', code)
      console.log('lx', lx)
      this[`tabIndex2${btnIndex}`] = index
      this[`tabParenIndex${btnIndex}`] = parenIndex
      this[`loading${btnIndex}`] = true
      $api('/csdnsydwjc_left13', { code: code, lx: lx }).then((res) => {
        this.currentTarget = 0
        this[`currentTarget${btnIndex}`] = 0
        this[`loading${btnIndex}`] = false
        this[`${field}s`] = res
        this[field] = res.slice(0, 8)
        console.log('object', this[field])
        if (this[field].length < 8) {
          var right = document.getElementsByClassName('btn-right')
          right[btnIndex].style.cursor = 'not-allowed'
        } else {
          var right = document.getElementsByClassName('btn-right')
          right[btnIndex].style.cursor = 'pointer'
        }
      })
    },
    to_prev(field, index = 0) {
      var left = document.getElementsByClassName('btn-left')
      if (this[`currentTarget${index}`] <= 0) {
        left[index].style.cursor = 'not-allowed'
        return false
      }
      this[`currentTarget${index}`]--
      if (this[`currentTarget${index}`] == 0) {
        left[index].style.cursor = 'not-allowed'
      }
      this[field] = this[`${field}s`].slice(this[`currentTarget${index}`] * 8, (this[`currentTarget${index}`] + 1) * 8)
    },
    to_next(field, index = 0) {
      if (this.lists.length > 8) {
        this[`currentTarget${index}`]++
        var left = document.getElementsByClassName('btn-left')
        left[index].style.cursor = 'pointer'
        this[field] = this[`${field}s`].slice(
          this[`currentTarget${index}`] * 8,
          (this[`currentTarget${index}`] + 1) * 8
        )
        // console.log(  this[`currentTarget${index}`])
        if (this[field].length === 0) {
          this[field] = this[`${field}s`].slice(0, 8)
          this[`currentTarget${index}`] = 0
        }
      }
    },

    // tab切换轮播
    tab_to_prev1() {
      this.$refs.tab1.prev()
    },
    tab_to_next1() {
      this.$refs.tab1.next()
    }, // tab切换轮播
    tab_to_prev2() {
      this.$refs.tab2.prev()
    },
    tab_to_next2() {
      this.$refs.tab2.next()
    }, // tab切换轮播
    tab_to_prev3() {
      this.$refs.tab3.prev()
    },
    tab_to_next3() {
      this.$refs.tab3.next()
    }, // tab切换轮播
    tab_to_prev4() {
      this.$refs.tab4.prev()
    },
    tab_to_next4() {
      this.$refs.tab4.next()
    },

    showPart2_chart(id, data) {
      let myEc = echarts.init(document.getElementById(id))
      let datas = []
      let lines = []
      data.forEach((item) => {
        let list1 = {
          name: item.labelname,
          id: item.labelcode,
          category: item.parent,
          draggable: true,
        }
        let list2 = {
          source: item.parent,
          target: item.labelcode,
          value: '',
        }
        datas.push(list1)
        lines.push(list2)
      })
      // var datas = [
      //   {
      //     name: "人员1",
      //     id: "1-1",
      //     category: 1,
      //     draggable: true,
      //   },
      //   {
      //     name: "文献",
      //     id: "3",
      //     category: 2,
      //     draggable: true,
      //   },
      //   {
      //     name: "机构",
      //     id: "2",
      //     category: 1,
      //     draggable: true,
      //   },
      //   {
      //     name: "项目",
      //     id: "0",
      //     category: 0,
      //     draggable: true,
      //   },
      //   {
      //     name: "人员",
      //     id: "1",
      //     category: 3,
      //     draggable: true,
      //   },
      //   {
      //     name: "人员2",
      //     id: "4",
      //     category: 2,
      //     draggable: true,
      //   },
      //   {
      //     name: "人员3",
      //     id: "5",
      //     category: 3,
      //     draggable: true,
      //   },
      // ];

      // var lines = [
      //   {
      //     source: "1",
      //     target: "1-1",
      //     value: "",
      //   },
      //   {
      //     source: "0",
      //     target: "1",
      //     value: "",
      //   },
      //   {
      //     source: "0",
      //     target: "2",
      //     value: "",
      //   },
      //   {
      //     source: "0",
      //     target: "3",
      //     value: "",
      //   },
      //   {
      //     source: "0",
      //     target: "4",
      //     value: "",
      //   },
      //   {
      //     source: "2",
      //     target: "5",
      //     value: "",
      //   },
      // ];
      const color1 = '#ff8400'
      const color2 = '#aa61b2'
      const color3 = '#0a95e6'
      const color4 = '#10a050'
      const color5 = '#f06467'
      const color6 = '#006acc'
      const color7 = '#ff0000'
      datas.forEach((node) => {
        if (node.category === 'dwjc-sf') {
          node.symbolSize = 100
          node.itemStyle = {
            color: color1,
          }
        } else if (node.category === 'dwjc-mx') {
          node.itemStyle = {
            color: color2,
          }
        } else if (node.category === 'dwjc-zj') {
          node.itemStyle = {
            color: color3,
          }
        } else if (node.category === 3) {
          node.itemStyle = {
            color: color4,
          }
        } else if (node.category === 4) {
          node.itemStyle = {
            color: color5,
          }
        } else if (node.category === 5) {
          node.itemStyle = {
            color: color6,
          }
        } else if (node.category === 6) {
          node.itemStyle = {
            color: color7,
          }
        } else if (node.category === 7) {
          node.itemStyle = {
            color: color8,
          }
        }
      })

      var option = {
        title: {
          text: '',
        },
        tooltip: { show: false },
        animationDurationUpdate: 500,
        label: {
          normal: {
            show: true,
          },
        },
        series: [
          {
            type: 'graph',
            layout: 'force', //采用力引导布局
            symbolSize: 100,
            legendHoverLink: true, //启用图例 hover 时的联动高亮。
            focusNodeAdjacency: true, //在鼠标移到节点上的时候突出显示节点以及节点的边和邻接节点。
            roam: true,
            label: {
              normal: {
                show: true,
                position: 'inside',
                fontSize: 26,
                color: '#fff',
              },
            },
            force: {
              repulsion: 600,
              edgeLength: 100,
            },
            edgeSymbolSize: [24, 50],
            edgeLabel: {
              normal: {
                show: true,
                textStyle: {
                  fontSize: 26,
                },
                formatter: '{c}',
              },
            },
            categories: [
              {
                itemStyle: {
                  normal: {
                    color: 'pink',
                  },
                },
              },
              {
                itemStyle: {
                  normal: {
                    color: '#0099FF',
                  },
                },
              },
              {
                itemStyle: {
                  normal: {
                    color: '#5DADE2',
                  },
                },
              },
            ],
            data: datas,
            links: lines,
            lineStyle: {
              normal: {
                opacity: 0.9,
                width: 1,
                curveness: 0,
              },
            },
          },
        ],
      }
      myEc.setOption(option)
      // 鼠标移入取消小手
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },

    // 区县的点击事件
    getOptionData(item, index) {
      let that = this
      if (index == 0) {
        this.optionValue = item
        this.showSelct = true
        // console.log(this.showSelct)
        // $api('csdnsydwjc_left22', { code: this.optionValue }).then((res) => {
        //   this.showLineEcharts(res, 'echarts4')
        // })
      } else {
        this.optionValue1 = item
        this.showSelct1 = true
        $api('csdnsydwjc_left31', { code: this.optionValue1 }).then((res) => {
          this.showLineEcharts2('echarts6', res)
        })
      }
    },
    showLineEcharts(datas, id) {
      let echarts0 = echarts.init(document.getElementById(id))
      let colorARrr = ['#FEAC6B', '#FCD785', '#DA70D6', '#3FA9D0', '#4DD6FF']
      let name = []
      let value = []
      datas.forEach((item) => {
        name.push(item.zymc)
        value.push(item.dycs)
      })
      let sum = Math.max(...value)
      let bfbData = this.countPercentage(value, sum)

      let option = {
        grid: {
          top: '10%',
          bottom: '-5%',
          left: '4%',
          right: '-8%',
          containLabel: true,
        },
        xAxis: {
          show: false,
        },
        yAxis: [
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: name,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
              interval: 0,
              color: '#fff',
              align: 'left',
              fontSize: 30,
              formatter: function (value, index) {
                return '{title|' + value + '}'
              },
              rich: {
                title: {
                  width: 100,
                },
              },
            },
          },
          {
            triggerEvent: true,
            show: true,
            inverse: true,
            data: value,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              shadowOffsetX: '-20px',
              color: '#fff',
              align: 'right',
              verticalAlign: 'bottom',
              lineHeight: 50,
              fontSize: 30,
              formatter: function (value, index) {
                // return value+"%    ";
                return value + '  '
              },
            },
          },
        ],
        series: [
          {
            name: '条',
            type: 'bar',
            showBackground: true,
            barBorderRadius: 30,
            yAxisIndex: 0,
            data: bfbData,
            barWidth: 10,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorARrr[params.dataIndex]
                },
                barBorderRadius: 10,
              },
              barBorderRadius: 4,
            },
            label: {
              normal: {
                color: '#fff',
                show: true,
                position: ['60px', '-40px'],
                textStyle: {
                  fontSize: 25,
                },
                formatter: function (a) {
                  return a.name
                },
              },
            },
          },
        ],
      }

      echarts0.setOption(option)
      // 鼠标移入取消小手
      echarts0.getZr().on('mousemove', (param) => {
        echarts0.getZr().setCursorStyle('default')
      })
    },

    //计算数组所占百分比
    countPercentage(countArray, j) {
      // var j = eval(countArray.join('+'));
      var resultArray = []
      for (var i = 0; i < countArray.length; i++) {
        // var k = Math.floor((countArray[i] / j) * 100) + "%";
        var k = Math.floor((countArray[i] / j) * 100)
        resultArray.push(k)
      }
      return resultArray
    },

    showLineEcharts1(dom, data) {
      let echarts0 = echarts.init(document.getElementById(dom))
      let data1 = []
      let data2 = []
      let data3 = []
      let data4 = []
      let seriesData = []
      let xData = []
      let max = Math.max(
        ...data.map((item) => {
          return item.fwyys
        })
      )
      for (let item of data) {
        let str = {
          name: item.zymc,
          value: item.fwyys,
        }
        let d1 = {
          name: '',
          value: max,
          symbolPosition: 'end',
        }
        let d2 = {
          name: '',
          value: max,
        }
        let d3 = {
          name: '',
          value: item.fwyys,
          symbolPosition: 'end',
        }
        let d4 = {
          name: item.zymc,
          value: max - item.fwyys,
        }
        seriesData.push(str)
        xData.push(item.zymc)
        data1.push(d1)
        data2.push(d2)
        data3.push(d3)
        data4.push(d4)
      }

      let option = {
        tooltip: {
          show: false,
          trigger: 'item',
          backgroundColor: 'rgba(0,0,0,0.5)',
          borderColor: 'rgba(89,211,255,1)',
          borderWidth: 2,
          padding: 5,
          textStyle: {
            color: 'rgba(89,211,255,1)',
            fontSize: 30,
            width: 300,
            height: 40,
          },
          // formatter: "{c}" + "%",

          formatter: '{c}',
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);',

          // 自定义的 CSS 样式
        },
        grid: {
          bottom: '25%',
          top: '20%',
          left: '0%',
          right: '-2%',
        },
        xAxis: {
          data: xData,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          // axisLabel: {
          //   // show: true,
          //   // textStyle: {
          //   //   color: "#fff",
          //   //   fontSize:28
          //   // },
          //   // margin: 30, //刻度标签与轴线之间的距离。
          // },
          axisLabel: {
            //坐标轴刻度标签的相关设置。
            textStyle: {
              fontSize: 26,
              color: '#fff',
            },
            margin: 20,
            formatter: function (params) {
              var newParamsName = '' // 最终拼接成的字符串
              var paramsNameNumber = params.length // 实际标签的个数
              var provideNumber = 7 // 每行能显示的字的个数
              var rowNumber = Math.ceil(paramsNameNumber / provideNumber) // 换行的话，需要显示几行，向上取整
              /**
               * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
               */
              // 条件等同于rowNumber>1
              if (paramsNameNumber > provideNumber) {
                /** 循环每一行,p表示行 */
                for (var p = 0; p < rowNumber; p++) {
                  var tempStr = '' // 表示每一次截取的字符串
                  var start = p * provideNumber // 开始截取的位置
                  var end = start + provideNumber // 结束截取的位置
                  // 此处特殊处理最后一行的索引值
                  if (p == rowNumber - 1) {
                    // 最后一次不换行
                    tempStr = params.substring(start, paramsNameNumber)
                  } else {
                    // 每一次拼接字符串并换行
                    tempStr = params.substring(start, end) + '\n'
                  }
                  newParamsName += tempStr // 最终拼成的字符串
                }
              } else {
                // 将旧标签的值赋给新标签
                newParamsName = params
              }
              //将最终的字符串返回
              return newParamsName
            },
          },
        },
        yAxis: {
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        series: [
          // 头
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [65, 35],
            symbolOffset: [0, -20],
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(54,127,223,1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(94,162,254,1)',
                    },
                  ],
                  false
                ),
              },
            },
            data: data1,
          },

          //底部立体柱
          {
            name: 'vvvv',
            stack: '1',
            type: 'bar',
            silent: true,
            barWidth: 65,
            barGap: '0%', // Make series be overlap
            data: seriesData,
            itemStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  type: 'linear',
                  global: false,
                  colorStops: [
                    {
                      //第一节下面
                      offset: 0,
                      color: 'rgba(0,255,245,0.5)',
                    },
                    {
                      offset: 1,
                      color: '#43bafe',
                    },
                  ],
                },
              },
            },
          },
          //三个最低下的圆片
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [65, 20],
            symbolOffset: [0, 12],
            z: 12,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(89,211,255,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(23,237,194,1)',
                  },
                ]),
              },
            },
            data: data2,
          },
          // 中间圆片
          {
            name: '',
            type: 'pictorialBar',
            symbolSize: [65, 32],
            symbolOffset: [0, -20],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(89,211,255,1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(23,237,194,1)',
                    },
                  ],
                  false
                ),
              },
            },
            z: 12,
            data: data3,
          },
          //上部立体柱
          {
            //上部立体柱
            stack: '1',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#3E8BE6',
                opacity: 0.7,
              },
              z: 10,
            },
            z: 20,
            label: {
              show: true,
              position: 'top',
              distance: 20,
              color: '#fff',
              fontSize: 30,
              formatter: function (item) {
                return max - item.value
              },
            },
            silent: true,
            barWidth: 65,
            barGap: '0%', // Make series be overlap
            data: data4,
          },
        ],
      }

      echarts0.setOption(option)
      // 鼠标移入取消小手
      echarts0.getZr().on('mousemove', (param) => {
        echarts0.getZr().setCursorStyle('default')
      })
    },
    showLineEcharts2(dom, data) {
      let echarts0 = echarts.init(document.getElementById(dom))
      let seriesData = []
      let xData = []

      for (let item of data) {
        let str = {
          value: item.num,
        }
        seriesData.push(str)
        xData.push(item.calldate)
      }

      let option = {
        title: {
          show: false,
          text: '价格到位率变动情况',
          x: 'center',
          textStyle: {
            color: '#ffffff',
            fontSize: '180%',
            align: 'center',
          },
        },

        grid: {
          bottom: '15%',
          top: '20%',
          left: '3%',
          right: '3%',
        },
        legend: {
          show: false,
          bottom: '10px',
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          backgroundColor: '#000000',
          textStyle: {
            color: 'white',
            fontSize: '27',
          },
        },
        xAxis: {
          type: 'category',
          boundaryGap: true, //坐标轴两边留空白策略
          // axisTick: {
          //   alignWithLabel: true, //刻度线与标签对其
          // },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#1F3A61',
            },
          },
          //  改变x轴字体颜色和大小
          axisLabel: {
            margin: 30,
            textStyle: {
              color: '#ffffff',
              fontSize: 30,
            },
          },
          data: xData,
        },
        yAxis: {
          type: 'value',
          name: '单位：次',
          nameTextStyle: {
            fontSize: 26,
            color: '#D6E7F9',
            padding: [0, 0, 20, 0],
          },
          scale: true, //脱离0的束缚
          axisLabel: {
            textStyle: {
              color: '#ffffff',
              fontSize: 30,
            },
          },
          //  改变x轴颜色
          axisLine: {
            lineStyle: {
              show: false,
              color: '#1F3A61',
            },
          },
        },
        series: [
          {
            name: '数值',
            type: 'line',
            stacked: true,
            smooth: true, //是否平滑曲线显示
            symbol: 'circle', //标记的图形。ECharts 提供的标记类型包括 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow'
            symbolSize: 15, //标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [20, 10] 表示标记宽为20，高为10[ default: 4 ]
            showSymbol: true, //是否显示 symbol, 如果 false 则只有在 tooltip hover 的时候显示
            lineStyle: {
              //线条样式
              normal: {
                width: 2, //线宽。[ default: 2 ]
              },
            },
            areaStyle: {
              //区域填充样式
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      //填充的颜色。
                      offset: 0, // 0% 处的颜色
                      color: 'rgba(104, 221, 109, 0.3)',
                    },
                    {
                      offset: 0.8, // 80% 处的颜色
                      color: 'rgba(104, 221, 109, 0.3)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)', //阴影颜色。支持的格式同color
                shadowBlur: 10, //图形阴影的模糊大小。该属性配合 shadowColor,shadowOffsetX, shadowOffsetY 一起设置图形的阴影效果
              },
            },
            itemStyle: {
              normal: {
                color: '#68DD77',
                borderColor: 'rgba(104, 221, 109, 0.27)', //图形的描边颜色。支持的格式同 color
                borderWidth: 26, //描边线宽。为 0 时无描边。[ default: 0 ]
              },
            },
            data: seriesData,
          },
        ],
      }

      echarts0.setOption(option)
      // 鼠标移入取消小手
      echarts0.getZr().on('mousemove', (param) => {
        echarts0.getZr().setCursorStyle('default')
      })
    },
  },
})
