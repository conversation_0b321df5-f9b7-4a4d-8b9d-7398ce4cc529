/*
 * common
 */
* {
  box-sizing: border-box;
}
[v-cloak] {
  display: none;
}

#cstz_app {
  overflow: hidden;
  /* background:url("/img/left-bg.png") no-repeat;
  background-size: 100%; */
}

.classFont {
  background: linear-gradient(to bottom, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.cstz_app_box {
  position: relative;
  width: 2045px;
  height: 1890px;
  padding: 10px 55px 30px;
}

.cstz_app_box .cstz_header {
  width: 1935px;
  display: flex;
  height: 130px;
  align-items: center;
  justify-content: space-between;
  background: url('/static/citybrain/csdn/img/一级标题3.png') no-repeat;
  background-position: 0 55px;
}

.cstz_app_box .cstz_header .title {
  font-size: 54px;
  font-family: 思源黑体 CNMEDIUM;
  background: linear-gradient(to bottom, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding-bottom: 10px;
}

.cstz_app_box .cstz_header .title .title_icon_one {
  display: inline-block;
  margin-right: 10px;
  width: 78px;
  height: 75px;
  vertical-align: bottom;
  background-image: url('/static/citybrain/csdn/img/一级标题1.png');
  background-size: 100% 100%;
}

.cstz_app_box .cstz_header .title_hr {
  position: relative;
  width: 70%;
  height: 21px;
  background-image: url('/static/citybrain/csdn/img/一级标题2.png');
  background-size: 100% 100%;
}

.cstz-top-content > div {
  margin: 30px 0;
}

.cstz-top-header .wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  margin: 0 20px;
  background: url('../img/cstzNew/cstz_left_header_bg.png') 50% 100% no-repeat;
  /* margin-bottom: 70px; */
  position: relative;
}

.cstz-top-header .wrapper > span {
  font-size: 40px;
  color: #fff;
  position: absolute;
  top: 110px;
}

.cstz-top-header .wrapper p:first-child {
  width: 966px;
  padding-left: 115px;
}

.cstz-top-header .wrapper .text {
  margin-right: 40px;
  font-family: '思源黑体 CNMEDIUM';
  font-size: 40px;
  color: #ffffff;
}

.cstz-top-header .wrapper p:last-child .text {
  margin-right: 20px;
  font-family: '思源黑体 CNMEDIUM';
  font-size: 34px;
  color: #ffffff;
}

.cstz-top-header .wrapper p:last-child .number {
  display: inline-block;
  width: 35px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  margin: 0 4px;
  background-color: #1b3a5d;
  border-radius: 8px;
}

.cstz-top-header .wrapper p:last-child > b {
  font-size: 34px;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.cstz-top-header .wrapper p:last-child .number b {
  font-family: BebasNeue;
  font-size: 34px;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.cstz-top-header .wrapper .number {
  display: inline-block;
  width: 56px;
  height: 78px;
  line-height: 78px;
  text-align: center;
  margin: 0 4px;
  background-color: #1b3a5d;
  border-radius: 8px;
}

.cstz-top-header .wrapper .number b {
  font-family: BebasNeue;
  font-size: 60px;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.cstz-top-header .wrapper p > b {
  font-size: 40px;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
}

.cstz-top-top10 .title {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  font-size: 40px;
  font-family: '思源黑体 CNMEDIUM';
  background: linear-gradient(to bottom, #ffffff, #4797ff);
  -webkit-background-clip: text;
  color: transparent;
}

.cstz-top-top10 .title span {
  margin: 0 20px;
}

.cstz-top-top10 .top-right {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  height: 55px;
  color: #d6e7f9;
  font-size: 32px;
}

#zEcharts0 .numColor {
  display: block;
  font-size: 32px;
  font-family: '思源黑体 CNMEDIUM';
  background: linear-gradient(to bottom, #fd852e, #cdffff, #fd852e);
  -webkit-background-clip: text;
  color: transparent;
}

#zEcharts0 span {
  font-size: 25px;
  display: block;
  color: #fff;
}

#zEcharts0 ul {
  display: flex;
  height: 230px;
  margin-left: 30px;
  justify-content: center;
}

#zEcharts0 ul li {
  flex: 1;
  position: relative;
  line-height: 40px;
  text-align: center;
  margin-left: 20px;
  vertical-align: baseline;
}

#zEcharts0 ul li div {
  position: absolute;
  bottom: -150px;
}

.cstz-top-top10 .top-right .text {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0 20px;
  padding: 0 10px;
  box-sizing: border-box;
  color: #d6e7f9;
}

.top-right .text > span {
  margin: 0 10px;
}

.top-right .text .num {
  font-size: 32px;
  background: linear-gradient(to bottom, #fff, #4690fb);
  -webkit-background-clip: text;
  color: transparent;
}

.top-right .text .num > span {
  font-size: 40px;
  font-weight: bold;
}

.top-right .icon {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #e2efff;
  margin-top: 10px;
}

.top-right .icon span {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50px;
  background-color: #e2efff;
}

.cstz-top-top10 .right_2 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: 360px;
  padding-right: 20px;
}

.right_2_0 {
  width: 45%;
  margin-bottom: 15px;
}

.right_2_0_0 {
  display: flex;
}

.right_2_0_0_0 {
  display: flex;
  font-family: SourceHanSansCN-Medium;
  font-size: 32px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  flex: 1;
}

.right_2_0_0_0 > div {
  color: #d6e7f9;
  font-size: 32px;
  margin-left: 20px;
}

.right_2_0_0_1 {
  font-family: BebasNeue;
  font-size: 30px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 48px;
  letter-spacing: 1px;
  color: #c0cedd;
}

.right_2_0_1 {
  width: 100%;
  height: 6px;
  background-color: #2e3f53;
}

.right_2_0_1 > div {
  max-width: 513px;
  height: 8px;
  background-image: linear-gradient(360deg, #df3c30, #ff9b78);
  margin-top: -1px;
}

.right_2_0_0_2 {
  font-size: 24px !important;
  font-weight: 700;
  color: #00aaf8 !important;
}

.cstz-top-flow .title {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  font-size: 40px;
  font-family: '思源黑体 CNMEDIUM';
  background: linear-gradient(to bottom, #ffffff, #4797ff);
  -webkit-background-clip: text;
  color: transparent;
}

.cstz-top-flow .title span {
  margin: 0 20px;
}

.cstz-top-flow .wrapper {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  height: 200px;
  background: url('../img/cstzNew/bg.png') 30px 100% no-repeat;
  padding: 0 10px 0 60px;
  box-sizing: border-box;
}

.cstz-top-flow .wrapper > div {
  position: relative;
  /* left: 95px; */
  top: 25px;
  width: 300px;
  height: 100%;
  text-align: center;
}

.cstz-top-flow .wrapper b {
  /* margin-left: -57px; */
  margin-top: 10px;
  width: 230px;
}

/* .cstz-top-flow .wrapper>div:last-child span {
  margin-left: -30px;
} */

.cstz-top-flow .wrapper span {
  font-family: '思源黑体 CNMEDIUM';
  font-size: 32px;
  display: inline-block;
  /* margin-left: -70px; */
  color: #ffffff;
}

.zer-color {
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff) !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
}

.cstz-top-flow .wrapper b {
  display: inline-block;
  text-align: center;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  /* background: linear-gradient(to bottom, #cbf2ff, #fff, #00c0ff, #cbf2ff, #00c0ff); */
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 600;
  font-family: BebasNeue;
  font-size: 60px;
}

.cstz-bottom-content {
  position: relative;
  height: 600px;
  /* background: url('../img/cstzNew/cstz_left_indicator_bg.png') 50% 50% no-repeat; */
}

.jjzb-bg-2771 {
  background-image: url('/static/citybrain/csdn/img/cstz-2771.png');
}

.jjzb-bg-0 {
  background-image: url('/static/citybrain/csdn/img/cstz-22.png');
}

.jjzb-bg-1 {
  background-image: url('/static/citybrain/csdn/img/cstz-23.png');
}

.jjzb-bg-2 {
  background-image: url('/static/citybrain/csdn/img/cstz-24.png');
}

.jjzb-bg-3 {
  background-image: url('/static/citybrain/csdn/img/cstz-22.png');
}

.jjzb-bg-4 {
  background-image: url('/static/citybrain/csdn/img/cstz-25.png');
}

.cstz-bottom-content .panel {
}

@keyframes rotate {
  0% {
    transform: rotateZ(0);
  }

  100% {
    transform: rotateZ(360deg);
  }
}

.cstz-bottom-content .panel li {
  position: absolute;
  width: 348px;
  height: 262px;
  background: url('../img/cstzNew/cstz_left_indicator_panel.png') no-repeat;
}

.cstz-bottom-content .panel li:first-child {
  top: 150px;
  left: 40%;
}

.cstz-bottom-content .panel li:nth-child(2) {
  top: 10px;
  left: 70%;
  opacity: 0.7;
}

.cstz-bottom-content .panel li:nth-child(3) {
  top: -50px;
  left: 50%;
  opacity: 0.4;
}

.cstz-bottom-content .panel li:nth-child(4) {
  top: -50px;
  left: 30%;
  opacity: 0.4;
}

.cstz-bottom-content .panel li:last-child {
  top: 10px;
  left: 10%;
  opacity: 0.7;
}

.cstz-bottom-content .panel .text {
  display: inline-block;
  width: 260px;
  text-align: center;
  margin: 40px 0 0 40px;
  font-family: MicrosoftYaHei;
  font-size: 32px;
  color: #ffffff;
}

.cstz-bottom-content .panel .num {
  display: inline-block;
  width: 100%;
  margin-top: 30px;
  text-align: center;
  letter-spacing: 0;
}

.cstz-bottom-content .panel .num span {
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-family: BebasNeue;
  font-size: 50px;
}

.cstz-bottom-content .panel .num b {
  font-family: MicrosoftYaHei;
  font-size: 28px;
  background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.cstz-bottom-content div.s-abso {
  cursor: pointer;
}

.cstz-bottom-content div.s-abso.active {
  background-image: url('/static/citybrain/csdn/img/cstz_left_2772_checked.png');
}

.charts_sxfl_back {
  background-image: url('/static/citybrain/csdn/img/ywt/ywt-left-echart_back.png');
  background-size: 100% 100%;
  width: 434px;
  height: 233px;
  position: absolute;
  left: 63px;
  top: 230px;
}
.charts_sxfl_list {
  position: absolute;
  left: 550px;
  top: 80px;
}
.chartDataList {
  display: flex;
  line-height: 50px;
  font-size: 30px;
}
.chartDataList :nth-child(1) {
  width: 15px;
  height: 15px;
  /*background: red;*/
  border-radius: 7px;
  margin: 20px;
}
.chartDataList :nth-child(2) {
  margin-right: 20px;
}
.ywtx {
  width: 100%;
  height: 490px;
  background: url('/static/citybrain/csdn/img/ywt/ywtx-back.png');
  background-size: 100% 100%;
  position: relative;
}
.ywtx :nth-child(1) {
  left: 240px;
  top: 30px;
}
.ywtx :nth-child(2) {
  left: 0px;
  top: 220px;
}
.ywtx :nth-child(3) {
  left: 1460px;
  top: 30px;
}
.ywtx :nth-child(4) {
  left: 1700px;
  top: 220px;
}
.ywtx-item {
  position: absolute;
}
.ywtx-item-value {
  width: 238px;
  height: 225px;
  background: url('/static/citybrain/csdn/img/ywt/ywtx-item.png');
  background-size: 100% 100%;
  color: #ffffff;
  line-height: 225px;
  text-align: center;
  font-size: 40px;
  font-weight: 700;
}
.ywtx-item-name {
  width: 238px;
  font-size: 40px;
  height: 38px;
  line-height: 38px;
  color: #ffffff;
  text-align: center;
}
.ywtx-item-name > img {
  margin-top: 16px;
}
.ggzy-item {
  width: 350px;
}
.ggzy-item-top {
  width: 240px;
  height: 240px;
  background: url('/static/citybrain/csdn/img/ywt/ggzy-item-blue.png');
  background-size: 100% 100%;
  line-height: 240px;
  font-size: 50px;
  font-weight: 700;
  text-align: center;
  margin: 0 auto;
}
.ggzy-item-top-red {
  background: url('/static/citybrain/csdn/img/ywt/ggzy-item-red.png');
}
.ggzy-item-top-yellow {
  background: url('/static/citybrain/csdn/img/ywt/ggzy-item-yellow.png');
}
.ggzy-item-name {
  height: 50px;
  color: #ffffff;
  font-size: 40px;
  line-height: 50px;
  text-align: center;
}
.title_icon {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title_icon i {
  display: inline-block;
}

.title_icon .icon_left {
  width: 864px;
  height: 53px;
  background: url('/static/citybrain/csdn/img/ywt/title-left.png') no-repeat center;
  background-size: 100% 100%;
  margin-right: 30px;
}

.title_icon .icon_right {
  width: 864px;
  height: 53px;
  background: url('/static/citybrain/csdn/img/ywt/title-right.png') no-repeat center;
  background-size: 100% 100%;
  margin-left: 30px;
}

.title_icon h2 {
  display: inline-block;
  font-size: 54px;
  font-family: '思源黑体 CNBOLD';
  background-image: linear-gradient(to bottom, #f0ffff, #74b4f4, #83b8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  white-space: nowrap;
}
.ywtx-container {
  width: 100%;
  height: 935px;
  display: flex;
  /* align-items: center; */
}
.ywtx-con {
  width: 1624px;
  height: 820px;
  background: url('/static/citybrain/csdn/img/ywt/ywtx-b-back.png');
  background-size: 100% 100%;
  margin: 36px auto;
  position: relative;
}
.ywtx-con :nth-child(1) {
  top: -50px;
  left: 230px;
}
.ywtx-con :nth-child(2) {
  top: 170px;
  left: -70px;
}
.ywtx-con :nth-child(3) {
  top: 480px;
  left: 55px;
}
.ywtx-con :nth-child(4) {
  top: 540px;
  left: 480px;
}
.ywtx-con :nth-child(5) {
  top: 540px;
  left: 900px;
}
.ywtx-con :nth-child(6) {
  top: 480px;
  left: 1280px;
}
.ywtx-con :nth-child(7) {
  top: 170px;
  left: 1380px;
}
.ywtx-con :nth-child(8) {
  top: -50px;
  left: 1080px;
}
.ywtx-con-part {
  width: 308px;
  text-align: center;
  color: #d6e7f9;
  font-size: 30px;
  position: absolute;
}
.ywtx-con-part-title {
  font-size: 34px;
  font-weight: 700;
  text-shadow: 0px 3px 16px rgba(0, 0, 0, 0.35);
  background: linear-gradient(0deg, #00ffc6 0%, #c7dcff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: -80px;
}
.ywtx-con-part-con {
  white-space: nowrap;
  margin-top: 3px;
}
.ywtx-con-center {
  position: absolute;
  left: 680px;
  top: 100px;
}
