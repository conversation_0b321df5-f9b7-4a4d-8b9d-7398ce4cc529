<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta
    name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
  />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>立体网格弹窗</title>
  <script src="/static/citybrain/hjbh/js/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/jslib/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
  <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
  <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
  <script src="/static/js/home_services/md5.js"></script>
  <script src="https://csdn.dsjj.jinhua.gov.cn:8101/static/js/jslib/simplify.js"></script>
  <script src="/static/js/jslib/turf.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <style>
    [v-cloak] {
      display: none;
    }
    .contentmain {
      width: 500px;
      height: 1250px;
      background: linear-gradient(180deg, rgba(2, 51, 110, 0.85), rgba(8, 37, 71, 0.85));
      position: relative;
      color: #fff;
      font-size: 30px;
      background-size: 100% 100%;
      padding: 40px 20px 20px 20px;
      box-sizing: border-box;
    }
    .header {
      height: 50px;
      line-height: 50px;
      font-size: 30px;
      font-weight: bolder;
      padding-left: 25px;
      background: url('/static/citybrain/tckz/img/main_mapIcon/header.svg') no-repeat;
      background-size: 100% 100%;
    }
    .items-title {
      height: 30px;
      margin-bottom: 10px;
    }
    .items-title > div {
      float: right;
      padding: 0 16px;
      height: 35px;
      cursor: pointer;
      /* background-image: linear-gradient(180deg,hsla(0,0%,100%,.4),hsla(0,0%,100%,0)); */
      border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
      box-sizing: border-box;
      transition: 0.3s;
      line-height: 30px;
      font-size: 25px;
      background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
      color: white;
    }
    .items-title > div:hover,
    #active {
      background: #2960cb;
      color: white;
    }
    /* 表格样式修改 */
    .hearder_h2 {
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 30px;
      font-weight: 500;
      text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.29);
      background: linear-gradient(180deg, #caffff 0%, #caffff 0%, #ffffff 0%, #00c0ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .hearder_h2 > span {
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 10px;
      white-space: nowrap;
    }
    .el-table {
      max-height: 420px !important;
      overflow: hidden;
      overflow-y: auto;
      color: rgb(197, 192, 192);
      padding-right: 5px !important;
      background: transparent !important;
    }
    .el-table th,
    .el-table tr {
      font-size: 22px !important;
    }
    .el-table tr {
      background: url('/static/citybrain/csdn/img/table_tr_bg.png') no-repeat;
      background-size: 99% 97%;
    }
    .el-table td,
    .el-table th.is-leaf {
      border: 0 !important;
    }
    .el-table tbody tr:hover > td {
      background: #1d4a7acb !important;
    }
    .el-table::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 2px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px; /* scrollbar-arrow-color: red; */
    }
    .el-table::-webkit-scrollbar-thumb {
      border-radius: 2px; /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 10px;
    }
    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }
    .el-checkbox,
    .el-checkbox__input {
      zoom: 120%;
    }
    .el-table .cell {
      line-height: normal;
    }
    .el-table .el-table__cell {
      padding: 8px 0 15px !important;
    }
    .el-checkbox__inner {
      width: 15px;
      height: 15px;
    }
    .number {
      display: inline-block;
      font-size: 35px;
    }
    .number .numbg {
      display: inline-block;
      width: 30px;
      height: 41px;
      line-height: 41px;
      text-align: center;
      background: url('/static/citybrain/hjbh/img/rkzt/numBg.png') no-repeat;
      background-size: contain;
      margin: 0 2px;
      border-radius: 8px;
    }
    /* el */
    .el-input {
      font-size: 30px;
    }
    .el-input__inner {
      height: 40px;
      line-height: 30px;
      background-color: rgba(25, 27, 35, 0);
      color: hsla(0, 0%, 100%, 0.8);
    }
    .el-input__icon {
      height: 30px;
      line-height: 30px;
    }
    .popper__arrow {
      display: none !important;
    }
    .el-select {
      margin: 0 20px;
    }
    .el-select-dropdown {
      background-color: rgba(25, 27, 35, 0.96);
    }
    .el-popper[x-placement^='bottom'] {
      margin-top: -2px;
    }
    .el-select-dropdown__item {
      color: #fff;
      height: 40px;
      line-height: 40px;
      font-size: 30px;
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #2a4b8b;
    }
    .el-select-dropdown__item.selected {
      background-color: #2a4b8b;
      color: #fff;
      font-weight: 400;
    }
    #circle_sou > div {
      font-size: 30px;
      padding: 2px 7px 3px 4px;
      border-radius: 14px;
      cursor: pointer;
    }
    .icon-nav-start {
      /* background: url('/zhddjhaqcsztqd/static/img/zhddzx/box/nav_start.png') no-repeat; */
      /* width: 30px;
      height: 40px;
      transform: scale(1.5); */
      /*position: absolute;*/
      /*left: 2%;*/
      background: linear-gradient(45deg, #3a3636, #0001b9);
    }
    .icon-nav-start:hover,
    .icon-nav-start-active {
      background: linear-gradient(45deg, #2f2c2c, #3579f8);
    }
    .el-slider__runway {
      width: 400px;
      height: 10px;
      border-radius: 10px;
      margin-left: 20px;
    }
    .el-slider__bar {
      height: 10px;
    }
    .el-slider__stop {
      height: 10px;
      width: 10px;
      background-color: #294e7ad4;
    }
    .el-slider__button {
      width: 7.7px;
      height: 7.7px;
      margin-top: -8px;
      margin-left: 10px;
      background-color: #0083ff;
      border-color: #fff;
      border-radius: 0;
      border-width: 1px;
      transform: translateX(-50%) rotate(45deg) !important;
    }
    .el-slider__button:after {
      content: '';
      position: absolute;
      background-image: url('/static/citybrain/tckz/img/main_mapIcon/slider.svg');
      width: 30px;
      height: 30px;
      left: -11px;
      top: -11px;
      transform: rotate(45deg);
      background-size: 100%;
    }
    .el-slider__runway.show-input {
      margin-right: 145px;
    }
    .el-input-number--small .el-input__inner {
      padding-left: 20px;
      padding-right: 20px;
    }
    .el-input-number--small .el-input-number__decrease,
    .el-input-number--small .el-input-number__increase {
      width: 25px;
    }
    .el-input-number__decrease,
    .el-input-number__increase {
      background: transparent;
      color: #fff;
      font-weight: bold;
    }
    [class*=' el-icon-'],
    [class^='el-icon-'] {
      font-weight: 700;
    }

    .describeContainer {
      overflow-y: scroll;
      height: 750px;
    }

    ::-webkit-scrollbar {
      width: 0;
    }
  </style>
</head>

<body>
<div id="ltwgDialogL7">
  <div id="result" class="contentmain">
    <div class="header" id="header" style="display: flex;justify-content: space-between;align-items: center">
      <span>{{dialogData.label}}</span>
      <i class="el-icon-circle-close" @click="closeIframe" style="cursor: pointer"></i>
    </div>
    <div style="display: flex;flex-direction: column;justify-content: space-evenly;align-items: flex-start;margin-top: 20px">
      <div class="s-flex s-row-center s-font-26 s-c-white s-text-center" v-for="(item,i) in contentObj">
        <p class="s-m-10">{{item.name}}：</p>
        {{item.value}}{{item.unit}}
      </div>
      <div class="s-font-26 s-c-white s-text-center">
        <p class="s-m-10" style="text-align: left !important;">基础简介：</p>
        <div class="describeContainer">
          {{descibe}}
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  let vm = new Vue({
    el: "#ltwgDialogL7",
    data: {
      dialogData: {},
      contentObj:[],
      descibe:""
    },
    computed:{

    },
    mounted() {
      const that = this;
      window.addEventListener("message", function (event) {
        //子获取父消息
        let newData;
        if (typeof event.data == "object") {
          newData = event.data;
        } else {
          newData = JSON.parse(event.data.argument);
        }
        console.log(newData,"接收到的数据");
        that.dialogData = newData.result;
        that.getContent(that.dialogData.label)
      });
    },
    methods: {
      //关闭弹窗
      closeIframe() {
        let data = JSON.stringify({
          type: 'closeIframe',
          name: 'L7ltwgDialog'
        })
        top.postMessage(data, '*')
        top.closeIframeByNames(['L7ltwgDialog'])
      },
      getContent(str) {
        switch (str) {
          case "网络经济中心":
            this.contentObj = [
              {
                name:"企业数",
                value:"129",
                unit:"家"
              },
              {
                name:"资产总额",
                value:"2665461.665",
                unit:"万元"
              },
              {
                name:"纳税总额",
                value:"25278.62207",
                unit:"万元"
              }
            ];
            this.descibe = "1.金华网络中心是金华科技园创业服务中心运营的六大孵化基地之一，项目总投资1.41亿元，占地11亩，总建筑面积26755平方米，共16层；\n" +
              "2.1-4层为公共服务区，5-16层为企业研发办公区域；\n" +
              "3.园区主要是以数字经济、总部经济等为主要业态的加速区、总部经济区，面向符合产业要求的成长型企业；"
            break;
          case "金华之心":
            this.contentObj = [
              {
                name:"企业数",
                value:"161",
                unit:"家"
              },
              {
                name:"资产总额",
                value:"4713446.109",
                unit:"万元"
              },
              {
                name:"纳税总额",
                value:"10714.8347",
                unit:"万元"
              }
            ];
            this.descibe = "金华之心·数字经济产业园位于金华市区江南核心位置，是2020年浙江省全省“4+1”重大项目，总投资约50亿元，分两期实施，一期现已全部投用，二期将于2023年上半年投入运营。作为市区目前体量最大的数字经济园区，金华之心抢抓长三角一体化重大机遇，以“科技创新、人才创新、产业创新”三大创新工程为引领，以“数字产业集群”为核心增长点，聚焦数字经济、现代商贸、总部经济等强势产业，致力打造成为浙中高端科技企业总部集聚区。\n" +
              "  金华之心现有在园企业150余家，形成了以阿帕奇、正元为龙头的软件信息服务业，以5173、利多麦手为龙头的电商产业、以印象软件、金报传媒为龙头的数字娱乐服务业的数字产业生态圈。园区拥有规上企业34家，国家高新技术企业8家。园区现已搭建金华之心·人才智谷、金华数字文化产业创新服务综合体、软件测评中心等协同创新项目，引入阿里云创新中心、一亿中流上市加速器、浙中电商直播综合体等科创服务平台，落地浙大金华研究院、浙师大金开研究院等科研服务机构，推动优质项目招引、中小企业扶持培育，增强产业经济活力，助力金华开发区争创省高能级战略平台。\n" +
              "  金华之心先后荣获省级公共服务示范平台、数字化示范小微企业园、省四星级小微企业园等荣誉。"
            break;
          case "中心医院":
            this.contentObj = [
              {
                name:"医院等级",
                value:"三级甲等",
                unit:""
              },
              {
                name:"医疗用房面积",
                value:"23",
                unit:"万余平方米"
              },
              {
                name:"开放床位数",
                value:"2578",
                unit:"张"
              },
              {
                name:"卫技人员数",
                value:"3607",
                unit:"人"
              }
            ];
            this.descibe = "金华市中心医院始建于1910年，是浙江中西部地区集医疗、科研、教学、预防、保健、康复为一体的三级甲等综合医院，2020年挂牌浙江大学医学院附属金华医院。在全国三级公立医院绩效考核中，连续五年进入A+序列，稳居全国百强，其中2022年排名全国第83名，较上一年提升12名。\n" +
              "  医院占地面积93.98亩，现有医疗用房面积23万余平方米，截至2023年12月31日，医院开放床位2578张，在岗职工3945人，其中卫技人员3607人，拥有高级职称699人，硕、博士858人，国务院特殊津贴专家9人、省有突出贡献中青年专家2人、省151人才1人、省医坛新秀13人、省卫生创新人才4人、市专业技术拔尖人才22人、市321专业技术人才30人。\n" +
              "  现有国家临床重点专科1个（普通外科），省级临床重点专科5个（神经外科、普通外科、消化内科、心血管内科、重症医学科），省区域专病中心9个（神经外科、普通外科、儿科、重症医学科、呼吸内科、血液科、麻醉科、心胸外科、消化内科），省医学扶植重点学科3个（骨科、神经外科、胰腺外科），省市共建学科2个（神经外科、普通外科），省中医名科1个（传统医学中心），省“十三五”中医药重点专科1个（脾胃病科），市医学重点学科30个，市临床重点专科3个，市中医药重点专科1个，市重点实验室1个；2023年11月获批浙江省博士后工作站；被授予国家高级卒中中心、中国胸痛中心、中国创伤救治联盟高级创伤中心、全国健康管理示范基地、国家级急诊医学示范基地、国家级住院医师规范化培训基地等。"
            break;
          case "金华世贸城市广场":
            this.contentObj = [
              {
                name:"项目地址",
                value:"婺城区-李渔路888号",
                unit:""
              },
              {
                name:"开业时间",
                value:"2022",
                unit:""
              },
              {
                name:"项目类型",
                value:"购物中心",
                unit:""
              },
              {
                name:"建筑面积",
                value:"18",
                unit:"万㎡"
              },
              {
                name:"营业时间",
                value:"10:00-22:00",
                unit:""
              }
            ];
            this.descibe = "金华世贸城市广场位于金华市李渔路888号，身处金华CBD核心腹地，是一座集购物、休闲、酒店、办公、娱乐、餐饮、文化、旅游等功能于一体的生态型、全新体验式商业综合体。金华世贸城市广场由金华第一百货集团运营，项目以零售、餐饮为主，零售占比29%，餐饮28%；生活配套约占12%；休闲娱乐占比11%；儿童业态占比8%；剩余的12%则为主力店，形成了强有力的品牌矩阵。品牌引进方面，项目着眼于创造更广泛的体验空间，引进的400+品牌中，包括30%金华首店，60%江南首店、80%以上同城旗舰店、升级店等。"
            break;
          case "雅苑社区":
            this.contentObj = [
              {
                name:"所属县（市、区）",
                value:"婺城区",
                unit:""
              },
              {
                name:"所属乡镇街道",
                value:"三江街道",
                unit:""
              },
              {
                name:"常住户数",
                value:"2550",
                unit:"户"
              },
              {
                name:"常住人数",
                value:"8970",
                unit:"人"
              }
            ];
            this.descibe = "雅苑社区地处江南商圈繁华区域，辖区内业态丰富，聚集了银泰百货、金华一百江南店、福泰隆超市、世贸中心等大型商场、超市15家，小微商铺241家，居民住宅小区14个，城中村1个，是一个集商贸圈、居民圈于一体的综合社区。"
            break;
        }
      }
    },
  });
</script>
</body>
</html>
