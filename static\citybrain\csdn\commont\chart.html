<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <!-- 水球 -->
  <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
  <!-- 三维饼图 -->
  <script src="/static/citybrain/hjbh/js/echarts-gl.min.js"></script>
  <!-- tooltip自动循环 -->
  <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
  <!-- 词云 -->
  <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>

  <title>chart</title>
  <style>
    html,body{
        margin: 0;
        padding: 0;
    }
    .container{
      width:3332px;
      height:2000px;
      display: flex;
      flex-wrap: wrap;
    }
    .chart{
      width:1666px;
      height:900px;
      background: #0a0f36;
      border:1px red solid;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="container">
      <div id="chart01" class="chart"></div>
      <div id="chart02" class="chart"></div>
      <div id="chart03" class="chart"></div>
      <div id="chart04" class="chart"></div>
      <div id="chart05" class="chart"></div>
      <div id="chart06" class="chart"></div>
      <div id="chart07" class="chart"></div>
      <div id="chart08" class="chart"></div>
      <div id="chart09" class="chart"></div>
      <div id="chart10" class="chart"></div>
      <div id="chart11" class="chart"></div>
      <div id="chart12" class="chart"></div>
      <div id="chart13" class="chart"></div>
      <div id="chart14" class="chart"></div>
      <div id="chart15" class="chart"></div>
      <div id="chart16" class="chart"></div>
      <div id="chart17" class="chart"></div>
      <div id="chart18" class="chart"></div>
      <div id="chart19" class="chart"></div>
      <div id="chart20" class="chart"></div>
      <div id="chart21" class="chart"></div>
      <div id="chart22" class="chart"></div>
      <div id="chart23" class="chart"></div>
      <div id="chart24" class="chart"></div>
      <div id="chart25" class="chart"></div>
      <div id="chart26" class="chart"></div>
      <div id="chart27" class="chart"></div>
      <div id="chart28" class="chart"></div>
      <div id="chart29" class="chart"></div>
      <div id="chart30" class="chart"></div>
      <div id="chart31" class="chart"></div>
      <div id="chart32" class="chart"></div>
    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var vm = new Vue({
      el: '#app',
      data: {

      },
      mounted() {
        //折线图
        this.getChart01('chart01')
        //柱状图
        this.getChart02('chart02')
        //饼图
        this.getChart03('chart03');
        //南丁格尔图
        this.getChart04('chart04');
        //水球
        this.getChart05('chart05');
        //横向柱状图
        this.getChart06('chart06');
        //透明柱状图
        this.getChart07('chart07');
        //词云
        this.getChart08('chart08');
        //折线图-滑行的光点
        this.getChart09('chart09');
        //三维环形饼图
        this.getChart10('chart10')
        //饼图-文字环绕
        this.getChart11('chart11')
        //漏斗图
        this.getChart12('chart12')
        //地图
        this.getChart13('chart13')
        //双饼图
        this.getChart14('chart14')
        //半圆饼图
        this.getChart15('chart15')
        //仪表盘
        this.getChart16('chart16')
        //双y周柱状+折线
        this.getChart17('chart17')
        //线条环形
        this.getChart18('chart18')
        //雷达图
        this.getChart19('chart19')
        //关系图
        this.getChart20('chart20')
        //关系图
        this.getChart21('chart21')
        //滚动横向柱状
        this.getChart22('chart22')
      },
      methods: {
        //折线图
        getChart01(id){
          let myEcharts = echarts.init(document.getElementById(id));
            let option = {
              color: ["#e86056", "#F5CC53", "#6AE4B2"],
              tooltip: {
                trigger: "axis",
                axisPointer: {
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '28',
              },
            },
            legend: {
                selectedMode: false,
                top: "0px",
                right: "24%",
                textStyle: {
                    color: "#fff",
                    fontSize: 28,
                    fontFamily: "SourceHanSansCN-Medium",
                },
                itemWidth: 18,
                itemHeight: 8,
            },
            grid: {
              left: "5%",
              right: "5%",
              bottom: "10%",
              top: "30%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
                data: ["8月1日","8月2日","8月3日","8月4日","8月5日","8月6日"],
                splitLine: { show: false },
                axisTick: {
                  //y轴刻度线
                  show: false,
                },
                axisLine: {
                    lineStyle: {
                        color: "rgb(119,179,241,.4)", // 颜色
                        width: 1, // 粗细
                    },
                },
                axisLabel: {
                  interval: 0,
                  // rotate: 40,
                  textStyle: {
                      color: "#ccc",
                      fontSize: 28,
                      fontFamily: "SourceHanSansCN-Medium",
                  },
                },
              },
            ],
            yAxis: [
                {
                    type: "value",
                    name: "单位：个",
                    nameTextStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                      padding: [0, 0, 20, 0],
                    },
                    splitLine: {
                        lineStyle: {
                            color: "rgb(119,179,241,.4)",
                        },
                    },
                    axisLabel: {
                        textStyle: {
                            color: "#ccc",
                            fontSize: 28,
                            fontFamily: "SourceHanSansCN-Medium",
                        },
                    },
                },
            ],
            series: [
                {
                    cursor: "auto",
                    name: "数量",
                    symbol: 'circle',     //设定为实心点
                    symbolSize: 15,   //设定实心点的大小
                    type: "line",
                    data: [65,50,70,90,68,75],
                    barWidth: 20,
                    itemStyle: {
                        normal: {
                            color: "rgb(0,136,212)",
                            borderColor: "rgba(0,136,212,0.2)",
                            borderWidth: 30,
                        },
                        },
                    label: {
                        show: false, //开启显示
                        position: "top", //在上方显示
                        textStyle: {
                            //数值样式
                            color: "#FFFFFF",
                            fontFamily: "SourceHanSansCN-Regular",
                            fontSize: 28,
                        },
                    },
                    markLine: {
                      symbol: "none",
                      label: {
                        normal: {
                          color: "#fff",
                          formatter: 60+'个',
                          fontSize: 25,
                          padding: 5,
                          backgroundColor: "rgba(145,193,80,0.8)",
                          show: true,
                          position: "end",
                          distance: 10,
                        },
                      },
                      lineStyle: {
                        type: "solid",
                        color: "#ccc",
                      },
                      data: [
                        {
                          yAxis: 60,
                        },
                      ],
                    },
                    areaStyle: {
                        normal: {
                            color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [
                                {
                                offset: 0,
                                color: "rgba(0, 136, 212, 0.3)",
                                },
                                {
                                offset: 0.8,
                                color: "rgba(0, 136, 212, 0)",
                                },
                            ],
                            false
                            ),
                            shadowColor: "rgba(0, 0, 0, 0.1)",
                            shadowBlur: 40,
                        },
                    },
                },
            ],
          };
          myEcharts.setOption(option)
          myEcharts.getZr().on('mousemove', param => {
            myEcharts.getZr().setCursorStyle('default')
          })
        },
        //柱状图
        getChart02(id){
          let myChart = echarts.init(document.getElementById(id));
          let xdata = ["婺城区", "金义新区", "兰溪市", "东阳市", "义乌市","永康市", "浦江县", "武义县", "磐安县","开发区"]
          let data = [100,80,60,40,20,30,50,70,90,10];
          let data1 = [80,60,40,20,40,50,70,50,70,50];;
          var option = {
              tooltip: {
                  trigger: "axis",
                  axisPointer: {
                      // 坐标轴指示器，坐标轴触发有效
                      type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                  },
                  borderWidth: 0,
                  backgroundColor: "rgba(0, 0, 0, 0.6)",
                  textStyle: {
                      color: "white",
                      fontSize: "28",
                  },
              },
              legend: {
                  orient: "horizontal",
                  itemWidth: 18,
                  itemHeight: 18,
                  top: "8%",
                  // icon: 'rect',
                  itemGap: 45,
                  textStyle: {
                      color: "#D6E7F9",
                      fontSize: 28,
                  },
              },
              grid: {
                  left: "5%",
                  right: "5%",
                  bottom: "10%",
                  top: "30%",
                  containLabel: true,
              },
              xAxis: [
                  {
                      type: "category",
                      data: xdata,
                      offset: 20,
                      axisLine: {
                      lineStyle: {
                          color: "#77b3f1",
                          opacity: 0.3,
                      },
                      },
                      axisTick: {
                      show: false,
                      },
                      axisLabel: {
                      interval: 0,
                      // rotate: -40,
                      textStyle: {
                          fontSize: 28,
                          color: "white",
                      },
                      },
                  },
              ],
              yAxis: [
                  {
                      name: "单位：个",
                      type: "value",
                      nameTextStyle: {
                        fontSize: 28,
                        color: "#D6E7F9",
                        padding: [0, 0, 20, 0],
                      },
                      splitLine: {
                      show: true,
                      lineStyle: {
                          color: "#77b3f1",
                          opacity: 0.1,
                          width: 2,
                      },
                      },
                      axisTick: {
                      show: true,
                      lineStyle: {
                          color: "#77b3f1",
                          opacity: 0.5,
                          width: 2,
                      },
                      },
                      axisLabel: {
                      textStyle: {
                          fontSize: 28,
                          color: "#D6E7F9",
                      },
                      },
                  }
              ],
              series: [
                  {
                      name: "县(市、区)建设数量",
                      type: "bar",
                      barWidth: "25%",
                      itemStyle: {
                      normal: {
                          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                              offset: 0,
                              color: "#CBF2FF",
                          },
                          {
                              offset: 0.5,
                              color: "#00C0FF",
                          },
                          {
                              offset: 1,
                              color: "#004F69",
                          },
                          ]),
                          barBorderRadius: 4,
                      },
                      },
                      data: data,
                      label: {
                          normal: {
                          show: true,
                          lineHeight: 10,
                          formatter: "{c}",
                          position: "top",
                          textStyle: {
                              color: "#fff",
                              fontSize: 20,
                          },
                          },
                      },
                  },
                  {
                      name: "市级接入数量",
                      type: "bar",
                      barWidth: "25%",
                      itemStyle: {
                      normal: {
                          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                              offset: 0,
                              color: "#CDFFFF",
                          },
                          {
                              offset: 0.5,
                              color: "#22E8E8",
                          },
                          {
                              offset: 1,
                              color: "#006B6B",
                          },
                          ]),
                          barBorderRadius: 4,
                      },
                      },
                      data: data1,
                      label: {
                          normal: {
                          show: true,
                          lineHeight: 10,
                          formatter: "{c}",
                          position: "top",
                          textStyle: {
                              color: "#fff",
                              fontSize: 20,
                          },
                          },
                      },
                  },
              ],
          };
          myChart.setOption(option)
          tools.loopShowTooltip(myChart, option, {loopSeries: true});
        },
        //饼图
        getChart03(id){
          let myEc = echarts.init(document.getElementById(id));
          let echartData = [
            {value: 2154,name: "曲阜师范大学"},
            {value: 3854,name: "潍坊学院"},
            {value: 3515,name: "青岛职业技术学院"},
            {value: 3515,name: "淄博师范高等专科"},
            {value: 3854,name: "鲁东大学"},
            {value: 2154,name: "山东师范大学"},
          ];
          let imgUrl='/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
          const option = {
          tooltip: {
              trigger: 'item',
              formatter: '{b}: <br/>{d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                  color: 'white',
                  fontSize: '30',
              },
          },
          legend: {
              orient: 'vertical',
              itemWidth: 18,
              itemHeight: 18,
              left: '60%',
              top: '25%',
              icon: 'circle',
              itemGap:50,
              textStyle: {
              color: '#D6E7F9',
              fontSize: 30,
              padding: [0, 0, 0, 20]
              },
              formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value
                  if (data[i].name == name) {
                      tarValue = data[i].value
                  }
              }
              var p = (tarValue / total) * 100
              return name + '  '  +tarValue+'件'
              },
          },
          graphic: [
              {
              z: 4,
              type: "image",
              id: "logo",
              left: "19.3%",
              top: "30.5%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [-30, 30], //中心点
              scale: [1.1, 1.1], //缩放
              style: {
                  image: imgUrl,
                  opacity: 1,
              },
              },
          ],
          series: [
              {
              name: "",
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['30%', '50%'],
              itemStyle: {
                  normal: {
                  borderColor: "#0A1934",
                  // borderWidth: 10
                  },
              },
              label: {
                  show: false,
              },
              data: echartData,
              },
          ],
          }
          myEc.setOption(option)
        },
        //南丁格尔图
        getChart04(id){
          let myEcharts = echarts.init(document.getElementById(id));
          let img = '/static/citybrain/csdn/img/chart_center.png'
          let data =  [
              { value:25, name: '8890' },
              { value:35, name: '智慧城管' },
              { value:35, name: '基层四平台' },
              { value:30, name: '消防救援' },
              { value:20, name: '市民发现' },
              { value:15, name: '智能感知' },
          ];
          let option = {
              tooltip: {
                  trigger: 'item',
                  formatter: '{b}: <br/> {d}%',
                  borderWidth: 0,
                  backgroundColor: 'rgba(0, 0, 0, 0.6)',
                  textStyle: {
                      color: 'white',
                      fontSize: '28',
                  },
              },
              graphic: {
                elements: [
                  {
                    type: 'image',
                    z: 3,
                    style: {
                      image: img,
                      width: 330,
                      height: 330,
                    },
                    left: '20%',
                    top: '31.5%',
                  },
                ],
              },
              legend: {
                  orient: 'vertical',
                  left: '60%',
                  top: '25%',
                  bottom:'20%',
                  icon: 'circle',
                  itemGap:80,
                  textStyle: {
                        rich: {
                          name: {
                              fontSize: 30,
                              color: '#ffffff',
                              padding: [0, 0, 0, 15]
                          },
                          value: {
                              fontSize: 30,
                              color: '#e9d0ab',
                              padding: [10, 5, 0, 15]
                          },
                      }
                  },
                  formatter: function (name) {
                      var data = option.series[0].data //获取series中的data
                      var total = 0
                      var tarValue
                      for (var i = 0, l = data.length; i < l; i++) {
                          total += data[i].value
                          if (data[i].name == name) {
                              tarValue = data[i].value
                          }
                      }
                      var p = ((tarValue / total) * 100).toFixed(2)
                      return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
                  },
              },
              series: [
                  {
                      name:'',
                      type: 'pie',
                      radius: ['40%', '60%'],
                      center: ['30%', '50%'],
                      roseType: 'area',
                      itemStyle: {
                          borderRadius: 5,
                      },
                      label: {
                          show: false,
                      },
                      data: data,
                  },
              ],
          }
          myEcharts.setOption(option)
        },
        //水球
        getChart05(id) {
          let myEc = echarts.init(document.getElementById(id));
          let color=["#0D9BCA", "#0bbef9"];
          let data = 0.6;
          let option = {
            title: [
              {
                text: "",
                x: "10%",
                y: "85%",
                textStyle: {
                  fontSize: 30,
                  fontWeight: "500",
                  color: "#fff",
                },
              },
            ],
            graphic: [{
              type: "group",
              left: "center",
              top: "60%",
              children: [
                {
                  type: "text",
                  z: 100,
                  left: "10",
                  top: "middle",
                  style: {
                    fill: "white",
                    text: "流量统计",
                    font: "40px Microsoft YaHei",
                  },
                },
              ],
            }],
            series: [
              {
                type: "liquidFill",
                radius: "60%",
                color: color,
                center: ["50%", "50%"],
                data: [
                  data,
                  {
                    value: data,
                    phase: Math.PI,
                  },
                ],
                label: {
                  normal: {
                    textStyle: {
                      fontSize: 40,
                      color: color[1],
                    },
                  },
                },
                itemStyle: {
                  normal: {
                    label: {
                      show: true,
                      formatter: (e) => {
                        return parseInt(e.data * 100);
                      },
                    },
                  },
                },
                backgroundStyle: {
                  borderWidth: 1,
                  color: "rgba(220, 192, 179, 0.06)",
                },
                outline:{
                  //水球图的外层边框 可设置 show:false  不显示
                  itemStyle:{
                      borderColor:'#DCDCDC',
                      borderWidth:3,
                  },
                  borderDistance:0,
                },
              },
            ],
          };
          myEc.setOption(option);
        },
        //横向柱状图
        getChart06(id) {
          let myEc = echarts.init(document.getElementById(id));
          var myColor = ["#eb2100","#eb3600","#d0570e","#d0a00e","#34da62","#00e9db","#00c0e9","#0096f3","#33CCFF","#33FFCC"];
          option = {
            // backgroundColor: "#0e2147",
            grid: {
              left: "10%",
              top: "12%",
              right: "10%",
              bottom: "10%",
              containLabel: true,
            },
            xAxis: [
              {
                show: false,
              },
            ],
            yAxis: [
              {
                axisTick: "none",
                axisLine: "none",
                offset: "27",
                axisLabel: {
                  textStyle: {
                    color: "#ffffff",
                    fontSize: "28",
                  },
                },
                data: [
                  "南昌转运中心",
                  "广州转运中心",
                  "杭州转运中心",
                  "宁夏转运中心",
                  "兰州转运中心",
                  "南宁转运中心",
                  "长沙转运中心",
                  "武汉转运中心",
                  "合肥转运中心",
                  "贵州转运中心",
                ],
              },
              {
                axisTick: "none",
                axisLine: "none",
                axisLabel: {
                  textStyle: {
                    color: "#ffffff",
                    fontSize: "28",
                  },
                },
                data: ["10", "9", "8", "7", "6", "5", "4", "3", "2", "1"],
              },
              {
                name: "分拨延误TOP 10",
                nameGap: "50",
                nameTextStyle: {
                  color: "#ffffff",
                  fontSize: "28",
                },
                axisLine: {
                  lineStyle: {
                    color: "rgba(0,0,0,0)",
                  },
                },
                data: [],
              },
            ],
            series: [
              {
                name: "条",
                type: "bar",
                yAxisIndex: 0,
                data: [4, 13, 25, 29, 38, 44, 50, 52, 60, 72],
                label: {
                  normal: {
                    show: true,
                    position: "right",
                    textStyle: {
                      color: "#ffffff",
                      fontSize: "24",
                    },
                  },
                },
                barWidth: 12,
                itemStyle: {
                  normal: {
                    color: function (params) {
                      var num = myColor.length;
                      return myColor[params.dataIndex % num];
                    },
                  },
                },
                z: 2,
              },
              {
                name: "白框",
                type: "bar",
                yAxisIndex: 1,
                barGap: "-100%",
                data: [99, 99.5, 99.5, 99.5, 99.5, 99.5, 99.5, 99.5, 99.5, 99.5],
                barWidth: 20,
                itemStyle: {
                  normal: {
                    color: "#0e2147",
                    barBorderRadius: 5,
                  },
                },
                z: 1,
              },
              {
                name: "外框",
                type: "bar",
                yAxisIndex: 2,
                barGap: "-100%",
                data: [100, 100, 100, 100, 100, 100, 100, 100, 100, 100],
                barWidth: 24,
                itemStyle: {
                  normal: {
                    color: function (params) {
                      var num = myColor.length;
                      return myColor[params.dataIndex % num];
                    },
                    barBorderRadius: 5,
                  },
                },
                z: 0,
              },
              {
                name: "外圆",
                type: "scatter",
                hoverAnimation: false,
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                yAxisIndex: 2,
                symbolSize: 35,
                itemStyle: {
                  normal: {
                    color: function (params) {
                      var num = myColor.length;
                      return myColor[params.dataIndex % num];
                    },
                    opacity: 1,
                  },
                },
                z: 2,
              },
            ],
          };
          myEc.setOption(option);
        },
        //透明柱状图
        getChart07(id) {
            let echarts0 = echarts.init(document.getElementById(id))
            xData = ["本年话务总量", "本年人工话务量", "每万客户呼入量","本年话务总量", "本年人工话务量", "每万客户呼入量"];
            yData = [0, 1230, 425 , 300, 1230, 425];
            option = {
              backgroundColor: "#061326",
              grid: {
                top: "20%",
                left: "0%",
                bottom: "10%",
                right: "10%",
                containLabel: true,
              },
              tooltip: {
                show: true,
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              animation: false,
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  axisTick: {
                    alignWithLabel: true,
                  },
                  nameTextStyle: {
                    color: "#82b0ec",
                  },
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#82b0ec",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                      fontSize:'20',
                    },
                    margin: 40,
                  },
                },
              ],
              yAxis: [
                {
                  show: false,
                  type: "value",
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                    },
                  },
                  splitLine: {
                    lineStyle: {
                      color: "#0c2c5a",
                    },
                  },
                  axisLine: {
                    show: false,
                  },
                },
              ],
              series: [
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: [60, 15],
                  symbolOffset: [0, -6], // 上部椭圆
                  symbolPosition: "end",
                  z: 12,
                  // "barWidth": "0",
                  label: {
                    normal: {
                      show: true,
                      position: "top",
                      // "formatter": "{c}%"
                      fontSize: 24,
                      fontWeight: "bold",
                      color: "#34DCFF",
                    },
                  },
                  color: "#2DB1EF",
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: [60, 15],
                  symbolOffset: [0, 7], // 下部椭圆
                  // "barWidth": "20",
                  z: 12,
                  color: "#2DB1EF",
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: function (d) {
                    return d > 0 ? [100, 20] : [0, 0];
                  },
                  symbolOffset: [0, 20], // 下部内环
                  z: 10,
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderColor: "#2EA9E5",
                      borderType: "solid",
                      borderWidth: 1,
                    },
                  },
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: [140, 25],
                  symbolOffset: [0, 30], // 下部外环
                  z: 10,
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderColor: "#19465D",
                      borderType: "solid",
                      borderWidth: 2,
                    },
                  },
                  data: yData,
                },
                {
                  type: "bar",
                  //silent: true,
                  barWidth: "60",
                  barGap: "10%", // Make series be overlap
                  barCateGoryGap: "10%",
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
                        {
                          offset: 0,
                          color: "#38B2E6",
                        },
                        {
                          offset: 1,
                          color: "#0B3147",
                        },
                      ]),
                      opacity: 0.8,
                    },
                  },
                  data: yData,
                },
              ],
            };

            echarts0.setOption(option)
          },
        //词云
        getChart08(id) {
          let echarts0 = echarts.init(document.getElementById(id))
          let mockData = [
            { name: "微信", value: 3328 },
            { name: "南方+", value: 1045 },
            { name: "东莞时间网", value: 834 },
            { name: "i东莞", value: 804 },
            { name: "新浪微博", value: 532 },
            { name: "今日头条", value: 493 },
            { name: "腾讯新闻", value: 479, },
            { name: "东莞阳光网", value: 387 },
            { name: "东莞日报", value: 289 },
            { name: "一点资讯", value: 287 },
            { name: "东方头条网", value: 233 },
            { name: "南方都市报", value: 228 },
            { name: "新粤网", value: 207 },
            { name: "南方plus", value: 206 },
            { name: "网易新闻", value: 201 },
            { name: "东方头条", value: 180 },
            { name: "趣头条", value: 178 },
            { name: "羊城派", value: 151 },
            { name: "东莞时报", value: 143 },
            { name: "莞讯网", value: 139 },
            { name: "广州日报", value: 137 },
            { name: "东莞阳光台", value: 132 },
            { name: "搜狐新闻", value: 129 },
            { name: "今日头条.APP", value: 116, },
            { name: "东莞阳光平台", value: 108 },
            { name: "腾讯新闻.APP", value: 107 },
            { name: "南方网", value: 103, },
            { name: "UC头条", value: 98 },
            { name: "凤凰新闻", value: 93 },
            { name: "报告诉", value: 77 },
            { name: "网易新闻.APP", value: 74 },
            { name: "中国小康网", value: 64 },
            { name: "东莞万江", value: 63 },
            { name: "信息时报", value: 59 },
            { name: "中国文明网", value: 58 },
            { name: "东莞网", value: 57 },
            { name: "搜狐新闻（自媒体）", value: 54 },
            { name: "南方日报", value: 54 },
            { name: "搜狐焦点", value: 53 },
            { name: "阳光社区", value: 52 },
            { name: "南方plus.APP", value: 47 },
            { name: "阳光望牛墩", value: 46 },
            { name: "中国报道", value: 43 },
            { name: "新浪新闻", value: 43 },
            { name: "房掌柜", value: 39 },
            { name: "广州日报网", value: 38 },
            { name: "ZAKER", value: 38 },
            { name: "一点资讯.APP", value: 35 },
            { name: "聚焦东莞", value: 35 },
            { name: "广州新闻网", value: 35 },
            { name: "新浪", value: 31 },
            { name: "东莞服务热线12345", value: 31 },
            { name: "人民网", value: 29 },
            { name: "阳光热线问政平台", value: 26 },
            { name: "党报头条", value: 26 },
            { name: "羊城晚报地方版", value: 24 },
            { name: "网易房产", value: 23 },
            { name: "中国网", value: 22 },
            { name: "金羊网", value: 21 },
            { name: "东莞长安", value: 21 },
            { name: "百家号", value: 21 },
            { name: "澎湃新闻", value: 20 },
            { name: "读特", value: 19 },
            { name: "东方头条.APP", value: 17 },
            { name: "阳光石排", value: 16 },
            { name: "新浪乐居", value: 16 },
            { name: "微信邦", value: 16 },
            { name: "搜狐新闻.APP", value: 16 },
            { name: "人民日报", value: 16 },
            { name: "百度新闻", value: 16 },
            { name: "南方都市报.APP", value: 15 },
            { name: "荔枝网", value: 15 },
            { name: "华人头条", value: 15 },
            { name: "广东建设报", value: 15 },
            { name: "中国", value: 14 },
            { name: "阳光黄江", value: 14 },
            { name: "东方网", value: 14 },
            { name: "网易", value: 12 },
            { name: "搜狐网", value: 12 },
            { name: "和讯", value: 12 },
            { name: "文化莞城", value: 11 },
            { name: "聊聊网", value: 11, },
            { name: "58同镇", value: 11 },
            { name: "凤凰网", value: 10 },
            { name: "新浪网", value: 9 },
            { name: "趣头条.APP", value: 9 },
            { name: "凤岗网", value: 9 },
            { name: "新快网_新快报", value: 8 },
            { name: "上游新闻", value: 8 },
            { name: "东莞市城市综合管理局", value: 8 },
            { name: "大众网", value: 8 },
            { name: "中国新闻网", value: 7 },
            { name: "第一推", value: 7 },
            { name: "大洋网", value: 7 },
            { name: "新浪网", value: 6 },
            { name: "新浪看点", value: 6 },
            { name: "手机和讯网", value: 6 },
          ].slice();

          // 随机颜色
          let randcolor = () => {
            let r = 100 + (Math.random() * 100);
            let g = 135 + (Math.random() * 100);
            let b = 100 + (Math.random() * 100);
            return `rgb(${r}, ${g}, ${b})`;
          },


          option = {
            // backgroundColor: "rgba(0,0,0,.5)",
            tooltip: {
              trigger: "item",
              padding: [10, 15],
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                fontSize: 20,
                color:'white'
              },
              formatter: (params) => {
                const { name, value } = params;
                return `平台：${name} <br/>数量：${value}`;
              },
            },
            series: [
              {
                type: "wordCloud",
                gridSize: 20,
                sizeRange: [12, 50],
                rotationRange: [0, 0],
                shape: "circle",
                textStyle: {
                  color: (params) => {
                    return randcolor();
                  },
                  emphasis: {
                    shadowBlur: 10,
                    shadowColor: "#333",
                  },
                },
                data: mockData,
              },
            ],
          };
          echarts0.setOption(option)
        },
        //折线图-滑行的光点
        getChart09(id){
          let myChart = echarts.init(document.getElementById(id));
            //数据
            var XName = [
              "金华市",
              "义乌市",
              "兰溪市",
              "永康市",
              "蒲江县",
              "武义县",
              "磐安县",
            ];
            var data1_1 = [500, 2000, 1000, 1800, 2200, 1200, 1500];
            var data2_1 = [200, 800, 900, 1500, 1800, 2200, 3000];
            var img = [
              "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABRCAYAAABFTSEIAAAACXBIWXMAAAsSAAALEgHS3X78AAAEp0lEQVR42u3cz4sjRRTA8W9Vd3Vn8mMmjj9WQWSRZQ+CsH+B7MnDIgiCd0E8CYJ/gOAIelo8ehUP/gF6WLw5/gMueFP2sIcF0dHd2Z1kknR11fOQZJJJMtlZd03H7HtQpNOTnpn+8Lrm1etmjIig8e/DKoECKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIqoAJudKTr+osZMNPvBUQBHwHsPF9fB9R0DeHMOQ6T6WOrhEzXBM4swDOL0M6CrArRVoq3t2dGUIb9fTvatg8ZZup1PDBgzPmy98mey6qfzjLz2WaWjEUZKEvGyi9nWyneMOvGIyFQo2Sbg4MUSChpU9IeTTUpJdsEajPZOJeJG5uBZj7rLLduWS5dGm6XNLEELOFUFj54ACJCaychkpDSASK3bwsXL0YgVpWJKwM0iy9Zy8HdGru7jvt3Pbu7w0wES7drTwAbjTHMGCsQcIAnYTC1/wRx0wEnl27JNgZI8HQ6Kc1mQq83RNzaMjPzXqDbjTQaJRFLxIyyMSxAXEkWrhrQzAAmo5HOjCQf7jflILxOkohL+aUPgV4vEGNJo+E5PAy02+UIMEwBxo0CPDP7Dg5SnEtpt1PA0e87XO25FOoh8IYIH2Y5b45RzGAQBiIltZoHxqMcjbksXAVgdc2EQMYzzzdotyeZWKuleULXJtwT4SODfC2QCWR+IF9KnjuX1Xbo99Op7LVE8iXlz0YBTk5SyLEEjo5OLuccEoFUvHfO+reuUPx4zftXAIcx1hdcF+/TvFab4A0Bs0VwqyhpVnkJT89/Q4DDQ0e77YCMwIUsFMeFZD856699URRvX4nxE4A/jbnxXp7v4Zw3ReGNSDHI8wFQjIafuoyn58L/fB6sth/Ybg9fez2TRC6QZcZYvgHsazF+MP7YCyLXcM7gvSXLDGBqYDg+NhwdmSpPoTrAkub0W+f4FSB1fDucIunMHSLpO8WAH0rSy8u+19MBCHB4OHzd2pI+CEUhpigEiN+l6WcdY252jLn5s7Wf472ImPcN8pUl/tEHoV4XWq1Ke4KrLmPsTA3oODpytFoOyJKSyzHyMSIxteWngMW5cSEdDJQUhTdZVgxOz3/+jFJm4+bA2e5JpNU6WZ4Fw99JwnWMKccwpeddP+B7GZTNUPKqybJy0O+Hs1YfMz9swwvpB8fbGDG0GuGkkK7V0hxSmZQpABI8l2z0v3sJf50qpAMJCd2qCulql3LD1lRGQjm7lEsDz0rkxTQOfiPPxUBcuJTbbhss/Y1eyi3NwsmKInmkZsKk5gtPUzNhvp11507CSy/X6XYStpvFudpZw1ZWIOF4Cq6SdtbKbioJyAhRTu3u9yMJXerN+ugvaQQsjcZ8Q3VnZwxlSDhe1lB9GjrSw5b+1avT8+Jw+979nNaOI6U3KpTrWAosxVQmygK4ld8X0ZtK/7eViExD7O1NQPb3T7fsl4/4sBpwYzPwjFbTo95Yl9l9Vd1YN1X/147HebSjary1AHyc5qc+XLQEQx9ve8Kg6xr6hKoCKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIq4JrHP8fEWV8FMTmOAAAAAElFTkSuQmCC",
              "image://data:image/png;base64,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",
              "image://data:image/png;base64,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",
              "image://data:image/png;base64,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",
            ];

            var data1_2 = [{ coords: [] }];
            var data2_2 = [{ coords: [] }];
            for (let i = 0; i < XName.length; i++) {
              data1_2[0].coords.push([XName[i], data1_1[i]]);
              data2_2[0].coords.push([XName[i], data2_1[i]]);
            }

            option = {
              grid: {
                top: "20%",
                left: "10%",
                bottom: "20%",
                right: "5%",
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                // orient: 'vertical',
                itemWidth: 18,
                itemHeight: 18,
                right: "10%",
                top: "5%",
                icon: "circle",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                  padding: [0, 0, 0, 20],
                },
                data: ["当年", "上年"],
              },
              yAxis: [
                {
                  name: "单位：件",
                  type: "value",
                  position: "left",
                  nameTextStyle: {
                    color: "#D6E7F9",
                    fontSize: 28,
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    lineStyle: {
                      type: "dashed",
                      color: "rgba(135,140,147,0.8)",
                    },
                  },
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.8,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              xAxis: [
                {
                  type: "category",
                  axisTick: {
                    show: false,
                  },
                  offset: 20,
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#0696f9",
                    },
                  },
                  axisLabel: {
                    inside: false,
                    textStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                      lineHeight: 22,
                    },
                  },
                  data: XName,
                },
              ],
              series: [
                {
                  symbolSize: 150,
                  symbol: img[2],
                  name: "当年",
                  type: "line",
                  data: data1_1,
                  itemStyle: {
                    normal: {
                      borderWidth: 5,
                      color: "#0696f9",
                    },
                  },
                },
                {
                  symbolSize: 150,
                  symbol: img[3],
                  name: "上年",
                  type: "line",
                  data: data2_1,
                  itemStyle: {
                    normal: {
                      borderWidth: 5,
                      color: "#d4dd48",
                    },
                  },
                },
                {
                  name: "滑行的光点",
                  type: "lines",
                  coordinateSystem: "cartesian2d",
                  symbolSize: 30,
                  polyline: true,
                  effect: {
                    show: true,
                    trailLength: 0,
                    symbol: "arrow",
                    period: 10, //光点滑动速度
                    symbolSize: 150,
                    symbol: img[0],
                  },
                  lineStyle: {
                    normal: {
                      width: 1,
                      opacity: 0.6,
                      curveness: 0.2,
                    },
                  },
                  data: data1_2,
                },
                {
                  name: "滑行的光点",
                  type: "lines",
                  coordinateSystem: "cartesian2d",
                  symbolSize: 30,
                  polyline: true,
                  effect: {
                    show: true,
                    trailLength: 0,
                    symbol: "arrow",
                    period: 10, //光点滑动速度
                    symbolSize: 150,
                    symbol: img[1],
                  },
                  lineStyle: {
                    normal: {
                      width: 1,
                      opacity: 0.6,
                      curveness: 0.2,
                    },
                  },
                  data: data2_2,
                },
              ],
            };
            myChart.setOption(option)
        },
        //三维环形饼图
        getChart10(id) {
          const myCharts = echarts.init(document.getElementById(id));

          function getParametricEquation(
            startRatio,
            endRatio,
            isSelected,
            isHovered,
            k,
            height
          ) {
            // 计算
            let midRatio = (startRatio + endRatio) / 2;

            let startRadian = startRatio * Math.PI * 2;
            let endRadian = endRatio * Math.PI * 2;
            let midRadian = midRatio * Math.PI * 2;

            // 如果只有一个扇形，则不实现选中效果。
            if (startRatio === 0 && endRatio === 1) {
              isSelected = false;
            }

            // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
            k = typeof k !== "undefined" ? k : 1 / 3;

            // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
            let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
            let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

            // 计算高亮效果的放大比例（未高亮，则比例为 1）
            let hoverRate = isHovered ? 1.05 : 1;

            // 返回曲面参数方程
            return {
              u: {
                min: -Math.PI,
                max: Math.PI * 3,
                step: Math.PI / 32,
              },

              v: {
                min: 0,
                max: Math.PI * 2,
                step: Math.PI / 20,
              },

              x: function (u, v) {
                if (u < startRadian) {
                  return (
                    offsetX +
                    Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                  );
                }
                if (u > endRadian) {
                  return (
                    offsetX +
                    Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                  );
                }
                return (
                  offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
                );
              },

              y: function (u, v) {
                if (u < startRadian) {
                  return (
                    offsetY +
                    Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                  );
                }
                if (u > endRadian) {
                  return (
                    offsetY +
                    Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                  );
                }
                return (
                  offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
                );
              },

              z: function (u, v) {
                if (u < -Math.PI * 0.5) {
                  return Math.sin(u);
                }
                if (u > Math.PI * 2.5) {
                  return Math.sin(u);
                }
                return Math.sin(v) > 0 ? 1 * height : -1;
              },
            };
          }

          // 生成模拟 3D 饼图的配置项
          function getPie3D(pieData, internalDiameterRatio) {
            let series = [];
            let sumValue = 0;
            let startValue = 0;
            let endValue = 0;
            let legendData = [];
            let k =
              typeof internalDiameterRatio !== "undefined"
                ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
                : 1 / 3;

            // 为每一个饼图数据，生成一个 series-surface 配置
            for (let i = 0; i < pieData.length; i++) {
              sumValue += pieData[i].value;

              let seriesItem = {
                name:
                  typeof pieData[i].name === "undefined"
                    ? `series${i}`
                    : pieData[i].name,
                type: "surface",
                parametric: true,
                wireframe: {
                  show: false,
                },
                pieData: pieData[i],
                pieStatus: {
                  selected: false,
                  hovered: true,
                  k: k,
                },
              };

              if (typeof pieData[i].itemStyle != "undefined") {
                let itemStyle = {};

                typeof pieData[i].itemStyle.color != "undefined"
                  ? (itemStyle.color = pieData[i].itemStyle.color)
                  : null;
                typeof pieData[i].itemStyle.opacity != "undefined"
                  ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                  : null;

                seriesItem.itemStyle = itemStyle;
              }
              series.push(seriesItem);
            }

            // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
            // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
            for (let i = 0; i < series.length; i++) {
              endValue = startValue + series[i].pieData.value;
              series[i].pieData.startRatio = startValue / sumValue;
              series[i].pieData.endRatio = endValue / sumValue;
              series[i].parametricEquation = getParametricEquation(
                series[i].pieData.startRatio,
                series[i].pieData.endRatio,
                false,
                false,
                k,
                series[i].pieData.value
              );

              startValue = endValue;

              legendData.push(series[i].name);
            }

            // 准备待返回的配置项，把准备好的 legendData、series 传入。
            let option = {
              tooltip: {
                show: true,
                formatter: (params) => {
                  if (params.seriesName !== "mouseoutSeries") {
                    return `${
                      params.seriesName
                    }<br/><span style="display:inline-block;margin-right:0.05rem;border-radius:0.1rem;width:0.1rem;height:0.1rem;background-color:${
                      params.color
                    };"></span>${
                      option.series[params.seriesIndex].pieData.value
                    }`;
                  }
                },
              },
              legend: {
                orient: 'vertical',
                itemWidth: 18,
                itemHeight: 18,
                left: '80%',
                top: '25%',
                icon: 'circle',
                itemGap:50,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 30,
                  padding: [0, 0, 0, 20]
                },
              },
              xAxis3D: {
                min: -1,
                max: 1,
              },
              yAxis3D: {
                min: -1,
                max: 1,
              },
              zAxis3D: {
                min: -1,
                max: 1,
              },

              grid3D: {
                show: false,
                boxHeight: 2, // 圆环的高度
                top: '10%',
                left:'-10%',
                environment: "auto", //背景色,auto为自适应颜色
                projection: "perspective", //投影方式，默认为透视投影'perspective'
                viewControl: {
                  beta: 260,// 3d效果可以放大、旋转等，请自己去查看官方配置
                  alpha: 25, // 角度
                  distance: 200, // 调整视角到主体的距离，类似调整zoom
                  rotateSensitivity: 0, // 设置为0无法旋转
                  zoomSensitivity: 0, // 设置为0无法缩放
                  panSensitivity: 0, // 设置为0无法平移
                  autoRotate: true, // 自动旋转
                },
              },
              series: series,
            };
            return option;
          }

          let option = getPie3D(
            [
              {
                name: "行政许可",
                value: 60,
                itemStyle: {
                  opacity: 0.9,
                  color: "#00C0FF",
                },
              },
              {
                name: "行政确认",
                value: 50,
                itemStyle: {
                  opacity: 0.9,
                  color: "#22E8E8",
                },
              },
              {
                name: "行政给付",
                value: 40,
                itemStyle: {
                  opacity: 0.9,
                  color: "#FFD461",
                },
              },
              {
                name: "行政处罚",
                value: 30,
                itemStyle: {
                  opacity: 0.9,
                  color: "#A9DB52",
                },
              },
              {
                name: "行政奖励",
                value: 25,
                itemStyle: {
                  opacity: 0.9,
                  color: "#B76FD8",
                },
              },
              {
                name: "行政裁决",
                value: 20,
                itemStyle: {
                  opacity: 0.9,
                  color: "#FD852E",
                },
              },
              {
                name: "行政强制",
                value: 15,
                itemStyle: {
                  opacity: 0.9,
                  color: "#FF4949",
                },
              },
            ],
          );
          myCharts.setOption(option);
          window.addEventListener("resize", () => {myCharts.resize();});
        },
        //饼图-文字环绕
        getChart11(id){
          let myChart = echarts.init(document.getElementById(id));
            let imgUrl='/static/citybrain/djtl/img/djtl-left/echarts-bg.png';
            let colorList = ["#41C8B0", "#DB7763","#2D70E9","#08A561","#A9DB52","#B76FD8","#FD852E","#FF4949","#FFC460","#B76FD8","#F99609"];
            let data = [
              {
                value: 10,
                name: "卫生健康",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "平安综治",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "安全应急",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "生态环境",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 4,
                name: "市政建设",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 4,
                name: "市容市貌",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 9,
                name: "园林绿化",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 8,
                name: "建筑规划",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "农林水利",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "社会服务",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "教育文旅",
                label: {
                  color: "#fff",
                  fontSize:28,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
            ];
            function angleText(i, num) {
              //每个元素的角度
              var everyAngle = 360 / num;
              //文字现在所在的角度
              var currentAngle = i * everyAngle + everyAngle / 2;
              //文字所在模块的所占角度
              var currentArea = (i + 1) * everyAngle;

              if (currentAngle <= 90) {
                return -currentAngle;
              } else if (currentAngle <= 180 && currentAngle > 90) {
                return 180 - currentAngle;
              } else if (currentAngle < 270 && currentAngle > 180) {
                return 180 - currentAngle;
              } else if (currentAngle < 360 && currentAngle >= 270) {
                return 360 - currentAngle;
              }
            }

            //有值的色图的正切处理
            let data3 = [];
            data3 = JSON.parse(JSON.stringify(data));
            for (var i = 0; i < data3.length; i++) {
              if (i === 0) {
                data3[i]["label"]["color"] = "#333";
                data3[i]["itemStyle"]["color"] = "rgba(25, 255, 224)";
                data3[i]["emphasis"]["itemStyle"]["color"] = "rgba(25, 255, 224)";
                data3[i]["label"]["rotate"] = angleText(i, data3.length);
              } else {
                data3[i]["label"]["color"] = "#fff";
                // data3[i]["itemStyle"]["color"] = "#4169E1"; //颜色配置
                data3[i]["itemStyle"]["color"] = colorList[i]; //颜色配置
                data3[i]["emphasis"]["itemStyle"]["color"] = "#6A5ACD";
                data3[i]["label"]["rotate"] = angleText(i, data3.length);
              }
            }

            //最外层大圈的数据
            let data1 = [];

            data1 = JSON.parse(JSON.stringify(data));
            for (var i = 0; i < data1.length; i++) {
              data1[i].value = 1;
              data1[i]["label"]["rotate"] = angleText(i, data1.length);
              if (i === 0) {
                data1[i]["label"]["color"] = "rgba(25, 255, 224)";
              }
            }

            //透明饼图的数据
            let data2 = [];

            for (var i = 0; i < data.length; i++) {
              if (i === 0) {
                data2.push({
                  value: 1,
                  itemStyle: {
                    color: "rgba(25, 255, 224,0.05)",
                  },
                });
              } else {
                data2.push({
                  value: 1,
                  itemStyle: {
                    color: "transparent",
                  },
                });
              }
            }

            let option = {
              grid: {},
              polar: {},
              angleAxis: {
                show: false,
                interval: 1,
                type: "category",
                data: [],
              },
              graphic: [
                {
                z: 4,
                type: "image",
                id: "logo",
                left: "43%",
                top: "36.7%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [50, 50], //中心点
                scale: [0.6, 0.6], //缩放
                style: {
                    image: imgUrl,
                    opacity: 1,
                },
                },
            ],
              //中间画圈圈的坐标轴
              radiusAxis: {
                show: false,
              },
              series: [
                {
                  type: "pie",
                  radius: ["68%", "100%"],
                  hoverAnimation: false,
                  itemStyle: {
                    color: "transparent",
                  },
                  labelLine: {
                    normal: {
                      show: false,
                      length: 30,
                      length2: 55,
                    },
                  },
                  label: {
                    normal: {
                      position: "inside",
                      align: "right",
                    },
                  },
                  name: "",
                  data: data1,
                },
                {
                  stack: "a",
                  type: "pie",
                  radius: ["20%", "75%"],
                  roseType: "area",
                  zlevel: 10,
                  itemStyle: {
                    // color: "#4169E1",
                    normal: {
                      borderColor: 'rgb(46 64 93)',
                      borderWidth: 1,
                    },
                  },
                  emphasis: {
                    itemStyle: {
                      color: "#6A5ACD",
                    },
                  },
                  label: {
                    normal: {
                      show: true,
                      textStyle: {
                        fontSize: 28,
                        color: "#fff",
                      },
                      position: "inside",
                      rotate: 30,
                      align: "right",
                      fontWeight: "bold",
                      formatter: "{c}",
                    },
                    emphasis: {
                      show: true,
                    },
                  },
                  animation: false,
                  data: data3,
                },
                {
                  type: "pie",
                  zlevel: 99,
                  radius: ["15%", "90%"],
                  selectedOffset: 0,
                  animation: false,
                  hoverAnimation: false,
                  label: {
                    normal: {
                      show: false,
                    },
                  },
                  data: data2,
                },
              ],
            };

            myChart.setOption(option);

            myChart.on("click", function (a) {
              //最外层的字体颜色重置变色
              for (var da1 = 0; da1 < option.series[0].data.length; da1++) {
                option.series[0].data[da1].label.color = "#fff";
              }

              //色圈的字体颜色和选中颜色重置
              for (var da2 = 0; da2 < option.series[1].data.length; da2++) {
                // option.series[1].data[da2].itemStyle.color = "#4169E1";
                option.series[1].data[da2].itemStyle.color = colorList[da2];
                option.series[1].data[da2].label.color = "#fff";
                //hover颜色重置
                option.series[1].data[da2].emphasis.itemStyle.color = "#6A5ACD";
              }

              //背景的透明饼图的重置
              for (var da3 = 0; da3 < option.series[2].data.length; da3++) {
                option.series[2].data[da3].itemStyle.color = "transparent";
              }

              option.series[1].data[a.dataIndex].itemStyle.color = "rgba(25, 255, 224)";
              option.series[1].data[a.dataIndex].label.color = "#333";
              //hover 颜色改变
              option.series[1].data[a.dataIndex].emphasis.itemStyle.color =
                "rgba(25, 255, 224)";
              option.series[0].data[a.dataIndex].label.color = "rgba(25, 255, 224)";
              option.series[2].data[a.dataIndex].itemStyle.color = "rgba(25, 255, 224,0.1)";
              //console.log(option)
              myChart.setOption(option);
            });

            myChart.on("mouseover", function (a) {
              myChart.dispatchAction({
                type: "highlight",
                seriesIndex: 1,
                dataIndex: a.dataIndex,
              });
            });

            myChart.on("mouseout", function (a) {
              myChart.dispatchAction({
                type: "downplay",
                seriesIndex: 1,
                dataIndex: a.dataIndex,
              });
            });
        },
        //漏斗图
        getChart12(id){
          let myChart = echarts.init(document.getElementById(id));
          var colors=['#80E6FF','#97FBD2','#FBEE83','#D485F9','#F27575','#FFBA69'];
          var url='https://q.cnblogs.com/Images/qdigg.gif';
          option = {
            color:colors,
            tooltip: {
              trigger: "axis",
              axisPointer: {
                  type: "cross",
                  label: {
                      backgroundColor: "red",
                  },
                  lineStyle: {
                      color: "#fff"
                  },
              },
            },
            series: [
              {
                top:"10%",
                type: 'funnel',
                left: '0%',
                width: '60%',
                height:'80%',
                gap: 16,
                minSize: 150,
                maxSize: 410,
                label: {
                    normal: {
                        position: 'inside',
                        formatter: '{c}',
                        fontSize: 28 ,
                    },
                    emphasis: {
                        formatter: '{b}: {c}'
                    }
                },
                itemStyle:{
                  borderWidth:0,
                  shadowColor: 'rgba(255, 255, 255, 0.5)',
                  shadowBlur: 10,
                },
                data: [
                  {value: 100, name: '大厅来访'},
                  {value: 80, name: '领导交办'},
                  {value: 60, name: '基层四平台'},
                  {value: 40, name: '线上来访'},
                  {value: 20, name: '线上预约'},
                ]
              },
              {
                top:"10%",
                type: 'funnel',
                left: '0%',
                width: '80%',
                height:'80%',
                gap: 16,
                z:1,
                minSize: 150,
                maxSize: 150,
                label: {
                    normal: {
                        color: '#fff',
                        position: 'right',
                        padding :[11,25,11,25],
                        width:50,
                        formatter:function(d){
                          var ins='{aa|'+d.name+'   '+'}{bb|'+d.value+'%}';
                          return ins
                        },
                        rich:{
                          aa:{
                            fontSize:28,
                          },
                          bb:{
                            fontSize:28,
                          }
                        }
                    }
                },
                //右侧的百分比显示的地方
                labelLine: {
                    show:true,
                    normal: {
                        length: 200,
                        position: 'right',
                        lineStyle: {
                            width: 1,
                            color:'#e8e9f1',
                            type:'solid'
                        },
                    },
                },
                //主体是透明的
                itemStyle: {
                    normal: {
                        color: 'transparent',
                        borderWidth:0,
                        opacity: 1
                    }
                },
                data: [
                  {value: 100, name: '大厅来访'},
                  {value: 80, name: '领导交办'},
                  {value: 60, name: '基层四平台'},
                  {value: 40, name: '线上来访'},
                  {value: 20, name: '线上预约'},
                ]
            }
            ]
          };
          myChart.setOption(option)
        },
        //地图
        async getChart13(id){
          let myEc = echarts.init(document.getElementById(id));
          let mapName = 'jinhua';
          let res = await $get('/hjbh/rkzt/330700_full')
          echarts.registerMap('zj', res)
          let option = {
              tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                      type: 'shadow',
                  },
              },
              title: {
                  left: 'center',
                  top: '50px',
                  textStyle: {
                  color: '#fff',
                  fontSize: '50',
                  },
              },
              color: ['#00eaff', '#00eaff', '#ffde00', '#fc9700', '#f44336'],
              visualMap: {
                  show: false,
                  // calculable: true,
                  seriesIndex: [0],
                  inRange: {},
              },
              geo: {
                  show: true,
                  map: 'zj',
                  roam: true,// 是否开启鼠标滚轮缩放
                  silent: true,
                  layoutSize: '100%',
                  layoutCenter: ['50%', '50%'],
                  label: {
                      normal: {
                          show: true,
                          fontSize: 30,
                          color: '#fff', //控制地图省市文字颜色
                      },
                      emphasis: {
                          show: true,
                          color: '#fff', //悬浮字体颜色
                      },
                  },

                  itemStyle: {
                      normal: {
                          //          	color: '#ddd',
                          borderColor: 'rgba(147, 235, 248, 1)',
                          borderWidth: 1,
                          areaColor: {
                          type: 'radial',
                          x: 0.5,
                          y: 0.5,
                          r: 0.8,
                          colorStops: [{
                              offset: 0,
                              color: 'rgba(175,238,238, 0)' // 0% 处的颜色
                          }, {
                              offset: 1,
                              color: 'rgba(	47,79,79, .2)' // 100% 处的颜色
                          }],
                          globalCoord: false // 缺省为 false
                          },
                          shadowColor: 'rgba(128, 217, 248, 1)',
                          // shadowColor: 'rgba(255, 255, 255, 1)',
                          shadowOffsetX: -2,
                          shadowOffsetY: 2,
                          shadowBlur: 10
                      },
                      emphasis: {
                          color: '#000',
                          areaColor: '#389BB7',
                      },
                  },
              },
              series: [

              ],
          }
          myEc.setOption(option)
        },
        //双饼图
        getChart14(id) {
          let data1 = [
            {
              name: "吸毒人员",
              value: 458,
            },
            {
              name: "精神病人",
              value: 962,
            },
            {
              name: "刑满释放人员",
              value: 1688,
            },
            {
              name: "越级上访人员",
              value: 2588,
            },
          ];
          let data2 = [
            {
              name: "吸毒人员涉事事件",
              value: 43,
            },
            {
              name: "精神病人涉事事件",
              value: 99,
            },
            {
              name: "刑满释放人员涉事事件",
              value: 198,
            },
            {
              name: "越级上访人员涉事事件",
              value: 206,
            },
          ];
          let myEc = echarts.init(document.getElementById(id));
          let imgUrl = "/static/citybrain/djtl/img/djtl-left/echarts-bg.png";
          const option = {
            tooltip: {
              trigger: "item",
              formatter: "{b}: <br/>{d}%",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            legend: [
              {
                orient: "vertical",
                itemWidth: 18,
                itemHeight: 18,
                left: "5%",
                top: "10%",
                icon: "circle",
                itemGap: 30,
                data:['吸毒人员涉事事件','精神病人涉事事件','刑满释放人员涉事事件','越级上访人员涉事事件'],
                textStyle: {
                  rich: {
                      name: {
                          color: "#D6E7F9",
                          padding: [0, 0, 10, 10],
                          fontSize: 24,
                      },
                      value: {
                          color: "#FEC762",
                          fontSize: 28,
                          padding: [0, 0, 0, 15],
                      }
                    }
                },
                formatter: function (name) {
                  let value = '';
                  for (let i = 0; i < data2.length; i++) {
                      if (data2[i].name == name) {
                        value = data2[i].value
                      }
                  }
                  return (
                    "{name|" + name.slice(0,6)+'\n'+ name.slice(6,name.length)+ "}\n{value|" + value + "件}"
                  )
                },
              },
              {
                orient: 'vertical',
                itemWidth: 18,
                itemHeight: 18,
                left: '80%',
                top: '10%',
                icon: 'circle',
                itemGap:30,
                textStyle: {
                  rich: {
                      name: {
                          color: "#D6E7F9",
                          padding: [0, 0, 10, 10],
                          fontSize: 24,
                      },
                      value: {
                          color: "#FEC762",
                          fontSize: 28,
                          padding: [0, 0, 0, 15],
                      }
                    }
                },
                data:['吸毒人员','精神病人','刑满释放人员','越级上访人员'],
                formatter: function (name) {
                  let value = '';
                  for (let i = 0; i < data1.length; i++) {
                      if (data1[i].name == name) {
                        value = data1[i].value
                      }
                  }
                  return (
                    "{name|" + name.slice(0,6)+'\n'+ name.slice(6,name.length)+ "}\n{value|" + value + "件}"
                  )
                },
              }
            ],
            graphic: [
              {
                z: 4,
                type: "image",
                id: "logo",
                left: "48%",
                top: "35%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [50, 50], //中心点
                scale: [0.8, 0.8], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: "",
                type: "pie",
                radius: ["35%", "50%"],
                center: ["56%", "50%"],
                itemStyle: {
                  normal: {
                    borderColor: "#0A1934",
                    // borderWidth: 10
                  },
                },
                label: {
                  show: false,
                },
                data: data1,
              },
              {
                name: "",
                type: "pie",
                radius: ["60%", "80%"],
                center: ["56%", "50%"],
                itemStyle: {
                  normal: {
                    borderColor: "#0A1934",
                    // borderWidth: 10
                  },
                },
                label: {
                  show: false,
                },
                data: data2,
              },
            ],
          };
          myEc.setOption(option);
        },
        //半圆饼图
        getChart15(id){
          let myEc = echarts.init(document.getElementById(id));
          let list = [{
                name: '吸毒人员涉事事件',
                value: 20,
                startColor: 'rgba(242, 160, 48, 1)',
                endColor: 'rgba(242, 160, 48, .2)'
            },
            {
                name: '精神病人涉事事件',
                value: 30,
                startColor: 'rgba(46, 201, 163, 1)',
                endColor: 'rgba(46, 201, 163, .2)'
            },
            {
                name: '刑满释放人员涉事事件',
                value: 40,
                startColor: 'rgba(72, 203, 82, 1)',
                endColor: 'rgba(72, 203, 82, .2)'
            },
            {
                name: '越级上访人员涉事事件',
                value: 50,
                startColor: 'rgba(43, 159, 254, 1)',
                endColor: 'rgba(43, 159, 254, .2)'
            }
        ]
          let data1 = [
              {
                name: "吸毒人员",
                value: 458,
              },
              {
                name: "精神病人",
                value: 962,
              },
              {
                name: "刑满释放人员",
                value: 1688,
              },
              {
                name: "越级上访人员",
                value: 2588,
              },
            ];
        var legendData = []
        var emptyData = []
        data1.forEach((el, index) => {
            legendData.push(el.name)
            emptyData.push({
                name: '',
                value: 0,

            })
        })
        var seriesData = data1.concat(emptyData)


        let listData = [];
        let totalValue = 0;
        let listLength = list.length;
        list.forEach((item, index) => {
            totalValue = totalValue + item.value;
            listData.push({
                value: item.value,
                name: item.name,
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: item.startColor // 0% 处的颜色
                            }, {
                                offset: 1,
                                color: item.endColor // 100% 处的颜色
                            }],
                            global: false // 缺省为 false
                        }
                    }
                },
            })
            // totalValue就是算出来隐藏的另一半的值,把它push进数组
            if (index == listLength - 1) {
                listData.push({
                    value: totalValue,
                    name: '隐藏的一半',
                    itemStyle: {
                        normal: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: 'rgba(0,0,0,0)' // 0% 处的颜色
                                }, {
                                    offset: 1,
                                    color: 'rgba(0,0,0,0)' // 100% 处的颜色
                                }],
                                global: false // 缺省为 false
                            }
                        }
                    },
                })
            }
        })

        option = {
            tooltip: {
              trigger: "item",
              formatter: "{b}: <br/>{d}%",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            legend: [{
              icon: "circle",
              itemGap: 24,
              itemWidth: 10,
              itemHeight: 10,
              top:'10%',
              left:'center',
              orient: "horizontal",
              data:['吸毒人员','精神病人','刑满释放人员','越级上访人员'],
              textStyle: {
                rich: {
                    name: {
                        color: "#D6E7F9",
                        padding: [0, 0, 0, 10],
                        fontSize: 28,
                    },
                    value: {
                        color: "#FEC762",
                        fontSize: 28,
                        padding: [0, 0, 0, 10],
                    }
                  }
              },
              formatter: function (name) {
                let value = '';
                for (let i = 0; i < seriesData.length; i++) {
                    if (seriesData[i].name == name) {
                      value = seriesData[i].value
                    }
                }
                return (
                  "{name|" + name.slice(0,6)+'\n'+ name.slice(6,name.length)+ "}\n{value|" + value + "件}"
                )
              },

            },{
              icon: "circle",
              itemGap: 24,
              itemWidth: 10,
              itemHeight: 10,
              top:'85%',
              left:'center',
              orient: "horizontal",
              data:['吸毒人员涉事事件','精神病人涉事事件','刑满释放人员涉事事件','越级上访人员涉事事件'],
              textStyle: {
                rich: {
                    name: {
                        color: "#D6E7F9",
                        padding: [0, 0, 10, 10],
                        fontSize: 28,
                    },
                    value: {
                        color: "#FEC762",
                        fontSize: 28,
                        padding: [0, 0, 0, 10],
                    }
                  }
              },
              formatter: function (name) {
                let value = '';
                for (let i = 0; i < listData.length; i++) {
                    if (listData[i].name == name) {
                      value = listData[i].value
                    }
                }
                return (
                  "{name|" + name.slice(0,6)+'\n'+ name.slice(6,name.length)+ "}\n{value|" + value + "件}"
                )
              },
            }
            ],
            series: [{
                name: '类型分布',
                type: 'pie',
                radius: ["35%", "50%"],
                center: ['50%', '80%'],
                startAngle: 180,
                labelLine: {
                    normal: {
                        show: false
                    }
                },
                label: {
                    normal: {
                        show: false
                    }
                },
                data: listData,
            },{
              name: "",
              type: "pie",
              radius: ["60%", "120%"],
              avoidLabelOverlap: false,
              startAngle: 180,
              center: ['50%', '80%'],
              roseType: "area",
              selectedMode: "single",
              label: {
                  normal: {
                      show: false,
                  },
              },
              labelLine: {
                  normal: {
                      show: false,
                  },
              },
              data: seriesData
            }
          ]
        };
        myEc.setOption(option);
        },
        //仪表盘
        getChart16(id){
          let myEc = echarts.init(document.getElementById(id));
          const GuageSVG = "/static/citybrain/szzf/img/yshj/gauge-bg.png";
          let option = {
            title: {
              bottom: "4%",
              left: "center",
              text: "全省第三",
              textStyle: {
                fontSize: 24,
                color: '#00C0FF',
              },
            },
            graphic: {
              elements: [
                {
                  type: "image",
                  style: {
                    image: GuageSVG,
                  },
                  left: "41%",
                  top: "85%",
                },
              ],
            },
            series: [
              {
                name: "刻度",
                type: "gauge",
                center: ["50%", "68%"],
                radius: "90%",
                min: 0,
                max: 100,
                splitNumber: 10, //刻度数量
                startAngle: 200,
                endAngle: -20,
                axisLine: {
                  show: true,
                  lineStyle: {
                    width: 1,
                    color: [[1, "rgba(0,0,0,0)"]],
                  },
                }, //仪表盘轴线
                axisLabel: {
                  show: true,
                  color: "#00C0FF",
                  distance: -30,
                  fontSize: 20,
                }, //刻度标签。
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#00C0FF",
                    width: 1,
                  },
                  length: -8,
                }, //刻度样式
                splitLine: {
                  show: true,
                  length: -12,
                  lineStyle: {
                    color: "#00C0FF",
                  },
                }, //分隔线样式
                detail: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
              },
              // 主图
              {
                type: "gauge",
                center: ["50%", "68%"],
                startAngle: 200,
                endAngle: -20,
                min: 0,
                max: 100,
                splitNumber: 12,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                      {
                        offset: 0,
                        color: "#3a79ee",
                      },
                      {
                        offset: 0.5,
                        color: "#30aaf5",
                      },
                      {
                        offset: 1,
                        color: "#2ad9fc",
                      },
                    ]),
                  },
                },
                progress: {
                  show: true,
                  roundCap: true,
                  width: 12,
                },
                pointer: {
                  show: false,
                },
                axisLine: {
                  roundCap: true,
                  lineStyle: {
                    width: 12,
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                detail: {
                  show: true,
                  offsetCenter: [0, 0],
                  formatter: function (params) {
                    return params + "\n平安指数";
                  },
                  textStyle: {
                    fontSize: 30,
                    color: "#FFC460",
                  },
                },
                data: [
                  {
                    value: 96.01,
                  },
                ],
              },
            ],
          };
          myEc.setOption(option);
        },
        //双y周柱状+折线
        getChart17(id){
          let myEc = echarts.init(document.getElementById(id));
          let option = {
            tooltip: {
              trigger: "axis",
              backgroundColor: "rgba(51, 51, 51, 0.7)",
              borderWidth: 0,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              textStyle: {
                color: "white",
                fontSize: "24",
              },
            },
            legend: {
              orient: "horizontal",
              // icon: "circle",
              itemGap: 45,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
            grid: {
              left: "5%",
              right: "6%",
              top: "20%",
              bottom: "1%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
                data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月"],
                axisLine: {
                  lineStyle: {
                    color: "rgb(119,179,241,.4)", // 颜色
                    width: 1, // 粗细
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#D6E7F9",
                    fontSize: 28,
                  },
                },
              },
            ],
            yAxis: [
              {
                name: "",
                type: "value",
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: "rgb(119,179,241,.4)",
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                  },
                },
              },
              {
                name: "",
                type: "value",
                max:100,
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: "rgb(119,179,241,.4)",
                  },
                },
                axisLabel: {
                  formatter: '{value}%',
                  textStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                  },
                },
              },
            ],
            series: [
              {
                name: "平安数据",
                type: "bar",
                barWidth: "25%",
                yAxisIndex: 0,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(0,192,255,0.99)",
                      },
                      {
                        offset: 1,
                        color: "rgba(0,192,255,0)",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: [46, 88, 2, 13, 21, 17, 13, 20],
              },{
                name: "平安指数",
                type: "line", // 直线ss
                yAxisIndex: 1,
                smooth: false,
                symbolSize: 10,
                itemStyle: {
                  normal: {
                    color: "#ffc460",
                  },
                },
                data: [8, 76, 44, 61, 13, 13, 20, 22],
              }
            ],
          };
          myEc.setOption(option);
        },
        //线条环形
        getChart18(id){
          let myEc = echarts.init(document.getElementById(id));
          const innerCircler_PaddingColor = new echarts.graphic.RadialGradient(
            0.5,
            0.5,
            0.8,
            [
              {
                offset: 0,
                color: '#4978EC00',
              },
              {
                offset: 0.5,
                color: '#4978EC00',
              },
              {
                offset: 1,
                color: '#4978ECff',
              },
            ],
            false
          );
          let datas = {
            name:"完成率",
            value:56.21
          }
          let splitNumber = 70;

          let option = {
            title: [
              {
                text: datas.value+"%",
                x: '50%',
                y: '37%',
                textAlign: 'center',
                textStyle: {
                  fontSize: '60',
                  color: '#00C0FF',
                  textAlign: 'center',
                },
              },
              {
                text: datas.name,
                left: '50%',
                top: '52%',
                textAlign: 'center',
                textStyle: {
                  fontSize: '40',
                  fontWeight: '400',
                  color: '#00C0FF',
                  textAlign: 'center',
                },
              }
            ],
            polar: {
              radius: ['51%', '47%'],
              center: ['50%', '50%'],
            },
            angleAxis: {
              max: 100,
              show: false,
              startAngle: 0,
            },
            radiusAxis: {
              type: 'category',
              show: true,
              axisLabel: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
            },
            series: [
              {
                name: "",
                type: "gauge",
                radius: "80%",
                center: ['50%', '50%'],
                z:5,
                startAngle: 0,
                endAngle: 360 * datas.value / 100,
                splitNumber: splitNumber * datas.value / 100,
                hoverAnimation: true,
                axisTick: {
                  show: false
                },
                splitLine: {
                  length:40,
                  lineStyle: {
                    width: 5,
                    color: '#00C0FF',
                  }
                },
                axisLabel: {
                  show: false
                },
                pointer: {
                  show: false
                },
                axisLine: {
                  lineStyle: {
                    opacity: 0
                  }
                },
                detail: {
                  show: false
                },
                data: [{
                  value: 0,
                  name: ""
                }]

              },
              {
                name: "",
                type: "gauge",
                radius: "80%",
                center: ['50%', '50%'],
                z:5,
                endAngle: 360,
                startAngle: 360 * datas.value / 100 + 0.7,
                splitNumber: splitNumber * (100-datas.value) / 100,
                hoverAnimation: true,
                axisTick: {
                  show: false
                },
                splitLine: {
                  length: 40,
                  lineStyle: {
                    width: 5,
                    color:  "#00526E"
                  }
                },
                axisLabel: {
                  show: false
                },
                pointer: {
                  show: false
                },
                axisLine: {
                  lineStyle: {
                    opacity: 0
                  }
                },
                detail: {
                  show: false
                },
                data: [{
                  value: 0,
                  name: ""
                }]
              },
              {
                name: '内部圈',
                type: 'gauge',
                colorBy: 'series',
                z: 2,
                splitNumber: 0,
                startAngle: 90,
                endAngle: -269.9999,
                radius: '60%',
                axisLine: {
                  lineStyle: {
                    color: [[1, '#028ae8']],
                    width: 3,
                    shadowColor: '#00FFFF',
                    shadowBlur: 0,
                    shadowOffsetX: 0,
                  },
                },
                tooltip: { show: false },
                axisLabel: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                itemStyle: {
                  show: false,
                },
                detail: {
                  show: false,
                },
                title: {
                  //标题
                  show: false,
                },
                data: [
                  {
                    name: 'title',
                    value: 90,
                  },
                ],

                // 指针
                pointer: { show: false },
                animationDuration: 500,
              },
              {
                type: 'pie',
                tooltip: {
                  show: false,
                },
                z: 10,
                hoverAnimation: false,
                legendHoverLink: false,
                radius: ['0%', '58%'],
                center: ['50%', '50%'],
                label: {
                  normal: {
                    show: true,
                  },
                },
                itemStyle: {
                  normal: {
                    color: innerCircler_PaddingColor,
                    label: {
                      show: false,
                    },
                    labelLine: {
                      show: false,
                    },
                  },
                },
                labelLine: {
                  normal: {
                    show: false,
                  },
                },
                data: [
                  {
                    value: 120,
                  },
                ],
              },
            ],
          };
          myEc.setOption(option);
        },
        //雷达图
        getChart19(id){
          let myEc = echarts.init(document.getElementById(id));
          let option = {
            tooltip: {
              //雷达图的tooltip不会超出div，也可以设置position属性，position定位的tooltip 不会随着鼠标移动而位置变化，不友好
              confine: true,
              enterable: true, //鼠标是否可以移动到tooltip区域内
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "28",
              },
            },
            radar: {
              name: {
                textStyle: {
                  color: "#FFFFFF",
                  fontSize: 28,
                },
              },
              shape: "polygon",
              center: ["50%", "50%"],
              radius: "60%",
              // startAngle: 120,
              scale: true,
              axisLine: {
                lineStyle: {
                  color: "rgba(5, 213, 255, .8)",
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  width: 1,
                  color: "rgba(5, 213, 255, .8)", // 设置网格的颜色
                },
              },
              indicator: [
                {
                  name: "培训补贴发放",
                  max: 100,
                },
                {
                  name: "企业非法经营",
                  max: 100,
                },
                {
                  name: "企业黑名单",
                  max: 100,
                },
                {
                  name: "企业法人老赖",
                  max: 100,
                },
                {
                  name: "xx企业失业分析",
                  max: 100,
                },
              ],
              splitArea: {
                show: false,
              },
            },
            grid: {
              position: "center",
            },
            polar: {
              center: ["50%", "50%"], // 默认全局居中
              radius: "0%",
            },
            angleAxis: {
              min: 0,
              interval: 5,
              clockwise: false,
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
              },
            },
            radiusAxis: {
              min: 0,
              interval: 20,
              splitLine: {
                show: false,
              },
            },
            legend: {
              top: 25,
              right: 20,
              icon: "rect",
              itemWidth: 17, // 图例标记的图形宽度。[ default: 25 ]
              itemHeight: 12, // 图例标记的图形高度。[ default: 14 ]
              itemGap: 9, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
              data: ["最高", "最低"],
            },
            series: [
              {
                name: "最高",
                type: "radar",
                symbol: "circle", // 拐点的样式，还可以取值'rect','angle'等
                symbolSize: 10, // 拐点的大小
                itemStyle: {
                  normal: {
                    color: "#05D5FF",
                  },
                },
                areaStyle: {
                  normal: {
                    color: "#05D5FF",
                    opacity: 0.3,
                  },
                },
                lineStyle: {
                  width: 2,
                  color: "#05D5FF",
                },
                label: {
                  normal: {
                    show: false,
                    formatter: (params) => {
                      return params.value;
                    },
                    color: "#fff",
                  },
                },
                data: [
                  {
                    value: [20, 50, 60, 60, 90, 80],
                  },
                ],
              },
              {
                name: "最低",
                type: "radar",
                symbol: "circle", // 拐点的样式，还可以取值'rect','angle'等
                symbolSize: 10, // 拐点的大小
                itemStyle: {
                  normal: {
                    color: "#F7DA6D",
                  },
                },
                areaStyle: {
                  normal: {
                    color: "#F7DA6D",
                    opacity: 0.3,
                  },
                },
                lineStyle: {
                  width: 2,
                  color: "#F7DA6D",
                },
                label: {
                  normal: {
                    show: false,
                    formatter: (params) => {
                      return params.value;
                    },
                    color: "#fff",
                  },
                },
                data: [
                  {
                    value: [30, 20, 50, 50, 100, 60],
                  },
                ],
              },
            ],
          };
          myEc.setOption(option);
        },
        //关系图
        getChart20(id){
          let myEc = echarts.init(document.getElementById(id));
          var listData = [
            {
              name: '根节点1',
              value: 0,
              draggable: false,
              fixed: true,
              y: 100, //myChart.getWidth() - 100
              x: myEc.getWidth() / 3,
              list: [
                {
                  name: '节点1',
                  value: 1,
                  list: [
                    { name: '节点1-1-1', value: '1-1-1' },
                    { name: '节点1-1-2', value: '1-1-2' },
                    { name: '节点1-1-3', value: '1-1-3' },
                    { name: '节点1-1-4', value: '1-1-4' },
                    { name: '节点1-1-5', value: '1-1-5' },
                  ],
                },
                {
                  name: '节点2',
                  value: 2,
                  list: [
                    { name: '节点1-2-1', value: '1-2-1' },
                    { name: '节点1-2-2', value: '1-2-2' },
                    { name: '节点1-2-3', value: '1-2-3' },
                    { name: '节点1-2-4', value: '1-2-4' },
                    { name: '节点1-2-5', value: '1-2-5' },
                  ],
                },
                {
                  name: '节点3',
                  value: 3,
                  list: [
                    { name: '节点1-3-1', value: '1-3-1' },
                    { name: '节点1-3-2', value: '1-3-2' },
                    { name: '节点1-3-3', value: '1-3-3' },
                    { name: '节点1-3-4', value: '1-3-4' },
                    {
                      name: '节点1-3-51111',
                      value: '1-3-5111',
                      list: [
                        { name: '节点1-3-111', value: '1-3-1' },
                        { name: '节点1-3-2111', value: '1-3-2' },
                        { name: '节点1-3-311', value: '1-3-3' },
                        { name: '节点1-3-4111', value: '1-3-4' },
                        { name: '节点1-3-511', value: '1-3-5' },
                      ]
                    },
                  ],
                },
              ],
            }
          ];
          var listSon = listData[0].list; //除一级节点外
          var legend = []; //二级节点列表 [val]
          var categories = []; //二级节点列表 {name:val}
          var list = [];
          var links = []; // source: '源节点名称', target: '目标节点名称'
          listSon.forEach((item) => {
              legend.push(item.name);
              categories.push({ name: item.name });
          });

          handleList(JSON.parse(JSON.stringify(listData)), 0);
          handleLinks(JSON.parse(JSON.stringify(listData)), 0);

          //  计算list
          function handleList(arr, idx, color, category) {
              arr.forEach((item, index) => {
                  if (item.name === null) {
                      return false;
                  }
                  // 设置节点大小
                  let symbolSize = 10;
                  // 每个节点所对应的文本标签的样式。
                  let label = null;
                  if (idx === 0) {
                      //根节点
                      symbolSize = 130;
                      label = {
                          position: 'inside',
                          rotate: 0,
                          fontSize: 22,
                          fontWeight: 'bold',
                          color: '#FFFFFF',
                      };
                  } else if (idx === 1) {
                      //一级节点
                      symbolSize = 60;
                      label = {
                          position: 'right',
                          rotate: 0,
                          fontSize: 15,
                          distance: 0,
                          fontWeight: 'bold',
                          color: '#F4BE18',
                      };
                  } else {
                      symbolSize = 20;
                      label = {
                          fontSize: 12,
                          color: '#D0C7AD',
                      };
                  }

                  //浮窗 前面圆颜色设置
                  if (idx === 0) {
                      //根节点
                      bgcolor = 'blue';
                  } else {
                      //一级节点
                      bgcolor = 'red';
                  }
                  let itemStyle = null;
                  if (item.list && item.list.length !== 0) {
                      //非子节点
                      itemStyle = {
                          borderColor: '#000',
                          color: bgcolor,
                      };
                  } else {
                      //子节点
                      itemStyle = {
                          color: 'transparent',
                          borderColor: '#ccc',
                      };
                  }
                  //实现节点发光效果
                  itemStyle = Object.assign(itemStyle, {
                      shadowColor: 'rgba(202, 144, 3, 0.5)',
                      shadowBlur: 20,
                  });

                  if (idx == 1) {
                      category = item.name;
                  }
                  let obj = {
                      name: item.name,
                      symbolSize: symbolSize,
                      category: category,
                      label,
                      color: bgcolor,
                      itemStyle,
                  };
                  obj = Object.assign(item, obj);
                  list.push(obj);
                  if (item.list && item.list.length > 0) {
                      handleList(item.list, idx + 1, color, category);
                  }
              });
          }
          // 计算links
          function handleLinks(arr, index) {
              arr.forEach((item) => {
                  if (item.list) {
                      item.list.forEach((item2, eq) => {
                          let lineStyle = null;
                          if (index === 0) {
                              //一级二级中间节点的线
                              lineStyle = {
                                  normal: {
                                      color: 'rgba(255,255,255,0.3)', //线
                                      width: 3,
                                  },
                              };
                          } else {
                              lineStyle = {
                                  normal: {
                                      color: 'rgba(255,255,255,0.5)', //线
                                      width: 1,
                                  },
                              };
                          }
                          let obj = {
                              source: item.name,
                              target: item2.name,
                              value: 1111, //连接线上文字
                              lineStyle,
                          };
                          links.push(obj);
                          if (item2.list && item.list.length > 0) {
                              handleLinks(item.list, index + 1);
                          }
                      });
                  }
              });
          }

          option = {
              backgroundColor: '#020f3c',
              tooltip: {},
              toolbox: {
                  show: true,
                  left: 'right',
                  right: 20,
                  top: 'bottom',
                  bottom: 20,
              },
              legend: {
                  show: true,
                  data: legend,
                  textStyle: {
                      color: '#fff',
                      fontSize: 10,
                  },
                  // inactiveColor: "#fff",
                  icon: 'circle',
                  type: 'scroll',
                  orient: 'vertical',
                  left: 'right',
                  right: 20,
                  top: 20,
                  bottom: 80,
                  // itemWidth: 12,
                  // itemHeight: 12,
                  pageIconColor: '#00f6ff',
                  pageIconInactiveColor: '#fff',
                  pageIconSize: 12,
                  pageTextStyle: {
                      color: '#fff',
                      fontSize: 12,
                  },
              },
              selectedMode: 'false',
              bottom: 20,
              left: 0,
              right: 0,
              confine: true,
              top: 0,
              animationDuration: 1500,
              animationEasingUpdate: 'quinticInOut',
              series: [
                  {
                      name: '知识图谱',
                      type: 'graph',
                      hoverAnimation: true,
                      layout: 'force',
                      force: {
                          repulsion: [-2, 100], //节点之间的斥力因子。支持数组表达斥力范围，值越大斥力越大。
                          gravity: 0.03, //节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
                          edgeLength: [20, 200], //边的两个节点之间的距离，这个距离也会受 repulsion:[10, 50]值越小则长度越长
                          layoutAnimation: true,
                      },
                      nodeScaleRatio: 0.6,
                      draggable: true,
                      roam: false, //鼠标缩放和平移
                      symbol: 'circle',
                      data: list,
                      links: links,
                      categories: categories,
                      cursor: 'pointer',
                      focusNodeAdjacency: true,
                      scaleLimit: {
                          //所属组件的z分层，z值小的图形会被z值大的图形覆盖
                          min: 0, //最小的缩放值
                          max: 9, //最大的缩放值
                      },
                      // edgeSymbol: ['circle', 'arrow'],
                      // edgeSymbolSize: [4, 8],
                      edgeLabel: {
                          //连接线上文字
                          normal: {
                              show: true,
                              textStyle: {
                                  fontSize: 10,
                              },
                              formatter: '{c}',
                          },
                      },
                      label: {
                          normal: {
                              show: true,
                              position: 'right',
                              color: '#fff',
                              distance: 10,
                          },
                      },
                      lineStyle: {
                          normal: {
                              width: 1.5,
                              curveness: 0,
                              type: 'solid',
                          },
                      },
                  }
              ],
          };
          myEc.setOption(option);
        },
        //固定位置的关系图
        getChart21(id){
          let myEc = echarts.init(document.getElementById(id));
          var size = 300;
          var size1 = [270,120];
          var size2 = [130,80];
          let option = {
            animationDuration: 1000,
            animationEasingUpdate: "quinticInOut",
            series: [
              {
                type: "graph",
                layout: "none", //"force"
                // zoom:4.5,
                roam: true, //鼠标缩放及平移
                focusNodeAdjacency: true, //是否在鼠标移到节点上的时候突出显示节点以及节点的边和邻接节点
                // force: {
                //   repulsion: 60,
                // },
                label: {
                  normal: {
                    show: true, //控制非高亮时节点名称是否显示
                    position: "inside",
                    fontSize: 32,
                    color: "#fff",
                  },
                },
                lineStyle: {
                  normal: {
                    width: 5,
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                        offset: 0.1,
                        color: 'rgba(220,239,255,0)'
                    }, {
                        offset: 0.5,
                        color: '#DCEFFF'
                    }, {
                        offset: 0.9,
                        color: 'rgba(220,239,255,0)'
                    }]),
                  },
                },
                data:[
                  {
                    name: "上游",
                    x: 150,
                    y: 300,
                    symbol: 'image://../../qyhx/images/gf/wj_1_1.png',
                    symbolSize: size,
                    // draggable: true,
                    label: {
                      normal: {
                        fontSize: 48,
                      },
                    },
                  },
                  {
                    name: "中游",
                    x: 300,
                    y: 300,
                    symbol: 'image://../../qyhx/images/gf/wj_1_2.png',
                    symbolSize: size,
                    label: {
                      normal: {
                        fontSize: 48,
                      },
                    },
                  },
                  {
                    name: "下游",
                    x: 450,
                    y: 300,
                    symbol: 'image://../../qyhx/images/gf/wj_1_3.png',
                    symbolSize: size,
                    label: {
                      normal: {
                        fontSize: 48,
                      },
                    },
                  },
                  {
                    x: 120,
                    y: 250,
                    symbol: 'image://../../qyhx/images/gf/wj_2_3.png',
                    "name": "制造装备",
                    "symbolSize": size1,
                  }, 
                  {
                    x: 180,
                    y: 250,
                    symbol: 'image://../../qyhx/images/gf/wj_2_1.png',
                    "name": "原材料",
                    "symbolSize": size1,
                    "category": "原材料",
                  }, {
                    x: 120,
                    y: 350,
                    symbol: 'image://../../qyhx/images/gf/wj_2_2.png',
                    "name": "能源供给",
                    "symbolSize": size1,
                  }, {
                    x: 180,
                    y: 350,
                    symbol: 'image://../../qyhx/images/gf/wj_2_2.png',
                    "name": "标准件常用件",
                    "symbolSize": size1,
                  }, 
                  {
                    x: 270,
                    y: 250,
                    "name": "支撑框架",
                    symbol: 'image://../../qyhx/images/gf/wj_2_3.png',
                    "symbolSize": size1,
                  }, {
                    x: 330,
                    y: 250,
                    "name": "工作头",
                    symbol: 'image://../../qyhx/images/gf/wj_2_1.png',
                    "symbolSize": size1,
                  }, {
                    x: 260,
                    y: 350,
                    "name": "原动件",
                    symbol: 'image://../../qyhx/images/gf/wj_2_2.png',
                    "symbolSize": size1,
                  }, {
                    x: 340,
                    y: 350,
                    "name": "传动装置",
                    symbol: 'image://../../qyhx/images/gf/wj_2_2.png',
                    "symbolSize": size1,
                  },{
                    x: 300,
                    y: 370,
                    "name": "调解装置",
                    symbol: 'image://../../qyhx/images/gf/wj_2_1.png',
                    "symbolSize": size1,
                  },
                  {
                    x: 450,
                    y: 250,
                    "name": "消费用户",
                    symbol: 'image://../../qyhx/images/gf/wj_2_1.png',
                    "symbolSize": size1,
                  },{
                    x: 450,
                    y: 350,
                    "name": "商品流通",
                    symbol: 'image://../../qyhx/images/gf/wj_2_2.png',
                    "symbolSize": size1,
                  },{
                    x: 160,
                    y: 230,
                    "name": "粉末冶/金材料1",
                    symbol: 'image://../../qyhx/images/gf/wj_3_1.png',
                    "symbolSize": size2,
                    label: {
                      normal: {
                        fontSize: 28,
                        formatter(val) {
                          return val.name.split("/").join("\n")
                        }
                      },
                    },
                  },{
                    x: 180,
                    y: 215,
                    "name": "粉末冶/金材料2",
                    symbol: 'image://../../qyhx/images/gf/wj_3_2.png',
                    "symbolSize": size2,
                    label: {
                      normal: {
                        fontSize: 28,
                        formatter(val) {
                          return val.name.split("/").join("\n")
                        }
                      },
                    },
                  },{
                    x: 200,
                    y: 230,
                    "name": "粉末冶/金材料3",
                    symbol: 'image://../../qyhx/images/gf/wj_3_3.png',
                    "symbolSize": size2,
                    label: {
                      normal: {
                        fontSize: 28,
                        formatter(val) {
                          return val.name.split("/").join("\n")
                        }
                      },
                    },
                  },{
                    x: 430,
                    y: 230,
                    "name": "粉末冶/金材料4",
                    symbol: 'image://../../qyhx/images/gf/wj_3_1.png',
                    "symbolSize": size2,
                    label: {
                      normal: {
                        fontSize: 28,
                        formatter(val) {
                          return val.name.split("/").join("\n")
                        }
                      },
                    },
                  },{
                    x: 450,
                    y: 215,
                    "name": "粉末冶/金材料5",
                    symbol: 'image://../../qyhx/images/gf/wj_3_2.png',
                    "symbolSize": size2,
                    label: {
                      normal: {
                        fontSize: 28,
                        formatter(val) {
                          return val.name.split("/").join("\n")
                        }
                      },
                    },
                  },{
                    x: 470,
                    y: 230,
                    "name": "粉末冶/金材料6",
                    symbol: 'image://../../qyhx/images/gf/wj_3_3.png',
                    "symbolSize": size2,
                    label: {
                      normal: {
                        fontSize: 28,
                        formatter(val) {
                          return val.name.split("/").join("\n")
                        }
                      },
                    },
                  }
                ],
                // 对应关系
                links: [
                  {
                    source: "上游",
                    target: "中游",
                  },
                  {
                    source: "中游",
                    target: "下游",
                  },
                  {
                    source: "上游",
                    target: "制造装备",
                  },
                  {
                    source: "上游",
                    target: "原材料",
                  },
                  {
                    source: "上游",
                    target: "能源供给",
                  },
                  {
                    source: "上游",
                    target: "标准件常用件",
                  },
                  {
                    source: "中游",
                    target: "支撑框架",
                  },
                  {
                    source: "中游",
                    target: "工作头",
                  },
                  {
                    source: "中游",
                    target: "原动件",
                  },
                  {
                    source: "中游",
                    target: "传动装置",
                  },
                  {
                    source: "中游",
                    target: "调解装置",
                  },
                  {
                    source: "下游",
                    target: "消费用户",
                  },
                  {
                    source: "下游",
                    target: "商品流通",
                  },
                  {
                    source: "原材料",
                    target: "粉末冶/金材料1",
                  },
                  {
                    source: "原材料",
                    target: "粉末冶/金材料2",
                  },
                  {
                    source: "原材料",
                    target: "粉末冶/金材料3",
                  },
                  {
                    source: "消费用户",
                    target: "粉末冶/金材料4",
                  },
                  {
                    source: "消费用户",
                    target: "粉末冶/金材料5",
                  },
                  {
                    source: "消费用户",
                    target: "粉末冶/金材料6",
                  }
                ],
              },
            ],
          };

          myEc.setOption(option);
        },
        //滚动横向柱状
        getChart22(id){
          let myEc = echarts.init(document.getElementById(id));
          let item = [
            {
              name: 'a',
              value: 100,
            },{
              name: 'b',
              value: 90,
            },{
              name: 'c',
              value: 80,
            },{
              name: 'd',
              value: 70,
            },{
              name: 'e',
              value: 60,
            },{
              name: 'f',
              value: 50,
            },{
              name: 'g',
              value: 40,
            },{
              name: 'h',
              value: 30,
            },{
              name: 'i',
              value: 20,
            },{
              name: 'j',
              value: 10,
            }
          ]
          let rank = true
          let titleWidth = 1500
          let getSymbolData = (item) => {
            let arr = [];
            for (var i = 0; i < item.length; i++) {
              arr.push({
                value: item[i].value,
                symbolPosition: "end",
              });
            }
            return arr;
          };
          let option = {
            graphic: {
              type: "text",
              left: 'center',
              top: 'middle',
              silent: true,
              invisible: item.length,
              style: {
                fill: "white",
                text: "暂无数据",
                font: '32px system-ui'
              }
            },
            grid: {
              top: 10,
              bottom: -30,
              right: '5%',
              left: 10,
              containLabel: true,
            },
            xAxis: {
              show: false,
            },
            yAxis: [
              {
                triggerEvent: true,
                show: true,
                inverse: true,
                data: item.map((item) => {
                  item.value;
                }),
                axisLine: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                  interval: 0,
                  color: "#CDE7FF",
                  align: "left",
                  margin: 80,
                  fontSize: 30,
                  formatter: function (value, index) {
                    return "{name|" + value + "}";
                  },
                  rich: {
                    title: {
                      width: 165,
                    },
                  },
                },
              },
              {
                triggerEvent: true,
                show: false,
                inverse: true,
                data: item.map((item) => {
                  item.name;
                }),
                axisLine: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                  interval: 0,
                  shadowOffsetX: "-20px",
                  color: ["#00C0FF"],
                  align: "right",
                  verticalAlign: "bottom",
                  lineHeight: 30,
                  fontSize: 20,
                  formatter: function (value, index) {
                    // if (item[index].unit) {
                    //   return item[index].value + item[index].unit;
                    // } else {
                    //   return item[index].value;
                    // }
                  },
                },
              },
            ],
            dataZoom: [
              {
                type: "inside",
                id: "insideY",
                yAxisIndex: [0, 1],
                start: 0,
                end: (3 / item.length) * 100,
                zoomOnMouseWheel: false,
                moveOnMouseMove: true,
                moveOnMouseWheel: true,
              },
            ],
            series: [
              {
                name: "XXX",
                type: "pictorialBar",
                symbol: "image://data:image/png;base64,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",
                symbolSize: [30, 30],
                symbolOffset: [20, 0],
                z: 12,
                itemStyle: {
                  normal: {
                    color: "#D6E7F9",
                  },
                },
                data: getSymbolData(item),
              },
              {
                name: "条",
                type: "bar",
                showBackground: true,
                barBorderRadius: 30,
                yAxisIndex: 0,
                data: item,
                barWidth: 5,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(
                      0,
                      0,
                      1,
                      0,
                      [
                        {
                          offset: 1,
                          color: "#1890FF",
                        },

                        {
                          offset: 0,
                          color: "rgba(24, 144, 255,0.2)",
                        },
                      ],
                      false
                    ),
                    barBorderRadius: 10,
                  },
                  barBorderRadius: 4,
                },
                label: {
                  normal: {
                    color: "#CDE7FF",
                    show: true,
                    position: [0, "-35px"],
                    textStyle: {
                      fontSize: 20,
                    },
                    rich: {
                      title: {
                        width: titleWidth || 465,
                        color: "#FFFFFF",
                        fontSize: 28,
                      },
                      value: {
                        color: "#FFFFFF",
                        fontSize: 28,
                      },
                    },

                    formatter: function (a, b) {
                      let res = "";

                      if (rank) {
                        if (a.name.length > 9) {
                          a.name = a.name.slice(0, 9) + "...";
                        }
                        let i = a.dataIndex + 1;
                        res =
                          "{title|" +
                          ("No." + i) +
                          "  " +
                          a.name +
                          "}" +
                          "{value|" +
                          a.value +
                          "条" +
                          "}";
                      } else {
                        if (a.name.length > 13) {
                          res = a.dataIndex + 1 + "." + a.name.slice(0, 13) + "...";
                        } else {
                          res = a.dataIndex + 1 + "." + a.name;
                        }
                      }
                      return res;
                    },
                  },
                },
              },
            ],
          };
          myEc.setOption(option);
        }
      }
    })
  </script>
</body>

</html>
