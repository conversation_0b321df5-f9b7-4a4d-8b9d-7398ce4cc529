<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>地图工具栏</title>
  <script src="/static/citybrain/hjbh/js/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <link rel="stylesheet" href="/static/citybrain/tckz/icon/iconfont.css" />
  <script src="/static/js/jslib/jquery-3.6.1.min.js"></script>
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <style>
    body,
    html {
      margin: 0;
    }
  
    [v-cloak] {
      display: none;
    }
  
    /* 设置滚动条的样式 */
    ::-webkit-scrollbar {
      width: 10px;
    }
  
    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      border-radius: 5px;
    }
  
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(35, 144, 207, 0.1);
    }
  
    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(27, 146, 215, 0.4);
    }
  
    .toolbar {
      width: 500px;
      height: max-content;
      background: rgba(3, 24, 39, 0.88);
      border: 2px solid #afdcfb;
      border-radius: 25px;
      box-sizing: border-box;
      position: relative;
      padding: 30px;
      color: #b6d1f0;
      font-size: 26px;
      overflow-y: scroll;
    }
  
    .box {
      /* width: 100%; */
      display: flex;
      flex-wrap: wrap;
      justify-content: left;
    }
  
    .el-divider--horizontal {
      margin: 10px 0 15px;
      opacity: 0.5;
    }
  
    .item {
      width: 50px;
      height: 50px;
      border: 1px solid #b6d1f0;
      border-radius: 10px;
      font-size: 30px;
      line-height: 50px;
      text-align: center;
      margin: 5px 17px;
      cursor: pointer;
      box-sizing: border-box;
    }
    #floodAna .item{
      margin: auto 17px;
    }
    .item-active {
      border: 1px solid #03f0ff;
      color: #03f0ff;
    }
  
    .iconfont {
      font-size: 35px;
    }
  
    .bottom {
      display: flex;
      justify-content: flex-end;
      /* margin: 10px 15px; */
    }
  
    .el-button--mini {
      font-size: 18px;
    }
  
    #area-box,
    #distance-box {
      position: absolute;
      top: 100px;
      right: 80px;
    }
  
    .el-slider__runway {
      width: 200px;
      height: 10px;
      margin-left: 30px;
    }
  
    .el-slider__bar {
      height: 10px;
    }
  
    .el-slider__button-wrapper {
      width: 60px;
      height: 60px;
      top: -25px;
    }
    .el-checkbox-group{
      font-size: 20px;
      margin: 5px 17px;
    }
    .el-checkbox__label {
      font-size: 20px;
      font-family: PangMenZhengDao;
      font-weight: bold;
      color: #c0d6ed;
      line-height: 58px;
    }
  
    .transparent {
      font-size: 26px;
      color: #c0d6ed;
      padding-left: 20px;
      box-sizing: border-box;
    }
  
    .transparent p {
      margin: 0;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <div class="toolbar">
      <div v-for="(items,index) in list" :key="index" style="margin-bottom:20px">
        <span>{{items.name}}</span>
        <el-divider></el-divider>
        <div class="box">
            <div
              class="item"
              v-for="(item,i) in items.mapTool"
              @click="mapFun(item)"
              :title="item.name"
            :class="((iconIndex==item.id)
            ||(item.name=='新建剖切' && sliceFlag)
            ||(item.name=='添加精模' && modelFlag)
            ||(item.name=='通视分析' && sightFlag)
            ||(item.name=='穹顶分析' && domSightFlag)
            ||(item.name=='城市天际线' && skylineFlag)
            ||(item.name=='日照分析' && lightFlag)) 
            ?  'item-active' : ''"
            >
            <!-- 上面一段实现空间分析工具的单独判断 -->
              <i v-if="item.name!='日照分析'&&item.name!='通视分析'" class="iconfont" :class="item.icon"></i>
              <img v-else :src=`/static/citybrain/csdn/img/icon/${item.icon}.png` alt="">
          </div>
        </div>
      </div>
      <!-- <span>地表透明度</span>
      <el-divider></el-divider>
      <div style="display: flex">
        <el-slider v-model="sliderValue" :min="min" :max="max" :show-tooltip="false" @change="sliderChange"></el-slider>
        <div class="transparent">
          <p>透明度：{{sliderValue/10}}</p>
        </div>
      </div> -->
      <span>淹没分析</span>
      <el-divider></el-divider>
      <div style="display: flex" id="floodAna">
        <div class="box">
          <div class="item" @click="floodSimulate()" title="绘制分析范围" :class="(iconIndex==4)?  'item-active' : ''">
            <i class="iconfont icon-icon-mian"></i>
          </div>
        </div>
        <div>
          <el-slider v-model="baseSliderValue" :min="baseMin" :max="baseMax" :show-tooltip="false"
            @change="baseSliderChange"></el-slider>
          <div class="transparent">
            <p>设置基面高度：{{baseSliderValue}}米</p>
          </div>
          <el-slider v-model="floodTimeSliderValue" :min="floodTimeMin" :max="floodTimeMax" :show-tooltip="false"
            @change="floodSliderChange"></el-slider>
          <div class="transparent">
            <p>模拟降雨量</p>
          </div>
        
        </div>
      </div>


      <div class="bottom">
        <el-button type="primary" size="mini" icon="el-icon-delete" @click="cleanall">清除</el-button>
      </div>
    </div>
    <div id="area-box"></div>
    <div id="distance-box"></div>
  </div>
</body>

<script>
  var souName = new Vue({
    el: '#app',
    data() {
      return {
        //地表透明度滑块
        sliderValue: 10,
        min: 0,
        max: 10,
        //淹没分析滑块
        baseSliderValue: 0,
        baseMin: 0,
        baseMax: 200,
        floodTimeSliderValue: 0,
        floodTimeMin: 0,
        floodTimeMax: 100,
        //按钮单选控制器
        iconIndex: null,

        modelFlag: false, //控制精模按钮
        sliceFlag: false, //控制剖切按钮
        sightFlag: false, //控制通视按钮
        domSightFlag: false, //控制通视按钮
        skylineFlag: false, //控制天际线按钮
        lightFlag: false, //控制日照按钮
        // list: [
        //   {
        //     id: 1,
        //     name: '绘图工具',
        //     mapTool: [
        //       {
        //         id: 11,
        //         name: '绘点',
        //         icon: 'icon-ditu',
        //       },
        //       {
        //         id: 12,
        //         name: '绘面',
        //         icon: 'icon-icon-mian',
        //       },
        //       {
        //         id: 13,
        //         name: '绘线',
        //         icon: 'icon-icon-xian',
        //       },
        //       {
        //         id: 14,
        //         name: '绘圆',
        //         icon: 'icon-yuanxinyuan',
        //       },
        //       {
        //         id: 15,
        //         name: '绘矩形',
        //         icon: 'icon-juxing',
        //       },
        //     ],
        //   },
        //   {
        //     id: 2,
        //     name: '测量工具',
        //     mapTool: [
        //       {
        //         id: 21,
        //         name: '面积测量',
        //         icon: 'icon-mianjiceliang',
        //       },
        //       {
        //         id: 22,
        //         name: '距离测量',
        //         icon: 'icon-juliceliang',
        //       },
        //       {
        //         id: 23,
        //         name: '图层面积测量',
        //         icon: 'icon-mianjiceliang1',
        //       },
        //     ],
        //   },
        //   {
        //     id: 3,
        //     name: '空间分析工具',
        //     mapTool: [
        //       {
        //         id: 31,
        //         name: '添加精模',
        //         icon: 'icon-kongjianfenxi',
        //       },
        //       {
        //         id: 32,
        //         name: '新建剖切',
        //         icon: 'icon-pouqie',
        //       },
        //       {
        //         id: 33,
        //         name: '通视分析',
        //         icon: 'icon-ts',
        //       },
        //       {
        //         id: 34,
        //         name: '穹顶分析',
        //         icon: 'icon-xingzhuang-banqiuti',
        //       },
        //       {
        //         id: 35,
        //         name: '城市天际线',
        //         icon: 'icon-tianjixian',
        //       },
        //       {
        //         id: 36,
        //         name: '日照分析',
        //         icon: 'icon-rz',
        //       },
        //     ],
        //   },
        // ],

        list: [
          {
            id: 1,
            name: '日照分析',
            mapTool: [
              {
                id: 1,
                name: '日照分析',
                icon: 'icon-rz',
              },
            ],
          },
        ],


      }
    },

    mounted() {
      this.beforeClose()
    },
    methods: {
      mapFun(item) {
        let view = top.window.mapUtil.mapview
        let drawType = {
          绘点: 'point',
          绘面: 'polygon',
          绘线: 'polyline',
          绘圆: 'circle',
          绘矩形: 'rectangle',
        }
        let measureType = {
          面积测量: 'area-box',
          距离测量: 'distance-box',
          图层面积测量:'图层面积测量',
        }
        let sliceLeft = '4890px'
        let sightLeft = '4890px'
        let daylightLeft = '4890px'
        let skyLineLeft = '4890px'
        let layerAreaRight='2874px'
        if (//判断页面是否折叠，移动空间分析工具面板
          top.document
            .getElementsByClassName('index_main_mapIcon')[0]
            .getAttribute('class')
            .indexOf('map_mapIcon_move') > -1
        ) {
          sliceLeft='6930px'
          sightLeft='6930px'
          daylightLeft='6930px'
          skyLineLeft='6930px'
        }
        switch (item.name) {
          case '添加精模':
            if (this.modelFlag) {
              top.ArcGisUtils.removePureModalLayer(view)
              this.modelFlag = false
            } else {
              top.ArcGisUtils.addPureModalLayer(view)
              this.modelFlag = true
            }
            break
          case '新建剖切':
            if (window.sliceWidget) {
              window.sliceWidget.destroy()
              window.sliceWidget = null
              this.sliceFlag = false
            } else {
              if (!top.document.getElementById('sliceContainer')) {
                //判断widget状态，并提升至index
                let widget = top.document.createElement('div')
                widget.id = 'sliceContainer'
                top.$('.container').append(widget)
              }
              top.$('#sliceContainer').css({'left':sliceLeft,'top':'1473px'})
              window.sliceWidget = top.ArcGisUtils.createSiceWidget(view, 'sliceContainer')
              this.sliceFlag = true
            }
            break
          case '通视分析':
            if (window.lineOfSightWidget) {
              //判断状态
              window.lineOfSightWidget.destroy()
              window.lineOfSightWidget = null
              this.sightFlag = false
            } else {
              if (!top.document.getElementById('lineOfSight')) {
                //判断widget状态，并提升至index
                let widget = top.document.createElement('div')
                widget.id = 'lineOfSight'
                top.$('.container').append(widget)
              }
              top.$('#lineOfSight').css({'left':sightLeft,'top':'1563px'})
              window.lineOfSightWidget = top.ArcGisUtils.createLineOfSightWidget(view, 'lineOfSight')
              this.sightFlag = true
            }
            break
          case '穹顶分析':
            if (window.domAnalysisRef) {
              //判断状态
              view.container.style.cursor = "default";
              window.domAnalysisRef.removeAll();
              window.domAnalysisRef  = null;
              this.domSightFlag = false;
            } else {
              window.domAnalysisRef = new top.ArcGisUtils.DomAnalysis({ // 创建实例，并传入监听回调
                View:view
              }, (total, visible, invisible, percent) => {
                console.log(total, visible, invisible, percent);
              });
              window.domAnalysisRef.autoDrawSphere();
              this.domSightFlag = true
            }
            break
          case '城市天际线':
            if (window.skylineRef) {
              //判断状态
              window.skylineRef.destroy();
              window.skylineRef = null;
              this.skylineFlag = false;
              top.$('#skyLine').css({ 'display': 'none' })
              top.$('#skyLineWidget').css({ 'display': 'none' })
              top.$('#skyLineMask').css({ 'display': 'none' })
            } else {
              let pos = {
                "position": {
                  "spatialReference": {
                    "latestWkid": 4490,
                    "wkid": 4490,
                    "vcsWkid": 5773,
                    "latestVcsWkid": 5773
                  },
                  "x": 119.64764282478615,
                  "y": 29.096521043880852,
                  "z": 45.46269989665598
                },
                "heading": 166.32915651655648,
                "tilt": 91.42968213747491,
              }
              window.skylineRef = new top.ArcGisUtils.Skyline({ view });
              window.skylineRef.on('loading',e=>{console.log(e);})
              if (!top.document.getElementById('skyLineWidget')) {
                let widget = top.document.createElement('div')
                widget.id = 'skyLineWidget'
                top.$('.container').append(widget)
              }
              top.$('#skyLineWidget').html(`
                  <button id="skyLineLocate" onclick="">定位到默认视角</button>
                  <button id="skyLineCreate" onclick="">获取天际线</button>
                  `)
              top.$('#skyLineWidget').css({
                'display': 'flex', 'flex-flow': 'column', 'position': 'absolute', 'left': skyLineLeft, 'top': '1473px', 'z-index': '997', 'color': 'white', 'text-align': 'center', 'border': 'solid 1px lightblue', 'background': 'rgba(3, 24, 39, 0.88)',
              })
              top.$('#skyLineWidget button').css({
                'cursor': 'pointer', 'height': '70px', 'font-size': '30px', 'background-color': '#adadad', 'margin': '7px 7px',
              })
              top.$('#skyLineWidget button').hover(
                function (e) {
                  top.$(e.target).css({
                    'background-color': '#949494',
                  })
                },
                function (e) {
                  top.$(e.target).css({
                    'background-color': '#adadad',
                  })
                }
              )

              top.$('#skyLineLocate').click(function () {
                view.goTo(pos)
              })
              top.$('#skyLineCreate').click(function () {
                if (!top.document.getElementById('skyLine')) {
                  let widget = top.document.createElement('div')
                  widget.id = 'skyLine'
                  top.$('.container').append(widget)
                }
                if (!top.document.getElementById('skyLineMask')) {//创建蒙版
                  let widget = top.document.createElement('div')
                  widget.id = 'skyLineMask'
                  top.$('.container').append(widget)
                }
                top.$('#skyLineMask').css({
                  'display': 'block',
                  'width': '100%',
                  'height': '100%',
                  'position': 'absolute',
                  'background': ' rgba(0, 0, 0, 0.8)',
                  'top': '0',
                  'z-index': '5',
                })
                top.$('#skyLine').html(`
                    <p>绘制中</p>
                    <div class="top-close" id="skyLineClose" onclick="this.parentNode.style.display = 'none';console.log(window.skylineRef,'success!')")></div>
                    <img src="" alt="" style="width:2600px;" id="skyLineImg">`)
                top.$('#skyLine').css({
                  'display': 'block',
                  'position': 'absolute',
                  'right': '2874px',
                  'top': '540px',
                  'width': '2600px',
                  'z-index': '997',
                  'font-size': '80px',
                  'color': 'white',
                  'text-align': 'center',
                  'border': 'solid 1px lightblue',
                  'background': 'rgba(3, 24, 39, 0.88)',
                  'height': '731.25px',
                })
                top.$('#skyLine p').css({
                  'display': 'block',
                  'position': 'absolute',
                  'left': '1300px',
                  'top': '310px',
                })
                top.$('#skyLineClose').css({
                  'position': 'absolute',
                  'right': '0',
                  'top': '0',
                  'z-index': '997',
                  'cursor': 'pointer',
                })
                window.skylineRef.start().then(url => {
                  top.$('#skyLineMask').css({ 'display': 'none' })
                  top.$('#skyLine p').css({ 'display': 'none' })
                  if (url) {
                    top.$('#skyLineImg').attr({ src: url })
                  }
                })
              })
              this.skylineFlag = true
            }
            break
          case '日照分析':
            if (window.lightWidget) {
              //判断状态
              window.lightWidget.destroy()
              window.lightWidget = null
              view.environment.lighting.date = new Date('Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)')
              view.environment.lighting.directShadowsEnabled = false
              this.lightFlag = false
            } else {
              if (!top.document.getElementById('daylightWidget')) {
                //判断widget状态，并提升至index
                let widget = top.document.createElement('div')
                widget.id = 'daylightWidget'
                top.$('.container').append(widget)
              }

              window.lightWidget = top.ArcGisUtils.createDaylightWidget(view, 'daylightWidget')
              window.lightWidget.when(() => {//修改面板样式
                window.parent.$(".esri-daylight").css({ "font-size": "30px", "padding": "27px", "width": "500px", "height": "300px" });//修改面板宽高
                window.parent.$("#daylightWidget >calcite-label").css({ "--calcite-ui-text-1": "white", "font-size": "30px" });//修改复选框描述文字颜色
                window.parent.$("h3.esri-widget__heading").css("font-size", "30px");//修改面板标题文字大小
                window.parent.$(".esri-daylight .esri-date-picker__calendar-toggle").css("font-size", "30px");//年月日字体大小
                top.$('#daylightWidget').css({ "left": daylightLeft, 'top': '1300px' })
              })
              view.environment.lighting.date = new Date()
              this.lightFlag = true
            }
            break;
          default:
            break
        }
        if (this.iconIndex == item.id) {
          //判断是否取消
          if (drawType[item.name]) {
            console.log('停止绘制工具')
            return
          } else if (measureType[item.name]) {
            console.log('停止测量工具')
            return
          }

          this.iconIndex = null
          return
        } else {
          if (drawType[item.name]) {
            if (!top.window.drawTool) {
              top.window.drawTool = new top.ArcGisUtils.Draw({ view })
            }
            top.window.drawTool.draw(drawType[item.name]).then((e) => {
              // console.log(e)
            }) //绘制工具
          } else if (item.name == '面积测量') {
            this.areameasureClearClick()
            this.distmeasureClearClick()
            top.$('#areaContainer').remove()

            window.areaMeasurementWidget = top.ArcGisUtils.createAreaMeasurementWidget(view, measureType[item.name])
          } else if (item.name == '距离测量') {
            this.areameasureClearClick()
            this.distmeasureClearClick()
            top.$('#areaContainer').remove()

            window.distanceMeasurementWidget = top.ArcGisUtils.measuringDistance(view, measureType[item.name])
          } 
          else if (item.name == '图层面积测量') {
            this.areameasureClearClick()
            this.distmeasureClearClick()
            top.$('#areaContainer').remove()

            if (!top.window.drawTool) {
              top.window.drawTool = new top.ArcGisUtils.Draw({ view })
            }
            if (!top.document.getElementById('areaContainer')) {
              //判断widget状态，并提升至index
              let widget = top.document.createElement('div')
              widget.id = 'areaContainer'
              top.$('.container').append(widget)
              top.$('#areaContainer').css({
                top:'540px',
                right: layerAreaRight,
                left:'auto',
                display:'none',
              })
              top.$('#areaContainer').html(`
                  <div class="areaPopup" style="background-color:#0f4f6e; color:#fff; font-size:28px;  border-radius:5px;">
                      <div style="width:100%; height:50px; line-height:50px; background-color:#0e74a5; border-radius:15px 15px 0 0;">
                        <span style="padding-left:15px">图层面积测量结果</span>
                        <span style="float:right; width:50px; text-align:center; cursor:pointer;" onclick="this.parentNode.parentNode.parentNode.style.display = 'none'">X</span>
                      </div>
                      <div class="items" style="width:100%; padding-left:40px; font-size:24px;">
                      </div>
                  </div>`)
            }
            async function layerAreaCalc() {
              top.window.layerGraphic = null;
              top.window.layerFeatures = null;
              top.window.layerArea = null;
              top.window.layerGraphic = await top.window.drawTool.draw('polygon');
              console.log(top.window.layerGraphic);

              for (let item of view.layerViews.items) {
                if (('feature' === item.layer.type || 'map-image' === item.layer.type) && ('HYD PY SymDiff' != item.layer.title)) {
                  let e = null;
                  // console.log(layerType[item.layer.type]);
                  switch (item.layer.type) {
                    case 'feature':
                      e = await top.ArcGisUtils.queryTaskByGeometry(
                        item.layer.source.queryTask.url,
                        top.window.layerGraphic.geometry,
                        true
                      );
                      top.window.layerFeatures = e.features
                      top.window.layerArea = await top.ArcGisUtils.geodesic4490Areas(top.window.layerFeatures, 'square-kilometers');
                      top.$('#areaContainer .items').append(`
                        <div style="height: 60px; line-height:60px;"><span>图层名称：</span><span>${item.layer.title}</span></div>
                        <div style="height: 60px; line-height:60px;"><span>图层面积：</span><span>${top.window.layerArea}平方公里</span></div>`)
                      break;
                    case 'map-image':
                      $.ajax({
                        type: "get",
                        data: {},
                        url: `${item.layer.url}?f=json`,
                        async: false,
                        dataType: "json",
                        success: async function (res) {
                          // console.log(res)
                          for (let i = 0; i < res.layers.length; i++) {
                            // console.log(`${item.layer.url}/${i}`);
                            e = await top.ArcGisUtils.queryTaskByGeometry(
                              `${item.layer.url}/${i}`,
                              top.window.layerGraphic.geometry,
                              true
                            );
                            top.window.layerFeatures = e.features
                            // console.log(top.window.layerFeatures);
                            top.window.layerArea = top.window.layerArea + await top.ArcGisUtils.geodesic4490Areas(top.window.layerFeatures, 'square-kilometers');
                            // console.log(top.window.layerArea);
                            top.$('#areaContainer .items').append(`
                              <div style="height: 60px; line-height:60px;"><span>图层名称：</span><span>${item.layer.title}</span></div>
                              <div style="height: 60px; line-height:60px;"><span>图层面积：</span><span>${top.window.layerArea}平方公里</span></div>`)
                          }
                        }
                      });
                      break;
                    default:
                      break;
                  }
                  // console.log(item.layer.title, '圈中面积为：', top.window.layerArea, '平方公里');
                  top.$('#areaContainer').css({
                    display: 'block'
                  })
                }
              }
            }
            layerAreaCalc();
          }

        }
        this.iconIndex = item.id == 31 || item.id == 32 || item.id == 33 || item.id == 34|| item.id == 35|| item.id == 36 ? this.iconIndex : item.id //将空间分析工具排除在只允许单个按钮活动的逻辑之外
      },

      floodSimulate() {
        let view = top.window.mapUtil.mapview
        if (top.window.rainFail) {
          this.iconIndex = null
          top.window.rainFail.stop()
          top.window.rainFail = null
          this.baseSliderValue=0
          this.floodTimeSliderValue=0
        } else {
          this.iconIndex = 4
          top.window.rainFail = new top.ArcGisUtils.RainfallAnalysis({ view });
          top.window.rainFail.addWaterRenderLayer(view).then(() => {
            top.window.rainFail.getWaterBaseSurface().then((value) => {
              this.baseSliderValue = value*1
              this.floodTimeSliderValue=60
              this.floodSliderChange()
            })
          });
        }
      },

      areameasureClearClick() {
        if (window.areaMeasurementWidget) {
            window.areaMeasurementWidget.clear()
        }
      },

      distmeasureClearClick() {
        if (window.distanceMeasurementWidget) {
            window.distanceMeasurementWidget.clear()
        }
      },

      sliderChange() {
        console.log('滑块的值==>', this.sliderValue / 10)
        if(!top.window.mapUtil.mapview)return;
        const { ground } = top.window.mapUtil.mapview.map
        ground.opacity = this.sliderValue / 10
      },
      
      baseSliderChange() {
        console.log('滑块的值==>', this.baseSliderValue)
        if (top.window.rainFail) {
          top.window.rainFail.setWaterElevationOffset(this.baseSliderValue*1)
          return
        }
        this.baseSliderValue = 0
      },

      floodSliderChange() {
        console.log('滑块的值==>', this.floodTimeSliderValue)
        if (top.window.rainFail) {
          top.window.rainFail.setWaterElevationOffset(this.baseSliderValue*1)
          top.window.rainFail.setRainfall(this.floodTimeSliderValue*1)
          top.window.rainFail.start()
          return
        }
        this.floodTimeSliderValue = 0
      },
      cleanall() {
        let view = top.window.mapUtil.mapview
        this.iconIndex = null
        this.modelFlag = false
        this.sliceFlag = false
        this.sightFlag = false
        this.lightFlag = false
        this.domSightFlag=false
        this.skylineFlag=false
        this.sliderValue=10
        this.baseSliderValue=0
        this.floodTimeSliderValue=0


        //将绘图工具置空
        top.window.drawTool?.destroy()
        top.window.drawTool = null

        //关闭测量工具
        this.areameasureClearClick()
        this.distmeasureClearClick()
        top.$('#areaContainer').remove()//销毁图层面积测量标签

        //清除精模
        top.ArcGisUtils.removePureModalLayer(view)
        //关闭剖切工具
        window.sliceWidget?.destroy()
        window.sliceWidget = null
        //关闭通视工具
        window.lineOfSightWidget?.destroy()
        window.lineOfSightWidget = null
        //关闭穹顶工具
        view.container.style.cursor = "default";
        window.domAnalysisRef?.removeAll()
        window.domAnalysisRef = null
        //关闭城市天际线
        window.skylineRef?.destroy();
        window.skylineRef = null;
        top.$('#skyLine')?.css({'display':'none'})
        top.$('#skyLineWidget')?.css({'display':'none'})
        top.$('#skyLineMask')?.css({'display':'none'})

        //关闭日照分析
        window.lightWidget?.destroy()
        window.lightWidget = null
        view.environment.lighting.date = new Date('Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)')
        view.environment.lighting.directShadowsEnabled = false

        //还原地表透明度
        const { ground } = view.map;
        ground.opacity = 1;

        //移除淹没效果
        top.window.rainFail?.stop();
        top.window.rainFail=null;

      },
      beforeClose() {
        window.addEventListener('beforeunload', () => {
          this.cleanall()
        })
      }

    },
  })
  
    top.emiter.on('rightIframeShow', () => {
      try {
        top.$('#areaContainer').css("right",'2874px')
        top.$('#daylightWidget').css({ "left": '4890px'})
        top.$('#lineOfSight').css("left",'4890px')
        top.$('#sliceContainer').css("left",'4890px')
        top.$('#skyLineWidget').css("left",'4890px')
      } catch (error) { }
    })
    top.emiter.on('rightIframeHide', () => {
      try {
        top.$('#areaContainer').css("right",'834px')
        top.$('#daylightWidget').css({ "left": '6930px'})
        top.$('#lineOfSight').css("left",'6930px')
        top.$('#sliceContainer').css("left",'6930px')
        top.$('#skyLineWidget').css("left",'6930px')
      } catch (error) { }
    })

</script>
</html>
