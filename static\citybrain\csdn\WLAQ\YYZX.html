<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>运营中心</title>
  <script src="../Vue/vue.js"></script>
  <script src="../../csdn/echarts/echarts.min.js"></script>
  <script src="../js/datav.min.vue.js"></script>
  <script src="../jquery/jquery-3.6.1.min.js"></script>
  <script src="../axios/axios.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <!--<script src="/static/js/jslib/echarts-wordcloud.min.js"></script>-->
  <!--<link rel="stylesheet" href="/static/css/sigma.css" />-->
  <!-- <script src="/static/js/jslib/umap2d.min.js"></script> -->
  <!--<script src="https://cdn.bootcdn.net/ajax/libs/mapbox-gl/2.7.0/mapbox-gl.min.js"></script>-->
  <!--<link rel="stylesheet" href="/static/css/animate.css" />-->
  <!--<script src="/static/citybrain/hjbh/js/echarts.js"></script>-->
</head>
<style>
  #app {
    width: 1920px;
    height: 1080px;
    background: url("../img/yyzxImg/background.png") no-repeat;
    background-size: cover;
    transform: scale(2);
    margin-left: 960px;
    margin-top: 544px;
  }

  /*顶部标题*/

  .Title {
    height: 134.5px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: url("../img/gzzxImg/TopTitle-background.png") no-repeat;
    background-size: cover;
  }

  .Title-leftBtn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 133px;
    height: 26.5px;
    margin: -50px 400px 0 0;
    cursor: pointer;
  }

  .Title-center {
    font-family: SourceHanSansCN-Bold;
    font-size: 40px;
    font-weight: bold;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    margin-top: -70px;
  }

  .Title-rightBtn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 133px;
    height: 26.5px;
    margin: -50px 0 0 400px;
    cursor: pointer;
  }

  .icon {
    width: 26.5px;
    height: 26.5px;
    margin-top: 5px;
  }

  .Btn-Text {
    font-family: SourceHanSansCN-Medium;
    font-size: 25px;
    font-weight: 600;
    font-stretch: normal;
    letter-spacing: 0px;
    background: linear-gradient(to bottom, #ebf2ff, #ffffff, #c3d8ee, #94a9db);
    -webkit-background-clip: text;
    color: transparent;
    white-space: nowrap;
  }

  .box-Content {
    width: 500px;
    height: 373px;
    padding: 15px 10px 10px 10px;
    box-sizing: border-box;
    overflow: hidden;
  }

  .table-title {
    width: 100%;
    height: 25px;
    background-color: #00396f;
    color: #77b3f1;
    font-size: 15px;
    font-weight: 400;
    font-family: Source Han Sans CN;
    display: flex;
    align-items: center;
  }

  .table-content {
    width: 100%;
    padding-bottom: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .table-content::-webkit-scrollbar {
    width: 0 !important
  }

  .table-item {
    margin-top: 3px;
    width: 478px;
    height: 25px;
    background: rgba(255, 255, 255, 0.0128);
    border: 1px solid rgb(255, 255, 255, 0.1);
    /* opacity: 0.16; */
    display: flex;
    align-items: center;
  }

  .table-item2 {
    margin-top: 4px;
    width: 478px;
    height: 25px;
    background: rgba(255, 255, 255, 0.08);
    /* opacity: 0.16; */
    display: flex;
    align-items: center
  }

  .table-item div,
  .table-item2 div {
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #D6E7F9;
    line-height: 17px;
  }

  .centerItem {
    display: flex;
    justify-content: center;
  }

  .right2-content {
    width: 500px;
    height: 178px;
    padding: 18px 77px 19px 86px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
  }

  .leaderbox {
    width: 138px;
    height: 142px;
    background-image: url("../img/yyzxImg/代班领导.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .staffbox {
    width: 138px;
    height: 142px;
    background-image: url("../img/yyzxImg/值班人员.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .right2-content .detail {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 70px;
  }

  .right2-content .detail .title,
  .right2-content .detail .name {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-left: 4px;
  }

  .right2-content .detail .phonenumber {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
  }

  /*底部内容*/

  .container {
    margin-top: -35px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .container-left {
    width: 500px;
    height: 970px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .container-center {
    width: 880px;
    height: 970px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .container-right {
    width: 500px;
    height: 970px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .left1 {
    width: 500px;
    height: 265px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
  }

  .left2 {
    width: 500px;
    height: 333.5px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 10px;
  }

  .left3 {
    width: 500px;
    height: 351.5px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 10px;
  }

  .center-top {
    width: 877px;
    flex: 1;
  }

  .center-middle {
    width: 877px;
    height: 88.5px;
    background: url("../img/yyzxImg/center-middle.png") no-repeat;
    background-size: cover;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .center-middle-item {
    width: 219.25px;
    height: 88.5px;
  }

  .center-bottom {
    width: 877px;
    height: 275px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
  }

  .right1 {
    width: 500px;
    height: 408px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
  }

  .right2 {
    width: 500px;
    height: 213px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 10px;
  }

  .right3 {
    width: 500px;
    height: 329px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 10px;
  }

  .box-Title {
    width: 390px;
    height: 35px;
    background: url("../img/gzzxImg/boxTitle.png") no-repeat;
    background-size: cover;
    overflow: hidden;
  }

  .box-Title-Text {
    font-family: SourceHanSansCN-Bold;
    font-size: 20px;
    font-weight: 600;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    margin: 4px 0 0 32.5px;
  }

  .gold {
    background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
    text-align: left;
  }

  .normal {
    color: #D6E7F9;
  }

  .blue {
    background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .red {
    background: linear-gradient(to bottom, #ffcdcd, #ffffff, #ff4949, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .green {
    background: linear-gradient(to bottom, #caffff, #ffffff, #22e8e8, #91f4f4, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .unit {
    font-size: 16px;
  }

  .MiddleName {
    font-size: 19px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #FFFFFF;
    text-align: center;
    margin-top: 15px;
  }

  .MiddleValue {
    font-size: 30px;
    font-family: Bebas Neue;
    font-weight: 600;
    text-align: center;
  }

  .selectBox {
    width: 363px;
    height: 27px;
    border-radius: 5px;
    border: 1px solid #5DBCFF;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin: 8px 10px 0 0;
  }

  .selectBox-item {
    width: 121px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #AFBDCF;
    line-height: 27px;
    text-align: center;
    cursor: pointer;
  }

  .itemRight {
    border-right: 1px solid #5DBCFF;
  }

  .activeItem {
    background: rgba(93, 188, 255, 0.5);
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #FFFFFF;
  }

  .centerBottomTable {
    width: 857.5px;
    height: 199px;
    margin: 20px 0 0 10px;
  }

  .centerBottomTable-Title {
    width: 100%;
    height: 25px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background: #00396F;
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #77B3F1;
  }

  .centerBottomTableTitle-item {
    text-align: left;
    margin-left: 20px;
  }

  .centerBottomTable-container {
    height: 181px;
    overflow-y: scroll;
  }

  ::-webkit-scrollbar {
    width: 0;
  }

  .centerBottomTable-container-line {
    width: 100%;
    height: 27px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background: rgba(255, 255, 255, 0.06);
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    margin-top: 3px;
  }

  .centerBottomTable-container-line-item {
    line-height: 25px;
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
  }

  .typeRed {
    background: linear-gradient(to bottom, #FF7777, #FF4949);
  }

  .typeYellow {
    background: linear-gradient(to bottom, #FFB077, #FD852E);
  }

  .typePurple {
    background: linear-gradient(to bottom, #D882FF, #B76FD8);
  }

  .typeBlue {
    background: #03A6DB;
  }

  .typeBox {
    width: 66px;
    height: 17.5px;
    line-height: 17.5px;
    text-align: center;
    font-size: 10px;
    overflow: hidden;
    border-radius: 3px;
  }

  .indexs {
    width: 100%;
    height: 470.5px;
  }

  .arrow {
    width: 152px;
    height: 72.5px;
    background: url("../img/yyzxImg/arrow.png") no-repeat;
    background-size: cover;
    margin: 44px 0 0 363px;
    animation: move 4s linear infinite;
  }

  @keyframes move {
    0% {
      transform: translate(0px, 0px);
    }

    50% {
      transform: translate(0px, -10px);
    }

    100% {
      transform: translate(0px, 0px);
    }
  }

  .centerTop-img {
    width: 147px;
    height: 146.5px;
    position: relative;
    cursor: pointer;
    animation: move 2s linear infinite;
  }

  .leftbox1 {
    display: flex;
  }

  .lb-left {
    margin-top: 23px;
  }

  .lb-leftitem {
    display: flex;
    /* align-items: center; */
    height: 52px;
    margin-bottom: 11px;
  }

  .lb-leftitem>img {
    width: 52px;
    height: 51px;
    margin-left: 32px;

  }

  .lb-leftitem>div:nth-child(2) {
    font-size: 18px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 52px;
    margin-left: 11px;
    margin-right: 22px;

  }

  .lb-leftitem>div:nth-child(3) {
    font-size: 24px;
    font-family: Bebas Neue;
    font-weight: 600;
    color: #FFFFFF;
    line-height: 52px;
    margin-left: 11px;
    margin-right: 22px;

  }

  .lb-right {
    box-sizing: border-box;
  }

  .index-item-content {
    position: relative;
    bottom: 118px;
    left: 14px;

  }

  .index-item-content>div:nth-child(1) {
    font-size: 33px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    font-style: italic;
    text-align: center;
  }

  .index-item-content>div:nth-child(2) {
    font-size: 17px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #FFFFFF;
    text-align: center;
  }

  .left2con {
    display: flex;
    box-sizing: border-box;
    padding-left: 15px;
    margin-top: 20px;
  }

  .left2bg {
    width: 158px;
    height: 252px;
    background-image: url("../img/yyzxImg/left2bg.png");
    background-size: cover;
    /* margin: 0 30px; */
  }

  .lbg1 {
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #FFFFFF;
    margin-left: 20px;
    margin-top: -10px;
    /* line-height: 50px; */
  }

  .lbg2 {
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    text-align: center;
    margin-top: 40px;
  }

  .lbg3 {
    font-size: 15px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
    text-align: center;
    margin-top: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .lbg3>div:first-child {
    font-size: 30px;
  }

  .lbg3>div:nth-child(2) {
    font-size: 18px;
    margin-top: 6px;
  }



  .left3-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 19px;
  }

  .left3-tab>div {
    width: 91px;
    height: 27px;
    background: rgba(93, 188, 255, 0);
    border: 1px solid #4FC3FF;
    line-height: 27px;
    text-align: center;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #FFFFFF;
    font-size: 14px;
    cursor: pointer;
  }

  .div-active {
    background: #4FC3FF !important;
  }

  /* 蓝白色渐变 */
  .lanbailine {
    background: linear-gradient(180deg, #CAFFFF 0, #CAFFFF 25%, #FFFFFF 50%, #00C0FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 金色渐变 */
  .godLine {
    background: linear-gradient(180deg, #ffeccb 0, #ffffff 25%, #ffc460 50%, #ffe2b0 75%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 青色渐变 */
  .qingLine {
    background: linear-gradient(180deg, #fff 25%, #A9DB52 50%, #F4F1FF 75%, #F0FFD7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
</style>

<body>
  <div id="app">
    <div class="Title">
      <div class="Title-leftBtn" @click="pageJump('jcfh')">
        <img src="../img/gzzxImg/leftBtn-icon.png" alt="" class="icon">
        <div class="Btn-Text">监测防护中心</div>
      </div>
      <div class="Title-center">安全运营中心</div>
      <div class="Title-rightBtn" @click="pageJump('aqgz')">
        <img src="../img/gzzxImg/ganzhi.png" alt="" class="icon">
        <div class="Btn-Text">安全感知中心</div>
      </div>
    </div>
    <div class="container">
      <div class="container-left">
        <div class="left1">
          <div class="box-Title">
            <div class="box-Title-Text">平台运行监控</div>
          </div>
          <div class="leftbox1">
            <div class="lb-left">
              <div class="lb-leftitem">
                <img src="../img/yyzxImg/pts.png" alt="">
                <div>平台数</div>
                <div class="lanbailine">{{pingtai}}</div>
              </div>
              <div class="lb-leftitem">
                <img src="../img/yyzxImg/zxs.png" alt="">
                <div>在线数</div>
                <div class="lanbailine">{{zaixian}}</div>
              </div>
              <div class="lb-leftitem">
                <img src="../img/yyzxImg/lxs.png" alt="">
                <div>离线数</div>
                <div class="lanbailine">{{lixian}}</div>
              </div>
            </div>
            <div class="lb-right">
              <div class="index-item">
                <dv-decoration-9 style="width:164px;height:161px;margin-left: 30px;margin-top:30px;">
                </dv-decoration-9>
                <div class="index-item-content">
                  <div class="godLine">{{zaixianlv + '%'}}</div>
                  <div>在线率</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="left2">
          <div class="box-Title">
            <div class="box-Title-Text">安全业务调度</div>
          </div>
          <div class="left2con">
            <div class="left2bg" v-for="(item,i) in left2" :key="i">
              <div class="lbg1">{{item.name}}</div>
              <div class="lbg2">已发起</div>
              <div class="lbg3">
                <div class="lanbailine">{{item.value1}}</div>
                <div class="lanbailine">项</div>
              </div>
              <div class="lbg2">未处置</div>
              <div class="lbg3">
                <div class="qingLine">{{item.value2}}</div>
                <div class="qingLine">项</div>
              </div>
            </div>
          </div>
        </div>
        <div class="left3">
          <div class="box-Title">
            <div class="box-Title-Text">协同处置</div>
          </div>
          <div class="left3-tab">
            <div :class="{'div-active':leftdata3num==i}" v-for="(item,i) in leftdata3" :key="i" @click="left3Click(i)">
              {{item}}</div>
          </div>
          <div id="leftEcharts2"
            style="width:940px;height:450px;transform:scale(0.5);margin-left:-230px;margin-top:-95px"></div>
        </div>
      </div>
      <div class="container-center">
        <div class="center-top">
          <div class="indexs">
            <img :src="shijian" alt="" class="centerTop-img" style="left: 150px;top: 50px;" @click="centerTopClick(0)">
            <img :src="renyuan" alt="" class="centerTop-img" style="left:200px;top: 170px;" @click="centerTopClick(1)">
            <img :src="renwu" alt="" class="centerTop-img" style="left: 245px;top: 50px;" @click="centerTopClick(2)">
          </div>
          <div class="arrow"></div>
        </div>
        <div class="center-middle">
          <div class="center-middle-item" v-for="(item,i) in centerMiddle" :key="i">
            <div class="MiddleName">{{item.name}}</div>
            <div class="MiddleValue gold">
              {{item.value}}
              <span class="unit" v-if="item.name.indexOf('单位') != -1">家</span>
              <span class="unit" v-else>名</span>
            </div>
          </div>
        </div>
        <div class="center-bottom">
          <div style="display: flex;justify-content: space-between;align-items: center">
            <div class="box-Title">
              <div class="box-Title-Text">实时通报</div>
            </div>
            <div class="selectBox">
              <div class="selectBox-item" v-for="(item,i) in selectBox" :key="i"
                :class="{itemRight:i < 2,activeItem:centerBottomChoose == i}" @click="changeBottom(i)">
                {{item.name + ": " + item.value}}
              </div>
            </div>
          </div>
          <div class="centerBottomTable">
            <div class="centerBottomTable-Title">
              <div class="centerBottomTableTitle-item" style="flex: 1;text-align: center">优先级</div>
              <div class="centerBottomTableTitle-item" style="flex: 3;">通报标题</div>
              <div class="centerBottomTableTitle-item" style="flex: 3;">通报时间</div>
              <div class="centerBottomTableTitle-item" style="flex: 2;">所属单位</div>
              <div class="centerBottomTableTitle-item" style="flex: 2;text-align: center">当前流程</div>
            </div>
            <div class="centerBottomTable-container">
              <div class="centerBottomTable-container-line" v-for="(item,i) in centerBottomTable" :key="i">
                <div class="centerBottomTable-container-line-item centerBottomTableTitle-item normal" style="flex: 1;">
                  <div
                    :class="{typeRed:item.type == '特别紧急',typeYellow:item.type == '紧急',typePurple:item.type == '重要',typeBlue:item.type == '一般'}"
                    class="typeBox">{{item.type}}</div>
                </div>
                <div class="centerBottomTable-container-line-item centerBottomTableTitle-item normal" style="flex: 3;"
                  :title="item.title">{{item.title}}</div>
                <div class="centerBottomTable-container-line-item centerBottomTableTitle-item normal" style="flex: 3;">
                  {{item.time}}</div>
                <div class="centerBottomTable-container-line-item centerBottomTableTitle-item normal" style="flex: 2;">
                  {{item.company}}</div>
                <div class="centerBottomTable-container-line-item centerBottomTableTitle-item green"
                  style="flex: 2;text-align: center">{{item.status}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="container-right">
        <div class="right1">
          <div class="box-Title">
            <div class="box-Title-Text">组织架构</div>
          </div>
          <div class="box-Content">
            <div class="table-title">
              <div style="flex: 2;margin-left: 20px;">单位</div>
              <div style="flex: 1.5;">首席网络安全官</div>
              <div style="flex: 1;">网络联络人</div>
            </div>
            <div class="table-content" style="height: 338px;">
              <div class="table-item" v-for="(item,i) in organizationData" :key="i">
                <div style="flex:2;margin-left: 20px;">{{item.dwmc}}</div>
                <div style="flex:1.5">{{item.sxwlaqg}}</div>
                <div style="flex:1;">{{item.wllxr}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="right2">
          <div class="box-Title">
            <div class="box-Title-Text">每日值班</div>
          </div>
          <div class="right2-content">
            <div class="leaderbox">
              <div class="detail">
                <div class="title gold">&nbsp代班领导</div>
                <div class="name gold">&nbsp{{leadername}}</div>
                <div class="phonenumber">{{leadernumber}}</div>
              </div>
            </div>
            <div class="staffbox">
              <div class="detail">
                <div class="title gold">&nbsp值班人员</div>
                <div class="name gold">&nbsp{{staffname}}</div>
                <div class="phonenumber">{{staffnumber}}</div>
              </div>
            </div>
            </biv>
          </div>
          <div class="right3">
            <div class="box-Title">
              <div class="box-Title-Text">考核评价</div>
            </div>
            <div class="box-Content">
              <div class="table-title">
                <div style="flex: 1.8;margin-left: 20px;">单位</div>
                <div style="flex: 1;" class="centerItem">安全事件</div>
                <div style="flex: 1;" class="centerItem">安全隐患</div>
                <div style="flex: 1;" class="centerItem">通报处置</div>
                <div style="flex: 1;margin-right: 15px;" class="centerItem">预期未处置</div>
              </div>
              <div class="table-content" style="height: 235px;">
                <div class="table-item2" v-for="(item,i) in evaluationData" :key="i">
                  <div style="flex:2;margin-left: 20px;">{{item.dwmc}}</div>
                  <div style="flex: 1; display: flex; justify-content: center;" class="centerItem">
                    {{item.aqsj}}</div>
                  <div style="flex: 1;" class="centerItem">{{item.aqyh}}</div>
                  <div style="flex: 1;" class="centerItem">{{item.tbcz}}</div>
                  <div style="flex: 1;margin-right: 15px;" class="centerItem">{{item.yqwcz}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    var vm = new Vue({
      el: '#app',
      data: {
        pingtai: 10,
        zaixian: 9,
        lixian: "",
        zaixianlv: "",
        left2: [{ name: "通报下发", value1: "", value2: "" }, { name: "信息下发", value1: "", value2: "" }, { name: "紧急响应", value1: "", value2: "" }],
        //中间部分
        topimgChoose: 0,
        shijian: "../img/yyzxImg/shijian.png",
        renyuan: "../img/yyzxImg/renyuan.png",
        renwu: "../img/yyzxImg/renwu.png",
        centerBottomChoose: 0,
        centerMiddle: [],
        selectBox: [{ name: "通报总数", value: "" }, { name: "未完成数", value: "" }, { name: "预期处置", value: "" }],
        centerBottomTable: [
          { type: "特别紧急", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "签收" },
          { type: "紧急", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "发起" },
          { type: "重要", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "审核" },
          { type: "一般", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "完成" },
          { type: "一般", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "完成" },
          { type: "一般", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "完成" },
          { type: "一般", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "完成" },
          { type: "一般", title: "XXXX标题", time: "2022-3-12 12:23:41", company: "xxxxxx单位", status: "完成" },
        ],
        //右侧部分
        leadername: "",
        leadernumber: "",
        staffname: "",
        staffnumber: "",
        organizationData: [
        ],
        evaluationData: [
        ],
        leftdata3: ["省级", '市级', '地市'],
        leftdata3num: 0,
      },
      watch: {
        topimgChoose(i) {
          switch (i) {
            case 0:
              this.shijian = "../img/yyzxImg/shijianactive.png";
              this.renyuan = "../img/yyzxImg/renyuan.png";
              this.renwu = "../img/yyzxImg/renwu.png";
              break;
            case 1:
              this.shijian = "../img/yyzxImg/shijian.png";
              this.renyuan = "../img/yyzxImg/renyuanactive.png";
              this.renwu = "../img/yyzxImg/renwu.png";
              break;
            case 2:
              this.shijian = "../img/yyzxImg/shijian.png";
              this.renyuan = "../img/yyzxImg/renyuan.png";
              this.renwu = "../img/yyzxImg/renwuactive.png";
              break;
          }
        }
      },
      computed: {},
      mounted() {
        this.getApiData()
        this.init();
        this.centerTopClick(1); //默认选中人员

        //中下四个指标
        $api("wlaq_aqyy_D01",{code:2}).then(res => {
          let result = res.filter(item => item.code == '2')
          this.centerMiddle = result
        })

        $api("wlaq_aqyy_R11").then(res => {
          this.organizationData = res;
        })

        $api("wlaq_aqyy_R21").then(res => {
          this.leadername = res[0].num
          this.leadernumber = res[1].num
          this.staffname = res[2].num
          this.staffnumber = res[3].num
        })

        $api("wlaq_aqyy_R31").then(res => {
          this.evaluationData = res;
        })


        //   leadername: "XXX",
        // leadernumber: "1563338898",
        // staffname: "XXX",
        // staffnumber: "1563338888",
      },
      methods: {

        getApiData() {
          //中下tab
          $api("wlaq_aqyy_C31").then(res => {
            console.log(res);
            this.selectBox[0].value = res[0].tbzs
            this.selectBox[1].value = res[0].wwcs
            this.selectBox[2].value = res[0].yqczs
          })
          //中下tab
          $api("wlaq_aqyy_D01").then(res => {
            this.pingtai = res[0].value
            this.zaixian = res[1].value
            this.lixian = res[0].value - res[1].value
            this.zaixianlv = ((res[1].value * 100)/res[0].value).toFixed(0)
          })
          //安全业务调度
          $api("wlaq_aqyy_L21").then(res => {
            this.left2[0].value1 = res[0].num
            this.left2[0].value2 = res[1].num
            this.left2[1].value1 = res[2].num
            this.left2[1].value2 = res[3].num
            this.left2[2].value1 = res[4].num
            this.left2[2].value2 = res[5].num
          })
        },
        init() {
          this.left3Click(this.leftdata3num)
        },
        pageJump(value) {
          if (value == 'jcfh') {
            location.href = '/static/citybrain/csdn/WLAQ/jcfhzx.html'
          } else {
            location.href = '/static/citybrain/csdn/WLAQ/GZZX.html'
          }
        },
        centerTopClick(i) {
          this.topimgChoose = i
        },
        changeBottom(i) {
          this.centerBottomChoose = i;
          //中下table
          $api("wlaq_aqyy_C32").then(res => {
            if (i == 0) {
              this.centerBottomTable = res.map(item => {
                return { type: item.yxj, title: item.tbbt, time: item.tbsj, company: item.ssdw, status: item.dqlc }
              })
            }
            if (i == 1) {
              let result = res.filter(item => item.dqlc != "完成")
              this.centerBottomTable = result.map(item => {
                return { type: item.yxj, title: item.tbbt, time: item.tbsj, company: item.ssdw, status: item.dqlc }
              })
            }
            if (i == 2) {
              let result = res.filter(item => item.dqlc != "完成" && item.dqlc != "签收")
              this.centerBottomTable = result.map(item => {
                return { type: item.yxj, title: item.tbbt, time: item.tbsj, company: item.ssdw, status: item.dqlc }
              })
            }
          })
        },
        showMyEcharts1(res) {
          let myChart = echarts.init(document.getElementById("leftEcharts2"));
          let option = {
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '28',
              },
            },
            legend: {
              orient: "horizontal",
              // icon: "circle",
              itemGap: 45,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
            grid: {
              left: "5%",
              right: "6%",
              top: "22%",
              bottom: "1%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
                data: res.map((m, n) => {
                  return m.name
                }),
                axisLine: {
                  lineStyle: {
                    color: "rgb(119,179,241,.4)", // 颜色
                    width: 1, // 粗细
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#D6E7F9",
                    fontSize: 28,
                  },
                },
              },
            ],
            yAxis: [
              {
                name: "单位：数据",
                type: "value",
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: "rgb(119,179,241,.4)",
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                  },
                },
              },
              {
                name: "",
                type: "value",
                max: 100,
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: "rgb(119,179,241,.4)",
                  },
                },
                axisLabel: {
                  formatter: '{value}%',
                  textStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                  },
                },
              },
            ],
            series: [
              {
                name: "通报总数",
                type: "bar",
                barWidth: "10%",
                yAxisIndex: 0,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#00FFFF",
                      },
                      {
                        offset: 1,
                        color: "rgba(0,192,255,0)",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: res.map(item => item.value1),
              }, {
                name: "已处理数",
                type: "bar",
                barWidth: "10%",
                yAxisIndex: 0,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#E8B304",
                      },
                      {
                        offset: 1,
                        color: "#FFF2DC",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: res.map(item => item.value2),
              }
            ],
          };
          myChart.setOption(option)
          myChart.getZr().on('mousemove', param => {
            myChart.getZr().setCursorStyle('default')
          })
        },
        left3Click(i) {
          this.leftdata3num = i
          //省
          if (i == 0) {
            $api("wlaq_aqyy_L31", { code: "330000" }).then(res => {
              this.showMyEcharts1(res)
            })
          }
          //市
          if (i == 1) {
            $api("wlaq_aqyy_L31", { code: "330700" }).then(res => {
              this.showMyEcharts1(res)
            })
          }
          //地
          if (i == 2) {
            $api("wlaq_aqyy_L31", { code: "330700" }).then(res => {
              this.showMyEcharts1(res)
            })
          }
        }
      },
    })
  </script>


</body>

</html>
