<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>显示搜结果详情弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/scjg/css/hjbh-right/common.css"
    />
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/turf.js"></script>
    <script src="/static/citybrain/csdn/js/drawCircle.js"></script>

    <style>
      * {
        margin: 0;
        padding: 0;
      }
      ::-webkit-scrollbar {
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      ::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }
      .sjzx_middle_left {
        /* width: 450px; */
        width: 650px;
        height: 930px;
        /* min-height: 600px; */
        /* background: linear-gradient(179deg, #0e1a40 0%, #064069 100%); */
        background: url("/static/citybrain/csdn/img/showDetails/bg_midLeft.png")
          no-repeat;
        background-size: 100% 100%;
      }
      .sjzx_middle_title {
        font-size: 36px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #d6e7f9;
        background: linear-gradient(
          180deg,
          #aed6ff 0%,
          #74b8ff 47.4853515625%,
          #9ccfff 50%,
          #ddeeff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
      }
      .sjzx_middle_title p {
        margin-top: 10px;
        height: 82px;
        line-height: 83px;
        white-space: nowrap;
      }
      .sjzx_middle_title p:before {
        content: "";
        height: 1px;
        top: -3%;
        position: relative;
        width: 18%;
        height: 1px;
        border-bottom: 3px solid #74b8ff;
        display: inline-block;
        margin-right: 5px;
        margin-bottom: 12px;
      }

      .sjzx_middle_title p:after {
        content: "";
        top: -3%;
        position: relative;
        width: 18%;
        height: 1px;
        border-bottom: 3px solid #74b8ff;
        display: inline-block;
        margin-left: 5px;
        margin-bottom: 12px;
      }
      .sjzx_middle_title .before {
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #74b8ff;
        /* transform: rotateZ(90deg); */
        border-radius: 5px;
        margin-bottom: 10px;
      }
      .sjzx_middle_title .after {
        /* display: inline-block; */
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #74b8ff;
        /* transform: rotateZ(90deg); */
        border-radius: 5px;
        margin-bottom: 10px;
      }
      .sjzx_middle_title p .tab {
        cursor: pointer;
      }
      .el-checkbox__input {
        float: right;
        margin-right: 20px;
      }
      .el-tree-node__label {
        font-size: 30px;
        font-family: PangMenZhengDao;
        font-weight: bold;

        color: #c0d6ed;
        line-height: 58px;
      }
      .el-tree-node__content {
        height: 50px !important;
        margin-bottom: 10px;
      }
      .is-focusable {
        background-color: unset;
      }

      .el-tree-node.is-current > .el-tree-node__content,
      .el-tree-node__content:hover {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      }
      .el-checkbox-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .el-checkbox {
        display: block;
        border-radius: 15px;
        margin-bottom: 2px;
        margin-right: 0;
      }
      .el-checkbox-group .el-checkbox:hover {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      }
      .el-checkbox-group .is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      }

      .el-checkbox__label {
        font-size: 32px;
        font-family: PangMenZhengDao;
        /* font-style: italic; */
        color: #c0d6ed;
        line-height: 50px;
        padding-left: 0;
      }

      .el-checkbox__inner {
        width: 28px;
        height: 28px;
        margin-top: 14px;
        margin-left: 15px;
        background-color: #344d67;
      }
      .auth-tree .el-checkbox__inner {
        width: 33px;
        height: 33px;
        margin-top: 21px;
        background-color: #344d67;
      }
      .sjzx_middle_left_container {
        /* height: 480px; */
        height: 100%;
        padding: 30px;
        box-sizing: border-box;
      }
      .checkbox-box-img {
        width: 35px;
        height: 35px;
        position: relative;
        top: 5px;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: #252316;
        border-color: #ffc561;
      }
      .el-checkbox__inner::after {
        width: 7px;
        height: 18px;
        left: 10px;
        color: #ffc561 !important;
      }
      .el-tree {
        background-color: unset;
      }
      .auth-tree > .el-tree-node > .el-tree-node__content .el-checkbox {
        display: none;
      }

      .el-icon-caret-left:before {
        font-size: 20px;
      }
      .el-tree-node__expand-icon {
        position: absolute;
        right: 0;
      }
      .el-tree-node__label {
        padding-left: 15px;
      }
      .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(-90deg);
        transform: rotate(-90deg);
      }
      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #c0d6ed;
      }
      .el-tree-node__content > label.el-checkbox {
        position: absolute;
        right: 0;
      }
      .tab-top {
        width: 100%;
        height: 370px;
        overflow: hidden;
        overflow-y: auto;
      }
      .con-bottom-ul {
        width: 100%;
        height: 450px;
        margin-top: 20px;
        overflow: hidden;
        overflow-y: auto;
      }
      .con-bottom-ul::-webkit-scrollbar {
        width: 0;
        height: 0;
      }
      .con-bottom-ul > li {
        height: 93px;
        margin-bottom: 20px;
        background-color: #00396f;
      }
      .li-top {
        height: 46px;
        line-height: 46px;
        font-size: 28px !important;
      }
      .flex-between {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
      }
      .flex-align-center {
        display: flex;
        align-items: center;
      }
      .icon-jydw {
        width: 35px;
        height: 35px;
        background-size: 100% 100%;
        margin-right: 5px;
        margin-left: 10px;
      }
      .icon-jydw > img {
        width: 100%;
        height: 100%;
      }
      .li-width {
        width: 487px;
      }
      .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .color3 {
        color: #fdfeff;
      }
      .fs-18 {
        font-size: 18px;
      }
      .fs-28 {
        font-size: 28px;
      }
      .icon_lxdh {
        background: url("/static/citybrain/csdn/img/showDetails/icon_tel.png")
          no-repeat;
        background-size: 100% 100%;
        width: 18px;
        height: 18px;
        margin-left: 8px;
      }
      .flex-end {
        display: flex;
        justify-content: end;
        align-items: center;
      }
      .icon-jl {
        background: url("/static/citybrain/csdn/img/showDetails/icon_jl.png")
          no-repeat;
        width: 22px;
        height: 28px;
        margin-right: 10px;
      }

      .el_page_sjzx .el-pagination button {
        height: 35px;
        width: 40px;
        background: transparent !important;
      }
      .el_page_sjzx .el-pagination button .el-icon {
        font-size: 22px !important;
        color: #ccc;
        font-weight: 400;
      }
      .el_page_sjzx .el-pagination button .el-icon:hover {
        color: #20aeff;
      }
      .el_page_sjzx .el-pagination__jump {
        font-size: 38px !important;
        font-weight: 400;
        color: #fffe9f;
        line-height: 45px;
      }
      .el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor {
        width: 140px;
      }
      .el_page_sjzx .el-pagination__jump.el-input.el-pagination__editor input {
        overflow: hidden;
        width: 96px;
        overflow: auto;
        height: 56px !important;
        color: #fffe9f;
        font-size: 30px;
        border: 2px solid #a7a889;
        border-radius: 4px;
        background: transparent !important;
      }
      .el_page_sjzx ul {
        margin-top: 2px !important;
      }
      .el_page_sjzx ul li {
        border: 2px solid transparent;
        margin-left: 10px !important;
        height: 30px;
        padding: 0 5px !important;
        font-size: 18px !important;
        color: #c0d6ed !important;
        background: transparent !important;
        font-weight: 500;
        line-height: 26px !important;
        border-radius: 4px;
      }
      .el_page_sjzx li.active {
        margin: 0;
        padding: 0;
        color: #20aeff !important;
        border: 2px solid #035b86;
      }
    </style>
  </head>

  <body>
    <div id="sjzx-middle">
      <!-- 网格图层区县 -->
      <div class="sjzx_middle_left">
        <!-- <div class="sjzx_middle_title">
          <p>
            <span class="before"></span>
            <span class="tab" class="s-c-blue-gradient">全科网格</span>
            <span class="after"></span>
          </p>
        </div> -->
        <div class="sjzx_middle_left_container">
          <div class="tab-top">
            <el-checkbox-group v-model="tabValue">
              <el-checkbox
                v-for="(item,index) in tabData"
                :label="item.type"
                :key="index"
                class="checkbox-box"
                @change="changeData"
              >
                <img
                  class="checkbox-box-img"
                  :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${item.type}.png`"
                  alt=""
                />
                <span>{{item.name}}</span>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="con-bottom">
            <ul class="con-bottom-ul">
              <li v-for="(item,index) in listData" @click="flytoPoint(item)">
                <div class="li-top flex-align-center">
                  <div class="icon-jydw">
                    <img
                      :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${item.subtype}.png`"
                      alt=""
                    />
                  </div>
                  <div class="s-c-blue-gradient ellipsis li-width">
                    {{item.name}}
                  </div>
                </div>
                <div class="flex-between">
                  <div
                    style="margin-left: 15px"
                    class="color3 flex-align-center"
                  >
                    <span>{{item.type}}</span>
                  </div>
                  <!-- <div style="margin-left: -12%;" class="flex-align-center">
                    <span class="color3 fs-18">董开军 13312341234</span>
                    <span class="icon_lxdh" style="display: block;"></span>
                  </div> -->
                  <div style="margin-right: 15px" class="flex-end">
                    <div class="icon-jl"></div>
                    <span class="color3 fs-28">{{item.jl}} km</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>

          <div
            class="el_page_sjzx"
            style="text-align: center"
            v-if="tabAllCount>0"
          >
            <el-pagination
              @current-change="pageChange"
              layout="prev, pager, next"
              :page-size="10"
              :total="tabAllCount"
              :current-page.sync="currentPage1"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#sjzx-middle",
    data: {
      tabValue: [
        "wlgz_lcdc",
        "wlgz_syqjcd",
        "wlgz_xf",
        "zhddzx_map_video_zbjk",
      ],
      tabData: [
        {
          name: "地质灾害监测点",
          imgName: "地质灾害监测",
          type: "wlgz_dzzh",
        },
        {
          name: "视频监控",
          imgName: "wlgzjc",
          type: "zhddzx_map_video_zbjk",
        },
        {
          name: "水库大坝监测点",
          imgName: "水库大坝监测站",
          type: "wlgz_skdbjcd",
        },
        {
          name: "环境设备",
          imgName: "yy",
          type: "wlgz_hjsb",
        },
        {
          name: "水雨情监测点",
          imgName: "jyzb",
          type: "wlgz_syqjcd",
        },
        {
          name: "路侧地磁",
          imgName: "yjwz",
          type: "wlgz_lcdc",
        },
        {
          name: "饮用水监测点",
          imgName: "饮用水监测点",
          type: "wlgz_yysjcd",
        },
        {
          name: "农业设备",
          imgName: "yjzj",
          type: "wlgz_nysb",
        },
        {
          name: "医疗器械冷库",
          imgName: "jyzb",
          type: "wlgz_ylqxlk",
        },
        {
          name: "桥梁检测",
          imgName: "bncs",
          type: "wlgz_qljc",
        },
        {
          name: "冷链冷库",
          imgName: "jyzb",
          type: "wlgz_lllk",
        },
        {
          name: "企业用电",
          imgName: "jyzb",
          type: "wlgz_qyyd",
        },
        {
          name: "智慧路灯",
          imgName: "jyzb",
          type: "wlgz_zhld",
        },
        {
          name: "其他",
          imgName: "wlgzjc",
          type: "wlgz_xf",
        },
      ],
      tabAllCount: 0,
      listData: [],
      allData: [],
      distance: null,
      sgPoint: null,
      currentPage1: 0,
    },
    mounted() {
      let that = this;
      window.addEventListener("message", function (event) {
        console.log("event==================", event);
        if ((event.data.status = "main_mapIcon" && event.data.data.distance)) {
          that.distance = event.data.data.distance;
          that.sgPoint = event.data.data.sgPoint;
          that.initFun();
          that.pointDataFun();
          that.funLoadXczdVideoPt();
        } else if (
          event.data &&
          event.data.type == "pointClick" &&
          event.data.data &&
          event.data.data.data
        ) {
          let dataArr = event.data.data;
          let msg = JSON.parse(dataArr.data);
          if (msg.subtype && msg.subtype.indexOf("wlgz_") > -1) {
            let coor = msg.lng.split(",");
            let name = msg.name;
            let deviceType = msg.deviceType;
            that.showPop(coor, name, deviceType);
          } else if (msg.pointId == "video") {
            let iframe1 = {
              type: "openIframe",
              name: "video_main",
              src:
                baseURL.url + "/static/citybrain/csdn/commont/video_main.html",
              width: "100%",
              height: "100%",
              left: "0",
              top: "0",
              zIndex: "1000",
              argument: msg,
            };
            window.parent.postMessage(JSON.stringify(iframe1), "*");
          }
        }
      });
    },

    created() {},
    methods: {
      initFun() {
        let that = this;
        let typeArr = this.tabValue.join(",");
        let type = typeArr.replace("zhddzx_map_video_zbjk", "");
        axios({
          method: "get",
          url: "https://csdn.dsjj.jinhua.gov.cn:8101/jhyjzh-server/screen_api/home/<USER>",
          params: {
            type: typeArr,
            distance: that.distance,
            point: that.sgPoint,
          },
        }).then(function (data) {
          let dataArr = data.data.data[0];
          that.allData = dataArr.result;
          that.tabAllCount = dataArr.result.length;
          that.pageChange(1);
        });
      },
      showPop(coor, name, deviceType) {
        let objData = {
          funcName: "customPop",
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                        onclick=" this.style.display = 'none'"
            style="
              width: 1000px;
              position: absolute;
              border-radius: 5px;
              background-color: rgba(10, 31, 53, 0.8);
              z-index: 999999;
              -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
              box-shadow: inset 0 0 40px 0 #5ba3fa;
              padding: 24px;
            "
          >
            <div class="container">
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 :</span>
                <span style="color: #eccc83; width: 70%">${name}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">类&nbsp; &nbsp;   &nbsp;型 :</span>
                <span style="color: #eccc83; width: 70%">${deviceType}</span>
              </div>
            </div>
          </div>`,
        };

        top.document
          .getElementById("map")
          .contentWindow.Work.funChange(JSON.stringify(objData));
      },
      pointDataFun(label) {
        let that = this;
        let typeArr = this.tabValue.join(",");
        let type = label ? label : typeArr.replace("zhddzx_map_video_zbjk", "");
        axios({
          method: "get",
          url: "https://csdn.dsjj.jinhua.gov.cn:8101/jhyjzh-server/screen_api/home/<USER>",
          params: { type: type, distance: that.distance, point: that.sgPoint },
        }).then(function (data) {
          let dataArr = data.data.data;
          dataArr.forEach((item) => {
            that.addPointFun(item);
          });
        });
      },
      pageChange(e) {
        if (e == 1) {
          this.listData = this.allData.slice(0, 10);
        } else {
          this.listData = this.allData.slice(10 * (e - 1), 10 * e);
        }
        console.log("allData==>", this.allData);
        this.tabAllCount = this.allData.length;
      },
      // 加载图层
      changeData(falg, e) {
        let label = e.path[0]._value;
        console.log("label==>", label);
        if (falg && label != "zhddzx_map_video_zbjk") {
          this.initFun();
          this.pointDataFun(label);
        } else if (label != "zhddzx_map_video_zbjk") {
          this.rmPoint(label);
          this.allData = this.allData.filter((item) => item.subtype != label);
          this.pageChange(1);
        } else if (falg && label == "zhddzx_map_video_zbjk") {
          this.funLoadXczdVideoPt();
        } else if (!falg && label == "zhddzx_map_video_zbjk") {
          this.rmPoint(label);
        }
      },
      flytoPoint(item) {
        let point = item.point.split(",");
        this.flytoAdd(point, 15);
      },
      // 添加视频
      funLoadXczdVideoPt() {
        let that = this;
        axios({
          method: "get",
          url: "https://csdn.dsjj.jinhua.gov.cn:8101/jhyjzh-server/screen_api/home/<USER>",
          params: {
            type: "type=zbjk",
            distance: that.distance,
            point: that.sgPoint,
          },
        }).then(function (data) {
          let res = data.data.data.zbjk;
          that.addPointFun(res);
        });
      },
      // 添加点位方法
      addPointFun(data) {
        top.document
          .getElementById("map")
          .contentWindow.Work.funChange(JSON.stringify(data));
      },
      // 清除点位
      rmPoint(id) {
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmPoint",
            pointId: id, //传id清除单类，不传清除所有
          })
        );
        try {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPop",
            })
          );
        } catch (error) {}
      },
      // 飞行
      flytoAdd(obj, zoom) {
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "flyto",
            flyData: {
              center: obj,
              zoom: zoom, //大
              // pitch: 2, //倾斜角
            },
          })
        );
      },
    },

    beforeDestroy() {},
  });
</script>
