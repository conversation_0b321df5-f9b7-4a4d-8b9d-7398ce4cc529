<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>视频详情弹窗</title>
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <link rel="stylesheet" href="/static/css/animate.css" />
  <script src="/elementui/js/index.js"></script>
  <script src="/echarts/echarts.min.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/moment.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 2160px;
      background-color: #00000065;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    .sjzx-middle {
      position: relative;
      width: 1500px;
      height: 970px;
      overflow: hidden;
      background: url('/static/citybrain/tckz/img/video-bg.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
    }

    .head {
      width: 100%;
      height: 70px;
      line-height: 70px;
      padding: 10px 50px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      text-align: center;
      margin-top: 20px;
    }

    .head>span {
      font-size: 40px !important;
      font-weight: 500;
      color: #fff;
    }

    .img {
      display: inline-block;
      margin: 20px;
      float: right;
      width: 34px;
      height: 34px;
      background-image: url("/static/citybrain/csdn/img/cstz2-middle/close-hover.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 20px;
    }

    .el-input.is-disabled .el-input__inner {
      background-color: #031827 !important;
      border-color: #04688b !important;
    }

    .sjzx-middle-con {
      width: 100%;
      height: calc(100% - 100px);
      padding: 30px 50px 50px 50px;
      box-sizing: border-box;
    }

    .m-con {
      width: 100%;
      height: 100%;
    }

    .table-css {
      width: 100%;
      height: 100%;
      font-size: 32px;
      color: #fff;
    }

    .table-th {
      width: 100%;
      height: 100px;
      background-image: linear-gradient(0deg, #00506a 0%, #0097c8 100%);
      border-bottom: solid 1px #00c0ff;
      display: flex;
    }

    .th-css {
      font-size: 32px;
      line-height: 100px;
      text-align: center;
      font-weight: bold;
    }

    .th-css:first-child {
      flex: 1;
      border-right: 1px solid #00c0ff;
    }

    .table-tr {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      height: 250px;
    }

    .title {
      font-size: 34px;
      height: 55px;
      padding-left: 20px;
      box-sizing: border-box;
      border-left: 10px solid #00b9f7;
    }

    .tr-left {
      width: 973px;
    }

    /* 设置滚动条的样式 */
    ::-webkit-scrollbar {
      width: 10px;
      height: 20px;
    }

    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      border-radius: 5px;
    }

    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(35, 144, 207, 0.3);
    }

    ::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(27, 146, 215, 0.5);
    }

    .top-guang {
      position: absolute;
      top: -55px;
      animation: jump1 5s infinite;
    }

    .bottom-guang {
      position: absolute;
      bottom: -108px;
      right: 0;
      animation: jump2 5s infinite;
    }

    .table-tr li {
      width: 695px;
      height: 50px;
      /* overflow: hidden; */
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      padding: 0 10px;
      box-sizing: border-box;
    }

    .el-date-picker__header-label {
      color: #fff;
      font-size: 28px;
      text-align: center;
    }

    .el-picker-panel__icon-btn {
      color: #fff;
    }

    .el-date-table td.disabled div {
      background-color: #5e5a5a;
    }

    .el-select-dropdown__item.selected {
      color: #409eff;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #17325a;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 330px;
    }

    .el-picker-panel {
      color: #fff;
      background: #053755;
      border: 1px solid #008fbe;
    }

    .el-date-table {
      font-size: 20px;
    }

    .el-table__expand-icon {
      color: #fff;
      font-size: 28px;
    }

    .el-input__icon {
      font-size: 35px;
      color: #fff;
      margin-left: 10px;
      line-height: 80px;
    }

    .el-input__suffix {
      right: 23px;
      top: -2px;
    }

    .el-input__inner {
      background-color: rgb(0 144 191 / 60%);
      color: #fff;
      font-size: 35px;
      border: 1px solid #008fbe;
      height: 60px;
      line-height: 80px;
      text-align: center;
    }

    .el-input--prefix .el-input__inner {
      padding-left: 50px;
      height: 80px;
    }

    .el-input--suffix .el-input__inner {
      padding-right: 50px;
    }

    .el-input__icon {
      font-size: 35px;
      color: #fff;
      margin-left: 10px;
      line-height: 80px;
    }

    .el-input__suffix {
      right: 23px;
      top: -2px;
    }

    #chart,
    #empty {
      width: 1400px;
      height: 500px;
    }

    #empty {
      line-height: 445px;
      text-align: center;
      font-size: 54px;
    }

    .online {
      color: #4ade4a;
      font-weight: bold;
    }

    .nonline {
      color: #d3741e;
      font-weight: bold;
    }

    .bottom {
      width: 641px;
      height: 250px;
      margin: 0 auto;
      background: url('/static/citybrain/tckz/img/video-bottom.png') no-repeat 0 -90px;
      position: relative;
      clip-path: polygon(0 0, 50% 100%, 100% 0);
    }

    .bottom>img {
      position: absolute;
      left: calc(50% - 86px);
      top: -10px;
    }

    .head>img {
      /* cursor: pointer;
                margin-top: 5px; */
    }

    .head>img:nth-of-type(1) {
      animation: jump3 2s infinite;
    }

    .head>img:nth-of-type(2) {
      animation: jump4 2s infinite;
    }

    .head:hover>img {
      animation: none;
    }

    .img {
      display: inline-block;
      margin: 20px;
      float: right;
      width: 34px;
      height: 34px;
      background-image: url("/static/citybrain/csdn/img/cstz2-middle/close-hover.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 20px;
    }

    .point {
      cursor: pointer !important;
    }

    .disPoint {
      cursor: not-allowed !important;
    }

    @keyframes jump1 {
      0% {
        transform: translate(0px, 0px);
        /*开始位置*/
      }

      50% {
        transform: translate(850px, 0px);
        /* 可配置跳动方向 */
      }

      100% {
        transform: translate(0px, 0px);
        /*结束位置*/
      }
    }

    @keyframes jump2 {
      0% {
        transform: translate(0px, 0px);
        /*开始位置*/
      }

      50% {
        transform: translate(-850px, 0px);
        /* 可配置跳动方向 */
      }

      100% {
        transform: translate(0px, 0px);
        /*结束位置*/
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="sjzx-middle bounceIn">
      <div class="guang top-guang">
        <img src="/static/citybrain/tckz/img/video-guang.png" alt="">
      </div>
      <div class="head">
        <img src="/static/citybrain/zlglzx/img/zlglzx-right/headerght.png" alt="" height="70" v-show="titleListShow"
          :class="leftDisabled?'disPoint':'point'" @click="prev">
        <span>{{titleName}}<span v-show="titleListShow"
            style="color: #00c0ff;">({{pointIndex+1}}/{{pointList.length}})</span></span>
        <img src="/static/citybrain/zlglzx/img/zlglzx-right/headerlef.png" alt="" height="70" v-show="titleListShow"
          :class="rightDisabled?'disPoint':'point'" @click="next">
        <div class="img" @click="closeIframe()"></div>
      </div>
      <div class="guang bottom-guang">
        <img src="/static/citybrain/tckz/img/video-guang.png" alt="">
      </div>
      <div class="sjzx-middle-con">
        <div class="m-con">
          <div class="table-css">
            <!-- <div class="title">基础数据</div> -->
            <div class="table-tr">
              <li v-for="(item,index) in dataList" :key="index">
                <span style="color: #00c0ff;">{{item.name}}：</span><span :title="item.value"
                  :class="item.value=='在线'?'online':item.value=='离线'?'nonline':''">{{item.value||'---'}}</span>
              </li>
            </div>
            <!-- <div class="title">监测数据</div> -->
            <div class="date-select">
              <div>
                <!-- :disabled="isExit==false" -->
                <span class="s-c-grey-light s-font-35">监测时间：</span>
                <el-date-picker v-model="time" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  placeholder="主数据时间" @change="changeDate" :disabled="isDisabled" :picker-options="{
                    disabledDate(time) { // 设置禁用状态，参数为当前日期，要求返回 Boolean
                      return time.getTime() > Date.now();
                    }
                  }">
                </el-date-picker>
              </div>
            </div>
            <div id="chart" v-loading="loading" v-show="isExit" element-loading-background="rgba(0, 0, 0, 0.8)"></div>
            <div id="empty" v-show="isExit==false">暂无历史数据</div>

          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <img class="breath-light" src="/static/citybrain/tckz/img/video-dian.png" alt="">
      <canvas id="canvas" ref="canvas"
        style="z-index: 100;position: fixed;top: 0;width: 641px;height: 250px;position: absolute;transform: rotate(180deg);"></canvas>
    </div>
  </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var video = new Vue({
    el: "#app",
    data: {
      showCollection: null,
      csrkStatus: null,
      titleName: "液位监测",
      deviceTypeName: "",
      PopCigData: {},
      dataList: [],
      gatherData: [],
      collectionData: [],
      loading: false,
      time: "2023-02-14",
      time1: "2023-02-15",
      currentData: [],
      isExit: true,
      echartsData: [],
      echartsObj: {
        xData: [],
        label: [],
        unit: [],
        yData: []
      },
      canvas: "",
      ctx: "",
      W: "",
      H: "",
      angle: 0,
      mp: 3000,
      particles: [],
      t: 0,
      pointList: [],
      titleList: [],
      pointIndex: 0,
      titleListShow: false,
      leftDisabled: true,
      rightDisabled: false,
      isDisabled: false
    },
    mounted() {
      let that = this
      window.addEventListener("message", function (event) {
        let info = event.data
        console.log("🚀 接收:", info)
        if (info.data && info.type == "wlgz") {
          that.titleName = info.data[0].type_two
          that.deviceTypeName = info.data[0].type_name
          console.log("🚀 点击事件:", that.deviceTypeName)
          that.currentData = info.data[0]
          that.handelPopCigData()
          that.pointList = info.data
          if (that.pointList.length > 1) {
            that.titleListShow = true
          }
          that.getDetailData()
        }
      })
      // that.time = moment().add(-2, 'd').format("YYYY-MM-DD")
      // that.time1 = moment().add(-1, 'd').format("YYYY-MM-DD")
      that._initCavas()

      // axios({
      //   method: "post",
      //   url: baseURL.url + "/dtdd/iot/aep/v1/api/device/list",
      //   data: {
      //     type_id: '8aada4a482c962bf018340630b4f0d75',
      //     page_size: 40000,
      //     page_num: 1,
      //   },
      // }).then(function (allRes) {
      //   console.log(allRes, 444)
      // })
    },
    methods: {
      prev() {
        this.pointIndex--
        if (this.pointIndex < 0) {
          this.leftDisabled = true
          this.pointIndex = 0
          return
        } else if (this.pointIndex == 0) {
          this.leftDisabled = true
          this.rightDisabled = false
          this.pointIndex = 0
        } else {
          this.rightDisabled = false
        }
        this.titleName = this.pointList[this.pointIndex].type_two
        this.deviceTypeName = this.pointList[this.pointIndex].type_name
        this.currentData = this.pointList[this.pointIndex]
        this.handelPopCigData()
        this.getDetailData()
      },
      next() {
        this.pointIndex++
        if (this.pointIndex > this.pointList.length - 1) {
          this.rightDisabled = true
          this.pointIndex = this.pointList.length - 1
          return
        } else if (this.pointIndex == this.pointList.length - 1) {
          this.rightDisabled = true
          this.leftDisabled = false
          this.pointIndex = this.pointList.length - 1
        } else {
          this.leftDisabled = false
        }
        this.titleName = this.pointList[this.pointIndex].type_two
        this.deviceTypeName = this.pointList[this.pointIndex].type_name
        this.currentData = this.pointList[this.pointIndex]
        this.handelPopCigData()
        this.getDetailData()

      },
      handelPopCigData() {
        this.PopCigData = {}
        let data = this.currentData
        let time = data.cjTime.slice(0, 4) + '-' + data.cjTime.slice(4, 6) + '-' + data.cjTime.slice(6, 8) || '--'
        let dict = {
          // "名称": data.device_name || '--',
          // "设备状态": data.device_zxstate,
          // "设备类型": data.type_name || '--',
          // "承载单位": data.dept_name || '--',
          // "所属区域": data.carrier_addr || '--',
          // "采集时间": time,
          "名称": data.device_name || '--',
          "分组类型": data.group_name,
          "设备类型": data.type_name || '--',
          "承载单位": data.dept_name || '--',
          "所属区域": data.administrative_division_name || '--',
          "所属乡镇": data.township_division_name,
        }
        this.time = time
        this.time1 = moment(new Date(time)).add(1, 'd').format("YYYY-MM-DD")
        this.PopCigData = dict
        this.dataList = this.objToArray(this.PopCigData)
      },
      objToArray(obj) {
        return Object.keys(obj).map((ele) => {
          return {
            name: ele,
            value: obj[ele],
          }
        })
      },
      changeDate(val) {
        this.time1 = moment(new Date(val)).add(1, 'd').format("YYYY-MM-DD")
        console.log(this.time1)
        this.getDetailData()
      },
      getDetailData() {
        console.log(this.currentData)
        if (this.currentData.device_type != '--') {
          this.isDisabled = false
          this.loading = true
          // /iot/aep/v1/gather/get-value
          // axios({
          //   method: 'post',
          //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/list',
          //   data: {
          //     device_data_type: this.currentData.device_type,
          //     did: this.currentData.did,
          //     page_size: 100,
          //     created_time_from: this.time + ' 00:00:00',
          //     created_time_to: this.time1 + " 00:00:00"
          //   },
          // }).then((res) => {
          //   this.loading = false
          //   this.gatherData = res.data.data.rows
          //   if (this.gatherData.length > 0) {
          //     this.isExit = true;
          //     this.hangdleEchartsData(this.gatherData)
          //   } else {
          //     this.isExit = false;
          //   }
          // })
          axios({
            method: 'post',
            url: baseURL.url + '/dtdd/gather/api/query/immediately',
            data: {
              data_num: "10",
              // time_start: "2024-04-10 15:33:54",
              // time_end: "2025-05-10 15:33:54",
              time_start: moment().subtract(15, 'days').format("YYYY-MM-DD HH:mm:ss"),
              time_end: moment().format("YYYY-MM-DD HH:mm:ss"),
              conditions: {
                device_code: this.currentData.device_code,
                // "administrative_division_code":"330701",
                // "township_division_code":"001",
                // "dept_code":"001",
                // "group_code":"02",
                // "type_code":"0003"
              }

              // device_data_type: this.currentData.device_type,
              // did: this.currentData.did,
              // page_size: 100,
              // created_time_from: this.time + ' 00:00:00',
              // created_time_to: this.time1 + " 00:00:00"
            },
          }).then((res) => {
            console.log("🚀 接口信息:", res.data.list)
            this.loading = false
            // this.gatherData = res.data.data.rows
            this.gatherData = res.data.list
            if (this.gatherData.length > 0) {
              this.isExit = true
              this.hangdleEchartsData(this.gatherData)
            } else {
              this.isExit = false
            }
          })
        } else {
          this.isDisabled = true
          this.isExit = false
        }
      },

      hangdleEchartsData(data) {
        this.echartsObj = {
          xData: [],
          label: [],
          offset: [],
          position: [],
          unit: [],
          yData: []
        }
        console.log("🚀 画图表:", data, this.deviceTypeName)
        $get("/wlgzNew").then((res) => {
          let dict = res[this.deviceTypeName]
          console.log('字典', dict)

          // 收集所有数据的键名
          let allKeys = new Set()
          data.forEach(ele => {
            if (ele.data) {
              Object.keys(ele.data).forEach(key => {
                allKeys.add(key)
              })
            }
          })

          // 初始化系列数据结构
          let seriesData = {}
          allKeys.forEach(key => {
            seriesData[key] = {
              name: dict[key] || key,  // 使用dict中的中文描述，如果没有则使用原键名
              data: []
            }
          })

          // 收集x轴数据和每个系列的值
          data.forEach((ele) => {
            this.echartsObj.xData.push(ele.gather_time)

            // 为每个键添加对应的值
            allKeys.forEach(key => {
              seriesData[key].data.push(ele.data && ele.data[key] ? ele.data[key] : null)
            })
          })

          // 转换为echarts需要的数组格式
          this.echartsObj.yData = Object.values(seriesData)

          this.lineEcharts3('chart', this.echartsObj)
        })


      },

      lineEcharts3(id, data) {
        echarts.init(document.getElementById(id)).dispose()
        let echarts0 = echarts.init(document.getElementById(id))
        let option = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'rgba(212, 232, 254, 1)',
              fontSize: 24,
            },
          },
          legend: {
            show: true,
            icon: 'circle',
            top: 30,
            left: 'center',
            itemWidth: 30,
            itemHeight: 25,
            itemGap: 100,
            right: '4%',
            textStyle: {
              color: '#D6E7F9',
              fontSize: 30,
            },
          },
          grid: {
            left: '5%',
            right: '2%',
            bottom: '15%',
            top: '20%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              lineStyle: {
                color: '#77B3F1',
              },
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 30,
              },
            },
            data: data.xData || [],
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#77B3F1',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#D6E7F9',
                fontSize: 30,
              },
            },
            splitLine: {
              lineStyle: {
                color: '#77B3F1',
              },
            },
          },
          series: []
        }

        // 为每个数据系列创建一个折线
        if (data.yData && data.yData.length > 0) {
          const colors = ['#00c0ff', '#ff9f7f', '#ffdb5c', '#9fe6b8', '#32c5e9', '#1d9dff']
          data.yData.forEach((series, index) => {
            option.series.push({
              name: series.name,
              type: 'line',
              smooth: false,
              data: series.data,
              lineStyle: {
                color: colors[index % colors.length],
                width: 3,
              },
              itemStyle: {
                color: colors[index % colors.length],
              },
            })
          })
        }

        echarts0.setOption(option, true)
      },



      // hangdleEchartsData(data) {
      //   this.echartsObj = {
      //     xData: [],
      //     label: [],
      //     offset: [],
      //     position: [],
      //     unit: [],
      //     yData: []
      //   }
      //   data = data.filter((item) => {
      //     return item.collection_value != null
      //   })
      //   console.log("🚀 画图表:", data, this.deviceTypeName)
      //   data.forEach((ele) => {
      //     this.echartsObj.xData.push((ele.CUIOT_CONSUMER_TIME).slice(11, 19))
      //   })
      //   let yarr = data.map((item) => {
      //     return item.collection_value
      //   })

      //   $get("/wlgzNew").then((res) => {

      //   })

      //   $get("/wlgz").then((res) => {
      //     console.log("🚀 再请求:", res.data[this.deviceTypeName])
      //     let legendList = res.data[this.deviceTypeName].lsxx
      //     // let list1 = Object.keys(yarr[0])
      //     // res[this.deviceTypeName].lsxx.forEach((i, index) => {
      //     // if (this.deviceTypeName == '磐安中药产业') {
      //     //   filterArr[index] = yarr1.filter((e) => {
      //     //     return e.eName == i
      //     //   })
      //     // }
      //     // console.log(filterArr)
      //     // if (this.deviceTypeName == '磐安中药产业') {
      //     // legendList = res[this.deviceTypeName].lsxx
      //     // }
      //     // if (list1.indexOf(i) > -1) {
      //     //   legendList.push(i)
      //     // }
      //     // })
      //     legendList.forEach((item, index) => {
      //       let arr = yarr.map(el => el[item] && el[item] != '无' ? Number(el[item]).toFixed(2) : 0)
      //       let str = {
      //         label: item,
      //         value: arr,
      //       }
      //       this.echartsObj.yData.push(str)
      //     })
      //     this.echartsObj.label = legendList
      //     // this.echartsObj.label = res[this.deviceTypeName].lsxx
      //     // this.echartsObj.yData = filterArr
      //     this.echartsObj.unit = res[this.deviceTypeName].unit
      //     this.echartsObj.position = res[this.deviceTypeName].position
      //     this.echartsObj.offset = res[this.deviceTypeName].offset
      //     console.log(this.echartsObj)
      //     this.lineEcharts2('chart', this.echartsObj)
      //   })

      // },

      // lineEcharts2(id, data) {
      //   echarts.init(document.getElementById(id)).dispose()
      //   let echarts0 = echarts.init(document.getElementById(id))
      //   let option = {
      //     title: {
      //       show: false,
      //       text: '',
      //       textStyle: {
      //         fontWeight: 'normal',
      //         fontSize: 16,
      //         color: '#F1F1F3',
      //       },
      //       left: '6%',
      //     },
      //     tooltip: {
      //       trigger: 'axis',
      //       borderWidth: 0,
      //       backgroundColor: 'rgba(0, 0, 0, 0.6)',
      //       axisPointer: {
      //         type: 'none',
      //       },
      //       textStyle: {
      //         color: 'rgba(212, 232, 254, 1)',
      //         fontSize: 24,
      //       },
      //     },
      //     legend: {
      //       show: true,
      //       icon: 'circle',
      //       top: 30,
      //       left: 'center',
      //       itemWidth: 30,
      //       itemHeight: 25,
      //       itemGap: 100,
      //       right: '4%',
      //       textStyle: {
      //         color: '#D6E7F9',
      //         fontSize: 30,
      //       },
      //     },
      //     grid: {
      //       left: '5%',
      //       right: '2%',
      //       bottom: '15%',
      //       top: '20%',
      //       containLabel: true,
      //     },
      //     xAxis: [
      //       {
      //         type: 'category',
      //         boundaryGap: true,
      //         axisLine: {
      //           lineStyle: {
      //             color: '#77B3F1',
      //           },
      //         },
      //         axisLabel: {
      //           margin: 10,
      //           // interval: 0,

      //           textStyle: {
      //             color: '#D6E7F9',
      //             fontSize: 30,
      //           },
      //         },
      //         data: data.xData,
      //       },
      //     ],
      //     dataZoom: [
      //       {
      //         show: true,
      //         height: 30,
      //         xAxisIndex: [0],
      //         bottom: 30,
      //         start: 10,
      //         end: 80,
      //         handleIcon:
      //           "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
      //         handleSize: "110%",
      //         handleStyle: {
      //           color: "#d3dee5",
      //         },
      //         textStyle: {
      //           color: "#fff",
      //           fontSize: 23
      //         },
      //         borderColor: "#90979c",
      //       },
      //       {
      //         type: "inside",
      //         show: true,
      //         height: 15,
      //         start: 1,
      //         end: 35,
      //       },
      //     ],
      //     yAxis: [],
      //     // series: this.echartsData
      //     series: []
      //   }
      //   if (data.yData && data.yData.length > 0) {
      //     for (let i = 0; i < data.yData.length; i++) {
      //       console.log(data.yData[i])
      //       option.series.push({
      //         name: data.yData[i].label,
      //         // name: data.label[i],
      //         type: 'line',
      //         smooth: false,
      //         // yAxisIndex: i,
      //         yAxisIndex: data.yData.length > 4 ? i == 4 ? 1 : 0 : i,
      //         data: data.yData[i].value,
      //         // data: data.yData[i].map((e) => { return e.eValue || 0 }),
      //       })
      //     }
      //   }
      //   if (data.unit && data.unit.length > 0) {
      //     for (let i = 0; i < data.unit.length; i++) {
      //       option.yAxis.push({
      //         type: 'value',
      //         name: data.unit[i],
      //         position: data.position[i],
      //         offset: data.offset[i],
      //         splitNumber: 5,
      //         nameTextStyle: {
      //           fontSize: 30,
      //         },
      //         axisTick: {
      //           show: false,
      //         },
      //         axisLine: {
      //           lineStyle: {
      //             color: '#77B3F1',
      //           },

      //         },
      //         axisLabel: {
      //           margin: 10,
      //           textStyle: {
      //             color: '#D6E7F9',
      //             fontSize: 30,
      //           },
      //           // formatter: "{value}" + data.unit[i],
      //         },
      //         splitLine: {
      //           lineStyle: {
      //             color: '#77B3F1',
      //           },
      //         },
      //       })
      //     }
      //     console.log(option.yAxis)
      //     console.log(option.series)
      //   }
      //   echarts0.setOption(option, true)
      // },






      closeIframe() {
        window.parent.postMessage(
          JSON.stringify({
            type: "closeIframe",
            name: "wlgz-dialog",
          }),
          "*"
        )
      },
      _initCavas() {
        this.canvas = document.getElementById("canvas")
        this.ctx = this.canvas.getContext("2d")

        //canvas dimensions
        this.W = window.innerWidth - 30
        this.H = window.innerHeight - 10
        this.canvas.width = this.W
        this.canvas.height = this.H

        //snowflake particles
        //雪花数量
        this.mp = 100
        this.particles = []
        for (var i = 0; i < this.mp; i++) {
          this.particles.push({
            x: Math.random() * this.W * 5, //x-coordinate
            y: Math.random() * this.H, //y-coordinate
            //改变大小
            r: Math.random() * 20 + 10, //radius
            d: Math.random() * this.mp //density
          })
        }
        clearInterval(localStorage.getItem('interval'))
        localStorage.setItem('interval', setInterval(this.draw, 25))
      },
      draw() {
        this.ctx.clearRect(0, 0, this.W, this.H)
        this.ctx.fillStyle = "rgba(146,192,227,1)"
        this.ctx.fillStyle = "border: 1px solid rgb(37, 211, 236,0.2)"
        this.ctx.fillStyle = "box-shadow: 0px 0px 10px 5px rgba(145,198,239,1)"
        this.ctx.beginPath()
        for (var i = 0; i < this.mp; i++) {
          var p = this.particles[i]
          this.ctx.moveTo(p.x, p.y)
          this.ctx.arc(p.x, p.y, p.r, 0, Math.PI * 2, true)
        }
        this.ctx.fill()
        this.update()
      },
      update() {
        // this.angle += 0.01;
        for (var i = 0; i < this.mp; i++) {
          var p = this.particles[i]
          p.y += Math.cos(this.angle + p.d) + 1 + p.r / 2
          p.x += Math.sin(this.angle) * 2

          if (p.x > this.W || p.x < 0 || p.y > this.H) {
            if (i % 3 > 0) {
              this.particles[i] = {
                x: Math.random() * this.W,
                y: -10,
                r: p.r,
                d: p.d
              }
            } else {
              if (Math.sin(this.angle) > 0) {
                //Enter fromth
                this.particles[i] = {
                  x: -5,
                  y: Math.random() * this.H,
                  r: p.r,
                  d: p.d
                }
              } else {
                this.particles[i] = {
                  x: this.W + 5,
                  y: Math.random() * this.H,
                  r: p.r,
                  d: p.d
                }
              }
            }
          }
        }
      },

    }
  });
</script>

</html>