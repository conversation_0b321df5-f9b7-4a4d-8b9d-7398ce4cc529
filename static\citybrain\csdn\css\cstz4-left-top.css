* {
  padding: 0;
  margin: 0;
}

.pointer {
  cursor: pointer;
}

.content {
  width: 1923px;
  height: 917px;
  background: url('/static/citybrain/csdn/img/cstz4-right/bg.png') no-repeat 100% 100%;
  overflow: hidden;
  position: relative;
}

.content::before {
  content: '';
  width: 100%;
  height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('/static/citybrain/csdn/img/cstz4-page/light.png');
  background-repeat: no-repeat;
  background-position: center -43px;
}

.title {
  line-height: 85px;
  text-align: center;
  padding-top: 9px;
  box-sizing: border-box;
}

.bgContent {
  width: 100%;
  height: 832px;
  background: url('/static/citybrain/csdn/img/cstz4/left-top.png') no-repeat;
  background-position: center center;
}

.revolve-box {
  width: 1420px;
  margin: 20px 200px 0 100px;
  height: 470px;
  position: relative;
  transform-style: preserve-3d;
}

.box1 {
  width: 500px;
  height: 400px;
  background: url('/static/citybrain/csdn/img/cstz4/left_dy.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 250px;
  left: 200px;
  animation: animX 13s cubic-bezier(0.36, 0, 0.64, 1) -6s infinite alternate,
    animY 13s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate;
}

.box2 {
  width: 500px;
  height: 400px;
  background: url('/static/citybrain/csdn/img/cstz4/left_mzdp.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 30px;
  left: 1090px;
  animation: animX 13s cubic-bezier(0.36, 0, 0.64, 1) -14.8s infinite alternate,
    animY 13s cubic-bezier(0.36, 0, 0.64, 1) -8.8s infinite alternate;
}

.box3 {
  width: 500px;
  height: 400px;
  background: url('/static/citybrain/csdn/img/cstz4/left_ty.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 450px;
  left: 1315px;
  animation: animX 13s cubic-bezier(0.36, 0, 0.64, 1) -23s infinite alternate,
    animY 13s cubic-bezier(0.36, 0, 0.64, 1) -17.6s infinite alternate;
}

.revolve-box:hover .box1,
.revolve-box:hover .box2,
.revolve-box:hover .box3 {
  animation-play-state: paused;
}

@keyframes animX {
  0% {
    left: -5%;
  }

  100% {
    left: 90%;
  }
}

@keyframes animY {
  0% {
    top: -5%;
  }

  100% {
    top: 95%;
  }
}

@keyframes scale {
  0% {
    transform: scale(0.5);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
}
