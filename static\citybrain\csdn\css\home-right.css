* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}
.container {
  width: 2070px;
  height: 1904px;
  padding: 10px 50px 30px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csdn/img/home/<USER>
  background-size: 100% 100%; */
}
.top-con {
  width: 100%;
  /* height: 60%; */
  display: flex;
}
.bottom-con {
  width: 100%;
  /* height: 40%; */
}
.szsh {
  width: 48%;
  height: 100%;
}
.szwh {
  width: 52%;
  height: 100%;
}
.head {
  width: 100%;
  height: 150px;
  font-size: 50px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 85px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csrk_3840/csrk_v2/img/header.png) no-repeat; */
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-position: -60px;
  background-size: 100% 100%;
  cursor: pointer;
}
.head1 {
  width: 100%;
  height: 150px;
  font-size: 50px;
  font-weight: 500;
  color: #ffffff;
  padding-left: 85px;
  box-sizing: border-box;
  /* background: url(/static/citybrain/csrk_3840/csrk_v2/img/header.png) no-repeat; */
  background: url(/static/citybrain/csdn/img/home/<USER>
  background-position: -35px;
  background-size: 100% 100%;
  cursor: pointer;
}
.box {
  width: 100%;
  height: calc(100% - 100px);
  padding: 0px 20px;
  box-sizing: border-box;
}
.wlsq {
  width: 100%;
  height: 534px;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 50px 0; */
  position: relative;
}
.wlsq-img {
  position: absolute;
  top: 0px;
  left: 50px;
  animation: jump 2s infinite;
}
.wlsq-t {
  width: 270px;
  text-align: center;
  font-size: 36px;
  font-family: PingFang SC;
  /* font-weight: bolder; */
  color: #ffffff;
  line-height: 60px;
}
.wlsq-t:first-child {
  position: absolute;
  top: 120px;
  left: 360px;
}
.wlsq-t:first-child .ys {
  font-size: 50px;
  background: linear-gradient(0deg, #ffcc00 0.4150390625%, #ffffff 99.5849609375%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.wlsq-t:nth-child(2) {
  position: absolute;
  top: 180px;
  left: 16px;
}
.wlsq-t:nth-child(2) .ys,
.wlsq-t:nth-child(3) .ys {
  font-size: 50px;
  background: linear-gradient(0deg, #ffffff 0%, #00aeff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.wlsq-t:nth-child(3) {
  position: absolute;
  top: 180px;
  right: -28px;
}
.yljg {
  width: 100%;
  height: 476px;
  position: relative;
  background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 30px 0;
}
.yljg-t:first-child {
  position: absolute;
  top: 115px !important;
  left: 40px !important;
  line-height: 90px;
}
.yljg-t:nth-child(2) {
  position: absolute;
  top: 115px !important;
  left: 657px !important;
  line-height: 90px;
}
.yljg-t:nth-child(1) .ys1 {
  color: #00eaef;
  font-size: 50px;
}
.yljg-t:nth-child(2) .ys1 {
  color: #fba701;
  font-size: 50px;
}
.tlt {
  position: absolute;
  left: 370px;
  bottom: 65px;
  color: #fff;
  font-size: 40px;
}
.wbdw {
  width: 100%;
  height: 534px;
  display: flex;
  justify-content: space-between;
}
.wbdw-t:first-child {
  position: absolute;
  top: 4px;
  left: 122px;
}
.wbdw-t:nth-child(2) {
  position: absolute;
  top: 137px;
  left: -70px;
}
.wbdw-t:nth-child(3) {
  position: absolute;
  top: 140px;
  right: -60px;
}
.wbdw > div:nth-of-type(1) {
  width: 570px;
  height: 100%;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 0px 150px; */
  position: relative;
  margin-top: 42px;
}
.szwh-img {
  position: absolute;
  top: 150px;
  right: 0px;
}
.wbdw > div:nth-of-type(2) {
  width: 475px;
  height: 100%;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat -100px 0px; */
  position: relative;
  margin-top: 50px;
}
.tsg-img {
  position: absolute;
  top: 15px;
  right: -90px;
}
.tsg-t {
  position: absolute;
  left: 30px !important;
  top: 135px !important;
}
.jq_jd {
  width: 100%;
  height: 476px;
  padding-top: 50px;
  box-sizing: border-box;
}
.jq_jd-back {
  width: 100%;
  height: 170px;
  background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 0px 50px;
  position: relative;
  font-size: 36px;
  color: #fff;
}
.jq_jd-title {
  margin-left: 140px;
}
.col-green {
  color: #00fffc;
}
.col-blue {
  color: #3d83e4;
}
.zdjg-img {
  position: absolute;
  top: 0px;
  left: 70px;
  /* animation: rotate 10s linear infinite; */
}
.szjj-con {
  display: flex;
  margin-top: -22px;
}
.zdjd {
  position: relative;
  width: 35%;
  height: 540px;
  /* background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat 100px 40px; */
  transform-style: preserve-3d;
}
.trans {
  width: 460px;
  height: 430px;
  position: relative;
}
.zdjd-t:first-child {
  position: absolute;
  top: 23px;
  left: 236px;
  font-size: 36px;
  animation: animX 6.5s cubic-bezier(0.36, 0, 0.64, 1) -3s infinite alternate,
    animY 6.5s cubic-bezier(0.36, 0, 0.64, 1) 0s infinite alternate;
}
.zdjd-t:nth-child(2) {
  position: absolute;
  top: 316px;
  left: -12px;
  font-size: 36px;
  animation: animX 6.5s cubic-bezier(0.36, 0, 0.64, 1) -7.4s infinite alternate,
    animY 6.5s cubic-bezier(0.36, 0, 0.64, 1) -4.4s infinite alternate;
}
.zdjd-t:nth-child(3) {
  position: absolute;
  top: 316px;
  right: -12px;
  font-size: 36px;
  animation: animX 6.5s cubic-bezier(0.36, 0, 0.64, 1) -11.8s infinite alternate,
    animY 6.5s cubic-bezier(0.36, 0, 0.64, 1) -8.8s infinite alternate;
}
.pazs {
  width: 65%;
  height: 540px;
  background: url('/static/citybrain/csdn/img/home/<USER>') no-repeat -60px 420px;
}
.pazs-title {
  font-size: 40px;
  color: #ffffff;
  display: flex;
}

/* 动画 */
@keyframes jump {
  0% {
    transform: translate(0px, 0px); /*开始位置*/
  }
  50% {
    transform: translate(0px, 20px); /* 可配置跳动方向 */
  }
  100% {
    transform: translate(0px, 0px); /*结束位置*/
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes animX {
  0% {
    left: -5%;
  }

  100% {
    left: 95%;
  }
}

@keyframes animY {
  0% {
    top: -5%;
  }

  100% {
    top: 95%;
  }
}

@keyframes scale {
  0% {
    transform: scale(0.5);
    opacity: 0.5;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
}
