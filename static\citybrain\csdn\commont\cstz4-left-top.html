<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>城市体征</title>
  <link rel="shortcut icon" href="#" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <!-- <script src="/static/js/jslib/Emiter.js"></script> -->
  <link rel="stylesheet" href="/static/css/xiaoguo.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/css/cstz4-left-top.css" />
</head>

<body>
  <div id="app" class="content">
    <div class="s-c-blue-gradient s-font-50 title s-w7 pointer" @click="openIframe('党建统领')">党建统领</div>
    <div class="bgContent">
      <div class="revolve-box">
        <div class="box1">
          <div style="padding: 25px 0 0 160px">
            <div class="s-font-50 s-w7" style="color: #fff">团员</div>
            <div class="s-w7">
              <span class="s-c-yellow-gradient s-font-50">{{tyStr.value}}</span>
              <span class="s-c-yellow-gradient s-font-35">{{tyStr.unit}}</span>
            </div>
          </div>
          <div id="echarts1" style="height: 250px"></div>
        </div>
        <div class="box2">
          <div style="padding: 25px 0 0 160px">
            <div class="s-font-50 s-w7" style="color: #fff">民主党派</div>
            <div class="s-w7">
              <span class="s-c-yellow-gradient s-font-50">{{mzdpStr.value}}</span>
              <span class="s-c-yellow-gradient s-font-35">{{mzdpStr.unit}}</span>
            </div>
          </div>
          <div id="echarts2" style="height: 195px"></div>
        </div>
        <div class="box3">
          <div style="padding: 25px 0 0 160px; margin-bottom: 30px">
            <div class="s-font-50 s-w7" style="color: #fff">党员</div>
            <div class="s-w7">
              <span class="s-c-yellow-gradient s-font-50">{{dyStr.value}}</span>
              <span class="s-c-yellow-gradient s-font-35">{{dyStr.unit}}</span>
            </div>
          </div>
          <div id="echarts3" style="height: 110px"></div>
          <div id="echarts4" style="height: 50px"></div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
<script>
  var vm = new Vue({
    el: '#app',
    data() {
      return {
        dyStr: {
          value: '-',
          unit: '',
        },
        mzdpStr: {
          value: '-',
          unit: '',
        },
        tyStr: {
          value: '-',
          unit: '',
        },
      }
    },
    mounted() {
      this.initFun()
    },
    methods: {
      initFun() {
        let that = this
        $api('djtlLeft001').then((res) => {
          let data1 = []
          let data2 = []
          let sum = []
          res.forEach((ele) => {
            if (ele.ymbq == '男党员总数' || ele.ymbq == '女党员总数') {
              data1.push(ele.value)
            } else if (ele.ymbq == '党员总数') {
              that.dyStr.value = ele.value
              that.dyStr.unit = ele.unit
              sum.push(ele.value)
            } else if (ele.ymbq == '大专以上党员总数') {
              data2.push(ele.value)
            } else if (ele.ymbq == '团员总数') {
              that.tyStr.value = ele.value
              that.tyStr.unit = ele.unit
            } else if (ele.ymbq == '民主党派成员总数') {
              that.mzdpStr.value = ele.value
              that.mzdpStr.unit = ele.unit
            }
          })
          that.getEcharts3('echarts3', data1, sum)
          that.getEcharts4('echarts4', data2, sum)
        })
        $api('djtlLeft005', { belong_to: '各民族党派人数' }).then((res) => {
          that.getEcharts2('echarts2', res)
        })
        $api('djtlLeft006', { belong_to: '各领域团员分布' }).then((res) => {
          that.getEcharts1('echarts1', res)
        })
      },
      openIframe(name) {
        let leftData = {
          type: 'openIframe',
          name: 'djtl-page',
          src: '/static/citybrain/djtl/pages/djtl-page.html',
          width: '7680px',
          height: '2005px',
          left: '0',
          top: '115px',
          zIndex: 100,
          argument: {
            name: name,
            type: '城市体征下钻',
          },
        }
        top.postMessage(JSON.stringify(leftData), '*')
      },
      getEcharts1(dom, datas) {
        let sumValue = 0
        let data = datas.map((ele) => {
          ele.value = ele.total.replace('万人', '')
          sumValue += Number(ele.value)
          return ele
        })
        let myChart = echarts.init(document.getElementById(dom))
        arrName = getArrayValue(data, 'name')
        arrValue = getArrayValue(data, 'value')
        objData = array2obj(data, 'name')
        optionData = getData(data)
        function getArrayValue(array, key) {
          var key = key || 'value'
          var res = []
          if (array) {
            array.forEach(function (t) {
              res.push(t[key])
            })
          }
          return res
        }

        function array2obj(array, key) {
          var resObj = {}
          for (var i = 0; i < array.length; i++) {
            resObj[array[i][key]] = array[i]
          }
          return resObj
        }

        function getData(data) {
          var res = {
            series: [],
            yAxis: [],
          }
          for (let i = 0; i < data.length; i++) {
            res.series.push({
              name: '团员结构',
              type: 'pie',
              clockWise: false, //顺时加载
              hoverAnimation: false, //鼠标移入变大
              radius: [65 - i * 10 + '%', 57 - i * 10 + '%'],
              center: ['18%', '50%'],
              label: {
                show: false,
              },
              itemStyle: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                borderWidth: 5,
              },
              data: [
                {
                  value: data[i].value,
                  name: data[i].name,
                },
                {
                  value: sumValue - data[i].value,
                  name: '',
                  itemStyle: {
                    color: 'rgba(0,0,0,0)',
                    borderWidth: 0,
                  },
                  tooltip: {
                    show: false,
                  },
                  hoverAnimation: false,
                },
              ],
            })
            res.series.push({
              name: '',
              type: 'pie',
              silent: true,
              z: 1,
              clockWise: false, //顺时加载
              hoverAnimation: false, //鼠标移入变大
              radius: [65 - i * 10 + '%', 57 - i * 10 + '%'],
              center: ['18%', '50%'],
              label: {
                show: false,
              },
              itemStyle: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                borderWidth: 5,
              },
              data: [
                {
                  value: 7.5,
                  itemStyle: {
                    color: 'rgba(0,0,0,0.5)',
                    borderWidth: 0,
                  },
                  tooltip: {
                    show: false,
                  },
                  hoverAnimation: false,
                },
                {
                  value: 2.5,
                  name: '',
                  itemStyle: {
                    color: 'rgba(0,0,0,0)',
                    borderWidth: 0,
                  },
                  tooltip: {
                    show: false,
                  },
                  hoverAnimation: false,
                },
              ],
            })
            res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + '%')
          }
          return res
        }

        let option = {
          grid: {
            top: '0%',
            left: '0%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          legend: {
            top: '5%',
            left: '34%',
            itemGap: 5,
            icon: 'circle',

            formatter: function (name) {
              return '{title|' + name + '} {value|' + objData[name].value + '万人}'
              // return '{title|' + name + '} {value|' + objData[name].percent + '}'
            },
            textStyle: {
              rich: {
                title: {
                  fontSize: 22,
                  color: '#fff',
                },
                value: {
                  fontSize: 22,
                  color: '#fff',
                },
              },
            },
          },
          tooltip: {
            show: true,
            trigger: 'item',
            // formatter: '{a}<br>{b}:{c}万人',
            formatter: '{a}<br>{b}:{c}万人({d}%)',
            textStyle: {
              fontSize: 24,
              color: '#fff',
            },
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
            borderWidth: 0,
          },

          color: ['#FF8700', '#ffc300', '#00e473', '#009DFF'],

          yAxis: [
            {
              type: 'category',
              inverse: true,
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                inside: true,
                textStyle: {
                  color: '#fff',
                  fontSize: 24,
                  fontFamily: 'SourceHanSansCN-Medium',
                },
                show: false,
              },
              data: optionData.yAxis,
            },
          ],
          xAxis: [
            {
              show: false,
            },
          ],
          series: optionData.series,
        }

        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      },
      getEcharts2(dom, data) {
        let myEc = echarts.init(document.getElementById(dom))
        let echartData = data.map((ele) => {
          ele.value = ele.total.replace('人', '')
          return ele
        })
        let all = 0
        echartData.map((v) => {
          all += v.value
        })
        const option = {
          color: ['#EAEA26', '#906BF9', '#FE5656', '#01E17E', '#3DD1F9', '#FFAD05'],
          tooltip: {
            show: true,
            trigger: 'item',
            formatter: `{b}: \n {c}人`,
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '24',
            },
          },
          legend: {
            top: '10%',
            left: '54%',
            itemGap: 20,
            icon: 'circle',

            textStyle: {
              fontSize: 24,
              color: '#fff',
            },
          },
          series: [
            {
              name: '',
              type: 'pie',
              roseType: 'area',
              radius: ['30%', '100%'],
              center: ['30%', '50%'],
              label: {
                show: false,
              },
              data: echartData,
            },
          ],
        }
        myEc.setOption(option)
        myEc.getZr().on('mousemove', (param) => {
          myEc.getZr().setCursorStyle('default')
        })
      },
      getEcharts3(dom, arr, total) {
        let myEc = echarts.init(document.getElementById(dom))
        let data = arr.map((res) => {
          res = ((Number(res) / total[0]) * 100).toFixed(2)
          return res
        })

        let icon =
          'path://M512.584639,219.893708c40.41173,0.258019,73.19961-32.274913,73.199609-72.557634,0-40.025725-32.78788-72.559681-73.199609-72.559681-40.473163,0-73.196538,32.533956-73.196538,72.559681,0,40.089206,32.723375,72.557634,73.196538,72.557634z,m73.330666,16.396499H439.129058c-55.266258,0-91.39098,48.28336-91.390981,94.203594v220.945238c0,42.847553,60.780905,42.847553,60.780905,0V347.144224h11.782872v555.564273c0,59.179548,82.417649,57.316077,84.337434,0V582.569248h15.696162V902.96754c3.391108,60.650871,84.340506,54.817796,84.340506-0.258019V347.144224h9.800631v204.234406c0,42.837314,62.696594,42.837314,62.696594,0V330.433391c0.126962-45.72979-36.116531-94.143184-91.257876-94.143184z'
        let icon1 =
          'path://M28.9624207,31.5315864 L24.4142575,16.4793596 C23.5227152,13.8063773 20.8817445,11.7111088 17.0107398,11.7111088 L12.112691,11.7111088 C8.24168636,11.7111088 5.60080331,13.8064652 4.70917331,16.4793596 L0.149791395,31.5315864 C-0.786976655,34.7595013 2.9373074,35.9147532 3.9192135,32.890727 L8.72689855,19.1296485 L9.2799493,19.1296485 C9.2799493,19.1296485 2.95992025,43.7750224 2.70031069,44.6924335 C2.56498417,45.1567684 2.74553639,45.4852068 3.24205501,45.4852068 L8.704461,45.4852068 L8.704461,61.6700801 C8.704461,64.9659872 13.625035,64.9659872 13.625035,61.6700801 L13.625035,45.360657 L15.5097899,45.360657 L15.4984835,61.6700801 C15.4984835,64.9659872 20.4191451,64.9659872 20.4191451,61.6700801 L20.4191451,45.4852068 L25.8814635,45.4852068 C26.3667633,45.4852068 26.5586219,45.1567684 26.4345142,44.6924335 C26.1636859,43.7750224 19.8436568,19.1296485 19.8436568,19.1296485 L20.3966199,19.1296485 L25.2043926,32.890727 C26.1862111,35.9147532 29.9105828,34.7595013 28.9625083,31.5315864 L28.9624207,31.5315864 Z M14.5617154,0 C17.4960397,0 19.8773132,2.3898427 19.8773132,5.33453001 C19.8773132,8.27930527 17.4960397,10.66906 14.5617154,10.66906 C11.6274788,10.66906 9.24611767,8.27930527 9.24611767,5.33453001 C9.24611767,2.3898427 11.6274788,0 14.5617154,0 L14.5617154,0 Z'
        let sum = 100
        option = {
          tooltip: {
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '24',
            },
            trigger: 'item',
            // formatter: function (param) {
            //   let value = param.value
            //   let name = param.name
            //   if (name == '男') {
            //     value = (data[0] / 100) * 100
            //   } else {
            //     value = (data[1] / 100) * 100
            //   }
            //   return `${name} : ${value}%`
            // },
            formatter: '{b0}: {c0}%',
          },
          grid: {
            top: 'center',
            height: 100,
            left: 100,
            right: 100,
          },
          xAxis: {
            max: 100,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          yAxis: {
            type: 'category',
            data: ['男', '女'],
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                color: '#fff',
                fontSize: 24,
              },
            },
            axisPointer: {
              label: {
                show: true,
                margin: 100,
              },
            },
          },
          series: [
            {
              type: 'pictorialBar',
              symbolRepeat: 'fixed',
              symbolMargin: '6!',
              symbolClip: true,
              symbolSize: [20, 40],
              symbolPosition: 'start',
              symbolBoundingData: 100,
              data: [
                {
                  value: data[0],
                  symbol: icon,
                  itemStyle: {
                    color: '#1DDBF9',
                  },
                },
                {
                  value: data[1],
                  symbol: icon1,
                  itemStyle: {
                    color: 'orange',
                  },
                },
              ],
              z: 10,
            },
            {
              type: 'pictorialBar',
              itemStyle: {
                normal: {
                  opacity: 0.2,
                },
              },
              animationDuration: 0,
              symbolRepeat: 'fixed',
              symbolMargin: '6!',
              symbolSize: [20, 40],
              symbolBoundingData: 100,
              symbolPosition: 'start',
              data: [
                {
                  value: 100,
                  symbol: icon,
                  itemStyle: {
                    color: '#1DDBF9',
                  },
                  tooltip: {
                    show: false,
                  },
                },
                {
                  value: 100,
                  symbol: icon1,
                  itemStyle: {
                    color: '#ff5983',
                  },
                  tooltip: {
                    show: false,
                  },
                },
              ],
              z: 5,
            },
          ],
        }

        myEc.setOption(option)
        myEc.getZr().on('mousemove', (param) => {
          myEc.getZr().setCursorStyle('default')
        })
      },
      getEcharts4(dom, barData, lineData) {
        let myEc = echarts.init(document.getElementById(dom))
        var category = ['大专及以上']

        var option = {
          tooltip: {
            show: true,
            trigger: 'item',
            textStyle: {
              fontSize: 24,
              color: '#fff',
            },
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
            borderWidth: 0,
            formatter: '{b0}: {c0}%',
          },
          grid: [
            {
              left: '30%',
              bottom: '',
              top: '',
              right: '15%', //在此图中可用于控制柱子的长度
            },
          ],
          xAxis: {
            show: false,
          },
          yAxis: {
            data: category,
            show: true,
            axisLabel: {
              margin: 10,
              textStyle: {
                color: '#fff',
                fontSize: 24,
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          series: [
            {
              // 蓝柱下面方块
              name: '',
              type: 'pictorialBar',
              symbol: 'Rect',
              barWidth: '60%',
              symbolOffset: ['100%', '-10%'],
              itemStyle: {
                normal: {
                  color: '#ffffff',
                },
              },
              z: -11,
              symbolRepeat: true,
              symbolSize: ['60%', '120%'],
              data: lineData,
              barGap: 50,
              barCategoryGap: 0,
              animationEasing: 'elasticOut',
              tooltip: {
                show: false,
              },
            },
            {
              // 蓝柱
              name: '', // blue bar
              type: 'pictorialBar',
              symbol: 'Rect',
              barWidth: '60%',
              symbolOffset: ['100%', '-10%'],
              itemStyle: {
                normal: {
                  barMaxWidth: '60%',
                  barBorderRadius: 100,
                  color: '#a9db52',
                },
              },
              symbolRepeat: true,
              symbolSize: ['60%', '120%'],
              data: barData,
            },
          ],
        }
        myEc.setOption(option)
        myEc.getZr().on('mousemove', (param) => {
          myEc.getZr().setCursorStyle('default')
        })
      },
    },

    beforeDestroy() { },
  })
</script>