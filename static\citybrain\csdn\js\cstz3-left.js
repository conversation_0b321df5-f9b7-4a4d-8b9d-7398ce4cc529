var vm = new Vue({
  el: '#cstz3-left',
  data: {
    initList: [],
    menuIndex1: null,
    menuIndex2: null,
    menuIndex3: null,
    menuIndex4: null,
    titleIndex1: null,
    titleIndex2: null,
    titleIndex3: null,
    titleIndex4: null,
    menuList1: [
      {
        name: '全部类目',
      },
      {
        name: '党建发展',
      },
      {
        name: '公职公务',
      },
      {
        name: '统战工作',
      },
      {
        name: '统战工作',
      },
      {
        name: '统战工作',
      },
    ],
    menuList2: [],
    menuList3: [],
    menuList4: [],
    list: [
      {
        name: '基层党组织数据',
        value: '10000',
      },
      {
        name: '党委数',
        value: '5000',
      },
      {
        name: '总支部数',
        value: '2000',
      },
      {
        name: '支部数',
        value: '600',
      },
    ],
    conLists1: [],
    conLists2: [],
    conLists3: [],
    conLists4: [],
    conList1: [],
    conList2: [],
    conList3: [],
    conList4: [],

    chartData4: [
      {
        name: '接待国内游客人数',
        value: 10,
      },
      {
        name: '接待国外游客人数',
        value: 20,
      },
    ],
    showEcharts0: true,
    showEcharts1: true,
    dangerValue: '',
  },
  created() {
    // top.document.getElementById('map').contentWindow.Work.change3D(9)
    //      fetch('/static/data/data.json', {//加载柱状体
    //       method: 'GET',
    //       mode: 'cors',
    //       headers: new Headers({
    //         Accept: 'application/json',
    //       }),
    //     }).then((res) => res.json()).then((e) => {
    //         e['城市体征_指标'].forEach((e) => {
    //               top.document
    //                 .getElementById('map')
    //                 .contentWindow.Work.funChange(JSON.stringify(e))
    //             })
    //     })
  },
  mounted() {
    this.init()
  },
  methods: {
    changeMiddle(name) {
      name == '党建统领' ? (this.showEcharts0 = !this.showEcharts0) : (this.showEcharts1 = !this.showEcharts1)
      // top.window.emiter.emit(top.EventType.cstz3Emit,name)
    },
    init() {
      $api('/cstz_zbs').then((res) => {
        this.initList = res
        this.menuList1 = res.filter((item) => {
          return item.parentId === 9
        })
        this.menuList2 = res.filter((item) => {
          return item.parentId === 10
        })
        this.menuClick1('list1-0', this.menuList1[0].id, this.menuList1[0].name)
        this.menuClick2('list2-0', this.menuList2[0].id, this.menuList2[0].name)
        //top.window.emiter.emit(top.EventType.cstz3Emit,this.menuList1)
      })
      $api('qyxyfx', { result: 3 }).then((res) => {
        this.dangerValue = res[0].percentage
      })
    },
    async menuClick1(index, id, name) {
      this.menuIndex1 = index
      this.titleIndex1 = name
      this.conLists1 = this.initList.filter((item) => {
        return item.parentId === id
      })
      this.conList1 = []
      let data = []
      for (let i = 0; i < this.conLists1.length; i++) {
        let res = await $api('/cstz_zbs_info', { categoryId: this.conLists1[i].id })
        data.push(res)
      }
      let len = this.conLists1.length
      this.conList1 = data.slice(-len)
      // this.getEcharts001("echarts01-1",'/static/citybrain/djtl/img/djtl-middle/cenleft002.png', 60, "男 75.9万")
      // this.getEcharts001("echarts01-2",'/static/citybrain/djtl/img/djtl-middle/cenleft003.png', 40, "女 50.6万")
    },
    async menuClick2(index, id, name) {
      this.menuIndex2 = index
      this.titleIndex2 = name
      this.conLists2 = this.initList.filter((item) => {
        return item.parentId === id
      })
      this.conList2 = []
      let data = []
      for (let i = 0; i < this.conLists2.length; i++) {
        let res = await $api('/cstz_zbs_info', { categoryId: this.conLists2[i].id })
        data.push(res)
      }
      let len = this.conLists2.length
      this.conList2 = data.slice(-len)
      // this.getEcharts02('echarts02')
    },
    toNumber(nStr) {
      nStr += ''
      x = nStr.split('.')
      x1 = x[0]
      x2 = x.length > 1 ? '.' + x[1] : ''
      var rgx = /(\d+)(\d{4})/
      while (rgx.test(x1)) {
        x1 = x1.replace(rgx, '$1' + ',' + '$2')
      }
      return x1 + x2
    },
    // 四级指标点击
    choseFourFun(item) {
      top.window.emiter.emit(top.EventType.szfzEmit, false)
      this.clearHotMapAll()
      this.clear3Dtext()
      this.clearPoint()
      // top.commonObj.hideMap()
      // if (item.aiName == '工业用电量' ||item.aiName == '规上企业用电量' || item.aiName == '日用电量'|| item.aiName == '全社会用电量') {
      // item.aiName=='工业用电量'?item.aiName='规上企业用电量' :item.aiName=='日用电量'?item.aiName='全社会用电量':item.aiName=item.aiName
      let leftData = {
        type: 'openIframe',
        name: 'cstz2-middle-diong',
        src: baseURL.url + '/static/citybrain/csdn/commont/cstz2-middle-diong1.html',
        left: '2380px',
        top: '360px',
        width: '2907px',
        height: '1485px',
        zIndex: '200',
        argument: {
          // id:id,
          // name:name,
          // value:value,
          // unit:unit,
          item: item,
          status: 'showEcharts',
        },
      }
      let leftData1 = {
        type: 'openIframe',
        name: 'cstz3-middle-diong',
        src: baseURL.url + '/static/citybrain/csdn/commont/cstz3-middle-diong.html',
        left: '2480px',
        top: '400px',
        width: '2602px',
        height: '1434px',
        zIndex: '200',
        argument: {
          // id:id,
          // name:name,
          // value:value,
          // unit:unit,
          id: item.sonId,
          status: 'showEcharts',
        },
      }
      window.parent.postMessage(JSON.stringify(leftData1), '*')
      // }
    },

    getEcharts001(dom, img, value, tle) {
      let myEc = echarts.init(document.getElementById(dom))
      let angle = 0 //角度，用来做简单的动画效果的
      let val = value
      let option = {
        graphic: {
          elements: [
            {
              type: 'image',
              style: {
                image: img,
                width: 70,
                height: 70,
              },
              left: 'center',
              top: 'center',
            },
          ],
        },
        title: [
          {
            text: tle,
            x: 'center',
            y: '85%',
            textStyle: {
              fontSize: 30,
              color: '#ffffff',
            },
          },
        ],
        series: [
          {
            name: 'ring5',
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                  startAngle: ((0 + angle) * Math.PI) / 180,
                  endAngle: ((90 + angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#16EAFF',
                  fill: 'transparent',
                  lineWidth: 3,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5',
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                  startAngle: ((180 + angle) * Math.PI) / 180,
                  endAngle: ((270 + angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#16EAFF',
                  fill: 'transparent',
                  lineWidth: 3,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5',
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
                  startAngle: ((270 + -angle) * Math.PI) / 180,
                  endAngle: ((40 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#16EAFF',
                  fill: 'transparent',
                  lineWidth: 3,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5',
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              return {
                type: 'arc',
                shape: {
                  cx: api.getWidth() / 2,
                  cy: api.getHeight() / 2,
                  r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65,
                  startAngle: ((90 + -angle) * Math.PI) / 180,
                  endAngle: ((220 + -angle) * Math.PI) / 180,
                },
                style: {
                  stroke: '#16EAFF',
                  fill: 'transparent',
                  lineWidth: 3,
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5',
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65
              let point = getCirlPoint(x0, y0, r, 90 + -angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#16EAFF', //粉
                  fill: '#0CD3DB',
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            name: 'ring5', //绿点
            type: 'custom',
            coordinateSystem: 'none',
            renderItem: function (params, api) {
              let x0 = api.getWidth() / 2
              let y0 = api.getHeight() / 2
              let r = (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.65
              let point = getCirlPoint(x0, y0, r, 270 + -angle)
              return {
                type: 'circle',
                shape: {
                  cx: point.x,
                  cy: point.y,
                  r: 4,
                },
                style: {
                  stroke: '#16EAFF', //绿
                  fill: '#0CD3DB',
                },
                silent: true,
              }
            },
            data: [0],
          },
          {
            type: 'pie',
            radius: ['53%', '42%'],
            silent: true,
            clockwise: true,
            startAngle: 90,
            z: 0,
            zlevel: 0,
            label: {
              normal: {
                position: 'center',
              },
            },
            data: [
              {
                value: value,
                name: '',
                itemStyle: {
                  normal: {
                    color: {
                      // 完成的圆环的颜色
                      colorStops: [
                        {
                          offset: 0,
                          color: '#5180F8', // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#16EAFF', // 100% 处的颜色
                        },
                      ],
                    },
                  },
                },
              },
              {
                value: 100 - value,
                name: '',
                label: {
                  normal: {
                    show: false,
                  },
                },
                itemStyle: {
                  normal: {
                    color: '#173164',
                  },
                },
              },
            ],
          },
        ],
      }

      //获取圆上面某点的坐标(x0,y0表示坐标，r半径，angle角度)
      function getCirlPoint(x0, y0, r, angle) {
        let x1 = x0 + r * Math.cos((angle * Math.PI) / 180)
        let y1 = y0 + r * Math.sin((angle * Math.PI) / 180)
        return {
          x: x1,
          y: y1,
        }
      }

      function draw() {
        angle = angle + 3
        myEc.setOption(option, true)
        //window.requestAnimationFrame(draw);
      }

      setInterval(function () {
        //用setInterval做动画感觉有问题
        draw()
      }, 100)

      myEc.setOption(option)
    },
    getEcharts02(dom) {
      let myEc = echarts.init(document.getElementById(dom))
      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '10%',
          top: '30%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: ['第二产业', '第二产业', '高技术产业'],
            offset: 20,
            axisLine: {
              lineStyle: {
                color: '#77b3f1',
                opacity: 0.3,
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              // rotate: -40,
              textStyle: {
                fontSize: 28,
                color: 'white',
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：亿美元',
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: '#D6E7F9',
              padding: [0, 0, 20, 0],
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#77b3f1',
                opacity: 0.1,
                width: 2,
              },
            },
            axisTick: {
              show: true,
              lineStyle: {
                color: '#77b3f1',
                opacity: 0.5,
                width: 2,
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '实际利用外资',
            type: 'bar',
            barWidth: '15%',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#CBF2FF',
                  },
                  {
                    offset: 0.5,
                    color: '#00C0FF',
                  },
                  {
                    offset: 1,
                    color: '#004F69',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: [12, 15, 18],
          },
        ],
      }

      myEc.setOption(option)
    },

    getEcharts04(dom, dataNew, length) {
      let myEc = echarts.init(document.getElementById(dom))
      let imgUrl = '../djtl/img/djtl-left/echarts-bg.png'
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: <br/>{d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
        },
        legend: {
          orient: 'vertical',
          itemWidth: 18,
          itemHeight: 18,
          left: '50%',
          top: 'center',
          icon: 'circle',
          itemGap: 45,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 30,
            padding: [0, 0, 0, 20],
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            var p = (tarValue / total) * 100
            return name + '\n\n' + tarValue + '人'
          },
        },
        graphic: [
          {
            z: 4,
            type: 'image',
            id: 'logo',
            left: '10.7%',
            top: '21%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [0.6, 0.6], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['25%', '50%'],
            itemStyle: {
              normal: {
                borderColor: '#0A1934',
                borderWidth: 1,
              },
            },
            label: {
              show: false,
            },
            data: dataNew,
          },
        ],
      }
      myEc.setOption(option)
    },

    /*
     * 清除热力图
     */
    clearHotMapAll() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmhotPowerMap',
          })
        )
      } catch (error) {}
    },
    // 清除地图撒点
    clearPoint() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rmPoint',
            pointId: '',
          })
        )
      } catch (error) {}
    },

    // 清除3D文字
    clear3Dtext() {
      try {
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'rm3Dtext',
          })
        )
      } catch (error) {}
    },
  },
})
