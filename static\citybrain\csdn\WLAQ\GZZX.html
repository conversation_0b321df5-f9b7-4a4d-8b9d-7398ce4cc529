<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>感知中心</title>
  <script src="../Vue/vue.js"></script>
  <script src="../../csdn/echarts/echarts.min.js"></script>
  <script src="../js/datav.min.vue.js"></script>
  <script src="../jquery/jquery-3.6.1.min.js"></script>
  <script src="../axios/axios.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <!--<script src="/static/js/jslib/echarts-wordcloud.min.js"></script>-->
  <!--<link rel="stylesheet" href="/static/css/sigma.css" />-->
  <!-- <script src="/static/js/jslib/umap2d.min.js"></script> -->
  <!--<script src="https://cdn.bootcdn.net/ajax/libs/mapbox-gl/2.7.0/mapbox-gl.min.js"></script>-->
  <!--<link rel="stylesheet" href="/static/css/animate.css" />-->
  <!--<script src="/static/citybrain/hjbh/js/echarts.js"></script>-->
</head>
<style>
  #app {
    width: 3840px;
    height: 2160px;
    background: url("../img/gzzxImg/background.png") no-repeat;
    background-size: cover;
  }

  /*顶部标题*/

  .Title {
    height: 269px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: url("../img/gzzxImg/TopTitle-background.png") no-repeat;
    background-size: cover;
  }

  .Title-leftBtn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 266px;
    height: 53px;
    margin: -100px 800px 0 0;
    cursor: pointer;
  }

  .Title-center {
    font-family: SourceHanSansCN-Bold;
    font-size: 80px;
    font-weight: bold;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    margin-top: -140px;
  }

  .Title-rightBtn {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 266px;
    height: 53px;
    margin: -100px 0 0 800px;
    cursor: pointer;
  }

  .icon {
    width: 53px;
    height: 53px;
    margin-top: 10px;
  }

  .Btn-Text {
    font-family: SourceHanSansCN-Medium;
    font-size: 50px;
    font-weight: 600;
    font-stretch: normal;
    letter-spacing: 0px;
    background: linear-gradient(to bottom, #ebf2ff, #ffffff, #c3d8ee, #94a9db);
    -webkit-background-clip: text;
    color: transparent;
    white-space: nowrap;
  }

  /*底部内容*/

  .container {
    margin-top: -70px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .container-left {
    width: 1000px;
    height: 1940px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .container-center {
    width: 1760px;
    height: 1940px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .container-right {
    width: 1000px;
    height: 1940px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .left1 {
    width: 1000px;
    height: 732px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
  }
  .left2 {
    width: 1000px;
    height: 673px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 20px;
  }
  .left3 {
    width: 1000px;
    height: 495px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 20px;
  }

  .center-Top {
    width: 1754px;
    height: 200px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    background: url("../img/gzzxImg/topbackground.png") no-repeat;
    background-size: cover;
  }

  .center-middle {
    width: 1754px;
    flex: 1;
  }

  .center-bottom {
    width: 1754px;
    height: 550px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
  }

  .right1 {
    width: 1000px;
    height: 510px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
  }
  .right2 {
    width: 1000px;
    height: 840px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 20px;
  }
  .right3 {
    width: 1000px;
    height: 550px;
    background: url("../img/gzzxImg/boxbackground.png") no-repeat;
    background-size: cover;
    margin-top: 20px;
  }

  .box-Title {
    width: 780px;
    height: 70px;
    background: url("../img/gzzxImg/boxTitle.png") no-repeat;
    background-size: cover;
    overflow: hidden;
  }

  .box-Title-Text {
    font-family: SourceHanSansCN-Bold;
    font-size: 40px;
    font-weight: 600;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    margin: 8px 0 0 65px;
  }


  .left1-line1 {
    height: 262px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .line1-left {
    margin-top: 44px;
  }

  .line-left-item {
    height: 94px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .item-left1 {
    width: 92px;
    height: 91px;
    background: url("../img/gzzxImg/icon1.png") no-repeat;
    background-size: cover;
  }

  .item-left2 {
    width: 92px;
    height: 91px;
    background: url("../img/gzzxImg/icon2.png") no-repeat;
    background-size: cover;
  }

  .item-right {
    margin-left: 31px;
  }

  .item-right-name {
    font-family: SourceHanSansCN-Medium;
    font-size: 32px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 2px;
    color: #ffffff;
    white-space: nowrap;
  }

  .item-right-value {
    font-family: BebasNeue;
    font-size: 48px;
    font-weight: 600;
    font-stretch: normal;
    letter-spacing: 0px;
    background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .box-innerTitle {
    width: 308px;
    height: 24px;
    background: url("../img/gzzxImg/boxTitleinner.png") no-repeat;
    background-size: cover;
    margin: 47px 0 0 39px;
  }

  .box-innerTitle-text {
    font-family: SourceHanSansCN-Medium;
    font-size: 36px;
    font-weight: 500;
    font-stretch: normal;
    letter-spacing: 6px;
    color: #ffffff;
    position: relative;
    bottom: 20px;
    left: 40px;
    white-space: nowrap;
  }

  .left2-line1 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 57px;
  }


  .left2-line1-item-left1 {
    width: 116px;
    height: 108px;
    background: url("../img/gzzxImg/icon3.png") no-repeat;
    background-size: cover;
  }

  .left2-line1-item-left2 {
    width: 116px;
    height: 108px;
    background: url("../img/gzzxImg/icon4.png") no-repeat;
    background-size: cover;
  }


  .left2-line2 {
    width: 484px;
    height: 54px;
    line-height: 54px;
    margin: 44px 0 0 259px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #5dbcff;
    border-radius: 10px;
  }

  .left2-line2-item1 {
    width: 242px;
    height: 54px;
    text-align: center;
    font-family: SourceHanSansCN-Regular;
    font-size: 28px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 2px;
    color: #afbdcf;
    border-right: 2px solid #5dbcff;
    cursor: pointer;
  }

  .left2-line2-item2 {
    width: 242px;
    height: 54px;
    text-align: center;
    font-family: SourceHanSansCN-Regular;
    font-size: 28px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 2px;
    color: #afbdcf;
    cursor: pointer;
  }

  .itemActive {
    background: #5dbcff;
    color: #fff;
  }

  .left-line3 {
    height: 262px;
    margin-top: 22px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .left-line3-item-left {
    width: 133px;
    height: 115px;
    background: url("../img/gzzxImg/icon5.png") no-repeat;
    background-size: cover;
  }

  .gold {
    background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
    text-align: left;
  }

  .blue {
    background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .red {
    background: linear-gradient(to bottom, #ffcdcd, #ffffff, #ff4949, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .green {
    background: linear-gradient(to bottom, #caffff, #ffffff, #22e8e8, #91f4f4, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .left3-container {
    width: 100%;
    height: 345px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 40px;
  }

  .icons {
    width: 116px;
    height: 108px;
  }

  .righticons {
    width: 210px;
    height: 164px;
  }

  .right2icons {
    width: 242px;
    height: 197px;
  }

  .rightitem-right {
    width: 219px;
    height: 140px;
    text-align: center;
    background-color: rgba(26,149,227,0.08);
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    border-top: 4px solid #0a9ac8;
  }

  .right1-container {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    margin-top: -20px;
  }

  .right2-container {
    width: 1000px;
    height: 691px;
    margin: 38px 0 0 0;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .right2-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }

  .right2-texts {
    font-family: PangMenZhengDao-3;
    font-size: 60px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 2px;
    color: #ffffff;
    font-style: italic;
    text-align: center;
  }

  .right2-box {
    margin-top: 27px;
    width: 220px;
    height: 404px;
    border-top: 4px solid #0a9ac8;
    background: rgba(26,149,227,.08);
    text-align: center;
  }

  .margin {
    margin-top: 10px;
  }

  .img-line {
    width: 180px;
    height: 4px;
    background-image: linear-gradient(90deg,
    #51c4ff 0%,
    #51c4ff 52%,
    #51c4ff 100%);
    opacity: 0.57;
    margin: 10px 0 0 20px;
  }

  .updown {
    width: 14px;
    height: 20px;
  }

  .unit {
    font-size: 32px;
  }

  .center-bottom-select {
    width: 726px;
    height: 54px;
    border-radius: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    line-height: 54px;
    border: solid 2px #4fc3ff;
    margin: 16px 20px 0 0;
  }

  .mapChangeBtns {
    position: absolute;
    width: 201px;
    height: 200px;
    left: 2600px;
    top: 480px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
  }

  .mapChange-item {
    width: 248px;
    height: 88px;
    background: url("../img/gzzxImg/btn1.png") no-repeat;
    background-size: cover;

    font-family: SourceHanSansCN-Regular;
    font-size: 32px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 108px;
    letter-spacing: 2px;
    color: #d6e7f9;
    text-align: center;
    cursor: pointer;
  }

  .activeMapChoose {
    width: 248px;
    height: 88px;
    background: url("../img/gzzxImg/btn2.png") no-repeat;
    background-size: cover;
    color: #ffe2b0;
  }

  .tooltip {
    width: 419px;
    height: 298px;
    background: url("../img/gzzxImg/tooltip.png") no-repeat;
    background-size: cover;
    padding: 40px;
  }

  .tipline1 {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-family: SourceHanSansCN-Bold;
    font-size: 36px;
    font-weight: 600;
    font-style: italic;
    font-stretch: normal;
    letter-spacing: 2px;
    margin-top: 20px;
    line-height: 35px;
  }

  .tipline {
    width: 400px;
    height: 30px;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 55px;
  }

  .tipline-left {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 240px;
    margin-left: 20px;
  }

  .tipline-right {
    font-family: SourceHanSansCN-Medium;
    font-size: 36px;
    font-style: italic;
    font-stretch: normal;
    letter-spacing: 2px;
    font-weight: 600;
  }

  .mapicon {
    width: 30px;
    height: 30px;
  }

  .tipText {
    font-family: SourceHanSansCN-Medium;
    font-size: 36px;
    font-weight: normal;
    font-style: italic;
    font-stretch: normal;
    letter-spacing: 2px;
    color: #d6e7f9;
  }

</style>

<body>
  <div id="app">
    <div class="Title">
      <div class="Title-leftBtn" @click="pageJump('jcfh')">
        <img src="../img/gzzxImg/leftBtn-icon.png" alt="" class="icon">
        <div class="Btn-Text">监测防护中心</div>
      </div>
      <div class="Title-center">安全感知中心</div>
      <div class="Title-rightBtn" @click="pageJump('aqyy')">
        <img src="../img/gzzxImg/rightBtn-icon.png" alt="" class="icon">
        <div class="Btn-Text">安全运营中心</div>
      </div>
    </div>
    <div class="container">
      <div class="container-left">
        <div class="left1">
          <div class="box-Title">
            <div class="box-Title-Text">云安全态势</div>
          </div>
          <div class="left1-line1">
            <div class="line1-left">
              <div class="line-left-item">
                <div class="item-left1"></div>
                <div class="item-right">
                  <div class="item-right-name">{{cloud.name}}</div>
                  <div class="item-right-value">{{cloud.value}}</div>
                </div>
              </div>
              <div class="line-left-item" style="margin-top: 31px">
                <div class="item-left2"></div>
                <div class="item-right">
                  <div class="item-right-name">{{cloud2.name}}</div>
                  <div class="item-right-value">{{cloud2.value}} <span class="unit">%</span> </div>
                </div>
              </div>
            </div>
            <div id="charts1" style="width: 473px;height: 262px;margin-top: 44px;"></div>
          </div>
          <div class="left1-line2">
            <div class="box-innerTitle">
              <div class="box-innerTitle-text">云安全事件趋势</div>
            </div>
            <div id="charts2" style="width: 913px;height: 270px;margin: 30px 0 0 39px;"></div>
          </div>
        </div>
        <div class="left2">
          <div class="box-Title">
            <div class="box-Title-Text">网络态势</div>
          </div>
          <div class="left2-line1">
            <div class="line-left-item">
              <div class="left2-line1-item-left1"></div>
              <div class="item-right">
                <div class="item-right-name">{{internet1.name}}</div>
                <div class="item-right-value gold">{{internet1.value}} <span class="unit">G</span> </div>
              </div>
            </div>

            <div class="line-left-item">
              <div class="left2-line1-item-left2"></div>
              <div class="item-right">
                <div class="item-right-name">{{internet2.name}}</div>
                <div class="item-right-value gold">{{internet2.value}} <span class="unit">G</span> </div>
              </div>
            </div>
          </div>
          <div class="left2-line2">
            <div class="left2-line2-item1" :class="{itemActive:left2Choose == 0}" @click="changeleft2Choose(0)">设备类型分布</div>
            <div class="left2-line2-item2" :class="{itemActive:left2Choose == 1}" @click="changeleft2Choose(1)">设备高级趋势</div>
          </div>
          <div class="left-line3">
            <div class="left-line3-left">
              <div class="line-left-item">
                <div class="left-line3-item-left"></div>
                <div class="item-right">
                  <div class="item-right-value">{{equipment.value}} <span class="unit">台</span> </div>
                  <div class="item-right-name">{{equipment.name}}</div>
                </div>
              </div>
            </div>
            <div id="charts3" style="width: 532px;height: 262px;"></div>
          </div>
        </div>
        <div class="left3">
          <div class="box-Title">
            <div class="box-Title-Text">终端安全态势</div>
          </div>
          <div class="left3-container">

            <div class="line-left-item" v-for="(item,i) in indexs" :key="i">
              <img :src="item.img" alt="" class="icons">
              <div class="item-right">
                <div class="item-right-name">{{item.name}}</div>
                <div class="item-right-value" :class="{red:item.name == '违规外联',gold:item.name == '未安装杀毒软件'}">{{item.value}}</div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="container-center">
        <div class="center-Top">
          <div class="line-left-item" v-for="(item,i) in topIndexs" :key="i">
            <img :src="item.img" alt="" class="icons">
            <div class="item-right">
              <div class="item-right-name">{{item.name}}</div>
              <div class="item-right-value gold" :class="{red:item.name == '违规外联',gold:item.name == '未安装杀毒软件'}">{{item.value}}</div>
            </div>
          </div>
        </div>
        <div class="center-middle">
          <div id="map" style="width: 1754px;height: 1190px;"></div>
          <div class="mapChangeBtns">
            <div class="mapChange-item" :class="{activeMapChoose:mapChoose == 0}" @click="mapChange(0)">金华地图</div>
            <div class="mapChange-item" :class="{activeMapChoose:mapChoose == 1}" @click="mapChange(1)">网络拓扑</div>
          </div>
        </div>
        <div class="center-bottom">
          <div style="display: flex;justify-content: space-between;align-items: center">
            <div class="box-Title">
              <div class="box-Title-Text">态势分析</div>
            </div>
            <div class="center-bottom-select">
              <div class="left2-line2-item2" @click="changeBottomChoose(1)" :class="{itemActive:bottomChoose == 1}">网络安全态势</div>
              <div class="left2-line2-item2" @click="changeBottomChoose(2)" :class="{itemActive:bottomChoose == 2}">实时预警态势</div>
              <div class="left2-line2-item2" @click="changeBottomChoose(3)" :class="{itemActive:bottomChoose == 3}">风险预警态势</div>
            </div>
          </div>

          <div id="charts4" style="width: 1760px;height: 480px;"></div>
        </div>
      </div>
      <div class="container-right">
        <div class="right1">
          <div class="box-Title">
            <div class="box-Title-Text">数据安全态势</div>
          </div>
          <div class="right1-container">
            <div class="line-left-item" v-for="(item,i) in right1" :key="i" style="margin-top: 80px">
              <img :src="item.img" alt="" class="righticons">
              <div class="rightitem-right">
                <div class="item-right-name">{{item.name}}</div>
                <div class="item-right-value" :class="{red:item.name == '违规外联',gold:item.name == '未安装杀毒软件'}">{{item.value}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="right2">
          <div class="box-Title">
            <div class="box-Title-Text">应用情况</div>
          </div>
          <div class="right2-container">
            <div class="right2-item" v-for="(item,i) in right2" :key="i">
              <img :src="item.img" alt="" class="right2icons">
              <div class="right2-texts">{{item.title}}</div>
              <div class="right2-box">
                <div class="item-right-name margin">{{item.name1}}</div>
                <div class="item-right-value">{{item.value1}} <span class="unit">个</span> </div>
                <div class="img-line"></div>
                <div class="item-right-name margin">{{item.name2}}</div>
                <div class="item-right-value" :class="{green:item.up == true,red:item.up == false}">
                  {{item.value2}}
                  <img src="../img/gzzxImg/up.png" alt="" v-if="item.up" class="updown">
                  <img src="../img/gzzxImg/down.png" alt="" v-else class="updown">
                </div>
                <div class="img-line"></div>
                <div class="item-right-name margin">{{item.name3}}</div>
                <div class="item-right-value red">{{item.value3}} <span class="unit">个</span> </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right3">
          <div class="box-Title">
            <div class="box-Title-Text">视频共享态势</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    var vm = new Vue({
      el: '#app',
      data:{

        topIndexs:[
          {name:"检测单位",value:"",img:"../img/gzzxImg/topicon1.png"},
          {name:"检测系统",value:"",img:"../img/gzzxImg/topicon2.png"},
          {name:"检测IP",value:"",img:"../img/gzzxImg/topicon3.png"},
          {name:"安全设备",value:"",img:"../img/gzzxImg/topicon4.png"},
          {name:"数据资产",value:"",img:"../img/gzzxImg/topicon5.png"}],

        cloud:{
          name:"云实例数",
          value: ""
        },
        cloud2:{
          name:"云平台可用率",
          value: ""
        },
        charts1Data:[],
        charts2Data:[],
        internet1:{
          name:"互联网出流量",
          value:""
        },
        internet2:{
          name:"互联网核心网络流量",
          value:""
        },
        left2Choose:0,
        equipment:{
          name:"安全设备数",
          value:0
        }, //安全设备数
        charts3Data:[], //网络态势图表
        indexs:[{name:"普通PC",value:"",img:"../img/gzzxImg/icon6.png"},{name:"信创PC",value:"",img:"../img/gzzxImg/icon7.png"},
          {name:"移动终端",value:"",img:"../img/gzzxImg/icon8.png"},{name:"未安装杀毒软件",value:"",img:"../img/gzzxImg/icon9.png"},
          {name:"违规外联",value:"",img:"../img/gzzxImg/icon10.png"}], //终端安全态势


        right1:[{name:"ECS",value:"",img:"../img/gzzxImg/icon11.png"},
          {name:"敏感数据库",value:"",img:"../img/gzzxImg/icon12.png"},
          {name:"高频预警",value:"",img:"../img/gzzxImg/icon13.png"},
          {name:"ODPS导出",value:"",img:"../img/gzzxImg/icon14.png"}], //数据安全态势
        right2:[
          {title:"上云应用",name1:"系统总数",name2:"本日新增",name3:"漏洞总数",value1:"",value2:"",value3:"",up:true,img:"../img/gzzxImg/icon15.png"},
          {title:"政务外网",name1:"系统总数",name2:"本日新增",name3:"漏洞总数",value1:"",value2:"",value3:"",up:false,img:"../img/gzzxImg/icon16.png"},
          {title:"互联网",name1:"系统总数",name2:"本日新增",name3:"漏洞总数",value1:"",value2:"",value3:"",up:false,img:"../img/gzzxImg/icon17.png"},
        ], //应用态势
        bottomChoose:1,
        charts4Data:[], //态势分析
        mapChoose:0,
        mapData:[
          {
            name:"婺城区",
            value:111,
            value2:55,
          },
          {
            name:"金东区",
            value:50,
            value2:55,
          },
          {
            name:"兰溪市",
            value:10,
            value2:55,
          },
          {
            name:"义乌市",
            value:80,
            value2:55,
          },
          {
            name:"永康市",
            value:140,
            value2:55,
          },
          {
            name:"武义县",
            value:42,
            value2:55,
          },
          {
            name:"磐安县",
            value:111,
            value2:55,
          },
          {
            name:"浦江县",
            value:11,
            value2:55,
          },
          {
            name:"东阳市",
            value:22,
            value2:55,
          },
        ]
      },
      watch:{},
      computed: {},
      mounted() {
        this.getApiData()
        this.changeBottomChoose(this.bottomChoose)
        this.init()
      },
      methods: {
        dataAddImg(oldData,apiData) {
          let result = apiData
          result.forEach((item,i) => {
            oldData.forEach((item2,j) => {
              if (item.label == item2.name) {
                item.img = item2.img
              }
            })
          })
          return result
        },
        getApiData() {
          //顶部指标
          $api("wlaq_aqgz_C11").then(res => {
            let result = res
            result.forEach((item,i) => {
              this.topIndexs.forEach((item2,j) => {
                if (item.name == item2.name) {
                  item.img = item2.img
                }
              })
            })
            this.topIndexs = result
          })
          //安全态势与网络态势
          $api("wlaq_aqgz_L11").then(res => {
            res.forEach((item,i) => {
              if (item.name == '云实例数') {
                this.cloud = {name:item.name,value:item.value}
              }
              if (item.name == '云平台可用率') {
                this.cloud2 = {name:item.name,value:item.value}
              }
              if (item.name == '互联网出流量') {
                this.internet1 = {name:item.name,value:item.value}
              }
              if (item.name == '互联网核心网络流量') {
                this.internet2 = {name:item.name,value:item.value}
              }
            })
          })
          //安全态势图表
          $api("wlaq_aqgz_L12").then(res => {
            res.forEach((item,i) => {
              this.equipment.value += Number(item.num)
            })
            this.charts1Data = res.map(item => {
              return {name:item.label,value:item.num}
            })
            this.showMyEcharts1()
          })
          //安全事件趋势图表
          $api("wlaq_aqgz_L12").then(res => {
            this.charts2Data = res.map(item => {
              return {name:item.label,value:item.num}
            })
            this.showMyEcharts2()
          })
          //网络态势图表
          $api("wlaq_aqgz_L21").then(res => {
            this.charts3Data = res.map(item => {
              return {name:item.label,value:item.num}
            })
            this.showMyEcharts3()
          })
          //终端安全态势
          $api("wlaq_aqgz_L31").then(res => {
            this.indexs = this.dataAddImg(this.indexs,res).map(item => {
              return {name:item.label,value:item.num,img:item.img}
            })
          })
          //数据安全态势
          $api("wlaq_aqgz_R11").then(res => {
            this.right1 = this.dataAddImg(this.right1,res).map(item => {
              return {name:item.label,value:item.num,img:item.img}
            })
          })
          //应用态势
          $api("wlaq_aqgz_R21").then(res => {
            console.log(res);
            res.forEach((item,i) => {
              if (item.label == "政务云-系统总数") {
                this.right2[0].value1 = item.num
              }
              if (item.label == "政务云-本日新增") {
                this.right2[0].value2 = item.num
              }
              if (item.label == "政务云-漏洞总数") {
                this.right2[0].value3 = item.num
              }
              if (item.label == "政务外网-系统总数") {
                this.right2[1].value1 = item.num
              }
              if (item.label == "政务外网-本日新增") {
                this.right2[1].value2 = item.num
              }
              if (item.label == "政务外网-漏洞总数") {
                this.right2[1].value3 = item.num
              }
              if (item.label == "互联网-系统总数") {
                this.right2[2].value1 = item.num
              }
              if (item.label == "互联网-本日新增") {
                this.right2[2].value2 = item.num
              }
              if (item.label == "互联网-漏洞总数") {
                this.right2[2].value3 = item.num
              }
            })
          })
        },
        pageJump(value) {
          if (value == 'jcfh') {
            location.href = '/static/citybrain/csdn/WLAQ/jcfhzx.html'
          } else {
            location.href = '/static/citybrain/csdn/WLAQ/YYZX.html'
          }
        },
        changeleft2Choose(i) {
          this.left2Choose = i
        },
        changeBottomChoose(i) {
          this.bottomChoose = i
          $api("wlaq_aqgz_C31").then(res => {
            if (i == 1) {
              this.charts4Data = res.filter(item => item.code === "1");
              this.showMyEcharts4()
            }
            if (i == 2) {
              this.charts4Data = res.filter(item => item.code === "2");
              this.showMyEcharts4()
            }
            if (i == 3) {
              this.charts4Data = res.filter(item => item.code === "3");
              this.showMyEcharts4()
            }
          })
        },
        mapChange(i) {
          this.mapChoose = i
        },
        init() {
          //云安全态势玫瑰图
          this.showMyEcharts1();
          //云安全态势折线图
          this.showMyEcharts2();
          //网络态势玫瑰图
          this.showMyEcharts3();
          //态势分析折线图
          this.showMyEcharts4();
          //地图
          this.showMapCharts();
        },
        showMapCharts() {
          let that = this
          let mCharts = echarts.init(document.getElementById('map'));
          $.get('/static/citybrain/csdn/map/map.json',(res)=>{
            echarts.registerMap('JinHuaMap',res)
            let option = {
              tooltip: {//hover后显示的数据配置
                trigger: 'item',
                backgroundColor: 'transparent',
                borderColor:'transparent',
                extraCssText:'box-shadow: unset',
                show: true,
                formatter: function(data){
                  let tips =`
                      <div class="tooltip">
                          <div class="tipline1 gold"> ${data.data.name}</div>
                          <div class="tipline">
                            <div class="tipline-left">
                              <img src="../img/gzzxImg/mapicon1.png" alt="" class="mapicon">
                              <div class="tipText">安全事件:</div>
                            </div>
                            <div class="tipline-right red">${data.data.value}</div>
                          </div>
                          <div class="tipline">
                            <div class="tipline-left">
                              <img src="../img/gzzxImg/mapicon2.png" alt="" class="mapicon">
                              <div class="tipText">风险隐患:</div>
                            </div>
                            <div class="tipline-right gold">${data.data.value2}</div>
                          </div>
                      </div>
                  `;
                  return tips;
                }
              },
              geo: [{//第一层地图默认颜色占比大小配置
                map: 'JinHuaMap',
                aspectScale: 1,
                roam: false, //是否允许缩放
                //zoom: 1.1, //默认显示级别
                layoutSize: '95%',
                layoutCenter: ['50%', '50%'],
                itemStyle: {
                  areaColor: 'transparent',
                  borderColor: '#37C1FD',
                  borderWidth: 4,
                },
                label: {
                  show: true,
                  fontSize:30,
                  color: '#fff'
                },
                emphasis: {
                  itemStyle: {
                    areaColor: '#0160AD'
                  },
                  label: {
                    //hover字体配置
                    show: true,
                    color: '#fff'
                  }
                },
                zlevel: 3,
              },
                {//底部第二层地图占比大小配置
                  map: 'JinHuaMap',
                  aspectScale: 1,
                  roam: false, //是否允许缩放
                  //zoom: 1.1, //默认显示级别
                  layoutSize: '95%',
                  layoutCenter: ['50%', '52%'],
                  itemStyle: {
                    areaColor: '#005DDC',
                    borderColor: '#329BF5',
                    borderWidth: 2,
                  },
                  zlevel: 1,
                  silent: true,
                }],
              series:[
                {
                  data: this.mapData,
                  geoIndex: 0,
                  type:'map'
                }
              ],
              visualMap:{
                min:0,
                max:120,
                inRange:{
                  color:['white', '#0160AD'], //控制颜色渐变的范围
                },
                calculable: true //出现滑块
              }
            }
            mCharts.setOption(option)
          })
        },
        showMyEcharts1() {
          let myChart = echarts.init(document.getElementById("charts1"));
          let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
          let option = {
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            legend: {
              orient: 'vertical',
              left: '60%',
              top: '15%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 20,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 30,
                    color: '#ffffff',
                    padding: [0, 20, 0, 0]
                  },
                  value: {
                    fontSize: 30,
                    color: '#e9d0ab',
                    padding: [0, 0, 0, 0]
                  },
                }
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                // var p = ((tarValue / total) * 100).toFixed(2)
                // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
                return '{name|' + name + '}{value|' + tarValue + '}'
              },
            },
            graphic: [
              {
                type: "image",
                id: "logo",
                left: "-0.5%",
                top: "-5%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [50, 50], //中心点
                scale: [0.2, 0.2], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: '',
                type: 'pie',
                radius: ['40%', '90%'],
                center: ['30%', '50%'],
                roseType: 'area',
                itemStyle: {
                  borderRadius: 0,
                },
                label: {
                  show: false,
                },
                data: this.charts1Data,
              },
            ],
          }
          myChart.setOption(option)
        },
        showMyEcharts2() {
          let myChart2 = echarts.init(document.getElementById("charts2"));
          let option = {
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            grid:{
              top:10,
              left:40,
              right:0,
              bottom:30
            },
            xAxis: {
              type: 'category',
              data: this.charts2Data.map(item => item.name),
              nameTextStyle:{
                color:'#d6e7f9'
              },
              axisLine:{
                lineStyle: {
                  color: '#77b3f1'
                }
              },
              axisLabel: {
                color:'#d6e7f9',
                fontSize:27
              }
            },
            yAxis: {
              type: 'value',
              axisLabel: {
                color:'#d6e7f9',
                fontSize:20
              }
            },
            series: [
              {
                type: 'line',
                data: this.charts2Data.map((item) => item.value),
                color: "rgba(255,255,255,.2)",
                symbolSize: 10,
                itemStyle:{
                  normal:{
                    lineStyle:{
                      color:'#ffc460',
                    }
                  }
                }
              },
            ],
          }
          myChart2.setOption(option)
        },
        showMyEcharts3() {
          let myChart = echarts.init(document.getElementById("charts3"));
          let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
          let option = {
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            legend: {
              orient: 'vertical',
              left: '55%',
              top: '15%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 20,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 24,
                    color: '#ffffff',
                    padding: [0, 4, 0, 0]
                  },
                  value: {
                    fontSize: 24,
                    color: '#e9d0ab',
                    padding: [0, 0, 0, 0]
                  },
                }
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                // var p = ((tarValue / total) * 100).toFixed(2)
                // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
                return '{name|' + name + '}{value|' + tarValue + '}'
              },
            },
            graphic: [
              {
                type: "image",
                id: "logo",
                left: "14.5%",
                top: "18%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [50, 50], //中心点
                scale: [0.3, 0.3], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: '',
                type: 'pie',
                radius: ['40%', '90%'],
                center: ['30%', '50%'],
                roseType: 'area',
                itemStyle: {
                  borderRadius: 0,
                },
                label: {
                  show: false,
                },
                data: this.charts3Data,
              },
            ],
          }
          myChart.setOption(option)
        },
        showMyEcharts4() {
          let myChart2 = echarts.init(document.getElementById("charts4"));
          let option = {
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            grid:{
              top:90,
              left:150,
              right:20,
              bottom:50
            },
            legend:{
              x:'right',
              y:'top',
              data:['攻击','阻断'],
              padding:[40,40,0,0],
              textStyle:{
                color:"#d6e7f9",
                fontSize:28
              }
            },
            xAxis: {
              type: 'category',
              data: this.charts4Data.map((item) => item.name),
              nameTextStyle:{
                color:'#d6e7f9'
              },
              axisLine:{
                lineStyle: {
                  color: '#77b3f1'
                }
              },
              axisLabel: {
                color:'#d6e7f9',
                fontSize:25
              }
            },
            yAxis: {
              name:"单位: 次",
              nameTextStyle:{
                padding:[0,40,20,0],
                color:'#d6e7f9',
                fontSize:28
              },
              type: 'value',
              axisLabel: {
                color:'#d6e7f9',
                fontSize:25
              }
            },
            series: [
              {
                name:'攻击',
                type: 'line',
                data: this.charts4Data.map((item) => item.value1),
                color: "rgba(255,255,255,.2)",
                symbolSize: 10,
                itemStyle:{
                  normal:{
                    lineStyle:{
                      color:'#00c0ff',
                    }
                  }
                }
              },
              {
                name:'阻断',
                type: 'line',
                data: this.charts4Data.map((item) => item.value2),
                color: "rgba(255,255,255,.2)",
                symbolSize: 10,
                itemStyle:{
                  normal:{
                    lineStyle:{
                      color:'#ffc460',
                    }
                  }
                }
              },
            ],
          }
          myChart2.setOption(option)
        },
      },
    })
  </script>


</body>

</html>
