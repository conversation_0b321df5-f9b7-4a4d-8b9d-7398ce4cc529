<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>城市体征</title>
    <link rel="shortcut icon" href="#" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <link rel="stylesheet" href="/static/css/xiaoguo.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/css/cstz4-right.css" />
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
  </head>

  <body>
    <div id="app">
      <div class="main main-1">
        <div class="top">
          <p class="s-c-blue-gradient pointer" @click="openIframe('安全有序')">安全有序</p>
        </div>
        <div class="bottom">
          <div class="box">
            <div class="left">
              <div class="left-box">
                <div class="left-content">
                  <p v-for="(item,index) in leftData" :key="index">
                    <span class="name-css">{{item.name}}</span>
                    <span class="value-css s-c-blue-gradient1">{{item.value}}</span>
                    <span class="unit-css s-c-blue-gradient1">{{item.unit}}</span>
                  </p>
                </div>
                <div class="icon-img">
                  <img src="/static/citybrain/csdn/img/cstz4-right/icon1.png" alt="" />
                </div>
              </div>
              <div class="left-box">
                <div class="left-content">
                  <div v-for="(item,index) in leftData2" style="margin-bottom: 10px">
                    <span class="name-css">{{item.name}}</span>
                    <div :id="`echarts${index}`" style="width: 200px; height: 40px"></div>
                  </div>
                </div>
                <div class="icon-img">
                  <img src="/static/citybrain/csdn/img/cstz4-right/icon2.png" alt="" />
                </div>
              </div>
            </div>
            <div class="center">
              <div class="lineOut"></div>
              <div class="lineIn"></div>
              <count-to :start-val="10" :end-val="startNum" :duration="3000" style="margin-left: 56px"></count-to>
            </div>
            <div class="right">
              <div class="left-box">
                <div class="icon-img">
                  <img src="/static/citybrain/csdn/img/cstz4-right/icon3.png" alt="" />
                </div>
                <div class="left-content">
                  <p v-for="(item,index) in rightData" :key="index">
                    <span class="name-css">{{item.name}}</span>
                  </p>
                </div>
              </div>
              <div class="left-box">
                <div class="icon-img">
                  <img src="/static/citybrain/csdn/img/cstz4-right/icon4.png" alt="" />
                </div>
                <div class="left-content">
                  <div id="echarts2" style="width: 220px; height: 180px; margin-top: 20px"></div>
                  <span class="name-css">日用电量</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script>
  var vm = new Vue({
    el: '#app',
    data() {
      return {
        leftData: [
          {
            name: '实时人口数量',
            value: '',
            unit: '',
          },
          {
            name: '常住人口数量',
            value: '',
            unit: '',
          },
        ],
        leftData2: [
          {
            name: '城镇化率',
            value: 0,
          },
          {
            name: '文盲率',
            value: 0.36,
          },
        ],
        rightData: [
          {
            name: '专业救援队伍',
          },
          {
            name: '应急避难场所',
          },
          {
            name: '综合救援队伍',
          },
          {
            name: '骨干救援队伍',
          },
          {
            name: '重大救援装备',
          },
        ],
        rightData2: {
          name: '日用电量',
        },
        setInterval: null,
        startNum: 0,
        num: 0,
      }
    },
    mounted: function () {
      $api('/cstz_zbs_info', { categoryId: 88 }).then((res) => {
        this.leftData[0].value = res[0].currentValue
        this.leftData[0].unit = res[0].unit
      })
      $api('/cstz_zbs_info', { categoryId: 90 }).then((res) => {
        // 常住人口
        this.leftData[1].value = res[0].currentValue
        this.leftData[1].unit = res[0].unit
        // 城镇化率
        this.leftData2[0].value = res[8].currentValue
        this.getEcharts1('echarts0', '#17B9AD', this.leftData2[0])
        // 文盲率
        this.leftData2[1].value = res[7].currentValue
        this.getEcharts1('echarts1', '#1588D1', this.leftData2[1])
      })
      $api('/cstz_zbs_zbmxhis', { id: 816 }).then((res) => {
        this.getEcharts2('echarts2', res)
      })
      $api('/cstz_gy_zbtj').then((res) => {
        this.startNum = res[2].val
        this.num = res[2].val
      })

      this.setInterval = setInterval(() => {
        this.startNum = 10
        setTimeout(() => {
          this.startNum = this.num
        }, 100)
      }, 10000)
    },
    methods: {
      openIframe(name) {
        let leftData = {
          type: 'openIframe',
          name: 'djtl-page',
          src: '/static/citybrain/djtl/pages/djtl-page.html',
          width: '7680px',
          height: '2005px',
          left: '0',
          top: '115px',
          zIndex: 100,
          argument: {
            name: name,
            type: '城市体征下钻',
          },
        }
        top.postMessage(JSON.stringify(leftData), '*')
      },
      getEcharts1(dom, color, category) {
        let myEc = echarts.init(document.getElementById(dom))
        var total = 100 // 数据总数
        var datas = [category]
        let option = {
          tooltip: {
            show: true,
            trigger: 'item',
            textStyle: {
              fontSize: 24,
              color: '#fff',
            },
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
            borderWidth: 0,
            formatter: function (parame) {
              return category.name + ' : ' + category.value + '%'
            },
          },
          xAxis: {
            max: total,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          grid: {
            top: '80px',
            left: 0,
            right: 0,
          },
          yAxis: [
            {
              type: 'category',
              inverse: false,
              data: category,
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
            },
          ],
          series: [
            {
              // 内
              type: 'bar',
              barWidth: 25,
              silent: true,
              itemStyle: {
                normal: {
                  color: color,
                },
              },
              data: datas,
              z: 1,
              animationEasing: 'elasticOut',
            },
            {
              // 分隔
              type: 'pictorialBar',
              itemStyle: {
                normal: {
                  color: '#30577A',
                },
              },
              symbolRepeat: 'fixed',
              symbolMargin: 2,
              symbol: 'rect',
              symbolClip: true,
              symbolSize: [3, 28],
              symbolPosition: 'start',
              symbolOffset: [3, -4],
              symbolBoundingData: this.total,
              data: [total],
              z: 2,
              animationEasing: 'elasticOut',
            },
            {
              name: '外框',
              type: 'bar',
              barGap: '-130%', // 设置外框粗细
              data: [total],
              barWidth: 40,
              itemStyle: {
                normal: {
                  barBorderRadius: [5, 5, 5, 5],
                  color: 'transparent', // 填充色
                  barBorderColor: color, // 边框色
                  barBorderWidth: 3, // 边框宽度
                },
              },
              z: 0,
            },
          ],
        }
        myEc.setOption(option)
      },
      getEcharts2(dom, res) {
        let myEc = echarts.init(document.getElementById(dom))

        var datas = res.map((item) => {
          return item.val_y
        })

        let option = {
          tooltip: {
            show: true,
            trigger: 'item',
            textStyle: {
              fontSize: 24,
              color: '#fff',
            },
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
            borderWidth: 0,
            // formatter: function (parame) {
            //   return category.name + ' : ' + category.value + '%'
            // },

            formatter: '{b}: {c}万千瓦时',
          },
          xAxis: {
            data: ['日用电量', '日用电量', '日用电量', '日用电量'],
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          yAxis: {
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          visualMap: {
            show: false,
            dimension: 1,
            pieces: [
              {
                gte: -20, // 获取最小值
                lte: 0,
                color: 'fff',
                colorAlpha: 1,
              },
            ], //pieces的值由动态数据决定
            seriesIndex: 1,
            outOfRange: {
              color: '#fff',
              colorAlpha: 1,
            },
          },
          grid: {
            top: 20,
            left: 50,
            right: 0,
            bottom: 0,
          },
          series: [
            {
              name: '邮件营销',
              type: 'line',
              stack: '总量',
              smooth: true,

              itemStyle: {
                borderColor: 'yellow',
                color: '#fff',
              },
              areaStyle: {
                color: 'rgba(160,180,200,0)',
              },
              lineStyle: {},
              data: datas,
            },
          ],
        }

        myEc.setOption(option)
      },
    },
    beforeDestroy() {
      clearInterval(this.setInterval)
    },
  })
</script>
