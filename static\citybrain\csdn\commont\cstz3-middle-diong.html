<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.min.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/citybrain/hjbh/js/date.js"></script>
  <style>
    html,
    body {
      padding: 0;
      margin: 0;
    }

    ::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 4px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 4px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .container {
      width: 2602px;
      height: 1434px;
      background: url('/static/citybrain/csdn/img/cstz3/new/back.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
    }

    .close {
      position: absolute;
      right: 40px;
      top: 50px;
      font-size: 60px;
      color: white;
      font-weight: bold;
    }

    .top {
      width: 100%;
      height: 93px;
      line-height: 93px;
      background: url('/static/citybrain/csdn/img/cstz3/new/top-back.png') no-repeat;
      background-size: 100% 100%;
      text-align: center;
      font-size: 60px;
      font-weight: bold;
      color: #ffffff;
      position: relative;
      top: 50px;
    }

    .title {
      width: 100%;
      text-align: center;
      font-size: 50px;
      color: #ffffff;
      margin: 100px auto 30px;
    }

    .title-img {
      margin-bottom: -7px;
    }

    .contant {
      width: 100%;
      height: 1090px;
      display: flex;
      padding: 0px 50px;
      box-sizing: border-box;
    }

    .con-left {
      width: 50%;
    }

    .con-right {
      width: 50%;
    }

    .left-box {
      padding: 0px 150px;
      box-sizing: border-box;
    }

    .grid-item {
      margin-top: 40px;
      display: flex;
    }

    .grid-item>img {
      width: 31px;
      height: 31px;
      margin-top: 13px;
    }

    .grid-item>span {
      margin-left: 30px;
      white-space: nowrap;
    }

    .grid-item>div {
      margin-left: 30px;
    }

    .mouse-pointer {
      cursor: pointer;
    }

    .mouse-not {
      cursor: not-allowed;
    }

    .auto {
      max-height: 230px;
      overflow-y: auto;
    }

    /*色卡*/
    .color-card {
      width: 662px;
      height: 100px;
      /* background: rgba(12, 38, 65, 0.9);
      border: 1px solid; */
      /* padding: 18px 0px 0px 24px; */
      box-sizing: border-box;
      position: relative;
      top: -27px;
    }

    .color {
      width: 660px;
      height: 35px;
      background: linear-gradient(92deg,
          #caf681 0%,
          #ffd053 25%,
          #ffd053 40%,
          #ffd053 45%,
          #ffa144 52%,
          #ed1f33 80%,
          #fd0100 88%);
      /* opacity: 0.8; */
      position: absolute;
      top: 55px;
      border-radius: 20px;
    }

    .number {
      width: 451px;
      height: 20px;
      justify-content: space-between;
      display: flex;
      /* display: flex; */
      position: absolute;
      top: 35px;
      left: 80px;
    }

    .bg {
      width: 12px;
      height: 31px;
      display: block;
      background: url(/static/citybrain/csdn/img/cstz/下三角.png);
      background-size: 100% 100%;
    }

    .kd {
      position: absolute;
      top: -29px;
      font-size: 24px;
    }

    .desc {
      font-size: 24px;
      color: #fff;
      position: absolute;
      left: -40px;
      top: 19px;
      line-height: 35px;
    }
  </style>
</head>

<body>
  <div id="cstz3-middle-diong">
    <div class="container">
      <div class="top">指标详情</div>
      <div class="close mouse-pointer" @click="backMiddle"><i class="el-icon-close"></i></div>
      <div class="title">
        <img class="title-img" src="/static/citybrain/csdn/img/cstz3/new/btn-left.png" alt="" />
        <span>{{listData1.name || "-"}}</span>
        <span v-show="listData1.name!='信用风险偏高企业占比'" class="s-c-yellow-gradient">
          {{listData1.value || "-"}}
        </span>
        <span v-show="listData1.name!='信用风险偏高企业占比'" class="s-c-yellow-gradient">
          {{listData1.unit || "-"}}
        </span>
        <span v-show="listData1.name=='信用风险偏高企业占比'" class="s-c-yellow-gradient">
          {{dangerValue || "-"}}
        </span>
        <img class="title-img" src="/static/citybrain/csdn/img/cstz3/new/btn-right.png" alt="" />
      </div>
      <div class="contant">
        <div class="con-left">
          <nav style="padding: 30px 160px 0">
            <s-header-title-2 title="基本信息" htype="2" />
          </nav>
          <div class="left-box">
            <div class="grid-item" v-for="(el,index) in listData2" :key="index">
              <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="" />
              <span class="s-c-blue-gradient s-font-40">{{el.name || "-"}}</span>
              <!-- 空气质量级别css样式，暂时用图片 -->
              <!-- <div class="color-card" v-if="index==6 && listData1.name=='空气质量指数（AQI)'">
                <div class="color"></div>
                <div class="number">
                  <div class="bg">
                    <div style="color: #f8d559;left: -8px;" class="kd">50</div>
                    <div class="desc">优</div>
                  </div>
                  <div class="bg">
                    <div style="color: #ffd053;left: 95px;" class="kd">100</div>
                    <div class="desc" style="left: 45px;">良</div>
                  </div>
                  <div class="bg">
                    <div style="color: #ffd053;left: 204px;" class="kd">150</div>
                    <div class="desc" style="left: 123px;">轻度污染</div>
                  </div>
                  <div class="bg">
                    <div style="color: #fc8841;left: 314px;" class="kd">200</div>
                    <div class="desc" style="left: 231px;">中度污染</div>
                  </div>
                  <div class="bg">
                    <div style="color: #f24038;left: 425px;" class="kd">250</div>
                    <div class="desc" style="left: 343px;">重度污染</div>
                    <div class="desc" style="left: 458px;width: 100px;">严重污染</div>
                  </div>
                </div>
              </div> -->
              <div v-if="index==6 && listData1.name=='空气质量指数（AQI)'">
                <img src="/static/citybrain/csdn/img/cstz/yjjb.png" alt="" />
              </div>
              <div class="s-c-white s-font-40" :class="index===0 ? 'auto' : ''" v-else>{{el.value || "-"}}</div>
            </div>
            <!-- <div class="grid-item">
                        <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="">
                        <span class="s-c-blue-gradient s-font-40">指标定义：</span>
                        <div class="s-c-white  s-font-40" style="max-height: 230px;overflow-y: auto;">{{item.description || ""}}</div>
                    </div>
                    <div class="grid-item">
                        <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="">
                        <span class="s-c-blue-gradient s-font-40">责任部门：</span>
                        <div class="s-c-white  s-font-40">{{item.source_dept || "-"}}</div>
                    </div>
                    
                    <div class="grid-item">
                        <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="">
                        <span class="s-c-blue-gradient s-font-40">业务责任人：</span>
                        <div class="s-c-white  s-font-40">{{"-"}}</div>
                    </div>
                    <div class="grid-item">
                        <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="">
                        <span class="s-c-blue-gradient s-font-40">分管领导：</span>
                        <div class="s-c-white  s-font-40">{{"-"}}</div>
                    </div>
                    <div class="grid-item">
                        <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="">
                        <span class="s-c-blue-gradient s-font-40">更新频率：</span>
                        <div class="s-c-white  s-font-40">{{item.update_freq || "-"}}</div>
                    </div>
                    <div class="grid-item">
                        <img src="/static/citybrain/csdn/img/cstz3/new/icon.png" alt="">
                        <span class="s-c-blue-gradient s-font-40">数据更新时间：</span>
                        <div class="s-c-white  s-font-40">{{item.update_time || "-"}}</div>
                    </div> -->
          </div>
        </div>
        <div class="con-right">
          <nav style="padding: 30px 160px 0">
            <s-header-title-2 title="指标趋势图" />
          </nav>
          <div class="right-box">
            <!-- <img src="/static/citybrain/csdn/img/cstz3/new/right.png" alt="" style="margin: 200px 0px 0px 50px;"> -->
            <div id="chart" style="width: 1200px; height: 600px; margin-top: 100px"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
<script>
  var vm = new Vue({
    el: '#cstz3-middle-diong',
    data: {
      id: '',
      item: [],
      listData1: [],
      listData2: [],
      dangerValue: '',
      emptyData: [
        {
          name: '指标定义',
          value: '--'
        },
        {
          name: '责任部门',
          value: '--'
        },
        {
          name: '所属系统',
          value: '--'
        },
        {
          name: '业务责任人',
          value: '--'
        },
        {
          name: '分管领导',
          value: '--'
        }, {
          name: '更新频率',
          value: '--'
        },
        {
          name: '阈值标准',
          value: '--'
        },
        {
          name: '阈值结果',
          value: '--'
        },
        {
          name: '数据更新时间',
          value: '--'
        }
      ],
      zbqstData: []
    },
    mounted() {
      let that = this

      window.addEventListener('message', function (event) {
        if ((event.data.status = 'showEcharts' && event.data.id)) {
          that.id = event.data.id
          $api('qyxyfx', { result: 3 }).then((res) => {
            that.dangerValue = res[0].percentage
          })
          that.queryListData(that.id)
          setTimeout(() => {
            that.queryChartData(that.id)
          }, 1400)
        }
      })
    },
    methods: {
      //回到中心页面
      backMiddle() {
        top.window.emiter.emit(top.EventType.szfzEmit, true)
        top.commonObj.showMap()
        top.commonObj.funCloseIframe({
          name: 'cstz3-middle-diong',
        })
      },
      queryListData(id) {
        $api('/cstz_zbs_zbmx', { id: id }).then((res) => {
          this.listData1 = []
          this.listData2 = []
          this.item = res
          this.listData1 = this.item[0].value
          this.listData2 = this.item[1].value ? this.item[1].value : this.emptyData
        })
      },
      queryChartData(id) {
        $api('/cstz_zbs_zbmxhis', { id: id }).then((res) => {
          if (res) {
            if (this.listData1.name === '信用风险偏高企业占比') {
              let item = [
                {
                  val_x: '2022.08.01',
                  val_y: '1.3',
                },
                {
                  val_x: '2022.09.01',
                  val_y: '0.45',
                },
                {
                  val_x: '2022.10.01',
                  val_y: '0.8',
                },
                {
                  val_x: '2022.11.01',
                  val_y: '1.2',
                },
                {
                  val_x: '2022.12.01',
                  val_y: this.dangerValue.split('%')[0],
                },
              ]
              this.getChart(item)
            } else if (this.listData1.name === '冷链主体登记数') {
              this.getChart(res.slice(0, 3))
            } else {
              this.zbqstData = res;
              this.getChart(res)
            }
          }
        })
      },
      getChart(item) {
        let xData = [],
          yData = []
        item.forEach((el) => {
          xData.push(el.val_x)
          // yData.push(el.val_y)
          yData.push([el.val_x, el.val_y, el.threshold_result || '000'])
        })
        // let times=[]
        // for (let i = 7; i >0; i--) {
        //     const yesterday = moment().subtract(i, 'days').format('M月D日')
        //     times.push(yesterday)
        // }
        // let unit=item.unit||"-"
        let myEcharts = echarts.init(document.getElementById('chart'))
        let option = {
          color: ['#e86056', '#F5CC53', '#6AE4B2'],
          title: {
            show: item.length == 0 ? true : false, // 无数据时展示 title
            textStyle: {
              color: '#fff',
              fontSize: 35
            },
            text: '暂无数据',
            left: 'center',
            top: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          legend: {
            selectedMode: false,
            top: '0px',
            right: '24%',
            textStyle: {
              color: '#fff',
              fontSize: 28,
              fontFamily: 'SourceHanSansCN-Medium',
            },
            itemWidth: 18,
            itemHeight: 8,
          },
          grid: {
            top: '20%',
            left: '10%',
            right: '5%',
            bottom: '2%',
            containLabel: true,
          },
          // graphic: {
          //   z: 4,
          //   type: 'text',     // 类型：文本
          //   left: 'center',
          //   top: 'middle',
          //   silent: true,     // 不响应事件
          //   invisible: item.length > 0,   // 有数据就隐藏
          //   style: {
          //     fill: '#fff',
          //     fontWeight: 'bold',
          //     text: '暂无数据',
          //     // fontFamily: 'Microsoft YaHei',
          //     fontSize: '30px'
          //   }
          // },
          xAxis: [
            {
              type: 'category',
              data: xData,
              splitLine: { show: false },
              axisTick: {
                //y轴刻度线
                show: false,
              },
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisLabel: {
                interval: 0,
                rotate: xData.length > 7 ? 40 : 0,
                textStyle: {
                  color: '#ccc',
                  fontSize: 28,
                  fontFamily: 'SourceHanSansCN-Medium',
                },
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  color: '#ccc',
                  fontSize: 28,
                  fontFamily: 'SourceHanSansCN-Medium',
                },
              },
            },
          ],
          series: [
            {
              cursor: 'auto',
              name: '',
              symbol: 'circle', //设定为实心点
              symbolSize: 15, //设定实心点的大小
              type: 'line',
              data: yData,
              barWidth: 20,
              itemStyle: {
                normal: {
                  color: function (params) {
                    if (params.data[2] !== '000') {
                      return 'red'
                    } else {
                      return 'rgb(0,136,212)'
                    }
                  },
                  borderColor: 'rgba(0,136,212,0.2)',
                  borderWidth: 30,
                  lineStyle: {
                    color: 'rgba(0,136,212)',
                  },
                },
              },
              label: {
                show: true, //开启显示
                position: 'top', //在上方显示
                textStyle: {
                  //数值样式
                  color: '#FFFFFF',
                  fontFamily: 'SourceHanSansCN-Regular',
                  fontSize: 28,
                },
                formatter: (params) => {
                  if (params.data[2] !== '000') {
                    return params.data[2]
                  } else {
                    return ''
                  }
                },
              },
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: 'rgba(0, 136, 212, 0.3)',
                      },
                      {
                        offset: 0.8,
                        color: 'rgba(0, 136, 212, 0)',
                      },
                    ],
                    false
                  ),
                  shadowColor: 'rgba(0, 0, 0, 0.1)',
                  shadowBlur: 40,
                },
              },
            },
          ],
        }
        myEcharts.setOption(option)
      },
    },
  })
</script>