<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>指标详情弹框</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <link rel="stylesheet" href="/static/citybrain/qyhx/css/map-info.css" />
  <script src="/static/citybrain/hjbh/js/vue.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <style>
    /* elementui列表下拉样式 */

    .el-input__inner {
      cursor: pointer;
      width: 300px;
      height: 70px;
      border-radius: 30px;
      color: #fff;
      background-color: #132c4ed0 !important;
      border: 1px solid #359cf8;
      font-size: 28px;
    }

    .el-date-editor .el-range-input {
      background: transparent;
      color: #fff;
      font-size: 22px;
    }

    .el-select-dropdown__item {
      font-size: 28px;
      color: #d2d3d4;
      line-height: 50px;
      height: 50px;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #3f6db3;
    }

    .el-select .el-input .el-select__caret {
      font-size: 20px;
    }

    .el-select-dropdown {
      max-width: 265px;
      /* left: 800px !important; */
      border: 1px solid #1b5ad7;
      background-color: rgba(19, 44, 78, 0.816);
    }

    .el-scrollbar__wrap {
      overflow-y: scroll !important;
      /* margin-bottom: -30px !important; */
      margin-right: -66px !important;
    }

    .dialog-top {
      display: flex;
      align-items: center;
    }

    .close {
      cursor: pointer;
      position: absolute;
      right: 45px;
      top: 85px;
    }

    .dialog-con {
      padding: 10px 20px;
      margin-top: 20px;
      height: calc(100% - 100px);
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <div class="container" style="position: relative;">
      <div class="dialog-top">
        <span style="margin-right: 40px;width: auto;">{{indexName}}</span>
        <el-select v-model="currentIndexId" ref="kkRef" @change="getINfo" filterable placeholder="请选择指标">
          <el-option v-for="item in zbSelectList" :key="item.zbid" :label="item.zbname" :value="item.zbid">
          </el-option>
        </el-select>
        <i class="el-icon-close close" @click="close"></i>
      </div>
      <div class="dialog-con" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="info-box">
          <ul class="info-ul">
            <li v-for="item in ulList">
              <p>{{item.name}}</p>
              <p>{{item.value}}</p>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var vm = new Vue({
      el: '#app',
      data: {
        zbSelectList: [],
        currentIndexId: null,
        ulList: [
          { name: '指标定义', value: '--' },
          { name: '指标数值', value: '--' },
          { name: '指标单位', value: '--' },
          { name: '所属系统', value: '--' },
          { name: '责任部门', value: '--' },
          // { name: '业务责任人', value: '--' },
          // { name: '分管领导', value: '--' },
          { name: '更新频率', value: '--' },
          { name: '阈值标准', value: '--' },
          { name: '阈值结果', value: '--' },
          { name: '更新时间', value: '--' },


        ],
        infoObj: {
          '指标定义': 'indicator_name',
          '指标数值': 'current_value',
          '指标单位': 'unit',
          '所属系统': 'belong_system',
          '责任部门': 'dept_resp',
          // '业务责任人': 'update_time',
          // '分管领导': 'update_freq',
          '更新频率': 'update_freq',
          '阈值标准': 'threshold_rule',
          '阈值结果': 'threshold_result',
          '更新时间': 'update_time',

        },
        indexName: "",
        loading: false,
      },
      mounted() {
        let that = this
        window.addEventListener('message', function (event) {
          if (event.data.type == '指标明细') {
            that.indexName = event.data.indexName;
            that.zbSelectList = event.data.data;
            that.currentIndexId = that.zbSelectList[0].zbid;
            that.getINfo()
          }
        })
      },

      methods: {
        getINfo() {
          this.loading = true;
          $api('cstz_gy_zbmx', { zbid: this.currentIndexId }).then((res) => {
            let result = res[0];
            this.loading = false;
            if (result) {
              this.ulList.forEach((item) => {
                let index = this.infoObj[item.name]
                item.value = result[index] || '--'
              })
            }
          })
        },
        close() {
          window.parent.postMessage(
            JSON.stringify({
              type: 'closeIframe',
              name: 'index-info',
            }),
            '*'
          )
        },
      },
    })
  </script>
</body>

</html>