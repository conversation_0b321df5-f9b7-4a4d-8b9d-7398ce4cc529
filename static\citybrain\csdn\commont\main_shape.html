<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>预警商圈点击弹窗</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      body {
        margin: 0;
        padding: 0;
        background-color: #00000065;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .sjzx-middle {
        position: relative;
        top: 595px;
        width: 1500px;
        height: 970px;
        background-color: #031827;
        box-shadow: -3px 2px 35px 0px #000000;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
        border-image-slice: 1;
      }
      .head {
        width: 100%;
        height: 70px;
        line-height: 70px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .head > span {
        font-size: 32px !important;
        font-weight: 500;
        color: #fff;
      }

      .img {
        cursor: pointer;
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url('/static/citybrain/csdn/img/cstz2-middle/close-hover.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .sjzx-middle-con {
        width: 100%;
        height: calc(100% - 100px);
        padding: 50px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-evenly;
        font-size: 30px;
        color: #fff;
      }
      .left,
      .right {
        width: 50%;
      }
      .line {
        line-height: 70px;
      }
      .btn_box {
        position: absolute;
        bottom: 100px;
        width: 40%;
      }
      .left .line:nth-child(4) {
        margin-top: 100px;
      }
      .to_btn {
        cursor: pointer;
        width: 175px;
        height: 54px;
        color: #fff;
        font-size: 35px;
        text-align: center;
        line-height: 50px;
        border: 1px solid rgb(72, 141, 231);
        background-color: rgba(8, 52, 110, 0.87);
      }
      .active_btn {
        cursor: no-drop;
        color: #999;
        background-color: rgba(8, 43, 88, 0.87);
      }
      #videoBox {
        position: absolute;
        top: 1120px;
        /* left: 751px; +1925*/
        left: 3950px !important;
        width: 750px;
        height: 350px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="sjzx-middle">
        <div class="head">
          <span>{{title}}</span>
          <div class="img" @click="closeMiddleIframe('main_shape')"></div>
        </div>
        <div class="sjzx-middle-con">
          <div class="left">
            <div class="line title" v-for="item,i in data">{{item.key}}：{{item.value}}</div>

            <!-- <div class="s-flex s-row-evenly btn_box">
              <div class="to_btn" @click="clickInfo">推送</div>
              <div class="to_btn active_btn" @click="clickNo">取消</div>
            </div> -->
          </div>
          <div class="right">
            <p class="s-w7 s-m-b-20 s-font-32">进度追踪</p>
            <div id="line_eh" style="width: 100%; height: 200px"></div>
            <div class="title s-m-t-10" v-for="item,i in progressData">
              {{item.key}}
              <span v-if="item.key!=''">：</span>
              {{item.value}}
            </div>
            <div class="s-m-t-20" id="videoBox">
              <!-- <img src="/static/citybrain/csrk_3840/img/csrk_right/city.png" alt="" /> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>

    <script>
      var ws = top.DHWsInstance
      window.addEventListener('message', function (event) {
        if (event.data && event.data.status == 'ctsz_yj') {
          let a = event.data.data
          let id = a.id
          vm.title = a.mc
          // let obj = [
          //   { key: '告警时间', value: a.sj },
          //   { key: '实时水位', value: a.sssw },
          //   { key: '预警等级', value: a.yjdj },
          //   { key: '事件类型', value: '城市管理-人口管理' },
          //   { key: '实时人数', value: a.ssrs + '人' + '(超均值' + a.cc + '%)' },
          //   { key: '七日均值', value: a.aveage_7 },
          //   { key: '预警等级', value: a.is_waring + '预警' },
          // ]
          // vm.progressData[0].value = a.areaName
          // vm.data = obj
          // vm.allData = a
          // vm.video_code = a.video_code
          // vm.create()
          vm.getData(id)
        }
      })

      let vm = new Vue({
        el: '#app',
        data: {
          title: '预警详情',
          allData: {},
          data: [
            // { key: '事件来源', value: '城市大脑' },
            // { key: '事件内容', value: '网格员人员异常聚集' },
            // { key: '事件编号', value: '000044245456ljd04065' },
            // { key: '事件时间', value: '2022-10-02 5:00' },
            // { key: '事件地点', value: '婺城区-城东街道-网格' },
            // { key: '事件类型', value: '城市管理-人口管理' },
          ],
          progressData: [
            // { key: '', value: '婺城区-城东街道-网格' },
            { key: '结案时间', value: '--' },
            { key: '结案信息', value: '--' },
          ],
          video_code: '',
        },
        mounted() {},
        methods: {
          // 获取数据
          getData(id) {
            let that = this
            $api('cjjswsj002', { id: id }).then((res) => {
              let obj = res[0]
              let num = +obj.yjdj
              let yjdj =
                num == 1 ? '特别重大' : num == 2 ? '重大预警' : num == 3 ? '较大预警' : num == 4 ? '一般预警' : ''
              that.data = [
                { key: '事件内容', value: obj.sjnr },
                { key: '事件时间', value: obj.sjsj },
                { key: '事件地点', value: obj.sjdd },
                { key: '事件类型', value: obj.sjlx },
                { key: '实时水位', value: obj.sssw },
                { key: '预警等级', value: yjdj },
              ]
              that.progressData = [
                { key: '结案时间', value: obj.jasj || '--' },
                { key: '结案信息', value: obj.jaxx || '--' },
              ]
              that.getEle('line_eh', obj.jdzz)
            })
          },
          // 创建视频
          create() {
            // 调用创建控件接口
            // if (!this.isLogin) {
            //     this.$Message.info('正在登陆客户端，请稍等......');
            //     return false;
            // }
            let _this = this
            // console.log();
            let videoObj = [
              {
                ctrlType: 'playerWin',
                ctrlCode: 'video_main_shape',
                ctrlProperty: {
                  displayMode: 1,
                  splitNum: 1,
                  channelList: [
                    {
                      channelId: _this.video_code,
                    },
                  ],
                },
                visible: true,
                domId: 'videoBox',
                dom: document.getElementById('videoBox'),
              },
            ]
            console.log('videoObj===>', videoObj)

            setTimeout(function () {
              ws.createCtrl(videoObj, document.getElementById('videoBox'))
                .then((res) => {
                  console.log(res)
                })
                .catch((e) => {
                  console.log(e)
                })
              ws.on('createCtrlResult', (res) => {
                console.warn(res)
              })
            }, 2000)
          },

          closeMiddleIframe(name) {
            ws.destroyCtrl(['video_main_shape'])
            let data = JSON.stringify({
              type: 'closeIframe',
              name: name,
            })
            window.parent.postMessage(data, '*')
          },
          clickInfo() {
            let data = this.allData
            axios({
              method: 'post',
              url: baseURL.url + '/jhyjzh-server/screen_api/event_push',
              data: {
                event_code: 1,
                find_way: 'aiznyp',
                collaborative_matters: '人员异常聚集',
                describe: '人员异常聚集',
                push_time: data.insert_time,
                lon: data.center_x,
                lat: data.center_y,
                address: data.areaName,
                grid: data.areaName,
                area_code: data.adcode,
                warning_level: data.is_waring + '预警',
              },
            }).then(function (data) {
              console.log(data)
              debugger
            })
          },
          clickNo() {},
          getEle(dom, jd) {
            var XName = ['上报', '处置中', '结案'],
              allData = [100, 100, 100]
            var numArr = []
            if (jd == '上报') {
              numArr = [100]
            } else if (jd == '处置中') {
              numArr = [100, 100]
            } else if (jd == '结案') {
              numArr = [100, 100, 100]
            }

            let charts01 = echarts.init(document.getElementById(dom))
            var data1 = [allData, numArr]
            var Line = ['线1', '线2']
            var color = ['#256589', '#2bfc08']
            var datas = []
            Line.map((item, index) => {
              if (index == 0) {
                datas.push({
                  symbolSize: 26,
                  symbol: 'circle',
                  hoverAnimation: false,
                  name: item,
                  type: 'line',
                  data: data1[index],
                  itemStyle: {
                    normal: {
                      borderWidth: 10,
                      color: color[index],
                    },
                  },
                })
              } else {
                datas.push({
                  symbolSize: 30,
                  symbol: 'circle',
                  hoverAnimation: false,
                  name: item,
                  type: 'line',
                  data: data1[index],
                  itemStyle: {
                    normal: {
                      borderWidth: 2,
                      color: color[index],
                    },
                  },
                })
              }
            })

            option = {
              grid: {
                left: '0%',
                top: '40%',
                bottom: '60%',
                right: '5%',
              },
              yAxis: [
                {
                  type: 'value',
                  position: 'right',
                  max: 100,
                  splitLine: {
                    show: false,
                  },
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    show: false,
                  },
                },
              ],
              xAxis: [
                {
                  type: 'category',
                  axisTick: {
                    show: false,
                  },
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: '#6A989E',
                    },
                  },
                  axisLabel: {
                    inside: true,
                    show: true,
                    textStyle: {
                      color: '#90deff', // x轴颜色
                      fontWeight: 'normal',
                      fontSize: 24,
                      lineHeight: -100,
                    },
                  },
                  data: XName,
                },
              ],
              series: datas,
            }

            charts01.setOption(option)
          },
        },
      })
    </script>
  </body>
</html>
