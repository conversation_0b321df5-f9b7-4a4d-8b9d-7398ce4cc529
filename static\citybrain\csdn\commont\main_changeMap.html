<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='UTF-8' />
  <title>切换地图场景</title>
  <link rel='stylesheet' href='/static/css/animate.css' />
  <script src='/static/citybrain/csdn/Vue/vue.js'></script>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .mapClick {
      width: 244px;
      height: 160px;
      border-radius: 10px;
      background-image: linear-gradient(to bottom, rgb(111 133 228), rgb(14, 64, 109), rgb(113 125 203));
      color: #ccc;
      font-size: 22px;
      cursor: pointer;
      padding-top: 8px;
      box-sizing: border-box;
    }

    .mapClick .mapClick_img1,
    .mapClick .mapClick_img3,
    .mapClick .mapClick_img4,
    .mapClick .mapClick_img2,
    .mapClick .mapClick_img5 {
      position: relative;
      width: 110px;
      height: 66px;
    }

    .mapClick .mapClick_img1:hover span,
    .mapClick .mapClick_img2:hover span,
    .mapClick .mapClick_img3:hover span,
    .mapClick .mapClick_img4:hover span,
    .mapClick .mapClick_img5:hover span {
      color: #fff;
      background-color: #3c76de;
    }

    .mapClick .mapClick_img1 span,
    .mapClick .mapClick_img3 span,
    .mapClick .mapClick_img4 span,
    .mapClick .mapClick_img2 span,
    .mapClick .mapClick_img5 span,
    .mapClick .mapClick_img6 span {
      position: absolute;
      right: 0;
      top: 36px;
      background-color: #0006;
      /* padding: 0 0; */
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center;
    }

    /*  .mapClick .mapClick_img1 span .active,
     .mapClick .mapClick_img2 span .active {
      background-color: #3c76de;
    } */
    .mapClick .mapClick_img1 {
      background: url('/static/citybrain/tckz/img/commont/qianse.png') no-repeat;
    }

    .mapClick .mapClick_img2 {
      background: url('/static/citybrain/tckz/img/commont/shense.png') no-repeat;
    }

    .mapClick .mapClick_img3 {
      background: url('/static/citybrain/tckz/img/commont/bm.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClick .mapClick_img4 {
      background: url('/static/citybrain/tckz/img/commont/jm.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClick .mapClick_img4 {
      background: url('/static/citybrain/tckz/img/commont/jm.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew {
      border-radius: 10px;
      background: #162b48;
      color: #ccc;
      font-size: 22px;
      cursor: pointer;
      padding: 5px;
      box-sizing: border-box;
    }

    .mapClickNew .mapClick_img1,
    .mapClickNew .mapClick_img3,
    .mapClickNew .mapClick_img4,
    .mapClickNew .mapClick_img2,
    .mapClickNew .mapClick_img5,
    .mapClickNew .mapClick_img6 {
      position: relative;
      width: 130px;
      height: 130px;
      border-radius: 10px;
      margin: 5px;
    }

    .mapClickNew .mapClick_img1:hover span,
    .mapClickNew .mapClick_img2:hover span,
    .mapClickNew .mapClick_img3:hover span,
    .mapClickNew .mapClick_img4:hover span,
    .mapClickNew .mapClick_img5:hover span,
    .mapClickNew .mapClick_img6:hover span {
      color: #fff;
      background-color: #3c76de;
    }

    .mapClickNew .mapClick_img1 span,
    .mapClickNew .mapClick_img3 span,
    .mapClickNew .mapClick_img4 span,
    .mapClickNew .mapClick_img2 span,
    .mapClickNew .mapClick_img5 span,
    .mapClickNew .mapClick_img6 span {
      position: absolute;
      right: 0;
      top: 100px;
      background-color: #0006;
      /* padding: 0 0; */
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center;
    }

    /*  .mapClick .mapClick_img1 span .active,
     .mapClick .mapClick_img2 span .active {
      background-color: #3c76de;
    } */
    .mapClickNew .mapClick_img1 {
      background: url('/static/citybrain/tckz/img/commont/qianse.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img2 {
      background: url('/static/citybrain/tckz/img/commont/shense.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img3 {
      background: url('/static/citybrain/tckz/img/commont/bm.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img4 {
      background: url('/static/citybrain/tckz/img/commont/qx.jpg') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img5 {
      background: url('/static/citybrain/tckz/img/commont/jm.png') no-repeat;
      background-size: 100% 100%;
    }

    .mapClickNew .mapClick_img6 {
      background: url('/static/citybrain/tckz/img/commont/jm.png') no-repeat;
      background-size: 100% 100%;
    }

    /* 左上角数字 */
    .num-icon {
      display: block;
      font-weight: bold;
      /* text-align: right;
      margin-right: 10px; */
      /* background-color: #0006; */
    }

    .suo-bg {
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      background-image: url('/static/citybrain/csdn/img/suo.png');
      background-position: center;
      background-size: 40px 40px;
      background-repeat: no-repeat;
      background-color: rgba(0, 0, 0, 0.396);
    }
  </style>
</head>

<body>
<div id='app'>
  <div class='mapClickNew'>
    <div style='display: flex; justify-content: space-evenly; align-items: center'>
      <div class='mapClick_img1 grey-bg' @click='changeMap0()'>
        <i class='num-icon'>0.2</i>
        <i v-if='!showScan' class='suo-bg'></i>
        <span :style="mapin_change==0 ? 'background-color: #3c76de' : ''">卫星影像</span>
      </div>
      <div class='mapClick_img1' @click='changeMap1()'>
        <span :style="mapin_change==1 ? 'background-color: #3c76de' : ''">卫星影像</span>
      </div>
      <div class='mapClick_img1' @click='changeMap2()'>
        <span :style="mapin_change==2 ? 'background-color: #3c76de' : ''">卫星影像</span>
      </div>
      <div class='mapClick_img2' @click='changeMap7()'>
        <span :style="mapin_change==7 ? 'background-color: #3c76de' : ''">矢量地图</span>
      </div>
      <div class='mapClick_img3' @click="changeMap('bm')">
        <span :style="mapin_changes=='bm' ? 'background-color: #3c76de' : ''">白模</span>
      </div>
      <div class='mapClick_img4' @click="changeMap('qx')">
        <span :style="mapin_changes=='qx' ? 'background-color: #3c76de' : ''">倾斜摄影</span>
      </div>
      <!--          <div class="mapClick_img5" @click="changeMap('jm')">-->
      <!--            <span :style="mapin_changes=='jm' ? 'background-color: #3c76de' : ''">精模</span>-->
      <!--          </div>-->
      <div class='mapClick_img6' @click="changeMap('dx')" style='display: none'>
        <span :style="mapin_changes=='dx' ? 'background-color: #3c76de' : ''">地形</span>
      </div>
    </div>
  </div>
</div>

<script>
  var changeMap = new Vue({
    el: '#app',
    data: {
      showScan: false,
      mapin_change: 7,
      mapin_changes: '',
      mapobj: {
        bm: ['qxLayer', 'jmLayer'],
        jm: ['qxLayer', 'bmLayer'],
        qx: ['bmLayer', 'jmLayer']
      }
    },
    mounted() {
      //   this.mapin_change = top.main_map_iconVm.mapin_change;
      this.showScan = localStorage.getItem('QRCode') ? true : false
      this.mapin_change = localStorage.getItem('mapType') ? localStorage.getItem('mapType') : 7
      this.mapin_changes = localStorage.getItem('mapTypes') ? localStorage.getItem('mapTypes') : ''
    },
    methods: {
      // 切换卫星影像2.0
      changeMap0() {
        this.showScan = localStorage.getItem('QRCode') ? true : false
        if (this.showScan) {
          this.mapin_change = 0
          localStorage.setItem('mapType', 0)
          top.mapUtil.tool.changeBaseMap('gfimg')
          this.closeIframe()
          this.togBackgroundImg()
        } else {
          let iframe1 = {
            type: 'openIframe',
            name: 'scan-box',
            src: '/static/citybrain/csdn/commont/scan-box.html',
            width: '380px',
            height: '420px',
            left: '4490px',
            top: '335px',
            zIndex: '1000'
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
        }
      },
      changeMap1() {
        //切换卫星影像
        this.mapin_change = 1
        localStorage.setItem('mapType', 1)
        // top.mapUtil.tool.change3D(1)
        top.mapUtil.tool.changeBaseMap('img')
        this.closeIframe()
        this.togBackgroundImg()
      },
      changeMap2() {
        //切换2023卫星影像
        this.mapin_change = 2
        localStorage.setItem('mapType', 2)
        top.mapUtil.tool.changeBaseMap('img2023')
        this.closeIframe()
        this.togBackgroundImg()
      },
      changeMap7() {
        //切换矢量地图
        this.mapin_change = 7
        localStorage.setItem('mapType', 7)
        // top.mapUtil.tool.change3D(7)
        top.mapUtil.tool.changeBaseMap('black')
        this.closeIframe()
        this.togBackgroundImg()
      },
      changeMap(type) {
        var id = type + 'Layer'
        if (this.mapin_changes == type) {
          this.mapin_changes = ''
          localStorage.setItem('mapTypes', '')
          top.mapUtil.removeLayer(id)
        } else {
          // let removeId = type == 'bm' ? 'qx' : type == 'qx' ? 'bm' : ''
          // top.mapUtil.removeLayer(removeId + 'Layer')
          console.log(this.mapobj[type])
          console.log(id)
          top.mapUtil.removeAllLayers(this.mapobj[type])
          top.mapUtil.loadModelLayer({
            layerid: id,
            type: type
          })
          this.mapin_changes = type
          localStorage.setItem('mapTypes', this.mapin_changes)
        }
        this.togBackgroundImg()
        this.closeIframe()
      },
      closeIframe() {
        top.frames['indexMapIcon'].mainIconVm.trunMapkuai = false
        let data = JSON.stringify({
          type: 'closeIframe',
          name: 'main_changeMap'
        })
        window.parent.postMessage(data, '*')
      },
      // 切换背景图片
      togBackgroundImg() {
        let mapType = localStorage.getItem('mapType')
        let mapTypes = localStorage.getItem('mapTypes')
        if (mapType != 0 && mapType != 1 && mapTypes != 'qx') {
          top.document.getElementsByClassName('page_left')[0].style.backgroundImage = 'url(\'/img/left-bg.png\')'
          top.document.getElementsByClassName('page_right')[0].style.backgroundImage = 'url(\'/img/right-bg.png\')'
          top.frames['ywtg_middle_tab'] ? (top.frames['ywtg_middle_tab'].vm.showBg = false) : ''
          top.document.getElementsByClassName('center-bottom')[0]
            ? (top.document.getElementsByClassName('center-bottom')[0].style.backgroundImage =
              'url(\'/static/citybrain/csdn/img/ywt/center-bc.png\')')
            : ''
        } else {
          top.document.getElementsByClassName('page_left')[0].style.backgroundImage = 'url(\'/img/left-bg2.png\')'
          top.document.getElementsByClassName('page_right')[0].style.backgroundImage = 'url(\'/img/right-bg2.png\')'
          top.frames['ywtg_middle_tab'] ? (top.frames['ywtg_middle_tab'].vm.showBg = true) : ''
          top.document.getElementsByClassName('center-bottom')[0]
            ? (top.document.getElementsByClassName('center-bottom')[0].style.backgroundImage =
              'url(\'/static/citybrain/csdn/img/ywt/center-bc2.png\')')
            : ''
        }
      }
      // changeMapbm() {
      //   //切换卫星影像
      //   this.mapin_changes = 'bm'
      //   top.mapUtil.removeLayer('qxLayer') //移除
      //   top.mapUtil.loadModelLayer({
      //     layerid: 'baimoLayer',
      //     type: 'bm',
      //   })
      // },
      // changeMapqx() {
      //   //切换矢量地图
      //   this.mapin_changes = 'jm'
      //   top.mapUtil.removeLayer('baimoLayer') //移除
      //   top.mapUtil.loadModelLayer({
      //     layerid: 'qxLayer',
      //     type: 'qx',
      //   })
      // },
    }
  })
</script>
</body>
</html>
