
    <!DOCTYPE html>
    <html>

    <head>
        <meta charset="utf-8" />
        <script src="./Vue/vue.js"></script>
        <script src="./jquery/jquery-3.6.1.min.js"></script>
        <script src="/static/citybrain/hjbh/js/echarts.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
    </head>
    <style>
        /* #ndtj {
            position: absolute;
            top: 200px;
            left: 200px;
        } */
        #leftEch {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 800px;
            height: 800px;
        }

        .rightEch {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 830px;
            height: 860px;
        }

        .rightEch #echarts001 {
            width: 830px;
            height: 420px;
        }

        .rightEch #echarts002 {
            width: 830px;
            height: 420px;
        }
    </style>

    <body>
        <div id="diong">
            <s-box :title="titleName + '情况'" ifrid="cstz2-dian-diong" style="width: 1700px;">
                <!-- 内容 -->
                <div style="height: 900px;position: relative;">
                    <div id="leftEch"></div>
                    <div class="rightEch">
                        <div id="echarts001"></div>
                        <div id="echarts002"></div>
                    </div>
                </div>
            </s-box>

        </div>

        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/js/jslib/Emiter.js"></script>
        <script src="/static/citybrain/hjbh/js/date.js"></script>

        <script>
            let wm=new Vue({
            el: '#diong',
            data:{
                titleName:""
            },
           created(){
                let that=this
                window.addEventListener('message',function (event){
                    if(event.data.indexOf('{')<0){
                        that.titleName=event.data
                        that.init()
                    }
                })
           },
            mounted() {

            },
            methods: {
                sortByUp(arr) {//降序
                    return function (a, b) {
                        return b[arr] - a[arr]
                    }
                },
                init(){
                    let that=this;
                    let line0Color={
                        color:"#00C0FF55",
                        areaColor:'rgba(0, 192, 255, 1)'
                    }
                    let line1Color={
                        color:"#00FFFF55",
                        areaColor:'rgba(0, 255, 255, 1)'
                    }

                    $api('/cstz_ydtc',{typeName:that.titleName}).then(res=>{
                        let yearArr=[];let dataArr={}; 
                        res.forEach(ele => {
                            yearArr.push(ele.year)
                        });
                        yearArr=Array.from(new Set(yearArr)).sort()
                        for (let i = 0; i < yearArr.length; i++) {
                            let attName='arr'+i;
                            dataArr[attName]=res.filter(item=>{
                                return item.year==yearArr[i]
                            })
                        }
                        for (let key in dataArr) {
                            let arr=[]
                            for (let a = 0; a < dataArr[key].length; a++) {
                                arr.push(parseInt(dataArr[key][a].type_value))
                            }
                            dataArr[key]=arr
                        }
                        that.getEcharts01('leftEch',that.titleName,yearArr,dataArr)
                    })
                    
                    $api('/cstz_dltbhb',{typeName:that.titleName}).then(res=>{
                        let monArr=[];let tongArr=[];huanArr=[];
                        res.forEach(ele=>{
                            monArr.push(ele.MONTH+'月')
                            tongArr.push(ele.same)
                            huanArr.push(ele.link)
                        })
                        that.setEcharts1('echarts001',line0Color,that.titleName,'同比增长',tongArr,monArr)
                        that.setEcharts1('echarts002',line1Color,that.titleName,'环比增长',huanArr,monArr)
                    })
                },
                getEcharts01(id,tet,year,objArr){
                    let charts01 = echarts.init(document.getElementById(id));
                    var time = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
                    
                    value1 =objArr.arr0,
                    value2 = objArr.arr1;
                    value3 = objArr.arr2;
                    let option = {
                    color: ["#0580f2", "#faa732", "#e1575d"],
                    title: {
                        text: tet+'年度变化情况',
                        x: '35%',
                        y:'10px',
                        textStyle: {
                        color: '#fff', //颜色
                        fontStyle: 'normal', //风格
                        fontWeight: 'normal', //粗细
                        fontSize: 26, //大小
                        align: 'right', //水平对齐
                        },
                    },
                    legend: {
                        top: "8%",
                        right: "2%",
                        itemWidth: 6,
                        // itemGap: 20,
                        textStyle: {
                        fontSize: 24,
                        color: "#78d5ff"
                        },
                    },
                    textStyle: {
                        color: '#fff', //颜色
                    },
                    grid: {
                        top:'13%',
                        left: '3%',
                        right: '3%',
                        bottom: '3%',
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        textStyle: {
                        fontSize: 35
                        },
                        formatter: function (e) {
                        if (e[2] == undefined) {
                            let value =e[0].seriesName+'年'+e[0].name +"：" + e[0].value+'千瓦' + "</br>"+
                                e[1].seriesName+'年'+e[1].name +"：" + e[1].value+'千瓦'
                            return value
                        }
                        let value = e[0].seriesName+'年'+e[0].name +"：" + e[0].value+'千瓦' + "</br>"+
                                e[1].seriesName+'年'+e[1].name +"：" + e[1].value+'千瓦' + "</br>"+
                                e[2].seriesName+'年'+e[2].name +"：" + e[2].value+'千瓦'
                        return value
                        },
                    },
                    xAxis: {
                        data: time,
                        axisLabel: {
                            interval:0,
                            rotate:45,
                            textStyle: {
                                color: '#fff', //坐标值得具体的颜色
                            },
                            fontSize: 24,
                        },
                    },
                    yAxis: {
                        // min: (value) => {
                        //     return (parseInt(value.min - 0.1))
                        // },
                        axisLabel: {
                            textStyle: {
                                color: '#fff', //坐标值得具体的颜色
                            },
                            fontSize: 24,
                        },
                        splitLine: { // 虚线
                            show: true,
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149,0.4)', //左边线的
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgba(88, 138, 139,.6)', //左边线的颜色
                                width: '0' //坐标线的宽度
                            },
                        },
                    },
                    series: [
                        {
                            name:year[0]+'年',
                            type: "line",
                            symbol: "circle",
                            symbolSize: [8, 8],
                            data: value1,
                        },
                        {
                            name: year[1]+'年',
                            type: "line",
                            data: value2,
                            symbol: "circle",
                            symbolSize: [8, 8],
                        },
                        {
                            name:year[2]+'年',
                            type: "line",
                            data: value3,
                            symbol: "circle",
                            symbolSize: [8, 8],
                        }
                    ],
                    };

                    charts01.setOption(option);
                },
                // 设置折线图
                setEcharts1(id,colors,tit,tit2,dataArr,time){    
                    let charts01 = echarts.init(document.getElementById(id));
                    let datax=  time
                    let datay=dataArr
                    
                    let option = {
                        title: {
                            text: tit+tit2,
                            x: '35%',
                            y:'30px',
                            textStyle: {
                            color: '#fff', //颜色
                            fontStyle: 'normal', //风格
                            fontWeight: 'normal', //粗细
                            fontSize: 26, //大小
                            align: 'right', //水平对齐
                            },
                        },
                        tooltip: {
                            //鼠标悬浮弹出提示框
                            trigger: "axis", //提示框弹出的触发时间，折线图和柱状图为axis
                            formatter: "2022年{b} : {c}%", //提示框提示的信息，{a}series内的名字，{b}为块状的名字，{c}为数值              
                            textStyle: {
                                fontSize: 30,
                            },
                        },
                        grid: {
                            //统计图距离边缘的距离
                            top: "25%",
                            left: "15%",
                            right: "2%",
                            bottom: "8%",
                        },
                        xAxis: [
                            {
                                type: "category", //数据类型为不连续数据
                                // boundaryGap: true, //坐标轴两边是否留白
                                axisLine: {
                                    //坐标轴轴线相关设置。数学上的x轴
                                    show: false,
                                    lineStyle: {
                                        color: "#233e64", //x轴颜色
                                    },
                                },
                                axisLabel: {
                                    interval:0,
                                    // rotate:45,
                                    //坐标轴刻度标签的相关设置
                                    textStyle: {
                                    color: "#ccc",
                                    fontSize:22
                                    },
                                },
                                axisTick: { show: true }, //刻度点数轴
                                data: datax
                            },
                        ],
                        yAxis: [
                            {
                            //y轴的相关设置
                            type: "value", //y轴数据类型为连续的数据
                            // 			min: 0,//y轴上的刻度最小值
                            // 			max:140,//y轴上的刻度最大值
                            splitNumber: 7, //y轴上的刻度段数
                            splitLine: {
                                //y轴上的y轴线条相关设置
                                show: true,
                                lineStyle: {
                                color: "#233e64",
                                },
                            },
                            axisLine: {
                                //y轴的相关设置
                                show: true,
                                lineStyle: {
                                color: "#233e64", //y轴颜色
                                },
                            },
                            axisLabel: {
                                //y轴的标签相关设置
                                formatter: "{value} %",
                                textStyle: {
                                color: "#ccc",
                                fontSize:22
                                },
                            },
                            axisTick: { show: true }, //刻度点数轴
                            },
                        ],
                        series: [
                            {
                            name: "",
                            type: "line", //统计图类型为折线图
                            smooth: true, //是否平滑曲线显示
                            symbolSize: 0, //数据点的大小，[0,0]//b表示宽度和高度
                            lineStyle: {
                                show:false,
                                //线条的相关设置
                                normal: {
                                color: "#3deaff", // 线条颜色
                                width: 0
                                },
                            },
                            areaStyle: {
                                //区域填充样式
                                normal: {
                                //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                                color: new echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [
                                    { offset: 0, color: colors.color },
                                    { offset: 1, color: colors.areaColor },
                                    ],
                                    false
                                ),
                                
                                shadowColor: "rgba(53,142,215, 0)", //阴影颜色
                                shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
                                },
                            },
                            data: datay
                            },
                        ],
                    };
        
                    charts01.setOption(option)
                }
      
            },
        })
        </script>
    </body>

    </html>
