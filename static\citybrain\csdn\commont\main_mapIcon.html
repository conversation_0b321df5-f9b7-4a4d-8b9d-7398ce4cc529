<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>右上地图操作按钮</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/animate.css" />
  <script src="/static/citybrain/csdn/Vue/vue.js"></script>
  <!-- <script src="./jquery/jquery-3.6.1.min.js"></script> -->
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/turf.js"></script>
  <!-- <script src="/static/citybrain/csdn/js/drawCircle.js"></script> -->
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
  <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
  <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
  <script src="/static/js/home_services/md5.js"></script>
  <script src="/static/citybrain/csdn/lib/base/vue-seamless-scroll.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/tckz/icon/iconfont.css" />
  <link rel="stylesheet" href="/static/citybrain/tckz/icon/font_3883404_c6cmje8nfb/iconfont.css" />

  <style>
    [v-cloak] {
      display: none;
    }

    body {
      margin: 0;
    }

    .rightBtn {
      /* position: absolute;
      right: 30px; */
      width: 127px;
      height: 720px;
      display: flex;
      flex-direction: column;
      /* justify-content: space-around; */
      align-items: center;
      overflow: hidden;
      /* transition: height 0.2s linear 0s; */
    }

    .rightBtn>div {
      margin-top: 5px;
    }

    .rightBtn p {
      margin: 0;
      cursor: pointer;
    }

    .rightBtn>p {
      margin-top: 5px !important;
    }

    .rightBtn>p:first-child {
      margin-top: 0 !important;
    }

    .rightBottom {
      position: absolute;
      right: 60px;
      top: 450px;
      /* width: 202px; */
      height: 250px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
    }

    .rightBtn .middleBtn01 {
      /* width: 95px;
              height: 95px; */
      width: 50px;
      height: 50px;
      background: url('/static/citybrain/tckz/img/main_mapIcon/home.png') 100% 100% no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }

    .rightBtn .middleBtn01:hover {
      background: url('/static/citybrain/tckz/img/main_mapIcon/home-active.png') 100% 100% no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }

    .rightBtn .middleBtn02 {
      width: 50px;
      height: 50px;
      background-size: 100% 100%;
    }

    .rightBtn .middleBtn03 {
      width: 50px;
      height: 50px;
      background: url('/static/citybrain/csdn/img/ywt/middle003.png') 100% 100% no-repeat;
      border: 1px solid #67b6f9;
    }

    .rightBtn .middleBtn03:hover {
      background: url('/static/citybrain/csdn/img/ywt/middle003-hover.png') 100% 100% no-repeat;
    }

    .rightBtn .middleBtn04 {
      width: 50px;
      height: 50px;
      background: url('/static/citybrain/csdn/img/ywt/middle004.png') 100% 100% no-repeat;
    }

    .rightBtn .middleBtn04:hover {
      background: url('/static/citybrain/csdn/img/ywt/middle004-hover.png') 100% 100% no-repeat;
    }

    .rightBtn .middleBtn055,
    .rightBtn .middleBtn06 {
      width: 50px;
      height: 50px;
      font-size: 30px;
      font-weight: bold;
      color: #d6e7f9;
      text-align: center;
      line-height: 45px;
      border: 1px solid #67b6f9;
      box-sizing: border-box;
      background-color: #1c2a47;
      border-radius: 8px;
    }

    .middleBtn066 {
      width: 50px;
      height: 50px;
      background-color: #1c2a47;
      border-radius: 8px;
    }

    /* .middleBtn066 >img{
      opacity: 0.6;
    }
    .middleBtn066:hover>img{
      opacity: 1;
    } */
    .rightBtn .middleBtn033 {
      width: 50px;
      height: 50px;
      cursor: pointer;
      /* border: 1px solid #67b6f9;
      box-sizing: border-box;
      background-image: url('/static/citybrain/tckz/img/main_mapIcon/middleBtn033.png');
      background-size: 103% 103%;
      background-repeat: no-repeat;
      background-position: -1px; */
    }

    .rightBtn .middleBtn033:hover {
      background-image: url('/static/citybrain/tckz/img/main_mapIcon/middleBtn033-hover.png');
      background-size: 104% 104%;
      background-repeat: no-repeat;
    }

    .middleBtn033-active {
      background-image: url('/static/citybrain/tckz/img/main_mapIcon/middleBtn033-hover.png') !important;
      background-size: 104% 104%;
      background-repeat: no-repeat;
    }

    .rightBtn .mapClick {
      position: absolute;
      top: 184px;
      right: 133px;
      width: 244px;
      display: flex;
      height: 76px;
      border-radius: 10px;
      background-image: linear-gradient(to bottom, rgb(111 133 228), rgb(14, 64, 109), rgb(113 125 203));
      color: #ccc;
      font-size: 22px;
      justify-content: space-evenly;
      align-items: center;
      cursor: pointer;
    }

    .rightBtn .mapClick .mapClick_img1,
    .rightBtn .mapClick .mapClick_img2 {
      position: relative;
      width: 110px;
      height: 66px;
    }

    .rightBtn .mapClick .mapClick_img1:hover span,
    .rightBtn .mapClick .mapClick_img2:hover span {
      color: #fff;
      background-color: #3c76de;
    }

    .rightBtn .mapClick .mapClick_img1 span,
    .rightBtn .mapClick .mapClick_img2 span {
      position: absolute;
      right: 0;
      top: 36px;
      /* padding: 0 0; */
      width: 100%;
      height: 30px;
      line-height: 30px;
      text-align: center;
    }

    /* .rightBtn .mapClick .mapClick_img1 span .active,
    .rightBtn .mapClick .mapClick_img2 span .active {
      background-color: #3c76de;
    } */
    .rightBtn .mapClick .mapClick_img1 {
      background: url('/static/citybrain/tckz/img/commont/qianse.png') no-repeat;
    }

    .rightBtn .mapClick .mapClick_img2 {
      background: url('/static/citybrain/tckz/img/commont/shense.png') no-repeat;
    }

    .search-box {
      width: 250px;
      position: absolute;
      top: 330px;
      right: 133px;
      color: #fff;
      background-image: linear-gradient(to bottom, rgb(111 133 228), rgb(14, 64, 109), rgb(113 125 203));
      border-radius: 10px;
      font-size: 30px;
      padding: 5px 20px;
      box-sizing: border-box;
    }

    .search-box>div {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-box>div>i {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #7387ff;
    }

    .search-box>div>span {
      display: inline-block;
      width: 140px;
      margin-left: 10px;
    }

    .search-box>div:last-child {
      margin-bottom: 0;
    }

    .sou-el>.el-button {
      border: 0 !important;
      color: #fff;
      width: 50px;
      height: 50px;
      border-radius: 12px;
      padding: 0;
      font-size: 24px;
      border: 1px solid #fff !important;
    }

    .middle02_2D {
      background-image: url('/static/citybrain/csdn/img/ywt/middle002D.png') !important;
    }

    .middle02_3D {
      background-image: url('/static/citybrain/csdn/img/ywt/middle002.png') !important;
    }

    .rightBtn .middleBtn06:hover {
      color: #fff;
      background: #1b63ae !important;
    }

    .mapPopup {
      box-shadow: none !important;
    }

    .ifont-30 {
      font-size: 30px !important;
    }

    .t-r-180 {
      display: inline-block;
      transform: rotateZ(180deg);
    }
  </style>
</head>

<body>
  <div id="app">
    <!-- v-show="!cstzState" -->
    <div class="rightBtn">
      <!-- 收缩 -->
      <p class="middleBtn066" title="收缩" @click="hideAndShowIcon">
        <img
          :src="showHide?'/static/citybrain/tckz/img/main_mapIcon/展开.png':'/static/citybrain/tckz/img/main_mapIcon/收起.png'"
          alt="" style="opacity: 1" />
      </p>
      <!-- 初始视角 -->
      <p class="middleBtn066" @click="mapNow" title="初始视角">
        <img src="/static/citybrain/tckz/img/main_mapIcon/地图复位b.png" alt="" />
      </p>
      <!-- 切换底图 -->
      <p class="middleBtn066" @click="openIframeMap" title="切换底图">
        <img
          :src="trunMapkuai?'/static/citybrain/tckz/img/main_mapIcon/地图切换b选中.png':'/static/citybrain/tckz/img/main_mapIcon/地图切换b.png'"
          alt="" />
      </p>
      <!-- 立体网格 -->
      <p class="middleBtn066" @click="openIframeMap3D" title="立体网格">
        <img
          :src="show3Dkuai?'/static/citybrain/tckz/img/main_mapIcon/立体网格模型选中.png':'/static/citybrain/tckz/img/main_mapIcon/立体网格模型.png'"
          alt="" />
      </p>
      <!-- 路况信息 -->
      <p class="middleBtn066" @click="openWid" title="路况信息">
        <img
          :src="showRoad?'/static/citybrain/tckz/img/main_mapIcon/路况信息b选中.png':'/static/citybrain/tckz/img/main_mapIcon/路况信息b.png'"
          alt="" />
      </p>
      <!-- 人群热力 -->
      <p class="middleBtn066" @click="openHot" title="人群热力">
        <img
          :src="showHot?'/static/citybrain/tckz/img/main_mapIcon/人口热力b选中.png':'/static/citybrain/tckz/img/main_mapIcon/人口热力b.png'"
          alt="" />
      </p>
      <!-- 区域查询 -->
      <p class="middleBtn066" @click="openCount" title="区域查询" id="qycxBtn">
        <img
          :src="showCount?'/static/citybrain/tckz/img/main_mapIcon/区域查询b选中.png':'/static/citybrain/tckz/img/main_mapIcon/区域查询b.png'"
          alt="" />
      </p>
      <!-- 要素查询 -->
      <!-- <p class="middleBtn066" @click="openCountYS" title="点选查询">
      <img
        :src="showCountYS?'/static/citybrain/tckz/img/main_mapIcon/要素查询b选中.png':'/static/citybrain/tckz/img/main_mapIcon/要素查询b.png'"
        alt=""
      />
    </p> -->
      <!-- 云查地 -->
      <p class="middleBtn066" @click="openYCD" title="云查地">
        <img src="/static/citybrain/tckz/img/main_mapIcon/云查地b.png" alt="" />
      </p>
      <!-- 搜索 -->
      <p class="middleBtn066" @click="searchFun" title="搜索" id="search">
        <img
          :src="searchClick?'/static/citybrain/tckz/img/main_mapIcon/搜索b选中.png':'/static/citybrain/tckz/img/main_mapIcon/搜索b.png'"
          alt="" />
      </p>
      <!-- 拨打电话 -->
      <p class="middleBtn066" @click="callPhoneFun" title="拨打电话">
        <img width="50px"
          :src="callPhone?'/static/citybrain/tckz/img/main_mapIcon/拨号选中.png':'/static/citybrain/tckz/img/main_mapIcon/拨号.png'"
          alt="" />
      </p>
      <!-- 路径规划 -->
      <p class="middleBtn066" @click="openRoute" title="路径规划">
        <img
          :src="routeClick?'/static/citybrain/tckz/img/main_mapIcon/route_a.png':'/static/citybrain/tckz/img/main_mapIcon/route.png'"
          alt="" />
      </p>
      <!-- 测量 -->
      <p class="middleBtn066" @click="openMapTools('测量')" title="测量">
        <img
          :src="showMapToolIndex=='测量'?'/static/citybrain/tckz/img/main_mapIcon/测量工具b选中.png':'/static/citybrain/tckz/img/main_mapIcon/测量工具b.png'"
          alt="" />
      </p>
      <!-- 标绘 -->
      <p class="middleBtn066" @click="openMapTools('标绘')" title="标绘">
        <img
          :src="showMapToolIndex=='标绘'?'/static/citybrain/tckz/img/main_mapIcon/绘图工具b选中.png':'/static/citybrain/tckz/img/main_mapIcon/绘图工具b.png'"
          alt="" />
      </p>
      <!-- 空间分析 -->
      <p class="middleBtn066" @click="openMapTools('空间分析')" title="空间分析" id="kjfxBtn">
        <img
          :src="showMapToolIndex=='空间分析'?'/static/citybrain/tckz/img/main_mapIcon/空间分析b选中.png':'/static/citybrain/tckz/img/main_mapIcon/空间分析b.png'"
          alt="" />
      </p>
      <!-- 地图工具 -->
      <!-- <p class="middleBtn06" :style="showToolbar ? 'background:#1B63AE' : ''" @click="openTool" title="地图工具">
      <i class="el-icon-s-tools"></i>
    </p> -->
      <!-- 图标搜索功能 -->
    </div>
  </div>
  <script src="/static/js/jslib/coordtransform.js"></script>
  <script>
    var mainIconVm = new Vue({
      el: '#app',
      data: {
        distance: null,
        // mapin_change: 7, //卫星影像1 矢量地图7
        showHide: false,
        trunMapkuai: false,
        trunMapkuai2: false,
        show3Dkuai: false,
        callPhone: false,
        click2D: false,
        showRoad: false,
        showHot: false,
        showCount: false,
        showCountYS: false,
        showQuhuaL4: false,
        showYCD: false,
        roadData: [],
        searchClick: false,
        routeClick: false,
        showToolbar: false,
        showMapTools: false,
        showMapToolIndex: null,
        is_put: false,
        videoMapId: [],
        timeOut: null,
        cameraList: [
          {
            name: '枪机在线',
            code: 'camera-zx-qiangji',
          },
          {
            name: '枪机离线',
            code: 'camera-lx-qiangji',
          },
          {
            name: '球机在线',
            code: 'camera-zx-qiuji',
          },
          {
            name: '球机离线',
            code: 'camera-lx-qiuji',
          },
          {
            name: '半球机在线',
            code: 'camera-zx-banqiu',
          },
          {
            name: '半球机离线',
            code: 'camera-lx-banqiu',
          },
          {
            name: '高点在线',
            code: 'camera-zx-gaodian',
          },
          {
            name: '高点离线',
            code: 'camera-lx-gaodian',
          },
        ],
      },
      mounted() {
        let that = this
        this.hideAndShowIcon()
        this.creditAuth()
      },
      methods: {
        hideAndShowIcon() {
          let height = document.getElementsByClassName('rightBtn')[0].clientHeight
          if (height == 766) {
            this.showHide = true
            document.getElementsByClassName('rightBtn')[0].style.height = '50px'
            parent.document.getElementsByClassName('index_main_mapIcon')[0].style.height = '60px'
            if (parent.document.getElementsByClassName('map-tool')[0]) {
              parent.document.getElementsByClassName('map-tool')[0].style.display = 'none'
            }
            sessionStorage.setItem('showMainIcon', true)
            top.postMessage({ type: 'showMainIcon', display: 'none' }, '*')
          } else {
            this.showHide = false
            document.getElementsByClassName('rightBtn')[0].style.height = '820px'
            parent.document.getElementsByClassName('index_main_mapIcon')[0].style.height = '820px'
            if (parent.document.getElementsByClassName('map-tool')[0]) {
              parent.document.getElementsByClassName('map-tool')[0].style.display = 'block'
            }
            sessionStorage.setItem('showMainIcon', false)
            top.postMessage({ type: 'showMainIcon', display: 'block' }, '*')
          }
        },
        openQuhuaL4() {
          let that = this
          that.showQuhuaL4 = !that.showQuhuaL4
          let moveLeft = '4815px'
          let moveTop = '800px'
          if (that.showQuhuaL4) {
            let iframe1 = {
              type: 'openIframe',
              name: 'wwgl',
              src: baseURL.url + '/static/citybrain/tckz/wggl.html', //
              width: '600px',
              height: '950px',
              left: moveLeft,
              top: '200px',
              zIndex: 997,
              argument: {},
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else {
            window.parent.frames["wggl"].postMessage(
              { wg_clear: "清除wg" },
              "*"
            );
            window.parent.lay.closeIframeByNames(["wggl"]);
          }
        },
        // 绘制区域查询人数
        openCount() {
          let that = this
          if (that.callPhone) this.callPhoneFun()
          if (that.searchClick) that.searchFun()
          // if (that.showToolbar) that.openTool()
          if (that.showMapTools) that.openMapTools()
          if (this.routeClick) this.openRoute()
          that.showCount = !that.showCount
          let moveLeft = '4890px'
          let moveTop = '800px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6930px'
            moveTop = '820px'
            this.is_put = true
          }
          if (that.showCount) {
            let iframe1 = {
              type: 'openIframe',
              name: 'sou_range',
              src: baseURL.url + '/static/citybrain/tckz/sou_range.html', //
              width: '500px',
              height: '950px',
              left: moveLeft,
              top: '200px',
              zIndex: 997,
              argument: {},
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
            // top.mapUtil.plotTool.active('polygon', (oncomplete) => {
            //   that.createWLGZ(oncomplete)
            //   // that.getCode(oncomplete)
            // })
          } else {
            if (top.frames['sou_range']) {
              window.parent.frames['sou_range'].rVm.initMap()
              window.parent.postMessage(
                JSON.stringify({
                  type: 'closeIframe',
                  name: 'sou_range',
                }),
                '*'
              )
            }
            // top.mapUtil.plotTool.close()
            // window.parent.mapUtil.removeLayer('drawMine')
            // if (top.frames['person_count']) {
            //   top.frames['person_count'].postMessage({ clear_point: '清除poi点位' }, '*')
            //   window.parent.postMessage(
            //     JSON.stringify({
            //       type: 'closeIframe',
            //       name: 'person_count',
            //     }),
            //     '*'
            //   )
            // }
          }
        },
        // 要素查询
        openCountYS() {
          let that = this
          that.showCountYS = !that.showCountYS
          if (that.showCountYS) {
            let moveLeft = '4650px'
            // 判断左右页面是否是收起和展开的状态
            if (
              top.document
                .getElementsByClassName('index_main_mapIcon')[0]
                .getAttribute('class')
                .indexOf('map_mapIcon_move') > -1
            ) {
              moveLeft = '6700px'
            }
            window.parent.postMessage(
              JSON.stringify({
                type: 'openIframe',
                name: 'person_countYS',
                src: baseURL.url + '/static/citybrain/csdn/commont/person_countYS.html',
                width: '750px',
                height: '700px',
                left: moveLeft,
                top: '500px',
                zIndex: '666',
                argument: {},
              }),
              '*'
            )
          } else {
            window.parent.mapUtil.removeLayer('drawMine')
            if (top.frames['person_countYS']) {
              top.frames['person_countYS'].postMessage({ clear_point: '清除poi点位' }, '*')
              window.parent.postMessage(
                JSON.stringify({
                  type: 'closeIframe',
                  name: 'person_countYS',
                }),
                '*'
              )
            }
          }
        },
        callPhoneFun() {
          if (this.showCount) this.openCount()
          if (this.showCountYS) this.openCountYS()
          if (this.showMapTools) this.openMapTools()
          if (this.searchClick) this.searchFun()
          if (this.routeClick) this.openRoute()
          this.callPhone = !this.callPhone
          let moveLeft = '4550px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6600px'
          }
          if (this.callPhone) {
            let leftData = {
              type: 'openIframe',
              name: 'callPhone_diong',
              src: baseURL.url + '/static/citybrain/tckz/callPhone_diong.html',
              width: '865px',
              height: '835px',
              left: moveLeft,
              top: '180px',
              zIndex: 997,
            }
            window.parent.postMessage(JSON.stringify(leftData), '*')
          } else {
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'callPhone_diong',
            })
            window.parent.postMessage(data, '*')
            try {
              window.parent.postMessage(JSON.stringify({ type: 'closeIframe', name: 'zbVideo' }), '*')
            } catch (error) { }
            try {
              window.parent.postMessage(JSON.stringify({ type: 'closeIframe', name: 'zbPhone' }), '*')
            } catch (error) { }
          }
        },
        //打开工具栏
        openTool() {
          if (this.callPhone) this.callPhoneFun()
          if (this.searchClick) this.searchFun()
          if (this.showCount) this.openCount()
          if (this.showCountYS) this.openCountYS()
          if (this.showMapTools) this.openMapTools()
          if (this.routeClick) this.openRoute()
          this.is_put = false
          this.showToolbar = !this.showToolbar
          let moveLeft = '4890px'
          let moveTop = '800px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6930px'
            moveTop = '820px'
            this.is_put = true
          }
          if (this.showToolbar) {
            let iframe1 = {
              type: 'openIframe',
              name: 'main_toolbar',
              src: baseURL.url + '/static/citybrain/csdn/commont/index_toolbar.html',
              width: '500px',
              height: '480px',
              left: moveLeft,
              top: moveTop,
              zIndex: 997,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else {
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'main_toolbar',
            })
            window.parent.postMessage(data, '*')
          }
        },
        openMapTools(toolName) {
          if (this.callPhone) this.callPhoneFun()
          if (this.searchClick) this.searchFun()
          if (this.showCount) this.openCount()
          if (this.showCountYS) this.openCountYS()
          if (this.routeClick) this.openRoute()
          let moveLeft = '4890px'
          let close = JSON.stringify({
            type: 'closeIframe',
            name: 'map_toolbar',
          })
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6930px'
          }
          if (!toolName) {
            this.showMapToolIndex = null
            this.showMapTools = false
            window.parent.postMessage(close, '*')
            return
          }
          if (this.showMapToolIndex == toolName) {
            this.showMapToolIndex = null
            this.showMapTools = false
            window.parent.postMessage(close, '*')
          } else if (toolName) {
            this.showMapToolIndex = toolName
            this.showMapTools = true
            let iframe1 = {
              type: 'openIframe',
              name: 'map_toolbar',
              src: baseURL.url + '/static/citybrain/csdn/commont/map_toolbar.html', //
              width: '500px',
              height: '1200px',
              left: moveLeft,
              top: '200px',
              zIndex: 997,
              argument: {
                type: 'map_toolbar',
                data: { toolName },
              },
            }
            window.parent.postMessage(close, '*')
            setTimeout(() => {
              window.parent.postMessage(JSON.stringify(iframe1), '*')
            }, 400)
          }
        },
        // 打开搜索弹窗
        searchFun() {
          // if (this.showToolbar) this.openTool()
          if (this.callPhone) this.callPhoneFun()
          if (this.showCount) this.openCount()
          if (this.showCountYS) this.openCountYS()
          if (this.showMapTools) this.openMapTools()
          if (this.routeClick) this.openRoute()
          this.searchClick = !this.searchClick
          let moveLeft = '4132px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6172px'
          }

          if (this.searchClick) {
            let leftData = {
              type: 'openIframe',
              name: 'index_name_sou',
              src: baseURL.url + '/static/citybrain/csdn/commont/index_name_sou.html',
              width: '1279px',
              height: '240px',
              left: moveLeft,
              top: '180px',
              zIndex: 997,
            }
            window.parent.postMessage(JSON.stringify(leftData), '*')
          } else {
            // 清除搜索网格
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'index_name_sou',
            })
            window.parent.postMessage(data, '*')
          }
        },
        // 最优路径 弹窗
        openRoute() {
          // if (this.showToolbar) this.openTool()
          if (this.callPhone) this.callPhoneFun()
          if (this.showCount) this.openCount()
          if (this.showCountYS) this.openCountYS()
          if (this.showMapTools) this.openMapTools()
          if (this.searchClick) this.searchFun()
          this.routeClick = !this.routeClick
          let moveLeft = '4600px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6650px'
          }
          if (this.routeClick) {
            window.parent.postMessage(
              JSON.stringify({
                type: 'openIframe',
                name: 'best_route',
                src: baseURL.url + '/static/citybrain/tckz/best_route.html',
                width: '801px',
                height: '1070px',
                left: moveLeft,
                top: '200px',
                zIndex: 999,
              }),
              '*'
            )
          } else {
            window.parent.frames['best_route'].postMessage({ clear_route: '清除路线' }, '*')
          }
        },
        // 上热力图
        openHot() {
          this.showHot = !this.showHot
          if (this.showHot) {
            /*$api('/cstz_qxrlt_new').then((res) => {
  let hotMapData = []
  let heatArr = []
  let len = res[0].heatmap.length
  let sumLen = 20000 - len
  if (len >= 20000) {
    heatArr = res[0].heatmap.slice(0, 20000)
  } else {
    heatArr = res[0].heatmap
    for (let j = 0; j < sumLen; j++) {
      let a = {
        count: 0,
        geohash: 0,
        lat: 0,
        lng: 0,
      }
      heatArr.push(a)
    }
  }
  heatArr.map((item) => {
    // 画热力图的数据
    let pointArr = []
    pointArr[0] = item.lng
    pointArr[1] = item.lat
    pointArr[2] = item.count
    pointArr[3] = item.geohash
    hotMapData.push(pointArr)
  })
  const mapData = {
    layerid: 'rkztHot0',
    data: hotMapData,
    distance: 800,
    alpha: 0.3,
    threshold: 6000,
  }

  top.mapUtil.loadHeatmapLayer(mapData)
})*/
            window.parent.ArcGisUtils.removeDEMFromMap(window.parent.view) //移除地势
            const mapData = {
              layerid: 'rkztHot0',
              type: 'dynamic',
            }

            top.mapUtil.loadHeatmapLayer(mapData)
          } else {
            window.parent.ArcGisUtils.addDEMToMap(window.parent.view) //加地势
            this.rmLayer('rkztHot0')
          }
        },
        // 打开路况
        async openWid() {
          let that = this
          this.showRoad = !this.showRoad
          // 畅通 缓行 拥堵 严重拥堵
          // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
          let hxData = []
          let ydData = []
          let yzydData = []
          let lineStr = ''
          let lineArr = []
          // 清除路况
          let mapIdArr = [
            'icon_road_index',
            'hxRoad_index',
            'ydRoad_index',
            'yzydRoad_index',
            '拥堵视频',
            '拥堵点1',
            'camera-index',
          ]
          this.rmAllLayer(mapIdArr)
          top.mapUtil.removeLayer('mouseente01')
          top.mapUtil.removeLayer('mouseente02')
          //  清除所有视频
          for (let i = 0; i < this.videoMapId.length; i++) {
            let id = this.videoMapId[i]
            this.rmLayer('拥堵视频' + id)
            this.rmLayer('roadText' + id)
          }
          // 清除定时器
          let end = setInterval(function () { }, 3)
          for (let i = 1; i <= end; i++) {
            clearInterval(i)
          }
          clearTimeout(this.timeOut)
          if (this.showRoad) {
            //日配额6万，秒并发17 百度的接口
            window.parent.mapUtil.loadTrafficLayer({
              layerid: 'icon_road_index',
            })

            this.pointRoadFun()

            let time = setInterval(() => {
              that.timeOut = setTimeout(that.pointRoadFun, 0)
            }, 1000 * 120)

            /*let data = await axios({
                method: 'get',
                url: 'https://jiaotong.baidu.com/openapi/v2/event/alarmlist?nodeId=947&roadType=1,2,3,4,5&eventSource=2,3&ak=9EY1KFlfi8pZZIol7QnCLlpRfCZTb6Zh&returnType=2',
                // url: 'https://jiaotong.baidu.com/openapi/v2/event/alarmlist?nodeId=947&roadType=1,2,3,4,5&eventSource=2,3&ak=lLZMejGEb5i744956UJWHr93RWALFE7u&returnType=2',
              })
              let color = [0, 255, 0, 1]
              let ctColor = [113, 205, 174, 1]
              let hxColor = [252, 194, 95, 1]
              let ydColor = [250, 108, 103, 1]
              let yzydColor = [180, 18, 14, 1]
              that.roadData = data.data.result.map((item) => {
                return item.linkStates
              })
              let index = 0
              that.roadData.forEach((item) => {
                for (let key in item) {
                  color =
                    key == 1
                      ? ctColor
                      : key == 2
                      ? hxColor
                      : key == 3
                      ? ydColor
                      : key == 4
                      ? yzydColor
                      : [80, 167, 255, 1]
                  let strData = item[key]
                  if (key != 1) {
                    let arrData = strData.split(';')
                    for (let i = 0; i < arrData.length; i++) {
                      // 排序
                      // let str=that.roadSort(arrData[i])
                      // 不排序
                      let str = arrData[i]
                      let arr = str.split(/[,;]/)
                      let coords = that.transTo4490(arr, color)
                      let coordsStr = coords.join(',')
                      lineArr.push(coordsStr)
                      key == 2
                        ? hxData.push(coords)
                        : key == 3
                        ? ydData.push(coords)
                        : key == 4
                        ? yzydData.push(coords)
                        : ''
                    }
                  }
                }
              })

              lineStr = lineArr.join(';')
              top.mapUtil.loadPolylineLayer({
                layerid: 'hxRoad_index',
                data: hxData,
                style: {
                  width: 10, //线宽
                  color: [250, 108, 103, 1], //rgba
                },
              })

              top.mapUtil.loadPolylineLayer({
                layerid: 'ydRoad_index',
                data: ydData,
                style: {
                  width: 10, //线宽
                  color: [250, 108, 103, 1], //rgba
                },
              })

              top.mapUtil.loadPolylineLayer({
                layerid: 'yzydRoad_index',
                data: yzydData,
                style: {
                  width: 10, //线宽
                  color: [250, 108, 103, 1], //rgba
                },
              })
              top.mapUtil.loadRoadVideo({
                layerid: '拥堵视频',
                videoType: '拥堵视频',
                distance: 100,
                lineStr: lineStr,
                onclick: this.onclick,
              })*/
          }
        },
        // 给拥堵上点
        pointRoadFun() {
          let that = this
          let str = "'婺城区','金东区','武义县','浦江县','磐安县','兰溪市','义乌市','东阳市','永康市','开发区'"
          // 清除路况
          // let mapIdArr = ['hxRoad_index', 'ydRoad_index', 'yzydRoad_index', '拥堵视频', '拥堵点1']
          // this.rmAllLayer(mapIdArr)
          // //  清除所有视频
          // for (let i = 0; i < this.videoMapId.length; i++) {
          //   let id = this.videoMapId[i]
          //   this.rmLayer(id)
          // }
          $api('/cstz_baiduydd', { addressName: str }).then((res) => {
            for (let i = 0; i < res.length; i++) {
              let time = new Date(res[i].insert_time)
              let h = time.getHours(),
                m = time.getMinutes()
              res[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
              res[i].idx = Number(res[i].idx)
            }
            var roadPointData = []
            res.map((ele) => {
              let roadName = `${ele.roadName}`
              let address = ele.description ? ele.description + ele.direction : '--'
              let arr = ele.location.split(',')
              let pointArr = that.transTo4490(arr)
              let point = pointArr[0] + ',' + pointArr[1]
              let str = {
                data: {
                  item: ele,
                  title: roadName,
                  linkStates: ele.linkStates,
                  key: ['指数', '时速', '拥堵距离', '持续时间'],
                  value: [ele.idx, ele.speed, ele.distance, ele.durationMin],
                  distance: ele.distance,
                },
                address: address,
                point: point,
                lng: pointArr[0],
                lat: pointArr[1],
                type: '路况拥堵点',
                idx: ele.idx,
                location: ele.location,
                linkStates: ele.linkStates,
              }
              roadPointData.push(str)
            })
            top.mapUtil.loadPointLayer({
              data: roadPointData,
              layerid: '拥堵点1',
              iconcfg: { image: '拥堵', iconSize: 0.5 },
              onclick: this.onclick,
            })
            clearTimeout(this.timeOut)
          })
        },
        onclick(e, list) {
          top.mapUtil.removeLayer('mouseente01')
          top.mapUtil.removeLayer('mouseente02')
          if (e.data.chn_code) {
            top.mapUtil.flyTo({
              destination: [e.data.gps_x, e.data.gps_y],
              // zoom: 15,
              offset: [0, -999],
            })
            let item = {
              obj: {
                // chn_name: e.data.chn_name,
                chn_name: e.data.video_name,
                pointList: list,
              },
              video_code: e.data.chn_code,
              csrk: true,
            }
            let iframe1 = {
              type: 'openIframe',
              name: 'video_main_code',
              src: baseURL.url + '/static/citybrain/csdn/commont/video_main_code.html',
              width: '100%',
              height: '100%',
              left: '0',
              top: '0',
              zIndex: '1000',
              argument: item,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else if (e.type == '路况拥堵点') {
            let coor = [e.lng, e.lat]
            let arr = {
              name: e.data.key,
              value: e.data.value,
            }
            let countStr = ''
            for (let index = 0; index < arr.name.length; index++) {
              countStr += `<div style='margin:0 10px'>${arr.name[index]}：${arr.value[index]}</div>`
            }
            let str = `
             <div
              style="
                position: relative;
                background: url('/static/citybrain/csdn/img/du_bg.png') no-repeat;
                background-size: 100% 100%;
                width: max-content;
                min-height: 320px;
              "
             >
                <nav class='s-flex s-m-l-20 s-m-r-20 s-row-between'>
                  <h2 style='margin-top: 20px; white-space: nowrap;font-size: 38px;' class='s-c-red-gradient s-flex'>
                    <img src='/static/citybrain/csdn/img/dupoint.png' width='50px' alt='' />
                    ${e.data.title}${e.data.item.eventSource}（${e.data.item.insert_time}）
                    <span style='font-size: 30px !important;font-style: italic;'>${e.data.item.congestAffectArea}</span>
                  </h2>
                  <span class='s-m-l-20 s-font-32 s-c-white' style='cursor: pointer;font-size:40px;width:34px;' onclick="this.parentNode.parentNode.style.display = 'none'">x</span>
                </nav>

                <header class='s-m-l-20 s-m-r-20 s-m-t-20 s-m-b-15 s-flex s-row-between;padding:10px;' style='padding:10px;box-sizing: border-box;'>
                  <div style='width: 80%; overflow: hidden' class='s-flex'>
                    <img src='/static/citybrain/csdn/img/address.png' width='40px' height='50px' class='s-m-r-10' alt='' />
                     <div>
                      <p class='s-c-yellow-gradient' style='width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;font-size:34px' title='${e.address}'>${e.address}</p>
                      <p  style='color: #fff;width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;font-size:32px' title='${e.data.item.congestTailDesc}'>源头：${e.data.item.congestTailDesc}</p>
                    </div>
                  </div>
                  <div
                    class='s-c-white'
                    style='background: rgba(216, 81, 81, 0.897); padding: 2px 5px; border-radius: 5px;height:40px;line-height:40px;font-size:30px;'
                  >${e.data.item.extraEventStatus}</div>
                </header>
                <footer
                  class='s-flex s-row-around s-m-l-20 s-m-r-20 s-m-b-20'
                  style='color: #05BE94; white-space: nowrap;font-size:30px'
                >
                ${countStr}
                </footer>
              </div> `
            let objData = {
              layerid: e.layerid,
              position: coor,
              offset: [36, -100],
              closeButton: true,
              content: str,
            }

            top.mapUtil._createPopup(objData)
            this.roadMapFun(e)
          }
        },
        onblur(e) {
          //onblur
          let info = e.data
          let str = ''
          if (e.status == 0) {
            str = `<div onclick=" this.style.display = 'none'"
                        style='
                          width: 300px;
                          position: absolute;
                          border-radius: 5px;
                          background-color: rgba(10, 31, 53, 0.8);
                          z-index: 999999;
                          box-shadow: inset 0 0 40px 0 #5ba3fa;
                          padding: 24px;'>
                    <div class='container1' style='font-size: 30px;color: white;text-align: center;'>
                      设备离线中...
                    </div>
                  </div>`
            let objData = {
              layerid: 'mouseente01',
              position: [e.lng, e.lat],
              content: str,
              offset: [50, 100],
            }
            top.mapUtil._createPopup(objData)
          } else {
            $api('xxwh_bqcx_name', { chnCode: info.chn_code }).then((res) => {
              $api('xxwh_dwzl_video_path', { chnCode: info.chn_code }).then((el) => {
                let url = ''
                let lable = ''
                let des = ''

                if (el[0] && el[0].path != null) {
                  url = baseURL.url + '/imgPath/' + el[0].path.split('fileServer/')[1]
                } else {
                  url = '/static/citybrain/tckz/img/video/404.png'
                }
                if (res[0]) {
                  let labelName = res[0].lableName.split(',').slice(0, 3)
                  let description = res[0].description.split('，')
                  labelName.forEach((item) => {
                    lable += `<div style='color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;'>${item}
                              </div>`
                  })
                  description.forEach((item) => {
                    des += `<div style='color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;'>${item}
                              </div>`
                  })
                } else {
                  lable = `<div style='color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;'>暂无
                              </div>`
                  des = `<div style='color: #dbdee2;
                                  font-size: 28px;
                                  height: 40px;
                                  line-height: 40px;
                                  padding: 0 20px;
                                  box-sizing: border-box;
                                  border-radius: 10px;
                                  margin-right: 10px;
                                  margin-bottom: 10px;'>暂无
                          </div>`
                }

                str = `<div onclick=" this.style.display = 'none'"
                          style='
                            width: 800px;
                            position: absolute;
                            border-radius: 5px;
                            background-color: rgba(10, 31, 53, 0.8);
                            z-index: 999999;
                            box-shadow: inset 0 0 40px 0 #5ba3fa;
                            padding: 24px;'>

                      <div class='container1'>
                        <div style='display:flex;justify-content: space-between;'>
                          <p title='${info.video_name
                  }' style='height: 30px;line-height: 30px;color: #fff;font-size: 30px;
                                  white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'>${info.video_name}</p>
                          <img style='width:30px;height:30px;'
                              src='${info.is_collection == 0
                    ? '/static/citybrain/tckz/img/not-col.png'
                    : '/static/citybrain/tckz/img/col.png'
                  }'>
                        </div>
                        <div style='width:100%;display:flex;flex-wrap: wrap;margin-top: 10px;'>
                          <span style='font-size:30px;color:#fff;line-height:40px;'>标签：</span>
                          ${lable}
                        </div>
                        <div style='width:100%;display:flex;flex-wrap: wrap;margin-top: 0px;'>
                          <span style='font-size:30px;color:#fff;line-height:40px;'>视频内容：</span>
                          ${des}
                        </div>
                        <img src='${url}' alt='' style='width:100%;height:400px;margin-top: 10px;'>
                      </div>
                    </div>`
                let objData = {
                  layerid: 'mouseente02',
                  position: [e.lng, e.lat],
                  content: str,
                  offset: [50, 100],
                }
                top.mapUtil._createPopup(objData)
              })
            })
          }
        },
        roadMapFun(obj) {
          let that = this
          let arrLngLats = obj.data.linkStates
          // 畅通 缓行 拥堵 严重拥堵
          // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
          this.rmAllLayer(['hxRoad_index', 'ydRoad_index', 'yzydRoad_index'])
          top.mapUtil.removeAllLayers(['camera-index'])
          //  清除所有视频
          for (let i = 0; i < this.videoMapId.length; i++) {
            let id = this.videoMapId[i]
            this.rmLayer('拥堵视频' + id)
            this.rmLayer('roadText' + id)
          }
          let hxData = []
          let ydData = []
          let yzydData = []
          let point = obj.location.split(',')
          let pointArr = that.transTo4490(point)
          this.videoMapId = []
          let arrList = []
          for (let key of Object.keys(arrLngLats)) {
            let line = arrLngLats[key]
            let arrData = line.split(';')
            let lineArr = []
            let lineStr = ''
            for (let i = 0; i < arrData.length; i++) {
              // 排序
              // let str=that.roadSort(arrData[i])
              // 不排序
              let str = arrData[i]
              let arr = str.split(/[,;]/)
              let coords = that.transTo4490(arr)
              let coordsStr = coords.join(',')
              lineArr.push(coordsStr)
              obj.idx >= 1.5 && obj.idx < 2
                ? hxData.push(coords)
                : obj.idx >= 2 && obj.idx < 4
                  ? ydData.push(coords)
                  : obj.idx >= 4
                    ? yzydData.push(coords)
                    : ''
            }
            lineStr = lineArr.join(';')
            let len = Math.ceil(lineArr.length / 2 - 1)
            let linePoint = lineArr[len].split(',')

            that.videoMapId.push(key)
            top.mapUtil.loadTextLayer({
              layerid: 'roadText' + key,
              data: [
                {
                  pos: [linePoint[0], linePoint[1]], //上文字经纬度
                  //内容
                  text: obj.data.distance,
                },
              ], //数据
              style: {
                size: 40, //文字大小
                color: [252, 198, 42, 1], //文字颜色
              },
            })
            top.mapUtil.loadRoadVideo({
              layerid: '拥堵视频' + key,
              videoType: '拥堵视频' + key,
              distance: 30,
              lineStr: lineStr,
              // onclick: this.onclick,
              callback: (e) => {
                e.forEach((item) => {
                  arrList.push(item)
                })
              },
            })
          }
          that.addPoint(arrList)
        },
        async addPoint(arrList) {
          // console.log('arrList=>',arrList)
          //去重
          let forData = []
          for (let i = 0; i < arrList.length; i++) {
            if (!forData.some((e) => e.id == arrList[i].id)) forData.push(arrList[i])
          }
          // console.log('forData=>',forData)
          let arr = []
          for (let i = 0; i < forData.length; i++) {
            let res = await $api('/xxwh_dwzlmore', { code: forData[i].chn_code }).then((res) => {
              let cameraType = res[0].isHighAltitude == 1 ? '4' : res[0].cameraType
              return {
                code: res[0].chn_code,
                pointId: 'camera-index',
                data: res[0],
                point: res[0].gps_x + ',' + res[0].gps_y,
                lng: res[0].gps_x,
                lat: res[0].gps_y,
                status: res[0].is_online,
                cameraType: cameraType,
                pointType: this.getPointType(res[0].is_online, cameraType),
              }
            })
            arr.push(res)
          }
          console.log(arr)
          // this.cameraList.forEach((item) => {
          //   this.getManyPoint(this.filterData(arr, item.name), item.code)
          // })
          this.getManyPoint(arr)
        },
        getPointType(is_online, cameraType) {
          let arr = is_online + '-' + cameraType
          let obj = {
            枪机在线: '1-1',
            枪机离线: '0-1',
            球机在线: '1-2',
            球机离线: '0-2',
            半球机在线: '1-3',
            半球机离线: '0-3',
            高点在线: '1-4',
            高点离线: '0-4',
          }
          for (key in obj) {
            if (obj[key] == arr) {
              return key
            }
          }
        },
        // filterData(mapObj, name) {
        //   let obj = {
        //     枪机在线: [1, '1'],
        //     枪机离线: [0, '1'],
        //     球机在线: [1, '2'],
        //     球机离线: [0, '2'],
        //     半球机在线: [1, '3'],
        //     半球机离线: [0, '3'],
        //     高点在线: [1, '4'],
        //     高点离线: [0, '4'],
        //   }
        //   return mapObj.filter((item) => {
        //     return item.status == obj[name][0] && item.cameraType == obj[name][1]
        //   })
        // },
        //一次绘制多种不同类型的点
        getManyPoint(pointData, pointId) {
          // if (pointData.length > 0) {
          //   top.mapUtil.loadPointLayer({
          //     data: pointData,
          //     layerid: pointId,
          //     iconcfg: {
          //       image: pointId,
          //       iconSize: 0.5,
          //     }, //图标
          //     onclick: this.onclick,
          //     onblur: this.onblur,
          //   })
          // }
          top.mapUtil.loadPointLayer({
            layerid: 'camera-index',
            data: pointData,
            onclick: this.onclick,
            onblur: this.onblur,
            cluster: true, //是否定义为聚合点位：true/false
            iconcfg: {
              image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
              iconSize: 0.5,
              iconlist: {
                field: 'pointType',
                list: [
                  {
                    value: '枪机在线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                  },
                  {
                    value: '枪机离线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
                  },
                  {
                    value: '球机在线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                  },
                  {
                    value: '球机离线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
                  },
                  {
                    value: '半球机在线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                  },
                  {
                    value: '半球机离线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
                  },
                  {
                    value: '高点在线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                  },
                  {
                    value: '高点离线',
                    size: '50',
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                  },
                ],
              },
            },
          })
        },
        // 处理路况的排序
        roadSort(item) {
          let allArr = []
          let ccc = item.split(',')
          let a, b
          let aIndex = 0
          let bIndex = 1
          let arrData = []
          ccc.forEach((str, index) => {
            if (index % 2 === 0) {
              a = str
              aIndex += 1
            } else {
              b = str
              bIndex += 1
            }
            if (a && b && bIndex - aIndex === 1) {
              let d = a + ',' + b
              arrData.push(d)
            }
          })
          let sortData = arrData.sort()
          allArr.push(...sortData)
          let strData = allArr.toString()
          return strData
        },
        // 处理路况的经纬度
        transTo4490(arr) {
          const length = arr.length / 2
          const pointArr = []
          // let mapName = top.mapUtil.map
          for (let i = 1; i <= length; i++) {
            const index = i * 2
            let jwd1 = coordtransform.bd09togcj02(arr[index - 2], arr[index - 1])
            let jwd2 = coordtransform.gcj02towgs84(jwd1[0], jwd1[1])
            let str = [jwd2[0], jwd2[1]].concat(0)
            pointArr.push(str)
          }
          return pointArr.flat()
        },
        // 打开地图场景盒子
        openIframeMap() {
          this.trunMapkuai = !this.trunMapkuai
          let moveLeft = '4580px'
          // let moveLeft = '4700px'
          //let moveLeft = '4390px'
          let moveTop = '200px'
          // let moveLeft = '5182px'
          // let moveTop = '340px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6630px'
            // moveLeft = '6750px'
            //moveLeft = '6450px'
            moveTop = '220px'
            // moveLeft = '7230px'
            // moveTop = '385px'
          }
          if (this.trunMapkuai) {
            let iframe1 = {
              type: 'openIframe',
              name: 'main_changeMap',
              src: baseURL.url + '/static/citybrain/csdn/commont/main_changeMap.html',
              width: '850px',
              // width: '730px',
              //width: '1040px',
              // width: '250px',
              // height: '160px',
              left: moveLeft,
              top: moveTop,
              zIndex: 997,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else {
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'main_changeMap',
            })
            // top.frames['main_changeMap'].changeMap.mapin_changes = ''
            window.parent.postMessage(data, '*')
            // top.mapUtil.tool.changeBaseMap('black')
            // top.mapUtil.removeLayer('bmLayer') //移除
            // top.mapUtil.removeLayer('qxLayer') //移除
          }
        },
        // 打开地图场景盒子
        openIframeMap3D() {
          this.show3Dkuai = !this.show3Dkuai
          let moveLeft = '4860px'
          let moveTop = '375px'
          // 判断左右页面是否是收起和展开的状态
          if (
            top.document
              .getElementsByClassName('index_main_mapIcon')[0]
              .getAttribute('class')
              .indexOf('map_mapIcon_move') > -1
          ) {
            moveLeft = '6910px'
            moveTop = '220px'
          }
          if (this.show3Dkuai) {
            let iframe1 = {
              type: 'openIframe',
              name: 'main_changeMap3D',
              src: baseURL.url + '/static/citybrain/csdn/commont/main_changeMap3D.html',
              width: '780px',
              height: '450px',
              left: '4660px',
              top: '193px',
              zIndex: 997,
            }
            window.parent.postMessage(JSON.stringify(iframe1), '*')
          } else {
            //清除立体网格图层
            this.clearAllLayers()
            let data = JSON.stringify({
              type: 'closeIframe',
              name: 'main_changeMap3D',
            })
            window.parent.postMessage(data, '*')

          }
        },
        //清除所有图层
        clearAllLayers() {
          // 使用常量定义正则表达式，以提高代码的可维护性
          const LayerRegex = /ltwg/;

          // 获取所有层的键，并筛选出匹配 MapWarningList 的层
          const layerKeys = Object.keys(top.mapUtil.layers).filter(item => LayerRegex.test(item));

          // 安全地移除匹配的层
          try {
            if (layerKeys && layerKeys.length) {
              top.mapUtil.removeAllLayers(layerKeys);
            } else {
              return
            }
          } catch (error) {
            console.error('Failed to remove ltwgLayers:', error);
          }
        },
        // 打开云查地 弹窗
        openYCD() {
          // top.vm.middleShow = false

          let nowData = Date.parse(new Date())
          let formData = new FormData()
          formData.append('appKey', '66f1608e-c30e-4f47-984e-dde2d29c682b')
          formData.append(
            'sign',
            hex_md5('66f1608e-c30e-4f47-984e-dde2d29c682b' + 'a323e590-3731-490d-ab52-82878d55760c' + nowData)
          )
          formData.append('time', nowData)
          formData.append(
            'source',
            JSON.stringify({
              rings: [
                [
                  [122.06377843006963, 30.071171396012677],
                  [122.06377843006963, 30.070334208577542],
                  [122.06428074253071, 30.070769546043813],
                  [122.06377843006963, 30.071171396012677],
                ],
              ],
            })
          )
          formData.append(
            'sourceType',
            JSON.stringify({
              rings: [
                [
                  [122.06377843006963, 30.071171396012677],
                  [122.06377843006963, 30.070334208577542],
                  [122.06428074253071, 30.070769546043813],
                  [122.06377843006963, 30.071171396012677],
                ],
              ],
            })
          )
          // formData.append('userId', '3131234')
          // formData.append('userName', '张三')
          formData.append('userId', top.commonObj.userId)
          formData.append('userName', top.commonObj.userInfo.nickName)
          axios({
            method: 'post',
            url: '/ycd/zjgt89/f7b706e9b59344bcae9058d58a231340/open/dcy/v1/submitTask',
            data: formData,
          }).then((res) => {
            // console.log(res)
            // debugger
            if (res.data.success) {
              // window.open(res.data.result)

              //第一种打开方式
              window.open(
                res.data.result,
                '项目接入系统',
                'directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=' +
                2160 +
                ', width=' +
                3840 +
                ', top=' +
                0 +
                ', left=' +
                1920 +
                ''
              )

              //第二种打开方式
              // top.emiter &&
              //   top.emiter.once('beforeCloseIframe', () => {
              //     top.vm.middleShow = true
              //   })
              // let url = baseURL.url + '/gtdcyfxjg/' + res.data.result.split('/gtdcyfxjg/')[1]
              // top.commonObj.openWinHtml('3840', '2160', url)
            } else {
              top.commonObj.Toast('对不起，没有权限')
            }
          })
        },
        mapNow() {
          this.click2D = false
          //回到初始位置
          top.mapUtil.tool.backHome()
        },
        map2Dor() {
          console.log('extrusionJson==>', extrusionJson)
          //切换2D或3D
          let num = this.click2D ? 3 : 2
          top.mapUtil.tool.changeMode(num)
          this.click2D = !this.click2D
          if (this.click2D) {
            /*top.mapUtil.map.addLayers({
  id: '3DRoom',
  type: 'fill-extrusion',
  source: {
    type: 'geojson',
    data: extrusionJson,
  },
  layout: {},
  paint: {
    'fill-extrusion-color': '#fff',
    'fill-extrusion-opacity': 1,
    'fill-extrusion-height': ['/', ['get', 'Shape_Leng'], 10],
    // "fill-extrusion-translate":[1,10],
  },
})*/
            top.mapUtil.tool.change3D(10)
            top.mapUtil.tool.changeScene()
          } else {
            this.rmLayer('3DRoom')
            // 7是深色地图
            top.mapUtil.tool.change3D(1)
          }
        },
        mapMix() {
          //缩大地图
          top.mapUtil.tool.zoomIn()
        },
        mapMis() {
          //缩小地图
          top.mapUtil.tool.zoomOut()
        },
        // 清除地图
        rmLayer(id) {
          top.mapUtil.removeLayer(id)
        },
        rmAllLayer(id) {
          top.mapUtil.removeAllLayers(id)
        },
        //计算多要素面及中空面最小外接矩形
        getRect(coordinates) {
          let minX
          let minY
          let maxX
          let maxY

          if (coordinates[0][0][0][0]) {
            //多要素面
            // 初始化最小外接矩形的坐标为第一个点
            minX = coordinates[0][0][0][0]
            minY = coordinates[0][0][0][1]
            maxX = coordinates[0][0][0][0]
            maxY = coordinates[0][0][0][1]
            // 遍历所有点，更新最小外接矩形的坐标
            for (let i = 0; i < coordinates.length; i++) {
              const polygon = coordinates[i]
              for (let j = 0; j < polygon.length; j++) {
                const ring = polygon[j]
                for (let k = 0; k < ring.length; k++) {
                  const point = ring[k]
                  const x = point[0]
                  const y = point[1]
                  minX = Math.min(minX, x)
                  minY = Math.min(minY, y)
                  maxX = Math.max(maxX, x)
                  maxY = Math.max(maxY, y)
                }
              }
            }
          } else {
            //中空面
            // 初始化最小外接矩形的坐标为第一个点
            minX = coordinates[0][0][0]
            minY = coordinates[0][0][1]
            maxX = coordinates[0][0][0]
            maxY = coordinates[0][0][1]
            // 遍历所有点，更新最小外接矩形的坐标
            for (let i = 0; i < coordinates.length; i++) {
              for (let j = 0; j < coordinates[i].length; j++) {
                const x = coordinates[i][j][0]
                const y = coordinates[i][j][1]

                minX = Math.min(minX, x)
                minY = Math.min(minY, y)
                maxX = Math.max(maxX, x)
                maxY = Math.max(maxY, y)
              }
            }
          }
          // 构建最小外接矩形的嵌套数组
          const rectangle = [
            [
              [minX, minY],
              [maxX, minY],
              [maxX, maxY],
              [minX, maxY],
              [minX, minY],
            ],
          ]
          // 返回最小外接矩形的嵌套数组
          return rectangle
        },

        createWLGZ(faceMap) {
          let this_ = this
          // 创建区域
          if (faceMap.geometry.coordinates.length > 1) {
            //对多要素面及空心面绘制外接矩形，适配查询接口
            let rect = this.getRect(faceMap.geometry.coordinates)
            faceMap.geometry.coordinates = this.getRect(faceMap.geometry.coordinates)
          }
          if (faceMap.geometry.coordinates[0] != []) {
            let str = ''
            faceMap.geometry.coordinates[0].forEach((ele) => {
              str += ele[0] + ',' + ele[1] + '|'
            })
            let faceStr = str.slice(0, str.length - 1)
            axios({
              method: 'post',
              url: baseURL.url + '/typeq/api/getu/project/create',
              data: {
                shape: faceStr,
                precision: 7,
              },
            }).then(function (res) {
              this_.getCount(res.data, faceMap)
            })
          }
        },
        getCount(id, faceMap) {
          let this_ = this
          let d = new Date()
          let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d
            .getHours()
            .toString()
            .padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d
              .getSeconds()
              .toString()
              .padStart(2, '0')}`
          var frontOneHour = new Date(d.getTime() - 3 * 60 * 60 * 1000)
          let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1
            }-${frontOneHour.getDate()} ${frontOneHour.getHours().toString().padStart(2, '0')}:${frontOneHour
              .getMinutes()
              .toString()
              .padStart(2, '0')}:${frontOneHour.getSeconds().toString().padStart(2, '0')}`

          axios({
            method: 'post',
            url: baseURL.url + '/typeq/api/getu/project/get',
            data: {
              id: id.data,
              type: 2,
              start_time: end,
              end_time: start,
            },
          }).then(function (res) {
            if (faceMap.properties.panelType && faceMap.properties.panelType == 'clickQuery') {
              let moveLeft = '4650px'
              // 判断左右页面是否是收起和展开的状态
              if (
                top.document
                  .getElementsByClassName('index_main_mapIcon')[0]
                  .getAttribute('class')
                  .indexOf('map_mapIcon_move') > -1
              ) {
                moveLeft = '6700px'
              }
              window.parent.postMessage(
                JSON.stringify({
                  type: 'openIframe',
                  name: 'person_countYS',
                  src: baseURL.url + '/static/citybrain/csdn/commont/person_countYS.html',
                  width: '750px',
                  height: '580px',
                  left: moveLeft,
                  top: '500px',
                  zIndex: '666',
                  argument: { person_count_data: { mapGeoJson: faceMap, countAll: res.data.data } },
                }),
                '*'
              )
            } else if (res.data && res.data.errno == 0) {
              let moveLeft = '4650px'
              // 判断左右页面是否是收起和展开的状态
              if (
                top.document
                  .getElementsByClassName('index_main_mapIcon')[0]
                  .getAttribute('class')
                  .indexOf('map_mapIcon_move') > -1
              ) {
                moveLeft = '6700px'
              }
              window.parent.postMessage(
                JSON.stringify({
                  type: 'openIframe',
                  name: 'person_count',
                  src: baseURL.url + '/static/citybrain/csdn/commont/person_count.html',
                  width: '750px',
                  height: '580px',
                  left: moveLeft,
                  top: '500px',
                  zIndex: '666',
                  argument: { person_count_data: { mapGeoJson: faceMap, countAll: res.data.data } },
                }),
                '*'
              )
            }
          })
        },
        // 查询人数
        getCode(faceMap) {
          let this_ = this
          if (faceMap.geometry.coordinates[0] != []) {
            let str = ''
            faceMap.geometry.coordinates[0].forEach((ele) => {
              str += ele[0] + ',' + ele[1] + '|'
            })
            let faceStr = str.slice(0, str.length - 1)
            let d = new Date()
            let start = `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()} ${d
              .getHours()
              .toString()
              .padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d
                .getSeconds()
                .toString()
                .padStart(2, '0')}`
            var frontOneHour = new Date(d.getTime() - 2 * 60 * 60 * 1000)
            let end = `${frontOneHour.getFullYear()}-${frontOneHour.getMonth() + 1
              }-${frontOneHour.getDate()} ${frontOneHour.getHours().toString().padStart(2, '0')}:${frontOneHour
                .getMinutes()
                .toString()
                .padStart(2, '0')}:${frontOneHour.getSeconds().toString().padStart(2, '0')}`

            axios({
              method: 'post',
              url: baseURL.url + '/typeq/api/getu/project/getByGeohash',
              data: {
                shape: faceStr,
                type: 2,
                start_time: end,
                end_time: start,
              },
            }).then(function (res) {
              if (res.data.data) {
                let moveLeft = '4650px'
                // 判断左右页面是否是收起和展开的状态
                if (
                  top.document
                    .getElementsByClassName('index_main_mapIcon')[0]
                    .getAttribute('class')
                    .indexOf('map_mapIcon_move') > -1
                ) {
                  moveLeft = '6700px'
                }
                window.parent.postMessage(
                  JSON.stringify({
                    type: 'openIframe',
                    name: 'person_count',
                    src: baseURL.url + '/static/citybrain/csdn/commont/person_count.html',
                    width: '750px',
                    height: '580px',
                    left: moveLeft,
                    top: '500px',
                    zIndex: '666',
                    argument: { person_count_data: { mapGeoJson: faceMap, countAll: res.data.data } },
                  }),
                  '*'
                )
              }
            })
          }
        },
        /*
         * 鉴权
         */
        creditAuth() {
          var appKey = 'zj_jh-API',
            masterSecret = 'vqVINnikQoL8fHbfgLzdzC62'

          var timestamp = Number(Math.round(new Date().getTime() / 1000).toString())

          var time_md5 = CryptoJS.MD5(timestamp.toString()).toString()

          var o_str = appKey + time_md5 + masterSecret,
            sha256_str = CryptoJS.SHA256(o_str).toString()

          var sign = sha256_str + masterSecret

          const reqParams = {
            appKey: appKey,
            sign: sign,
            timestamp: timestamp,
            version: 'v1.0',
          }

          return axios({
            method: 'post',
            url: baseURL.url + '/typeq/api/auth/creditAuth',
            data: reqParams,
          }).then(function (res) {
            if (res.data.errno === 0) {
              window.accessToken = res.data.data.accessToken

              axios.defaults.headers.common['Access-Token'] = res.data.data.accessToken
            }
          })
        },
      },
      beforeDestroy() {
        // 清除定时器
        let end = setInterval(function () { }, 3)
        for (let i = 1; i <= end; i++) {
          clearInterval(i)
        }
        clearTimeout(this.timeOut)
      },
    })
    let timer = null;
    top.emiter.on("AiOpenWgLayer", (res) => {
      //未打开则打开
      if (!mainIconVm.show3Dkuai) {
        mainIconVm.openIframeMap3D()
        timer = tcglInitTimer(openMapLayer.bind(this, res));
      } else {
        //已打开
        timer = tcglInitTimer(openMapLayer.bind(this, res));
      }
    })
    top.emiter.on("AiCloseWgLayer", (res) => {
      //未打开则打开
      if (!mainIconVm.show3Dkuai) {
        mainIconVm.openIframeMap3D()
        timer = tcglInitTimer(closeMapLayer.bind(this, res));
      } else {
        //已打开
        timer = tcglInitTimer(closeMapLayer.bind(this, res));
      }
    })
    function openMapLayer(res) {
      top.emiter.emit('AiOpenWgMapLayer', res)
    }
    function closeMapLayer(res) {
      top.emiter.emit('AiCloseWgMapLayer', res)
    }
    function tcglInitTimer(...callbacks) {
      const that = this;
      let timer = setInterval(() => {
        if (top.window["main_changeMap3D"]) {
          clearInterval(timer);
          callbacks.forEach(callback => callback.call(that)); // 遍历并调用所有回调
        } else {
          return;
        }
      }, 3000);

      // 返回定时器ID，以便在外部可以清除定时器
      return timer;
    }
    top.emiter.on('longActiveType', () => {
      mainIconVm.showHot = false
      mainIconVm.showCount = false
      mainIconVm.showCountYS = false
      mainIconVm.searchClick = false
      mainIconVm.trunMapkuai = false
      mainIconVm.showToolbar = false
      top.mapUtil.plotTool.close()
      if (mainIconVm.callPhone) mainIconVm.callPhoneFun()
      if (mainIconVm.routeClick) mainIconVm.openRoute()
      if (mainIconVm.showMapTools) mainIconVm.openMapTools()
      // 清除定时器
      let end = setInterval(function () { }, 3)
      for (let i = 1; i <= end; i++) {
        clearInterval(i)
      }
      clearTimeout(this.timeOut)
      if (mainIconVm.showRoad) {
        mainIconVm.showRoad = false
        mainIconVm.openWid()
      }
    })
    top.emiter.on('rightIframeShow', () => {
      try {
        parent.document.getElementById('index_name_sou').style.left = '4132px'
      } catch (error) { }
      try {
        parent.document.getElementById('callPhone_diong').style.left = '4550px'
      } catch (error) { }
      try {
        parent.document.getElementById('person_count').style.left = '4650px'
      } catch (error) { }
      try {
        parent.document.getElementById('person_countYS').style.left = '4650px'
      } catch (error) { }
      try {
        parent.document.getElementById('main_changeMap').style.left = '4490px'
        parent.document.getElementById('main_changeMap').style.top = '175px'
      } catch (error) { }
      try {
        parent.document.getElementById('main_toolbar').style.left = '4890px'
        parent.document.getElementById('main_toolbar').style.top = '800px'
      } catch (error) { }
      try {
        parent.document.getElementById('map_toolbar').style.left = '4890px'
      } catch (error) { }
      try {
        parent.document.getElementById('best_route').style.left = '4600px'
      } catch (error) { }
    })
    top.emiter.on('rightIframeHide', () => {
      try {
        parent.document.getElementById('index_name_sou').style.left = '6172px'
      } catch (error) { }
      try {
        parent.document.getElementById('callPhone_diong').style.left = '6600px'
      } catch (error) { }
      try {
        parent.document.getElementById('person_count').style.left = '6700px'
      } catch (error) { }
      try {
        parent.document.getElementById('person_countYS').style.left = '6700px'
      } catch (error) { }
      try {
        parent.document.getElementById('main_changeMap').style.left = '6550px'
        parent.document.getElementById('main_changeMap').style.top = '220px'
      } catch (error) { }
      try {
        parent.document.getElementById('main_toolbar').style.left = '6930px'
        parent.document.getElementById('main_toolbar').style.top = '820px'
      } catch (error) { }
      try {
        parent.document.getElementById('map_toolbar').style.left = '6930px'
      } catch (error) { }
      try {
        parent.document.getElementById('best_route').style.left = '6650px'
      } catch (error) { }
    })
    window.parent.emiter.on('drawShapeSeachCount', (geoJson) => {
      if (mainIconVm.callPhone) mainIconVm.callPhoneFun()
      if (mainIconVm.searchClick) mainIconVm.searchFun()
      if (mainIconVm.showMapTools) mainIconVm.openMapTools()
      if (mainIconVm.routeClick) mainIconVm.openRoute()
      if (mainIconVm.showCount) mainIconVm.openCount()
      mainIconVm.showCountYS = true
      try {
        window.parent.mapUtil.removeLayer('drawMine')
        window.parent.mapUtil.plotTool.close()
      } catch { }
      if (!geoJson.hl)
        window.parent.mapUtil.loadPolygonLayer({
          layerid: 'drawMine',
          data: {
            type: 'FeatureCollection',
            features: [{ type: 'Feature', geometry: geoJson.geometry, properties: { area: geoJson.area } }],
          },
          zoomToLayer: false, //禁止绘制时的 飞行事件，默认为true
          style: {
            strokeColor: [247, 185, 14, 1], //多边形轮廓颜色透明度
            fillColor: [247, 185, 14, 0.1], //多边形填充色
          },
        })
      mainIconVm.createWLGZ({
        type: 'Feature',
        geometry: geoJson.searchgeometry,
        properties: {
          area: geoJson.area,
          searchgeometry: geoJson.searchgeometry,
          panelType: 'clickQuery',
          arclayerid: geoJson.graphic?.layer.id || '',
        },
      })
      window.parent.frames['person_countYS'].vm.$refs.multipleTable.clearSelection()
    })
  </script>
</body>

</html>